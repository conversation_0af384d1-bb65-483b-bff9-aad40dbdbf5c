<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

switch ($metodo) {
    case 'GET':
        // Listar todas as listas do usuário
        $query = "SELECT id, nome, descricao, cor, data_criacao, data_atualizacao
                 FROM appestudo.lexjus_listas
                 WHERE usuario_id = $1
                 ORDER BY data_atualizacao DESC";

        $result = pg_query_params($conexao, $query, [$usuario_id]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao consultar listas']);
            exit;
        }

        $listas = [];
        while ($row = pg_fetch_assoc($result)) {
            // Para cada lista, buscar os artigos
            $query_artigos = "SELECT artigo_numero
                             FROM appestudo.lexjus_lista_artigos
                             WHERE lista_id = $1";

            $result_artigos = pg_query_params($conexao, $query_artigos, [$row['id']]);

            $artigos = [];
            while ($artigo = pg_fetch_assoc($result_artigos)) {
                $artigos[] = $artigo['artigo_numero'];
            }

            $listas[] = [
                'id' => (int)$row['id'],
                'nome' => $row['nome'],
                'descricao' => $row['descricao'],
                'cor' => $row['cor'] ?? '#e74c3c',
                'data_criacao' => $row['data_criacao'],
                'data_atualizacao' => $row['data_atualizacao'],
                'artigos' => $artigos
            ];
        }

        echo json_encode(['listas' => $listas]);
        break;

    case 'POST':
        // Criar nova lista
        if (!isset($dados['nome'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Nome da lista não informado']);
            exit;
        }

        $nome = $dados['nome'];
        $descricao = $dados['descricao'] ?? '';
        $cor = $dados['cor'] ?? '#e74c3c';

        // Iniciar transação
        pg_query($conexao, "BEGIN");

        try {
            // Inserir a lista
            $query = "INSERT INTO appestudo.lexjus_listas (usuario_id, nome, descricao, cor)
                     VALUES ($1, $2, $3, $4)
                     RETURNING id";

            $result = pg_query_params($conexao, $query, [$usuario_id, $nome, $descricao, $cor]);

            if (!$result) {
                throw new Exception(pg_last_error($conexao));
            }

            $row = pg_fetch_assoc($result);
            $lista_id = $row['id'];

            // Adicionar artigos à lista, se fornecidos
            if (isset($dados['artigos']) && is_array($dados['artigos'])) {
                foreach ($dados['artigos'] as $artigo_numero) {
                    $query_artigo = "INSERT INTO appestudo.lexjus_lista_artigos (lista_id, artigo_numero)
                                    VALUES ($1, $2)";

                    $result_artigo = pg_query_params($conexao, $query_artigo, [$lista_id, $artigo_numero]);

                    if (!$result_artigo) {
                        throw new Exception(pg_last_error($conexao));
                    }
                }
            }

            // Confirmar transação
            pg_query($conexao, "COMMIT");

            echo json_encode([
                'sucesso' => true,
                'mensagem' => 'Lista criada com sucesso',
                'lista_id' => (int)$lista_id
            ]);

        } catch (Exception $e) {
            // Reverter transação em caso de erro
            pg_query($conexao, "ROLLBACK");

            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao criar lista: ' . $e->getMessage()]);
        }
        break;

    case 'PUT':
        // Atualizar lista existente
        if (!isset($dados['id']) || !isset($dados['nome'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'ID e nome da lista são obrigatórios']);
            exit;
        }

        $lista_id = $dados['id'];
        $nome = $dados['nome'];
        $descricao = $dados['descricao'] ?? '';
        $cor = $dados['cor'] ?? '#e74c3c';

        // Verificar se a lista pertence ao usuário
        $query_verificar = "SELECT id FROM appestudo.lexjus_listas
                           WHERE id = $1 AND usuario_id = $2";

        $result_verificar = pg_query_params($conexao, $query_verificar, [$lista_id, $usuario_id]);

        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Lista não encontrada ou sem permissão']);
            exit;
        }

        // Atualizar a lista
        $query = "UPDATE appestudo.lexjus_listas
                 SET nome = $1, descricao = $2, cor = $3, data_atualizacao = CURRENT_TIMESTAMP
                 WHERE id = $4 AND usuario_id = $5";

        $result = pg_query_params($conexao, $query, [$nome, $descricao, $cor, $lista_id, $usuario_id]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao atualizar lista']);
            exit;
        }

        echo json_encode(['sucesso' => true, 'mensagem' => 'Lista atualizada com sucesso']);
        break;

    case 'DELETE':
        // Excluir lista
        if (!isset($dados['id'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'ID da lista não informado']);
            exit;
        }

        $lista_id = $dados['id'];

        // Verificar se a lista pertence ao usuário
        $query_verificar = "SELECT id FROM appestudo.lexjus_listas
                           WHERE id = $1 AND usuario_id = $2";

        $result_verificar = pg_query_params($conexao, $query_verificar, [$lista_id, $usuario_id]);

        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Lista não encontrada ou sem permissão']);
            exit;
        }

        // Iniciar transação
        pg_query($conexao, "BEGIN");

        try {
            // Primeiro, excluir todos os artigos da lista
            $query_artigos = "DELETE FROM appestudo.lexjus_lista_artigos WHERE lista_id = $1";
            $result_artigos = pg_query_params($conexao, $query_artigos, [$lista_id]);

            if (!$result_artigos) {
                throw new Exception(pg_last_error($conexao));
            }

            // Depois, excluir a lista
            $query_lista = "DELETE FROM appestudo.lexjus_listas WHERE id = $1 AND usuario_id = $2";
            $result_lista = pg_query_params($conexao, $query_lista, [$lista_id, $usuario_id]);

            if (!$result_lista) {
                throw new Exception(pg_last_error($conexao));
            }

            // Confirmar transação
            pg_query($conexao, "COMMIT");

            echo json_encode(['sucesso' => true, 'mensagem' => 'Lista excluída com sucesso']);

        } catch (Exception $e) {
            // Reverter transação em caso de erro
            pg_query($conexao, "ROLLBACK");

            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao excluir lista: ' . $e->getMessage()]);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>