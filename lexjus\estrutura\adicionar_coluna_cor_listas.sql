-- Adicionar coluna de cor para as listas personalizadas
-- Execute este script para adicionar a funcionalidade de cores nas listas

-- Adicionar coluna cor na tabela lexjus_listas
ALTER TABLE appestudo.lexjus_listas 
ADD COLUMN IF NOT EXISTS cor VARCHAR(7) DEFAULT '#e74c3c';

-- Comentário para documentação
COMMENT ON COLUMN appestudo.lexjus_listas.cor IS 'Cor da lista em formato hexadecimal (ex: #e74c3c)';

-- Atualizar listas existentes com cor padrão se não tiverem
UPDATE appestudo.lexjus_listas 
SET cor = '#e74c3c' 
WHERE cor IS NULL OR cor = '';

-- Criar índice para otimizar consultas por cor (opcional)
CREATE INDEX IF NOT EXISTS idx_lexjus_listas_cor 
    ON appestudo.lexjus_listas(cor);

-- Verificar se a coluna foi adicionada corretamente
SELECT column_name, data_type, column_default 
FROM information_schema.columns 
WHERE table_schema = 'appestudo' 
  AND table_name = 'lexjus_listas' 
  AND column_name = 'cor';
