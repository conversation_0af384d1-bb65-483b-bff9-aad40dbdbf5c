/* LexJus VOID - Modern Interface */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary: #00008B;
    --primary-light: #3949ab;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;

    /* Gradientes baseados no novo esquema */
    --primary-gradient: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    --secondary-gradient: linear-gradient(135deg, var(--accent) 0%, var(--primary-light) 100%);
    --accent-gradient: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
    --card-bg: var(--card-background);
    --light-bg: var(--background);
    --text-primary: var(--text);
    --text-secondary: var(--accent);
    --shadow-light: 0 4px 6px -1px var(--shadow-color);
    --shadow-medium: 0 10px 15px -3px var(--shadow-color);
    --shadow-heavy: 0 20px 25px -5px var(--shadow-color);
    --border-radius: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --active: #4169E1;
    --hover: #232338;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;
    --card-background: #13132b;

    /* Gradientes para modo escuro */
    --primary-gradient: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    --secondary-gradient: linear-gradient(135deg, var(--accent) 0%, var(--primary) 100%);
    --accent-gradient: linear-gradient(135deg, var(--primary) 0%, #5a67d8 100%);
    --card-bg: var(--card-background);
    --light-bg: var(--background);
    --text-primary: var(--text);
    --text-secondary: var(--accent);
    --shadow-light: 0 4px 6px -1px var(--shadow-color);
    --shadow-medium: 0 10px 15px -3px var(--shadow-color);
    --shadow-heavy: 0 20px 25px -5px var(--shadow-color);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--light-bg);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(0, 0, 139, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(57, 73, 171, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(74, 74, 74, 0.03) 0%, transparent 50%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

.container {
    width: 95%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Barra de Navegação Superior */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    /*margin-bottom: 2rem;*/
    border-bottom: 1px solid var(--border);
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    padding: 1rem 1.5rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary);
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
}

.nav-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Header moderno */
.header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.count-badge {
    background: var(--primary);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    min-width: 1.5rem;
    text-align: center;
    line-height: 1;
}

.nav-btn:hover .count-badge {
    background: white;
    color: var(--primary);
}



.theme-btn:active {
    transform: scale(0.95);
}

/* Responsividade para a barra de navegação */
@media (max-width: 768px) {
    .top-nav {
        padding: 0.75rem 1rem;
    }

    .nav-brand span {
        display: none;
    }

    .nav-btn .btn-text {
        display: none;
    }

    .nav-btn {
        padding: 0.75rem;
        min-width: 50px;
        justify-content: center;
    }

    .nav-controls {
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .top-nav {
        padding: 0.5rem;
    }

    .nav-brand {
        font-size: 1rem;
    }

    .nav-brand i {
        font-size: 1.2rem;
    }

    .nav-btn {
        padding: 0.5rem;
        min-width: 45px;
    }

    .theme-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /*margin-bottom: 1rem;*/
    position: relative;
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 400;
    margin-bottom: 2rem;
}

/* Barra de busca moderna */
.search-container {
    max-width: 500px;
    margin: 0 auto 2rem;
    position: relative;
}

.search-box {
    width: 100%;
    padding: 1rem 1.5rem 1rem 3rem;
    border: 2px solid var(--border);
    border-radius: 50px;
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    font-size: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    color: var(--text-primary);
}

.search-box:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-medium), 0 0 0 3px rgba(0, 0, 139, 0.1);
}

.search-box::placeholder {
    color: var(--text-secondary);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1.2rem;
}

/* Contador de resultados */
#resultsCounter {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    animation: fadeInUp 0.3s ease-out;
}

#resultsCounter i {
    color: var(--primary);
    margin-right: 0.5rem;
}

#resultsCounter strong {
    color: var(--primary);
}

/* Grid moderno */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

/* Cards modernos */
.card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: var(--shadow-light);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: var(--transition);
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary);
    background: var(--hover);
}

.card:nth-child(3n+1):hover {
    background: linear-gradient(135deg, rgba(0, 0, 139, 0.05) 0%, rgba(57, 73, 171, 0.05) 100%);
}

.card:nth-child(3n+2):hover {
    background: linear-gradient(135deg, rgba(57, 73, 171, 0.05) 0%, rgba(74, 74, 74, 0.03) 100%);
}

.card:nth-child(3n+3):hover {
    background: linear-gradient(135deg, rgba(74, 74, 74, 0.03) 0%, rgba(0, 0, 139, 0.05) 100%);
}

.card h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    position: relative;
    z-index: 2;
}

/* Destaque para correspondências de busca */
.card.exact-match {
    border: 2px solid var(--primary);
    box-shadow: 0 0 20px rgba(0, 0, 139, 0.3);
    transform: scale(1.02);
    order: -1; /* Aparece primeiro */
}

.card.exact-match::before {
    transform: scaleX(1);
    background: var(--primary-gradient);
    height: 6px;
}

.card.exact-match h2 {
    color: var(--primary);
    font-weight: 700;
}

.card.partial-match {
    opacity: 0.8;
    order: 1;
}

/* Animação especial para correspondência exata */
@keyframes exactMatchPulse {
    0% { box-shadow: 0 0 20px rgba(0, 0, 139, 0.3); }
    50% { box-shadow: 0 0 30px rgba(0, 0, 139, 0.5); }
    100% { box-shadow: 0 0 20px rgba(0, 0, 139, 0.3); }
}

.card.exact-match {
    animation: exactMatchPulse 2s ease-in-out infinite;
}

/* Loading animation */
.card-loading {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

/* Global Loading Overlay */
.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 1;
    visibility: visible;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.global-loading.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 2rem;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: globalLoadingSpin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-text p {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

.loading-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.loading-progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
    animation: loadingProgress 3s ease-in-out;
}

@keyframes globalLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    25% { width: 30%; }
    50% { width: 60%; }
    75% { width: 85%; }
    100% { width: 100%; }
}

/* Loading state for cards (simplified) */
.card.loading {
    opacity: 0.3;
    pointer-events: none;
    transform: scale(0.95);
    transition: all 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mensagem de nenhum resultado */
#noResultsMessage {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-size: 1.2rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border);
    margin: 2rem 0;
    animation: fadeInUp 0.3s ease-out;
}

#noResultsMessage i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: var(--primary);
}

#noResultsMessage p {
    margin: 0.5rem 0;
}

#noResultsMessage strong {
    color: var(--primary);
}

/* Barra de Progresso */
.progress-container {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.progress-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--hover);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 4px;
    width: 0%;
    transition: width 0.6s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Stats bar */
.stats-bar {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    border-radius: 50px;
    box-shadow: var(--shadow-light);
    text-align: center;
    border: 1px solid var(--border);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Modal clean */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    padding: 2rem;
    box-sizing: border-box;
    animation: modalFadeIn 0.2s ease-out;
}

/* Modal do artigo com prioridade maior quando aberto sobre outros modais */
.modal.modal-priority {
    z-index: 1100;
    background: rgba(0, 0, 0, 0.7);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background: var(--card-bg);
    margin: auto;
    padding: 2.5rem;
    border: 1px solid var(--border);
    width: 90%;
    max-width: 700px;
    max-height: 85vh;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--card-bg);
    border: 2px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    color: var(--text-secondary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.modal-close-btn:hover {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.modal-close-btn:active {
    transform: scale(0.95);
}

/* Header do Modal (simplificado) */
.modal-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
    text-align: center;
}

/* Navegação do Modal (Rodapé) */
.modal-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
    gap: 1rem;
}

.modal-nav-spacer {
    flex-grow: 1;
}

/* Botões de Navegação do Modal */
.modal-nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 8px;
    background: var(--card-bg);
    color: var(--text-primary);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border);
    min-width: 120px;
}

.modal-nav-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 139, 0.3);
}

.modal-nav-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: var(--hover);
    color: var(--text-secondary);
    border-color: var(--border);
}

.modal-nav-btn:disabled:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: var(--hover);
    color: var(--text-secondary);
    border-color: var(--border);
}

.modal-nav-prev {
    justify-content: flex-start;
}

.modal-nav-next {
    justify-content: flex-end;
}

/* Animação de transição entre artigos */
.modal-content.transitioning {
    opacity: 0.7;
    transform: scale(0.98);
    transition: all 0.2s ease;
}

/* Responsividade para navegação do modal */
@media (max-width: 768px) {
    .modal-navigation {
        margin-top: 1.5rem;
        padding-top: 1rem;
        gap: 0.5rem;
    }

    .modal-nav-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
        min-width: 100px;
    }

    .modal-nav-btn span {
        display: none;
    }

    .modal-nav-btn {
        min-width: 48px;
        justify-content: center;
    }
}

#modalArtigoNumero {
    font-size: 2rem;
    font-weight: 600;
    color: var(--primary);
    margin: 0;
    text-align: center;
}

#modalArtigoConteudo {
    line-height: 1.7;
}

/* Justificação geral para todo texto do modal */
#modalArtigoConteudo,
#modalArtigoConteudo * {
    text-align: justify;
}

/* Exceções para elementos que não devem ser justificados */
#modalArtigoNumero,
#modalArtigoConteudo strong,
.modal-actions,
.modal-navigation,
.notas-header {
    text-align: center;
}

#modalArtigoConteudo p,
#modalArtigoConteudo ul,
#modalArtigoConteudo div > p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

#modalArtigoConteudo strong {
    color: var(--primary);
    font-weight: 600;
    font-size: 1rem;
    display: block;
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

/* Container do Caput */
#modalArtigoCaputContainer {
    margin-bottom: 2rem;
}

#modalArtigoCaput {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-primary);
    margin-top: 0.5rem;
    padding: 1.5rem;
    background: var(--hover);
    border-radius: 8px;
    border-left: 4px solid var(--primary);
    font-weight: 400;
    text-align: justify;
}

#modalArtigoConteudo ul {
    list-style: none;
    padding-left: 0;
}

#modalArtigoConteudo ul li {
    margin-bottom: 0.75rem;
    padding: 1rem;
    background: transparent;
    border-radius: 6px;
    border-left: 2px solid var(--border);
    transition: all 0.2s ease;
    font-size: 0.95rem;
    text-align: justify;
    line-height: 1.6;
}

#modalArtigoConteudo ul li:hover {
    background: var(--hover);
    border-left-color: var(--primary);
}

/* Parágrafos numerados */
.paragrafo-numerado-bloco {
    margin-bottom: 1.5rem;
    padding: 1.25rem;
    background: var(--hover);
    border-radius: 8px;
    border-left: 4px solid var(--primary);
}

.paragrafo-numerado-bloco .paragrafo-numero {
    font-weight: 600;
    color: var(--primary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.paragrafo-numerado-bloco .paragrafo-texto {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 0.95rem;
    text-align: justify;
}

.paragrafo-numerado-bloco .alineas-lista {
    padding-left: 1rem;
    margin-top: 1rem;
}

.paragrafo-numerado-bloco .alineas-lista li {
    background: transparent;
    border-left-color: var(--border);
    font-size: 0.9rem;
    text-align: justify;
}

/* Alíneas aninhadas nos incisos */
#modalArtigoConteudo ul ul li {
    text-align: justify;
}

/* Containers de conteúdo */
#modalArtigoIncisosContainer,
#modalArtigoParagrafoUnicoContainer,
#modalArtigoParagrafosNumeradosContainer,
#modalArtigoAlineasArtigoContainer {
    margin-bottom: 1.5rem;
}

#modalArtigoParagrafoUnicoContainer {
    padding: 1.25rem;
    background: var(--hover);
    border-radius: 8px;
    border-left: 4px solid var(--accent);
    text-align: justify;
}

/* Controles do Modal */
.modal-controls {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-favoritar,
.btn-adicionar-lista,
.btn-gerenciar-listas,
.btn-marcar-lido {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-favoritar {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-favoritar:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

.btn-favoritar.favoritado {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.btn-favoritar.favoritado i {
    color: white;
}

.btn-adicionar-lista {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-adicionar-lista:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.btn-gerenciar-listas {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-gerenciar-listas::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-gerenciar-listas:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
}

.btn-gerenciar-listas:hover::before {
    left: 100%;
}

/* Animação de entrada para o botão Gerenciar Listas */
.btn-gerenciar-listas {
    animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.btn-marcar-lido {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 139, 0.3);
}

.btn-marcar-lido:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 139, 0.4);
}

.btn-marcar-lido:active {
    transform: translateY(0);
}

.btn-marcar-lido.lido {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-marcar-lido.lido:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Estilo para artigos lidos - apenas fundo verde */
.card.artigo-lido {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(32, 201, 151, 0.08) 100%);
    border-color: rgba(40, 167, 69, 0.3);
}

.card.artigo-lido:hover {
    border-left-color: #20c997;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.12) 0%, rgba(32, 201, 151, 0.12) 100%);
    border-color: rgba(40, 167, 69, 0.4);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
}

.card.artigo-lido h2 {
    color: #1e7e34;
}

.card.artigo-lido p {
    color: #155724;
}

/* Sistema de Notas */
.notas-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);
}

.notas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.notas-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notas-header h3 i {
    color: #f39c12;
}

.btn-adicionar-nota {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
}

.btn-adicionar-nota:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.4);
}

.notas-container {
    min-height: 60px;
}

.nenhuma-nota {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    background: var(--hover);
    border-radius: 8px;
    border: 2px dashed var(--border);
}

.nenhuma-nota i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
    color: #f39c12;
}

.nenhuma-nota p {
    margin: 0.5rem 0;
    font-weight: 500;
}

.nenhuma-nota small {
    opacity: 0.7;
}

/* Formulário de Nova Nota */
.form-nova-nota {
    background: var(--hover);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border);
    margin-top: 1rem;
}

/* Editor Rico */
.editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    align-items: center;
}

.toolbar-group {
    display: flex;
    gap: 0.25rem;
    align-items: center;
    padding-right: 0.5rem;
    border-right: 1px solid var(--border);
}

.toolbar-group:last-child {
    border-right: none;
    padding-right: 0;
}

.toolbar-btn {
    background: transparent;
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-primary);
    font-size: 0.85rem;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: var(--hover);
    border-color: var(--primary);
}

.toolbar-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.color-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid var(--border);
    padding: 0;
    margin: 0 2px;
    position: relative;
}

.color-btn:hover {
    border-color: var(--text-primary);
    transform: scale(1.1);
}

.color-btn.active {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px var(--primary);
}

.toolbar-select {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    color: var(--text-primary);
    font-size: 0.85rem;
    cursor: pointer;
    min-width: 80px;
}

.toolbar-select:focus {
    outline: none;
    border-color: var(--primary);
}

.editor-content {
    min-height: 120px;
    max-height: 300px;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: 0 0 6px 6px;
    background: var(--card-bg);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.6;
    overflow-y: auto;
    transition: border-color 0.3s ease;
}

.editor-content:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
}

.editor-content[placeholder]:empty::before {
    content: attr(placeholder);
    color: var(--text-secondary);
    opacity: 0.7;
    pointer-events: none;
}

.editor-content[placeholder]:empty:focus::before {
    opacity: 0.5;
}

/* Estilos para o conteúdo formatado */
.editor-content strong,
.nota-content strong {
    font-weight: bold;
}

.editor-content em,
.nota-content em {
    font-style: italic;
}

.editor-content u,
.nota-content u {
    text-decoration: underline;
}

.editor-content [style*="font-size"],
.nota-content [style*="font-size"] {
    line-height: 1.4;
}

/* Responsividade da toolbar */
@media (max-width: 480px) {
    .editor-toolbar {
        flex-direction: column;
        gap: 0.75rem;
    }

    .toolbar-group {
        border-right: none;
        border-bottom: 1px solid var(--border);
        padding-bottom: 0.5rem;
        padding-right: 0;
        width: 100%;
        justify-content: center;
    }

    .toolbar-group:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
}

.form-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    justify-content: flex-end;
}

.btn-salvar-nota {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 139, 0.3);
}

.btn-salvar-nota:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 139, 0.4);
}

.btn-cancelar-nota {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border);
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cancelar-nota:hover {
    background: var(--hover);
    color: var(--text-primary);
    border-color: var(--text-secondary);
}

/* Notas Existentes */
.nota-item {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
}

.nota-item:hover {
    border-color: #f39c12;
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.1);
}

.nota-content {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: 0.75rem;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.nota-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-secondary);
    border-top: 1px solid var(--border);
    padding-top: 0.75rem;
}

.nota-data {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.nota-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-editar-nota,
.btn-excluir-nota {
    background: none;
    border: none;
    padding: 0.3rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-editar-nota {
    color: #3498db;
}

.btn-editar-nota:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #2980b9;
}

.btn-excluir-nota {
    color: #e74c3c;
}

.btn-excluir-nota:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #c0392b;
}

/* Indicadores nos cards - usando elementos HTML reais */
.card-indicator {
    position: absolute;
    z-index: 10;
    pointer-events: none;
}

.indicator-notas {
    top: 0.75rem;
    left: 0.75rem;
    color: #f39c12;
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.indicator-listas {
    top: 0.75rem;
    left: 2rem; /* Ao lado do indicador de notas */
    color: #3498db;
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Quando não tem notas, o indicador de listas fica na posição original */
.card:not(.tem-notas) .indicator-listas {
    left: 0.75rem;
}

.indicator-favorito {
    top: 0.75rem;
    left: 3.25rem; /* Ao lado dos outros indicadores */
    color: #e74c3c;
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Ajustar posição quando não tem notas */
.card:not(.tem-notas) .indicator-favorito {
    left: 2rem; /* Ao lado do indicador de listas */
}

/* Ajustar posição quando não tem notas nem listas */
.card:not(.tem-notas):not(.tem-listas) .indicator-favorito {
    left: 0.75rem; /* Posição original */
}

/* Animações removidas para não atrapalhar a concentração do usuário */

/* Responsividade */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .grid-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .card {
        padding: 1.5rem;
        min-height: 120px;
    }

    .card h2 {
        font-size: 1.5rem;
    }

    .modal-content {
        padding: 1.5rem;
        margin: 1rem;
        width: calc(100% - 2rem);
        border-radius: 8px;
    }

    #modalArtigoNumero {
        font-size: 1.5rem;
    }

    .stats-bar {
        gap: 1rem;
    }

    .stat-item {
        padding: 0.75rem 1.5rem;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2rem;
    }

    .grid-container {
        grid-template-columns: 1fr;
    }

    .search-box {
        padding: 0.75rem 1rem 0.75rem 2.5rem;
    }

    .search-icon {
        left: 0.75rem;
    }
}

/* Estilos dos Modais de Favoritos e Listas */
.modal-favoritos,
.modal-listas {
    max-width: 900px;
    max-height: 80vh;
    overflow-y: auto;
}

.favoritos-header,
.listas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.listas-header {
    justify-content: flex-end;
}

.favoritos-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 1px solid var(--border);
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.tab-btn:hover:not(.active) {
    background: var(--hover);
    color: var(--text-primary);
}

.btn-nova-lista {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 139, 0.3);
}

.btn-nova-lista:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 139, 0.4);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.favoritos-container,
.listas-container {
    min-height: 200px;
}

.nenhum-favorito,
.nenhuma-lista {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    background: var(--hover);
    border-radius: 8px;
    border: 2px dashed var(--border);
}

.nenhum-favorito i,
.nenhuma-lista i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: #e74c3c;
}

.favorito-item {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.favorito-item:hover {
    border-color: #e74c3c;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.1);
    transform: translateY(-2px);
}

.favorito-item h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.favorito-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

.lista-item {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.lista-item:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.lista-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.lista-nome {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lista-cor {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.lista-count {
    background: var(--primary);
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    min-width: 1.5rem;
    text-align: center;
}

.lista-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-editar-lista,
.btn-excluir-lista {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-editar-lista {
    color: #3498db;
}

.btn-editar-lista:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #2980b9;
}

.btn-excluir-lista {
    color: #e74c3c;
}

.btn-excluir-lista:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #c0392b;
}

.lista-artigos {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.artigo-tag {
    background: var(--hover);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--border);
    cursor: pointer;
    transition: all 0.2s ease;
}

.artigo-tag:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* Modal de Nova Lista */
.modal-nova-lista {
    max-width: 500px;
}

.form-nova-lista {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.input-nome-lista {
    padding: 0.75rem;
    border: 1px solid var(--border);
    border-radius: 6px;
    font-size: 1rem;
    background: var(--card-bg);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.input-nome-lista:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
}

.cores-lista {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.cores-lista input[type="radio"] {
    display: none;
}

.cor-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.cor-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cores-lista input[type="radio"]:checked + .cor-option {
    border-color: var(--text-primary);
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.cores-lista input[type="radio"]:checked + .cor-option::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.btn-salvar-lista {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 139, 0.3);
}

.btn-salvar-lista:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 139, 0.4);
}

.btn-cancelar-lista {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cancelar-lista:hover {
    background: var(--hover);
    color: var(--text-primary);
    border-color: var(--text-secondary);
}

/* Modal de Adicionar à Lista */
.modal-adicionar-lista {
    max-width: 500px;
}

.artigo-selecionado {
    background: var(--hover);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.artigo-selecionado span {
    color: var(--primary);
    font-weight: 600;
}

.listas-disponiveis {
    max-height: 300px;
    overflow-y: auto;
}

.nenhuma-lista-disponivel {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    background: var(--hover);
    border-radius: 8px;
    border: 2px dashed var(--border);
}

.btn-criar-primeira-lista {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 139, 0.3);
}

.btn-criar-primeira-lista:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 139, 0.4);
}

.lista-opcao {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.lista-opcao:hover {
    border-color: var(--primary);
    background: var(--hover);
}

.lista-opcao-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lista-opcao-nome {
    font-weight: 500;
    color: var(--text-primary);
}

.lista-opcao-count {
    background: var(--primary);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 50px;
    min-width: 1.2rem;
    text-align: center;
}

/* Modal de Gerenciar Listas */
.modal-gerenciar-listas {
    max-width: 500px;
}

.listas-do-artigo {
    max-height: 300px;
    overflow-y: auto;
}

.nenhuma-lista-artigo {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    background: var(--hover);
    border-radius: 8px;
    border: 2px dashed var(--border);
}

.lista-artigo-item {
    background: var(--card-bg);
    border: 1px solid var(--border);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.lista-artigo-item:hover {
    border-color: var(--primary);
    background: var(--hover);
}

.lista-artigo-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lista-artigo-nome {
    font-weight: 500;
    color: var(--text-primary);
}

.btn-remover-da-lista {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-remover-da-lista:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

/* Estilos para tema escuro */
[data-theme="dark"] {
    /* Artigos lidos no tema escuro */
    .card.artigo-lido {
        border-left: 4px solid #20c997;
        background: linear-gradient(135deg, rgba(32, 201, 151, 0.1) 0%, rgba(40, 167, 69, 0.1) 100%);
        border-color: rgba(32, 201, 151, 0.3);
    }

    .card.artigo-lido:hover {
        border-left-color: #17a2b8;
        background: linear-gradient(135deg, rgba(32, 201, 151, 0.15) 0%, rgba(40, 167, 69, 0.15) 100%);
        border-color: rgba(32, 201, 151, 0.4);
        box-shadow: 0 8px 25px rgba(32, 201, 151, 0.2);
    }

    .card.artigo-lido h2 {
        color: #20c997;
    }

    .card.artigo-lido p {
        color: #17a2b8;
    }
}

.header-barra {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 20px;
    border-bottom: 1px solid var(--border);
    /*margin-bottom: 15px;
    border-radius: 0 0 10px 10px;*/
    box-shadow: 0 2px 4px var(--shadow-color);
    background: var(--card-background);
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .logo img {
        height: 50px;
        width: auto;
        transition: opacity 0.3s ease;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        background: var(--hover);
    }

    .user-info i {
        color: var(--primary);
        font-size: 1.2rem;
    }

    .user-name {
        color: var(--text);
        font-weight: 600;
    }

    .theme-btn {
        background: transparent;
        border: none;
        color: var(--primary);
        cursor: pointer;
        font-size: 1.2rem;
        padding: 8px;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .theme-btn:hover {
        background: var(--hover);
    }

    .logo {
        position: relative;
    }

    .logo-dark {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
    }

    [data-theme="dark"] .logo-light {
        opacity: 0;
    }

    [data-theme="dark"] .logo-dark {
        opacity: 1;
    }

