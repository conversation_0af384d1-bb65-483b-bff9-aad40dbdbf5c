#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def limpar_referencias_finais(texto):
    """
    Remove referências específicas que não foram capturadas pelas regex anteriores
    """
    if not texto:
        return texto
    
    # Remove (Vide ADPF X)
    texto = re.sub(r'\s*\(Vide\s+ADPF\s+\d+\)\s*', ' ', texto, flags=re.IGNORECASE)
    
    # Remove (Revogado pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Revogad[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)
    
    # Remove (VETADO)
    texto = re.sub(r'\s*\(VETADO\)\s*', ' ', texto, flags=re.IGNORECASE)
    
    # Remove espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)
    
    return texto.strip()

def processar_artigo(artigo):
    """
    Processa um artigo removendo referências de todos os campos
    """
    # Limpar caput
    if artigo.get('caput'):
        artigo['caput'] = limpar_referencias_finais(artigo['caput'])
    
    # Limpar incisos
    if artigo.get('incisos'):
        artigo['incisos'] = [limpar_referencias_finais(inciso) for inciso in artigo['incisos']]
    
    # Limpar parágrafo único
    if artigo.get('paragrafo_unico'):
        artigo['paragrafo_unico'] = limpar_referencias_finais(artigo['paragrafo_unico'])
    
    # Limpar parágrafos numerados
    if artigo.get('paragrafos_numerados'):
        for paragrafo in artigo['paragrafos_numerados']:
            if paragrafo.get('texto'):
                paragrafo['texto'] = limpar_referencias_finais(paragrafo['texto'])
            if paragrafo.get('incisos'):
                paragrafo['incisos'] = [limpar_referencias_finais(inciso) for inciso in paragrafo['incisos']]
            if paragrafo.get('alineas'):
                paragrafo['alineas'] = [limpar_referencias_finais(alinea) for alinea in paragrafo['alineas']]
    
    return artigo

def main():
    print("=== LIMPEZA FINAL DE REFERÊNCIAS ===")
    
    # Carregar arquivo JSON
    try:
        with open('codigo_penal.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Carregados {len(artigos)} artigos do arquivo JSON")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return
    
    # Processar cada artigo
    artigos_limpos = []
    referencias_removidas = 0
    
    for artigo in artigos:
        artigo_original = json.dumps(artigo, ensure_ascii=False)
        artigo_limpo = processar_artigo(artigo.copy())
        artigo_novo = json.dumps(artigo_limpo, ensure_ascii=False)
        
        if artigo_original != artigo_novo:
            referencias_removidas += 1
        
        artigos_limpos.append(artigo_limpo)
    
    print(f"📊 Processados {len(artigos_limpos)} artigos")
    print(f"🧹 Referências removidas em {referencias_removidas} artigos")
    
    # Salvar arquivo JSON limpo
    try:
        with open('codigo_penal_limpo.json', 'w', encoding='utf-8') as f:
            json.dump(artigos_limpos, f, ensure_ascii=False, indent=2)
        print("✅ Arquivo JSON limpo salvo como: codigo_penal_limpo.json")
    except Exception as e:
        print(f"❌ Erro ao salvar JSON: {e}")
    
    # Salvar arquivo JS limpo
    try:
        from datetime import datetime
        with open('codigo_penal_limpo.js', 'w', encoding='utf-8') as f:
            f.write("// Código Penal Brasileiro - Dados extraídos e limpos automaticamente\n")
            f.write(f"// Gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"// Total de artigos: {len(artigos_limpos)}\n")
            f.write("// Referências legislativas removidas para melhor legibilidade\n\n")
            
            f.write("const codigoPenalArtigos = ")
            json.dump(artigos_limpos, f, ensure_ascii=False, indent=2)
            f.write(";\n\n")
            
            # Adicionar funções de busca
            f.write("""
// Função para buscar artigo por número
function buscarArtigo(numero) {
    return codigoPenalArtigos.find(artigo => 
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}

// Função para buscar artigos por texto
function buscarArtigosPorTexto(texto) {
    const textoLower = texto.toLowerCase();
    return codigoPenalArtigos.filter(artigo => 
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo => 
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        codigoPenalArtigos,
        buscarArtigo,
        buscarArtigosPorTexto
    };
}
""")
        print("✅ Arquivo JS limpo salvo como: codigo_penal_limpo.js")
    except Exception as e:
        print(f"❌ Erro ao salvar JS: {e}")
    
    print("\n🎉 Limpeza final concluída!")
    print("📄 Arquivos gerados:")
    print("   - codigo_penal_limpo.json")
    print("   - codigo_penal_limpo.js")

if __name__ == '__main__':
    main()
