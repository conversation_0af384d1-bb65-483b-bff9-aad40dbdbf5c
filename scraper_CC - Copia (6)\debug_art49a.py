#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def debug_art49a():
    """Debug específico para o Art. 49-A"""

    print("=== DEBUG DO ART. 49-A ===")

    # Carregar o arquivo HTML
    with open('L10406.html', 'r', encoding='utf-8') as f:
        conteudo = f.read()

    # Buscar especificamente o art49a
    name = "art49a"
    print(f"Buscando: {name}")

    # Encontrar a posição do <a name>
    padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
    match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

    if not match_pos:
        print("❌ Não encontrou o padrão de busca")
        return

    print(f"✅ Encontrou na posição: {match_pos.start()}")
    inicio = match_pos.end()

    # Aplicar a nova lógica de delimitação para artigos com sufixos
    sufixo_letra = 'a'
    numero_base = '49'
    proximo_numero = int(numero_base) + 1  # 50

    # Primeiro, buscar o <a name> do próximo artigo
    padrao_proximo_artigo = rf'<a name="art{proximo_numero}[^"]*"'
    match_proximo = re.search(padrao_proximo_artigo, conteudo[inicio:], re.IGNORECASE)

    if match_proximo:
        # Encontrou o próximo artigo, usar essa posição como limite
        fim = match_proximo.start()
        print(f"✅ Encontrou próximo artigo na posição {fim}")
        texto_restante = conteudo[inicio:inicio+fim]
    else:
        # Não encontrou próximo artigo, usar limite maior
        print("❌ Não encontrou próximo artigo, usando limite maior")
        texto_restante = conteudo[inicio:inicio+4000]

    print(f"Tamanho do texto extraído: {len(texto_restante)}")

    # Aplicar o padrão de extração (novo padrão para artigos com sufixos)
    padrao_art = r'</a>(.*?)(?=<a name="art\d+"|$)'
    match_art = re.search(padrao_art, texto_restante, re.DOTALL | re.IGNORECASE)

    if not match_art:
        print("❌ Não conseguiu extrair o texto do artigo")
        return

    texto_html = match_art.group(1)
    print(f"Texto HTML extraído ({len(texto_html)} chars):")
    print("=" * 50)
    print(texto_html[:500])
    print("=" * 50)

    # Limpar HTML
    texto = re.sub(r'<[^>]+>', '', texto_html)
    texto = texto.replace('&nbsp;', ' ')
    texto = texto.replace('&amp;', '&')
    texto = re.sub(r'\s+', ' ', texto)
    texto = texto.strip()

    print(f"Texto limpo ({len(texto)} chars):")
    print("=" * 50)
    print(texto)
    print("=" * 50)

    # Verificar se contém "Parágrafo único"
    if 'Parágrafo único' in texto:
        print("✅ Contém 'Parágrafo único'")

        # Aplicar a lógica de separação
        partes = texto.split('Parágrafo único')
        print(f"Dividido em {len(partes)} partes:")

        for i, parte in enumerate(partes):
            print(f"  Parte {i}: {parte[:100]}...")

        if len(partes) >= 2:
            caput_parte = partes[0].strip()
            paragrafo_parte = 'Parágrafo único' + partes[1].strip()

            print(f"\nCAPUT EXTRAÍDO: {caput_parte}")
            print(f"\nPARÁGRAFO EXTRAÍDO: {paragrafo_parte[:200]}...")

            # Aplicar lógica de corte para artigos com sufixos
            padroes_corte = [
                rf'Art\.\s*{proximo_numero}[^\d]',  # Art. 50 (não 500)
                rf'Art\.\s*{numero_base}-[B-Z]',   # Art. 49-B
            ]

            print(f"\nPadrões de corte: {padroes_corte}")

            for padrao in padroes_corte:
                match_corte = re.search(padrao, paragrafo_parte, re.IGNORECASE)
                if match_corte:
                    print(f"✅ Encontrou padrão de corte: {padrao} na posição {match_corte.start()}")
                    paragrafo_parte_cortado = paragrafo_parte[:match_corte.start()].strip()
                    print(f"Parágrafo após corte: {paragrafo_parte_cortado}")
                    break
            else:
                print("❌ Nenhum padrão de corte encontrado")

    else:
        print("❌ NÃO contém 'Parágrafo único'")

if __name__ == "__main__":
    debug_art49a()
