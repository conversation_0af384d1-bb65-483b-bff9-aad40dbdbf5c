#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para analisar textos taxados (com strikethrough) no arquivo HTML do Código Civil
"""

import re

def extrair_textos_taxados(arquivo_html):
    """
    Extrai todos os textos que estão dentro de tags <strike> no arquivo HTML
    """
    try:
        with open(arquivo_html, 'r', encoding='utf-8') as file:
            conteudo = file.read()
    except UnicodeDecodeError:
        # Tenta com encoding latin-1 se utf-8 falhar
        with open(arquivo_html, 'r', encoding='latin-1') as file:
            conteudo = file.read()

    # Encontra todas as tags <strike> usando regex
    pattern = r'<strike>(.*?)</strike>'
    matches = re.findall(pattern, conteudo, re.DOTALL | re.IGNORECASE)

    textos_taxados = []

    for i, match in enumerate(matches, 1):
        # Remove tags HTML internas
        texto_limpo = re.sub(r'<[^>]+>', '', match)

        # Remove quebras de linha excessivas e espaços
        texto_limpo = re.sub(r'\s+', ' ', texto_limpo.strip())

        if texto_limpo:  # Só adiciona se não estiver vazio
            textos_taxados.append({
                'numero': i,
                'texto': texto_limpo,
                'html_original': f'<strike>{match}</strike>'
            })

    return textos_taxados

def analisar_artigos_taxados(textos_taxados):
    """
    Analisa os textos taxados para identificar artigos e suas partes
    """
    artigos_taxados = {}

    for item in textos_taxados:
        texto = item['texto']

        # Procura por padrões de artigos
        match_artigo = re.search(r'Art\.\s*(\d+[º°]?)', texto)
        if match_artigo:
            numero_artigo = match_artigo.group(1)

            if numero_artigo not in artigos_taxados:
                artigos_taxados[numero_artigo] = []

            artigos_taxados[numero_artigo].append({
                'tipo': 'caput' if 'Art.' in texto[:20] else 'inciso/paragrafo',
                'texto': texto,
                'html': item['html_original']
            })
        else:
            # Procura por incisos ou parágrafos
            if re.search(r'^(I{1,3}|IV|V|VI|VII|VIII|IX|X)\s*-', texto):
                # É um inciso
                artigos_taxados.setdefault('incisos_avulsos', []).append({
                    'tipo': 'inciso',
                    'texto': texto,
                    'html': item['html_original']
                })
            elif 'Parágrafo' in texto:
                # É um parágrafo
                artigos_taxados.setdefault('paragrafos_avulsos', []).append({
                    'tipo': 'paragrafo',
                    'texto': texto,
                    'html': item['html_original']
                })
            else:
                # Outros textos taxados
                artigos_taxados.setdefault('outros', []).append({
                    'tipo': 'outro',
                    'texto': texto,
                    'html': item['html_original']
                })

    return artigos_taxados

def main():
    arquivo_html = 'scraper_CC/CÓDIGO CIVIL - LEI 10406_2002.html'

    print("🔍 Analisando textos taxados no Código Civil...")
    print("=" * 60)

    # Extrai todos os textos taxados
    textos_taxados = extrair_textos_taxados(arquivo_html)

    print(f"📊 Total de textos taxados encontrados: {len(textos_taxados)}")
    print("=" * 60)

    # Analisa os artigos taxados
    artigos_taxados = analisar_artigos_taxados(textos_taxados)

    # Mostra os resultados organizados
    for artigo, conteudos in sorted(artigos_taxados.items()):
        if artigo in ['incisos_avulsos', 'paragrafos_avulsos', 'outros']:
            continue

        print(f"\n📜 ARTIGO {artigo} (TAXADO):")
        print("-" * 40)

        for conteudo in conteudos:
            print(f"  {conteudo['tipo'].upper()}: {conteudo['texto'][:100]}...")

    # Mostra incisos avulsos
    if 'incisos_avulsos' in artigos_taxados:
        print(f"\n📋 INCISOS AVULSOS TAXADOS ({len(artigos_taxados['incisos_avulsos'])}):")
        print("-" * 40)
        for item in artigos_taxados['incisos_avulsos']:
            print(f"  • {item['texto'][:80]}...")

    # Mostra parágrafos avulsos
    if 'paragrafos_avulsos' in artigos_taxados:
        print(f"\n📝 PARÁGRAFOS AVULSOS TAXADOS ({len(artigos_taxados['paragrafos_avulsos'])}):")
        print("-" * 40)
        for item in artigos_taxados['paragrafos_avulsos']:
            print(f"  • {item['texto'][:80]}...")

    # Mostra outros textos
    if 'outros' in artigos_taxados:
        print(f"\n❓ OUTROS TEXTOS TAXADOS ({len(artigos_taxados['outros'])}):")
        print("-" * 40)
        for item in artigos_taxados['outros']:
            print(f"  • {item['texto'][:80]}...")

    print("\n" + "=" * 60)
    print("✅ Análise concluída!")

    # Salva relatório detalhado
    with open('relatorio_textos_taxados.txt', 'w', encoding='utf-8') as f:
        f.write("RELATÓRIO DETALHADO - TEXTOS TAXADOS NO CÓDIGO CIVIL\n")
        f.write("=" * 60 + "\n\n")

        f.write(f"Total de textos taxados: {len(textos_taxados)}\n\n")

        for i, item in enumerate(textos_taxados, 1):
            f.write(f"{i:03d}. {item['texto']}\n")
            f.write(f"     HTML: {item['html_original']}\n\n")

    print("📄 Relatório detalhado salvo em: relatorio_textos_taxados.txt")

if __name__ == "__main__":
    main()
