#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper v4 Melhorado para o Código de Processo Penal da fonte Corpus927
Versão que corrige problemas específicos de incisos faltantes e parágrafos cortados
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def limpar_texto(texto):
    """
    Limpa o texto removendo caracteres desnecessários
    """
    if not texto:
        return ""
    
    # Remover quebras de linha e espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)
    
    # Remover referências legais entre parênteses
    texto = re.sub(r'\s*\([^)]*(?:Lei|Decreto|Incluído|Redação|Vide|Vigência|VETADO)[^)]*\)\s*', '', texto, flags=re.IGNORECASE)
    
    return texto.strip()

def extrair_numero_artigo(link_artigo):
    """
    Extrai o número do artigo do link
    """
    if not link_artigo:
        return None
    
    texto_artigo = link_artigo.get_text(strip=True)
    
    # Padrões para extrair número do artigo
    patterns = [
        r'Art\.\s*(\d+)(?:º|°)?(?:-([A-Z]))?',
        r'Artigo\s+(\d+)(?:º|°)?(?:-([A-Z]))?'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, texto_artigo, re.IGNORECASE)
        if match:
            numero = int(match.group(1))
            sufixo = match.group(2) if match.group(2) else ""
            return f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")
    
    return None

def extrair_estrutura_completa_artigo(link_artigo, soup):
    """
    Extrai a estrutura completa do artigo analisando o HTML de forma mais precisa
    """
    artigo_id = extrair_numero_artigo(link_artigo)
    if not artigo_id:
        return None
    
    # Encontrar o parágrafo que contém o artigo
    paragrafo_artigo = link_artigo.find_parent('p')
    if not paragrafo_artigo:
        return None
    
    # Extrair caput - texto após o número do artigo
    texto_paragrafo = paragrafo_artigo.get_text(separator=' ', strip=True)
    caput_match = re.search(r'Art\.\s*\d+(?:º|°)?(?:-[A-Z])?\s*\.?\s*(.+)', texto_paragrafo, re.IGNORECASE)
    caput = limpar_texto(caput_match.group(1)) if caput_match else ""
    
    # Inicializar estrutura do artigo
    artigo = {
        "artigo": artigo_id,
        "caput": caput,
        "incisos": [],
        "paragrafos_numerados": [],
        "paragrafo_unico": None
    }
    
    # Buscar todos os elementos seguintes até o próximo artigo
    elemento_atual = paragrafo_artigo
    
    while True:
        elemento_atual = elemento_atual.find_next_sibling(['p', 'div'])
        
        if not elemento_atual:
            break
            
        # Parar se encontrar outro artigo
        if elemento_atual.find('a', class_='numero_artigo'):
            break
            
        # Parar se encontrar título/seção
        texto_elemento = elemento_atual.get_text(separator=' ', strip=True)
        if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO)', texto_elemento, re.IGNORECASE):
            break
            
        # Pular elementos muito pequenos
        if len(texto_elemento.strip()) < 3:
            continue
        
        # Processar diferentes tipos de elementos
        processar_elemento(elemento_atual, artigo)
    
    return artigo

def processar_elemento(elemento, artigo):
    """
    Processa um elemento específico e adiciona à estrutura do artigo
    """
    texto = elemento.get_text(separator=' ', strip=True)
    
    # Verificar se é parágrafo único
    if re.search(r'Parágrafo\s+único', texto, re.IGNORECASE):
        paragrafo_match = re.search(r'Parágrafo\s+único\.?\s*(.+)', texto, re.IGNORECASE | re.DOTALL)
        if paragrafo_match and not artigo["paragrafo_unico"]:
            texto_paragrafo_unico = paragrafo_match.group(1).strip()
            artigo["paragrafo_unico"] = limpar_texto(texto_paragrafo_unico)
    
    # Verificar se é parágrafo numerado
    elif re.search(r'§\s*\d+(?:º|°)?', texto):
        paragrafo_match = re.search(r'(§\s*\d+(?:º|°)?)\s*(.+)', texto, re.DOTALL)
        if paragrafo_match:
            numero_paragrafo = paragrafo_match.group(1).strip()
            texto_paragrafo = limpar_texto(paragrafo_match.group(2))
            
            # Verificar se já existe este parágrafo
            paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo["paragrafos_numerados"])
            if not paragrafo_existe and texto_paragrafo:
                # Buscar alíneas dentro do parágrafo
                alineas = extrair_alineas_do_texto(texto_paragrafo)
                
                artigo["paragrafos_numerados"].append({
                    "numero": numero_paragrafo,
                    "texto": texto_paragrafo,
                    "incisos": [],
                    "alineas": alineas
                })
    
    # Verificar se é inciso (romano)
    elif re.search(r'^[IVX]+\s*[-–]', texto):
        inciso_texto = limpar_texto(texto)
        if inciso_texto and inciso_texto not in artigo["incisos"]:
            artigo["incisos"].append(inciso_texto)
    
    # Verificar se é alínea isolada
    elif re.search(r'^[a-z]\)', texto):
        alinea_texto = limpar_texto(texto)
        if alinea_texto:
            # Adicionar à última estrutura disponível (parágrafo)
            if artigo["paragrafos_numerados"]:
                ultimo_paragrafo = artigo["paragrafos_numerados"][-1]
                if alinea_texto not in ultimo_paragrafo["alineas"]:
                    ultimo_paragrafo["alineas"].append(alinea_texto)

def extrair_alineas_do_texto(texto):
    """
    Extrai alíneas que podem estar dentro do texto de um parágrafo
    """
    alineas = []
    
    # Buscar padrões de alíneas no texto
    alinea_matches = re.finditer(r'([a-z]\)\s*[^;]+(?:;|$))', texto)
    for match in alinea_matches:
        alinea = limpar_texto(match.group(1))
        if alinea and alinea not in alineas:
            alineas.append(alinea)
    
    return alineas

def scraper_corpus927_v4_melhorado():
    """
    Função principal do scraper v4 melhorado
    """
    print("=" * 60)
    print("🔍 SCRAPER CPP V4 MELHORADO - FONTE CORPUS927")
    print("=" * 60)
    
    url = "https://corpus927.enfam.jus.br/legislacao/cpp-41"
    
    try:
        print(f"📡 Fazendo requisição para: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"✅ Requisição bem-sucedida. Status: {response.status_code}")
        print(f"📄 Tamanho do conteúdo: {len(response.content):,} bytes")
        
        # Parse do HTML
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Encontrar todos os artigos
        links_artigos = soup.find_all('a', class_='numero_artigo')
        print(f"🔍 Encontrados {len(links_artigos)} links de artigos")
        
        artigos_extraidos = []
        artigos_processados = set()  # Para evitar duplicações
        
        for i, link_artigo in enumerate(links_artigos):
            try:
                artigo = extrair_estrutura_completa_artigo(link_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])
                    
                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")
                        
            except Exception as e:
                print(f"⚠️ Erro ao processar artigo {i+1}: {e}")
                continue
        
        # Ordenar por número do artigo
        def extrair_numero_para_ordenacao(artigo_id):
            match = re.search(r'Art\.\s*(\d+)', artigo_id)
            return int(match.group(1)) if match else 0
        
        artigos_extraidos.sort(key=lambda x: extrair_numero_para_ordenacao(x["artigo"]))
        
        print(f"✅ Extração concluída. Total: {len(artigos_extraidos)} artigos únicos")
        
        # Salvar arquivos
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Arquivo JSON
        nome_arquivo_json = 'cpp_corpus927_v4_melhorado.json'
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_extraidos, f, ensure_ascii=False, indent=2)
        print(f"💾 Arquivo JSON salvo: {nome_arquivo_json}")
        
        # Estatísticas
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   • Total de artigos: {len(artigos_extraidos)}")
        if artigos_extraidos:
            print(f"   • Primeiro artigo: {artigos_extraidos[0]['artigo']}")
            print(f"   • Último artigo: {artigos_extraidos[-1]['artigo']}")
            
            # Contar estruturas
            total_incisos = sum(len(art['incisos']) for art in artigos_extraidos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos_extraidos)
            total_paragrafos_unicos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'])
            
            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")
            
            # Verificar qualidade dos textos
            caputs_longos = sum(1 for art in artigos_extraidos if art['caput'] and len(art['caput']) > 50)
            paragrafos_longos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'] and len(art['paragrafo_unico']) > 50)
            
            print(f"   • Caputs longos (>50 chars): {caputs_longos}")
            print(f"   • Parágrafos únicos longos (>50 chars): {paragrafos_longos}")
            
            # Verificar primeiros artigos para debug
            print(f"\n🔍 VERIFICAÇÃO DOS PRIMEIROS ARTIGOS:")
            for i, art in enumerate(artigos_extraidos[:5]):
                print(f"   {art['artigo']}: {len(art['incisos'])} incisos, {len(art['paragrafos_numerados'])} parágrafos")
        
        return artigos_extraidos
        
    except requests.RequestException as e:
        print(f"❌ Erro na requisição: {e}")
        return []
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return []

if __name__ == '__main__':
    artigos = scraper_corpus927_v4_melhorado()
    if artigos:
        print(f"\n🎉 Scraping V4 Melhorado concluído com sucesso!")
        print(f"📁 Arquivo gerado: cpp_corpus927_v4_melhorado.json")
        print(f"🔧 Versão V4: Estrutura completa e precisa!")
    else:
        print(f"\n❌ Falha no scraping V4 Melhorado!")
