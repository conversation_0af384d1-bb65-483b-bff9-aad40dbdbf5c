#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT DE VERIFICAÇÃO DA LIMPEZA DO CAPUT
==========================================

Este script verifica se a limpeza do caput foi bem-sucedida,
identificando possíveis duplicações restantes.

USO:
    python verificar_limpeza_caput.py

ARQUIVOS:
    Input: codigo_civil_formato_lexjus_limpo.json (ou outro arquivo especificado)
"""

import json
import re
import os
import sys

def extrair_numero_artigo(artigo_str):
    """Extrai o número do artigo (ex: 'Art. 1071º' -> '1071')"""
    return artigo_str.replace('Art. ', '').replace('º', '').replace('°', '')

def verificar_duplicacoes(data):
    """Verifica duplicações no caput"""
    problemas = []
    
    for artigo in data:
        caput = artigo['caput']
        numero = extrair_numero_artigo(artigo['artigo'])
        
        # Padrões problemáticos
        padroes_problema = [
            rf'^Art\.?\s*{re.escape(numero)}[ºo°\.\s]',
            rf'^{re.escape(numero)}[ºo°]\.',
        ]
        
        # Para números com 4+ dígitos, verificar também versão com ponto
        if len(numero) >= 4:
            numero_com_ponto = numero[:-3] + '.' + numero[-3:]
            padroes_problema.extend([
                rf'^Art\.?\s*{re.escape(numero_com_ponto)}\.',
                rf'^{re.escape(numero_com_ponto)}\.',
            ])
        
        for padrao in padroes_problema:
            if re.match(padrao, caput, re.IGNORECASE):
                problemas.append({
                    'artigo': artigo['artigo'],
                    'numero': numero,
                    'caput': caput,
                    'padrao_encontrado': padrao
                })
                break
    
    return problemas

def verificar_outros_problemas(data):
    """Verifica outros padrões problemáticos"""
    outros_problemas = []
    
    for artigo in data:
        caput = artigo['caput']
        
        # Verificar padrões estranhos
        if re.match(r'^[.\d]+\s', caput):
            outros_problemas.append({
                'artigo': artigo['artigo'],
                'caput': caput[:80] + '...',
                'tipo': 'Começa com número/ponto'
            })
        elif caput.startswith('.'):
            outros_problemas.append({
                'artigo': artigo['artigo'],
                'caput': caput[:80] + '...',
                'tipo': 'Começa com ponto'
            })
        elif not caput.strip():
            outros_problemas.append({
                'artigo': artigo['artigo'],
                'caput': '[VAZIO]',
                'tipo': 'Caput vazio'
            })
    
    return outros_problemas

def analisar_estrutura(data):
    """Analisa a estrutura geral do arquivo"""
    total_artigos = len(data)
    
    # Contar tipos de conteúdo
    com_incisos = len([art for art in data if art.get('incisos') and len(art['incisos']) > 0])
    com_paragrafos = len([art for art in data if art.get('paragrafos_numerados') and len(art['paragrafos_numerados']) > 0])
    com_paragrafo_unico = len([art for art in data if art.get('paragrafo_unico')])
    
    return {
        'total': total_artigos,
        'com_incisos': com_incisos,
        'com_paragrafos': com_paragrafos,
        'com_paragrafo_unico': com_paragrafo_unico
    }

def main():
    print("=== VERIFICAÇÃO DA LIMPEZA DO CAPUT ===")
    
    # Determinar arquivo a verificar
    arquivo = 'codigo_civil_formato_lexjus_limpo.json'
    if len(sys.argv) > 1:
        arquivo = sys.argv[1]
    
    if not os.path.exists(arquivo):
        print(f"ERRO: Arquivo {arquivo} não encontrado!")
        print(f"USO: python {sys.argv[0]} [arquivo.json]")
        return
    
    try:
        # Carregar arquivo
        print(f"Verificando arquivo: {arquivo}")
        with open(arquivo, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Análise da estrutura
        estrutura = analisar_estrutura(data)
        print(f"\n📊 ESTRUTURA DO ARQUIVO:")
        print(f"  Total de artigos: {estrutura['total']}")
        print(f"  Com incisos: {estrutura['com_incisos']} ({estrutura['com_incisos']/estrutura['total']*100:.1f}%)")
        print(f"  Com parágrafos numerados: {estrutura['com_paragrafos']} ({estrutura['com_paragrafos']/estrutura['total']*100:.1f}%)")
        print(f"  Com parágrafo único: {estrutura['com_paragrafo_unico']} ({estrutura['com_paragrafo_unico']/estrutura['total']*100:.1f}%)")
        
        # Verificar duplicações
        print(f"\n🔍 VERIFICAÇÃO DE DUPLICAÇÕES:")
        problemas = verificar_duplicacoes(data)
        
        if problemas:
            print(f"  ❌ {len(problemas)} artigos com duplicações encontradas:")
            for i, problema in enumerate(problemas[:5]):
                print(f"    {i+1}. {problema['artigo']}: {problema['caput'][:60]}...")
            if len(problemas) > 5:
                print(f"    ... e mais {len(problemas) - 5} artigos")
        else:
            print(f"  ✅ Nenhuma duplicação encontrada!")
        
        # Verificar outros problemas
        print(f"\n🔍 VERIFICAÇÃO DE OUTROS PROBLEMAS:")
        outros = verificar_outros_problemas(data)
        
        if outros:
            print(f"  ⚠️  {len(outros)} artigos com outros problemas:")
            for i, problema in enumerate(outros[:3]):
                print(f"    {i+1}. {problema['artigo']} ({problema['tipo']}): {problema['caput']}")
            if len(outros) > 3:
                print(f"    ... e mais {len(outros) - 3} artigos")
        else:
            print(f"  ✅ Nenhum outro problema encontrado!")
        
        # Mostrar exemplos de artigos limpos
        print(f"\n📋 EXEMPLOS DE ARTIGOS LIMPOS:")
        exemplos = [0, 3, 7, 24, 40, 1070]  # Artigos 1, 4, 8, 25, 41, 1071
        for i in exemplos:
            if i < len(data):
                artigo = data[i]
                print(f"\n{artigo['artigo']}:")
                print(f"  Caput: {artigo['caput'][:100]}...")
        
        # Resumo final
        print(f"\n📈 RESUMO DA VERIFICAÇÃO:")
        if not problemas and not outros:
            print(f"  🎉 ARQUIVO PERFEITO! Nenhum problema encontrado.")
        else:
            total_problemas = len(problemas) + len(outros)
            print(f"  ⚠️  {total_problemas} problemas encontrados que precisam de atenção.")
        
        print(f"  📁 Arquivo verificado: {arquivo}")
        print(f"  📊 Total de artigos: {estrutura['total']}")
        
    except Exception as e:
        print(f"ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
