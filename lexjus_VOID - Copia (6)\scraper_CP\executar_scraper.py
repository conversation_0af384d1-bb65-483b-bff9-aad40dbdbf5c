#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de execução simplificada do Scraper do Código Penal
Executa todo o processo de extração e limpeza automaticamente
"""

import subprocess
import sys
import os

def executar_comando(comando, descricao):
    """
    Executa um comando e mostra o resultado
    """
    print(f"\n🔄 {descricao}...")
    try:
        resultado = subprocess.run(comando, shell=True, capture_output=True, text=True, encoding='utf-8')
        if resultado.returncode == 0:
            print(f"✅ {descricao} - Concluído!")
            if resultado.stdout.strip():
                print(resultado.stdout)
        else:
            print(f"❌ {descricao} - Erro!")
            if resultado.stderr.strip():
                print(f"Erro: {resultado.stderr}")
        return resultado.returncode == 0
    except Exception as e:
        print(f"❌ {descricao} - Exceção: {e}")
        return False

def verificar_arquivos():
    """
    Verifica se os arquivos necessários existem
    """
    arquivos_necessarios = [
        'scraper_planalto.py',
        'limpar_referencias_finais.py'
    ]
    
    print("🔍 Verificando arquivos necessários...")
    for arquivo in arquivos_necessarios:
        if os.path.exists(arquivo):
            print(f"✅ {arquivo} - Encontrado")
        else:
            print(f"❌ {arquivo} - NÃO ENCONTRADO!")
            return False
    return True

def main():
    print("=" * 60)
    print("🏛️  SCRAPER DO CÓDIGO PENAL - PLANALTO")
    print("=" * 60)
    print("📋 Este script executa todo o processo automaticamente:")
    print("   1. Extração dos artigos do Código Penal")
    print("   2. Limpeza das referências legislativas")
    print("   3. Geração dos arquivos finais")
    print("=" * 60)
    
    # Verificar se os arquivos necessários existem
    if not verificar_arquivos():
        print("\n❌ Arquivos necessários não encontrados!")
        print("Certifique-se de que está executando na pasta correta.")
        return
    
    # Passo 1: Executar scraper principal
    sucesso1 = executar_comando(
        "python scraper_planalto.py",
        "Executando scraper principal (extração dos artigos)"
    )
    
    if not sucesso1:
        print("\n❌ Falha na extração dos artigos. Verifique a conexão com a internet.")
        return
    
    # Verificar se os arquivos foram gerados
    if not os.path.exists('codigo_penal.json'):
        print("\n❌ Arquivo codigo_penal.json não foi gerado!")
        return
    
    # Passo 2: Executar limpeza final
    sucesso2 = executar_comando(
        "python limpar_referencias_finais.py",
        "Executando limpeza final (remoção de referências)"
    )
    
    if not sucesso2:
        print("\n⚠️  Limpeza final falhou, mas os arquivos básicos foram gerados.")
    
    # Verificar arquivos finais
    print("\n📁 Verificando arquivos gerados:")
    arquivos_saida = [
        ('codigo_penal.js', 'Arquivo JavaScript básico'),
        ('codigo_penal.json', 'Arquivo JSON básico'),
        ('codigo_penal_limpo.js', 'Arquivo JavaScript limpo (recomendado)'),
        ('codigo_penal_limpo.json', 'Arquivo JSON limpo (recomendado)')
    ]
    
    arquivos_encontrados = 0
    for arquivo, descricao in arquivos_saida:
        if os.path.exists(arquivo):
            tamanho = os.path.getsize(arquivo)
            print(f"✅ {arquivo} - {descricao} ({tamanho:,} bytes)")
            arquivos_encontrados += 1
        else:
            print(f"❌ {arquivo} - {descricao} (não encontrado)")
    
    # Resumo final
    print("\n" + "=" * 60)
    if arquivos_encontrados >= 2:
        print("🎉 SCRAPER EXECUTADO COM SUCESSO!")
        print(f"📊 {arquivos_encontrados} de {len(arquivos_saida)} arquivos gerados")
        print("\n📋 Próximos passos:")
        print("   1. Use 'codigo_penal_limpo.js' no seu projeto (recomendado)")
        print("   2. O arquivo contém 483 artigos do Código Penal completo")
        print("   3. Inclua no HTML: <script src='codigo_penal_limpo.js'></script>")
        print("   4. Use as funções: buscarArtigo(numero) e buscarArtigosPorTexto(texto)")
    else:
        print("⚠️  SCRAPER EXECUTADO COM PROBLEMAS")
        print("Verifique os erros acima e tente novamente.")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
