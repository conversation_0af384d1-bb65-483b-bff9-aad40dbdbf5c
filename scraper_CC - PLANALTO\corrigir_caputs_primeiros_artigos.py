#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT PARA CORRIGIR OS CAPUTS DOS PRIMEIROS ARTIGOS DO CÓDIGO CIVIL
Corrige especificamente os artigos 1º ao 9º que têm estrutura HTML diferente
"""

import json
import re
import os

def corrigir_caputs_primeiros_artigos():
    """Corrige os caputs dos primeiros artigos baseado na análise do HTML"""

    # Carregar o arquivo JSON (usar o arquivo corrigido se existir)
    arquivo_entrada = 'cc_final_perfeito_corrigido.json' if os.path.exists('cc_final_perfeito_corrigido.json') else 'cc_final_perfeito.json'
    with open(arquivo_entrada, 'r', encoding='utf-8') as f:
        artigos = json.load(f)

    # Dicionário com os caputs corretos dos primeiros artigos
    caputs_corretos = {
        "Art. 1º": "Toda pessoa é capaz de direitos e deveres na ordem civil.",
        "Art. 2º": "A personalidade civil da pessoa começa do nascimento com vida; mas a lei põe a salvo, desde a concepção, os direitos do nascituro.",
        "Art. 3º": "São absolutamente incapazes de exercer pessoalmente os atos da vida civil os menores de 16 (dezesseis) anos.",
        "Art. 4º": "São incapazes, relativamente a certos atos ou à maneira de os exercer:",
        "Art. 5º": "A menoridade cessa aos dezoito anos completos, quando a pessoa fica habilitada à prática de todos os atos da vida civil.",
        "Art. 6º": "A existência da pessoa natural termina com a morte; presume-se esta, quanto aos ausentes, nos casos em que a lei autoriza a abertura de sucessão definitiva.",
        "Art. 7º": "Pode ser declarada a morte presumida, sem decretação de ausência:",
        "Art. 8º": "Se dois ou mais indivíduos falecerem na mesma ocasião, não se podendo averiguar se algum dos comorientes precedeu aos outros, presumir-se-ão simultaneamente mortos.",
        "Art. 9º": "Serão registrados em registro público:"
    }

    # Corrigir os caputs
    artigos_corrigidos = 0
    for artigo in artigos:
        if artigo["artigo"] in caputs_corretos:
            artigo["caput"] = caputs_corretos[artigo["artigo"]]
            artigos_corrigidos += 1
            print(f"Corrigido: {artigo['artigo']}")

    # Corrigir também alguns incisos e parágrafos únicos conhecidos
    for artigo in artigos:
        if artigo["artigo"] == "Art. 4º":
            artigo["incisos"] = [
                "I - os maiores de dezesseis e menores de dezoito anos;",
                "II - os ébrios habituais e os viciados em tóxico;",
                "III - aqueles que, por causa transitória ou permanente, não puderem exprimir sua vontade;",
                "IV - os pródigos."
            ]
            artigo["paragrafo_unico"] = "A capacidade dos indígenas será regulada por legislação especial."

        elif artigo["artigo"] == "Art. 5º":
            artigo["paragrafo_unico"] = "Cessará, para os menores, a incapacidade:"
            artigo["incisos"] = [
                "I - pela concessão dos pais, ou de um deles na falta do outro, mediante instrumento público, independentemente de homologação judicial, ou por sentença do juiz, ouvido o tutor, se o menor tiver dezesseis anos completos;",
                "II - pelo casamento;",
                "III - pelo exercício de emprego público efetivo;",
                "IV - pela colação de grau em curso de ensino superior;",
                "V - pelo estabelecimento civil ou comercial, ou pela existência de relação de emprego, desde que, em função deles, o menor com dezesseis anos completos tenha economia própria."
            ]

        elif artigo["artigo"] == "Art. 7º":
            artigo["incisos"] = [
                "I - se for extremamente provável a morte de quem estava em perigo de vida;",
                "II - se alguém, desaparecido em campanha ou feito prisioneiro, não for encontrado até dois anos após o término da guerra."
            ]
            artigo["paragrafo_unico"] = "A declaração da morte presumida, nesses casos, somente poderá ser requerida depois de esgotadas as buscas e averiguações, devendo a sentença fixar a data provável do falecimento."

        elif artigo["artigo"] == "Art. 9º":
            artigo["incisos"] = [
                "I - os nascimentos, casamentos e óbitos;",
                "II - a emancipação por outorga dos pais ou por sentença do juiz;",
                "III - a interdição por incapacidade absoluta ou relativa;",
                "IV - a sentença declaratória de ausência e de morte presumida."
            ]

    # Salvar arquivo corrigido
    with open('cc_final_perfeito_corrigido.json', 'w', encoding='utf-8') as f:
        json.dump(artigos, f, ensure_ascii=False, indent=2)

    print(f"\n✅ Correção concluída!")
    print(f"📊 {artigos_corrigidos} caputs corrigidos")
    print(f"📁 Arquivo salvo: cc_final_perfeito_corrigido.json")

    # Estatísticas finais
    artigos_com_caput = sum(1 for art in artigos if art['caput'])
    total_incisos = sum(len(art['incisos']) for art in artigos)
    total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos)
    total_paragrafos_unicos = sum(1 for art in artigos if art['paragrafo_unico'])

    print(f"\n📈 ESTATÍSTICAS FINAIS:")
    print(f"   Total de artigos: {len(artigos)}")
    print(f"   Artigos com caput: {artigos_com_caput} ({artigos_com_caput/len(artigos)*100:.1f}%)")
    print(f"   Total de incisos: {total_incisos}")
    print(f"   Total de parágrafos numerados: {total_paragrafos}")
    print(f"   Total de parágrafos únicos: {total_paragrafos_unicos}")

    # Mostrar primeiros artigos corrigidos
    print(f"\n📋 PRIMEIROS 10 ARTIGOS CORRIGIDOS:")
    for art in artigos[:10]:
        caput_preview = art['caput'][:50] + "..." if len(art['caput']) > 50 else art['caput']
        print(f"   {art['artigo']}: '{caput_preview}'")

    return artigos

if __name__ == '__main__':
    print("INICIANDO CORREÇÃO DOS CAPUTS DOS PRIMEIROS ARTIGOS")
    print("Objetivo: CORRIGIR ARTIGOS 1º AO 9º COM CAPUTS CORRETOS")
    print("-" * 80)

    artigos_corrigidos = corrigir_caputs_primeiros_artigos()

    print(f"\n🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!")
    print(f"✅ Código Civil com {len(artigos_corrigidos)} artigos e caputs corretos!")
