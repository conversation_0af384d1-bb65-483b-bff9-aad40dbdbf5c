
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"1",
  
  "macros":[{"function":"__e"},{"function":"__c","vtp_value":"google.com.br"},{"function":"__c","vtp_value":0}],
  "tags":[{"function":"__ogt_1p_data_v2","priority":13,"vtp_isAutoEnabled":true,"vtp_autoCollectExclusionSelectors":["list",["map","exclusionSelector",""]],"vtp_isEnabled":true,"vtp_cityType":"CSS_SELECTOR","vtp_manualEmailEnabled":false,"vtp_firstNameType":"CSS_SELECTOR","vtp_countryType":"CSS_SELECTOR","vtp_cityValue":"","vtp_emailType":"CSS_SELECTOR","vtp_regionType":"CSS_SELECTOR","vtp_autoEmailEnabled":true,"vtp_postalCodeValue":"","vtp_lastNameValue":"","vtp_phoneType":"CSS_SELECTOR","vtp_phoneValue":"","vtp_streetType":"CSS_SELECTOR","vtp_autoPhoneEnabled":false,"vtp_postalCodeType":"CSS_SELECTOR","vtp_emailValue":"","vtp_firstNameValue":"","vtp_streetValue":"","vtp_lastNameType":"CSS_SELECTOR","vtp_autoAddressEnabled":false,"vtp_regionValue":"","vtp_countryValue":"","vtp_isAutoCollectPiiEnabledFlag":false,"tag_id":4},{"function":"__ccd_ga_first","priority":12,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":18},{"function":"__set_product_settings","priority":11,"vtp_instanceDestinationId":"G-5S1SQJV11Q","vtp_foreignTldMacroResult":["macro",1],"vtp_isChinaVipRegionMacroResult":["macro",2],"tag_id":17},{"function":"__ccd_ga_regscope","priority":10,"vtp_settingsTable":["list",["map","redactFieldGroup","DEVICE_AND_GEO","disallowAllRegions",false,"disallowedRegions",""],["map","redactFieldGroup","GOOGLE_SIGNALS","disallowAllRegions",true,"disallowedRegions",""]],"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":16},{"function":"__ccd_em_download","priority":9,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":15},{"function":"__ccd_em_form","priority":8,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":14},{"function":"__ccd_em_outbound_click","priority":7,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":13},{"function":"__ccd_em_page_view","priority":6,"vtp_historyEvents":true,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":12},{"function":"__ccd_em_scroll","priority":5,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":11},{"function":"__ccd_em_site_search","priority":4,"vtp_searchQueryParams":"q,s,search,query,keyword","vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":10},{"function":"__ccd_em_video","priority":3,"vtp_includeParams":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":9},{"function":"__ccd_conversion_marking","priority":2,"vtp_conversionRules":["list",["map","matchingRules","{\"type\":5,\"args\":[{\"stringValue\":\"purchase\"},{\"contextValue\":{\"namespaceType\":1,\"keyParts\":[\"eventName\"]}}]}"]],"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":8},{"function":"__ccd_auto_redact","priority":1,"vtp_redactEmail":true,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":7},{"function":"__gct","vtp_trackingId":"G-5S1SQJV11Q","vtp_sessionDuration":0,"tag_id":1},{"function":"__ccd_ga_last","priority":0,"vtp_instanceDestinationId":"G-5S1SQJV11Q","tag_id":6}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"}],
  "rules":[[["if",0],["add",13]],[["if",1],["add",0,14,12,11,10,9,8,7,6,5,4,3,2,1]]]
},
"runtime":[ [50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__ccd_auto_redact",[46,"a"],[50,"u",[46,"aI"],[36,[2,[15,"aI"],"replace",[7,[15,"t"],"\\$1"]]]],[50,"v",[46,"aI"],[52,"aJ",[30,["c",[15,"aI"]],[15,"aI"]]],[52,"aK",[7]],[65,"aL",[2,[15,"aJ"],"split",[7,""]],[46,[53,[52,"aM",[7,["u",[15,"aL"]]]],[52,"aN",["d",[15,"aL"]]],[22,[12,[15,"aN"],[45]],[46,[53,[36,["d",["u",[15,"aI"]]]]]]],[22,[21,[15,"aN"],[15,"aL"]],[46,[53,[2,[15,"aM"],"push",[7,[15,"aN"]]],[22,[21,[15,"aL"],[2,[15,"aL"],"toLowerCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toLowerCase",[7]]]]]]],[46,[22,[21,[15,"aL"],[2,[15,"aL"],"toUpperCase",[7]]],[46,[53,[2,[15,"aM"],"push",[7,["d",[2,[15,"aL"],"toUpperCase",[7]]]]]]]]]]]]],[22,[18,[17,[15,"aM"],"length"],1],[46,[53,[2,[15,"aK"],"push",[7,[0,[0,"(?:",[2,[15,"aM"],"join",[7,"|"]]],")"]]]]],[46,[53,[2,[15,"aK"],"push",[7,[16,[15,"aM"],0]]]]]]]]],[36,[2,[15,"aK"],"join",[7,""]]]],[50,"w",[46,"aI","aJ","aK"],[52,"aL",["y",[15,"aI"],[15,"aK"]]],[22,[28,[15,"aL"]],[46,[36,[15,"aI"]]]],[22,[28,[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[41,"aM"],[3,"aM",[17,[15,"aL"],"search"]],[65,"aN",[15,"aJ"],[46,[53,[52,"aO",[7,["u",[15,"aN"]],["v",[15,"aN"]]]],[65,"aP",[15,"aO"],[46,[53,[52,"aQ",[30,[16,[15,"s"],[15,"aP"]],[43,[15,"s"],[15,"aP"],["b",[0,[0,"([?&]",[15,"aP"]],"=)([^&]*)"],"gi"]]]],[3,"aM",[2,[15,"aM"],"replace",[7,[15,"aQ"],[0,"$1",[15,"q"]]]]]]]]]]],[22,[20,[15,"aM"],[17,[15,"aL"],"search"]],[46,[36,[15,"aI"]]]],[22,[20,[16,[15,"aM"],0],"&"],[46,[3,"aM",[2,[15,"aM"],"substring",[7,1]]]]],[22,[21,[16,[15,"aM"],0],"?"],[46,[3,"aM",[0,"?",[15,"aM"]]]]],[22,[20,[15,"aM"],"?"],[46,[3,"aM",""]]],[43,[15,"aL"],"search",[15,"aM"]],[36,["z",[15,"aL"],[15,"aK"]]]],[50,"y",[46,"aI","aJ"],[22,[20,[15,"aJ"],[17,[15,"r"],"PATH"]],[46,[53,[3,"aI",[0,[15,"x"],[15,"aI"]]]]]],[36,["f",[15,"aI"]]]],[50,"z",[46,"aI","aJ"],[41,"aK"],[3,"aK",""],[22,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[46,[53,[41,"aL"],[3,"aL",""],[22,[30,[17,[15,"aI"],"username"],[17,[15,"aI"],"password"]],[46,[53,[3,"aL",[0,[15,"aL"],[0,[0,[0,[17,[15,"aI"],"username"],[39,[17,[15,"aI"],"password"],":",""]],[17,[15,"aI"],"password"]],"@"]]]]]],[3,"aK",[0,[0,[0,[17,[15,"aI"],"protocol"],"//"],[15,"aL"]],[17,[15,"aI"],"host"]]]]]],[36,[0,[0,[0,[15,"aK"],[17,[15,"aI"],"pathname"]],[17,[15,"aI"],"search"]],[17,[15,"aI"],"hash"]]]],[50,"aA",[46,"aI","aJ"],[41,"aK"],[3,"aK",[2,[15,"aI"],"replace",[7,[15,"m"],[15,"q"]]]],[22,[30,[20,[15,"aJ"],[17,[15,"r"],"URL"]],[20,[15,"aJ"],[17,[15,"r"],"PATH"]]],[46,[53,[52,"aL",["y",[15,"aK"],[15,"aJ"]]],[22,[20,[15,"aL"],[44]],[46,[36,[15,"aK"]]]],[52,"aM",[17,[15,"aL"],"search"]],[52,"aN",[2,[15,"aM"],"replace",[7,[15,"n"],[15,"q"]]]],[22,[20,[15,"aM"],[15,"aN"]],[46,[36,[15,"aK"]]]],[43,[15,"aL"],"search",[15,"aN"]],[3,"aK",["z",[15,"aL"],[15,"aJ"]]]]]],[36,[15,"aK"]]],[50,"aB",[46,"aI"],[22,[20,[15,"aI"],[15,"p"]],[46,[53,[36,[17,[15,"r"],"PATH"]]]],[46,[22,[21,[2,[15,"o"],"indexOf",[7,[15,"aI"]]],[27,1]],[46,[53,[36,[17,[15,"r"],"URL"]]]],[46,[53,[36,[17,[15,"r"],"TEXT"]]]]]]]],[50,"aC",[46,"aI","aJ"],[41,"aK"],[3,"aK",false],[52,"aL",["e",[15,"aI"]]],[38,[15,"aL"],[46,"string","array","object"],[46,[5,[46,[52,"aM",["aA",[15,"aI"],[15,"aJ"]]],[22,[21,[15,"aI"],[15,"aM"]],[46,[53,[36,[15,"aM"]]]]],[4]]],[5,[46,[53,[41,"aN"],[3,"aN",0],[63,[7,"aN"],[23,[15,"aN"],[17,[15,"aI"],"length"]],[33,[15,"aN"],[3,"aN",[0,[15,"aN"],1]]],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]]],[4]]],[5,[46,[54,"aN",[15,"aI"],[46,[53,[52,"aO",["aC",[16,[15,"aI"],[15,"aN"]],[17,[15,"r"],"TEXT"]]],[22,[21,[15,"aO"],[44]],[46,[53,[43,[15,"aI"],[15,"aN"],[15,"aO"]],[3,"aK",true]]]]]]],[4]]]]],[36,[39,[15,"aK"],[15,"aI"],[44]]]],[50,"aH",[46,"aI","aJ"],[52,"aK",[30,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"I"]]],[7]]],[22,[20,[2,[15,"aK"],"indexOf",[7,[15,"aJ"]]],[27,1]],[46,[53,[2,[15,"aK"],"push",[7,[15,"aJ"]]]]]],[2,[15,"aI"],"setMetadata",[7,[17,[15,"h"],"I"],[15,"aK"]]]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","encodeUriComponent"]],[52,"e",["require","getType"]],[52,"f",["require","parseUrl"]],[52,"g",["require","internal.registerCcdCallback"]],[52,"h",[15,"__module_metadataSchema"]],[52,"i",[17,[15,"a"],"instanceDestinationId"]],[52,"j",[17,[15,"a"],"redactEmail"]],[52,"k",[17,[15,"a"],"redactQueryParams"]],[52,"l",[39,[15,"k"],[2,[15,"k"],"split",[7,","]],[7]]],[22,[1,[28,[17,[15,"l"],"length"]],[28,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["b","[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}","gi"]],[52,"n",["b",[0,"([A-Z0-9._-]|%25|%2B)+%40[A-Z0-9.-]","+\\.[A-Z]{2,}"],"gi"]],[52,"o",[7,"page_location","page_referrer","page_path","link_url","video_url","form_destination"]],[52,"p","page_path"],[52,"q","(redacted)"],[52,"r",[8,"TEXT",0,"URL",1,"PATH",2]],[52,"s",[8]],[52,"t",["b","([\\\\^$.|?*+(){}]|\\[|\\[)","g"]],[52,"x","http://."],[52,"aD",15],[52,"aE",16],[52,"aF",23],[52,"aG",24],["g",[15,"i"],[51,"",[7,"aI"],[22,[15,"j"],[46,[53,[52,"aJ",[2,[15,"aI"],"getHitKeys",[7]]],[65,"aK",[15,"aJ"],[46,[53,[22,[20,[15,"aK"],"_sst_parameters"],[46,[6]]],[52,"aL",[2,[15,"aI"],"getHitData",[7,[15,"aK"]]]],[22,[28,[15,"aL"]],[46,[6]]],[52,"aM",["aB",[15,"aK"]]],[52,"aN",["aC",[15,"aL"],[15,"aM"]]],[22,[21,[15,"aN"],[44]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aK"],[15,"aN"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"Y"]]],[15,"aF"],[15,"aD"]]]]]]]]]]]],[22,[17,[15,"l"],"length"],[46,[53,[65,"aJ",[15,"o"],[46,[53,[52,"aK",[2,[15,"aI"],"getHitData",[7,[15,"aJ"]]]],[22,[28,[15,"aK"]],[46,[6]]],[52,"aL",[39,[20,[15,"aJ"],[15,"p"]],[17,[15,"r"],"PATH"],[17,[15,"r"],"URL"]]],[52,"aM",["w",[15,"aK"],[15,"l"],[15,"aL"]]],[22,[21,[15,"aM"],[15,"aK"]],[46,[53,[2,[15,"aI"],"setHitData",[7,[15,"aJ"],[15,"aM"]]],["aH",[15,"aI"],[39,[2,[15,"aI"],"getMetadata",[7,[17,[15,"h"],"Y"]]],[15,"aG"],[15,"aE"]]]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_conversion_marking",[46,"a"],[22,[30,[28,[17,[15,"a"],"conversionRules"]],[20,[17,[17,[15,"a"],"conversionRules"],"length"],0]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.copyPreHit"]],[52,"c",["require","internal.evaluateBooleanExpression"]],[52,"d",["require","internal.registerCcdCallback"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f","first_visit"],[52,"g","session_start"],[41,"h"],[41,"i"],["d",[17,[15,"a"],"instanceDestinationId"],[51,"",[7,"j"],[52,"k",[8,"preHit",[15,"j"]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"k"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"M"],true]],[4]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"O"]]],[46,[53,[22,[28,[15,"h"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"f"]]],[3,"h",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"h"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"P"],true]],[4]]]]]]]]]],[22,[2,[15,"j"],"getMetadata",[7,[17,[15,"e"],"V"]]],[46,[53,[22,[28,[15,"i"]],[46,[53,[52,"l",["b",[15,"j"],[8,"omitHitData",true,"omitMetadata",true]]],[2,[15,"l"],"setEventName",[7,[15,"g"]]],[3,"i",[8,"preHit",[15,"l"]]]]]],[65,"l",[17,[15,"a"],"conversionRules"],[46,[53,[22,["c",[17,[15,"l"],"matchingRules"],[15,"i"]],[46,[53,[2,[15,"j"],"setMetadata",[7,[17,[15,"e"],"W"],true]],[4]]]]]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]],[36]]
 ,[50,"__ccd_em_download",[46,"a"],[50,"q",[46,"w"],[36,[1,[15,"w"],[21,[2,[2,[15,"w"],"toLowerCase",[7]],"match",[7,[15,"p"]]],[45]]]]],[50,"r",[46,"w"],[52,"x",[2,[17,[15,"w"],"pathname"],"split",[7,"."]]],[52,"y",[39,[18,[17,[15,"x"],"length"],1],[16,[15,"x"],[37,[17,[15,"x"],"length"],1]],""]],[36,[16,[2,[15,"y"],"split",[7,"/"]],0]]],[50,"s",[46,"w"],[36,[39,[12,[2,[17,[15,"w"],"pathname"],"substring",[7,0,1]],"/"],[17,[15,"w"],"pathname"],[0,"/",[17,[15,"w"],"pathname"]]]]],[50,"t",[46,"w"],[41,"x"],[3,"x",""],[22,[1,[15,"w"],[17,[15,"w"],"href"]],[46,[53,[41,"y"],[3,"y",[2,[17,[15,"w"],"href"],"indexOf",[7,"#"]]],[3,"x",[39,[23,[15,"y"],0],[17,[15,"w"],"href"],[2,[17,[15,"w"],"href"],"substring",[7,0,[15,"y"]]]]]]]],[36,[15,"x"]]],[50,"v",[46,"w"],[52,"x",[8]],[43,[15,"x"],[15,"i"],true],[43,[15,"x"],[15,"e"],true],[43,[15,"w"],"eventMetadata",[15,"x"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmDownloadActivity"]],[52,"e","speculative"],[52,"f","ae_block_downloads"],[52,"g","file_download"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnLinkClick"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","parseUrl"]],[52,"o",["require","internal.sendGtagEvent"]],[52,"p",[0,"^(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|","mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$"]],[52,"u",["l",[8,"checkValidation",true]]],[22,[28,[15,"u"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.linkClick",[51,"",[7,"w","x"],["x"],[52,"y",[8,"eventId",[16,[15,"w"],"gtm.uniqueEventId"],"deferrable",true]],[52,"z",[16,[15,"w"],"gtm.elementUrl"]],[52,"aA",["n",[15,"z"]]],[22,[28,[15,"aA"]],[46,[36]]],[52,"aB",["r",[15,"aA"]]],[22,[28,["q",[15,"aB"]]],[46,[53,[36]]]],[52,"aC",[8,"link_id",[16,[15,"w"],"gtm.elementId"],"link_url",["t",[15,"aA"]],"link_text",[16,[15,"w"],"gtm.elementText"],"file_name",["s",[15,"aA"]],"file_extension",[15,"aB"]]],["v",[15,"y"]],["o",["m"],[15,"g"],[15,"aC"],[15,"y"]]],[15,"u"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_form",[46,"a"],[50,"t",[46,"aA"],[52,"aB",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aB"],"event_usage",[7,8]],[43,[15,"aA"],[15,"m"],[15,"aB"]]],[50,"u",[46,"aA","aB"],[52,"aC",[30,[16,[15,"aA"],[15,"m"]],[8]]],[43,[15,"aC"],[15,"l"],true],[43,[15,"aC"],[15,"g"],true],[22,[16,[15,"aB"],"gtm.formCanceled"],[46,[53,[43,[15,"aC"],[15,"n"],true]]]],[43,[15,"aA"],[15,"m"],[15,"aC"]]],[50,"v",[46,"aA","aB","aC"],[52,"aD",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[20,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aD"],"length"],0],[46,[53,["s",[15,"aD"],[15,"aA"],[15,"aB"],[15,"aC"]]]]],[52,"aE",[2,["r"],"filter",[7,[51,"",[7,"aF"],[36,[21,[2,[15,"aF"],"indexOf",[7,"AW-"]],0]]]]]],[22,[18,[17,[15,"aE"],"length"],0],[46,[53,[43,[15,"aC"],"deferrable",true],["s",[15,"aE"],[15,"aA"],[15,"aB"],[15,"aC"]]]]]],[52,"b",["require","internal.isFeatureEnabled"]],[52,"c",[15,"__module_featureFlags"]],[52,"d",["require","internal.getProductSettingsParameter"]],[52,"e",["require","templateStorage"]],[52,"f",[15,"__module_ccdEmFormActivity"]],[52,"g","speculative"],[52,"h","ae_block_form"],[52,"i","form_submit"],[52,"j","form_start"],[52,"k","isRegistered"],[52,"l","em_event"],[52,"m","eventMetadata"],[52,"n","form_event_canceled"],[52,"o",[17,[15,"a"],"instanceDestinationId"]],[22,["d",[15,"o"],[15,"h"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"f"],"A",[7,[17,[15,"a"],"instanceDestinationId"],[17,[15,"a"],"skipValidation"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"e"],"getItem",[7,[15,"k"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"setItem",[7,[15,"k"],true]],[52,"p",["require","internal.addFormInteractionListener"]],[52,"q",["require","internal.addFormSubmitListener"]],[52,"r",["require","internal.getDestinationIds"]],[52,"s",["require","internal.sendGtagEvent"]],[52,"w",[8]],[52,"x",[51,"",[7,"aA","aB"],[22,[15,"aB"],[46,["aB"]]],[52,"aC",[16,[15,"aA"],"gtm.elementId"]],[22,[16,[15,"w"],[15,"aC"]],[46,[36]]],[43,[15,"w"],[15,"aC"],true],[52,"aD",[8,"form_id",[15,"aC"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"first_field_id",[16,[15,"aA"],"gtm.interactedFormFieldId"],"first_field_name",[16,[15,"aA"],"gtm.interactedFormFieldName"],"first_field_type",[16,[15,"aA"],"gtm.interactedFormFieldType"],"first_field_position",[16,[15,"aA"],"gtm.interactedFormFieldPosition"]]],[52,"aE",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aE"]],["u",[15,"aE"],[15,"aA"]],["v",[15,"j"],[15,"aD"],[15,"aE"]]]],[52,"y",["b",[17,[15,"c"],"DV"]]],[52,"z",[51,"",[7,"aA","aB"],["x",[15,"aA"],[44]],[52,"aC",[8,"form_id",[16,[15,"aA"],"gtm.elementId"],"form_name",[16,[15,"aA"],"gtm.interactedFormName"],"form_destination",[16,[15,"aA"],"gtm.elementUrl"],"form_length",[16,[15,"aA"],"gtm.interactedFormLength"],"form_submit_text",[39,[15,"y"],[16,[15,"aA"],"gtm.formSubmitElementText"],[16,[15,"aA"],"gtm.formSubmitButtonText"]]]],[43,[15,"aC"],"event_callback",[15,"aB"]],[52,"aD",[8,"eventId",[17,[15,"a"],"gtmEventId"]]],["t",[15,"aD"]],["u",[15,"aD"],[15,"aA"]],["v",[15,"i"],[15,"aC"],[15,"aD"]]]],[22,[15,"y"],[46,[53,[52,"aA",["require","internal.addDataLayerEventListener"]],[52,"aB",["require","internal.enableAutoEventOnFormSubmit"]],[52,"aC",["require","internal.enableAutoEventOnFormInteraction"]],[52,"aD",["aC"]],[22,[28,[15,"aD"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formInteract",[15,"x"],[15,"aD"]],[52,"aE",["aB",[8,"checkValidation",false,"waitForTags",false]]],[22,[28,[15,"aE"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],["aA","gtm.formSubmit",[15,"z"],[15,"aE"]]]],[46,[53,["p",[15,"x"]],["q",[15,"z"],[8,"waitForCallbacks",false,"checkValidation",false]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_outbound_click",[46,"a"],[50,"s",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",""],[22,[1,[15,"y"],[17,[15,"y"],"href"]],[46,[53,[41,"aA"],[3,"aA",[2,[17,[15,"y"],"href"],"indexOf",[7,"#"]]],[3,"z",[39,[23,[15,"aA"],0],[17,[15,"y"],"href"],[2,[17,[15,"y"],"href"],"substring",[7,0,[15,"aA"]]]]]]]],[36,[15,"z"]]],[50,"t",[46,"y"],[22,[28,[15,"y"]],[46,[36,[44]]]],[41,"z"],[3,"z",[17,[15,"y"],"hostname"]],[52,"aA",[2,[15,"z"],"match",[7,"^www\\d*\\."]]],[22,[1,[15,"aA"],[16,[15,"aA"],0]],[46,[3,"z",[2,[15,"z"],"substring",[7,[17,[16,[15,"aA"],0],"length"]]]]]],[36,[15,"z"]]],[50,"u",[46,"y"],[22,[28,[15,"y"]],[46,[36,false]]],[52,"z",[2,[17,[15,"y"],"hostname"],"toLowerCase",[7]]],[22,[1,[17,[15,"b"],"enableGa4OutboundClicksFix"],[28,[15,"z"]]],[46,[53,[36,false]]]],[41,"aA"],[3,"aA",[2,["t",["q",["p"]]],"toLowerCase",[7]]],[41,"aB"],[3,"aB",[37,[17,[15,"z"],"length"],[17,[15,"aA"],"length"]]],[22,[1,[18,[15,"aB"],0],[29,[2,[15,"aA"],"charAt",[7,0]],"."]],[46,[53,[32,[15,"aB"],[3,"aB",[37,[15,"aB"],1]]],[3,"aA",[0,".",[15,"aA"]]]]]],[22,[1,[19,[15,"aB"],0],[12,[2,[15,"z"],"indexOf",[7,[15,"aA"],[15,"aB"]]],[15,"aB"]]],[46,[53,[36,false]]]],[36,true]],[50,"x",[46,"y"],[52,"z",[8]],[43,[15,"z"],[15,"j"],true],[43,[15,"z"],[15,"f"],true],[43,[15,"y"],"eventMetadata",[15,"z"]]],[52,"b",[13,[41,"$0"],[3,"$0",["require","internal.getFlags"]],["$0"]]],[52,"c",["require","internal.getProductSettingsParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmOutboundClickActivity"]],[52,"f","speculative"],[52,"g","ae_block_outbound_click"],[52,"h","click"],[52,"i","isRegistered"],[52,"j","em_event"],[52,"k",[17,[15,"a"],"instanceDestinationId"]],[22,["c",[15,"k"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"k"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"l",["require","internal.addDataLayerEventListener"]],[52,"m",["require","internal.enableAutoEventOnLinkClick"]],[52,"n",["require","internal.getDestinationIds"]],[52,"o",["require","internal.getRemoteConfigParameter"]],[52,"p",["require","getUrl"]],[52,"q",["require","parseUrl"]],[52,"r",["require","internal.sendGtagEvent"]],[52,"v",["o",[15,"k"],"cross_domain_conditions"]],[52,"w",["m",[8,"affiliateDomains",[15,"v"],"checkValidation",true,"waitForTags",false]]],[22,[28,[15,"w"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["l","gtm.linkClick",[51,"",[7,"y","z"],[52,"aA",["q",[16,[15,"y"],"gtm.elementUrl"]]],[22,[28,["u",[15,"aA"]]],[46,[53,["z"],[36]]]],[52,"aB",[8,"link_id",[16,[15,"y"],"gtm.elementId"],"link_classes",[16,[15,"y"],"gtm.elementClasses"],"link_url",["s",[15,"aA"]],"link_domain",["t",[15,"aA"]],"outbound",true]],[43,[15,"aB"],"event_callback",[15,"z"]],[52,"aC",[8,"eventId",[16,[15,"y"],"gtm.uniqueEventId"],"deferrable",true]],["x",[15,"aC"]],["r",["n"],[15,"h"],[15,"aB"],[15,"aC"]]],[15,"w"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_page_view",[46,"a"],[50,"q",[46,"r"],[52,"s",[8]],[43,[15,"s"],[17,[15,"f"],"G"],true],[43,[15,"s"],[17,[15,"f"],"AG"],true],[43,[15,"r"],"eventMetadata",[15,"s"]]],[22,[28,[17,[15,"a"],"historyEvents"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.setRemoteConfigParameter"]],[52,"d",["require","templateStorage"]],[52,"e",[15,"__module_ccdEmPageViewActivity"]],[52,"f",[15,"__module_metadataSchema"]],[52,"g","ae_block_history"],[52,"h","page_view"],[52,"i","isRegistered"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"e"],"A",[7,[15,"j"]]],[22,[2,[15,"d"],"getItem",[7,[15,"i"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnHistoryChange"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",[8,"interval",1000,"useV2EventName",true]],[52,"p",["l",[15,"o"]]],[22,[28,[15,"p"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"d"],"setItem",[7,[15,"i"],true]],["k","gtm.historyChange-v2",[51,"",[7,"r","s"],["s"],[52,"t",[16,[15,"r"],"gtm.oldUrl"]],[22,[20,[16,[15,"r"],"gtm.newUrl"],[15,"t"]],[46,[36]]],[52,"u",[16,[15,"r"],"gtm.historyChangeSource"]],[22,[1,[1,[21,[15,"u"],"pushState"],[21,[15,"u"],"popstate"]],[21,[15,"u"],"replaceState"]],[46,[53,[36]]]],[52,"v",[8]],[22,[17,[15,"a"],"includeParams"],[46,[53,[43,[15,"v"],"page_location",[16,[15,"r"],"gtm.newUrl"]],[43,[15,"v"],"page_referrer",[15,"t"]]]]],[52,"w",[8,"eventId",[16,[15,"r"],"gtm.uniqueEventId"],"deferrable",true]],["q",[15,"w"]],["n",["m"],[15,"h"],[15,"v"],[15,"w"]]],[15,"p"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_scroll",[46,"a"],[50,"p",[46,"q"],[52,"r",[8]],[43,[15,"r"],[15,"i"],true],[43,[15,"r"],[15,"e"],true],[43,[15,"q"],"eventMetadata",[15,"r"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmScrollActivity"]],[52,"e","speculative"],[52,"f","ae_block_scroll"],[52,"g","scroll"],[52,"h","isRegistered"],[52,"i","em_event"],[52,"j",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"j"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"j"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"h"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"k",["require","internal.addDataLayerEventListener"]],[52,"l",["require","internal.enableAutoEventOnScroll"]],[52,"m",["require","internal.getDestinationIds"]],[52,"n",["require","internal.sendGtagEvent"]],[52,"o",["l",[8,"verticalThresholdUnits","PERCENT","verticalThresholds",90]]],[22,[28,[15,"o"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"h"],true]],["k","gtm.scrollDepth",[51,"",[7,"q","r"],["r"],[52,"s",[8,"eventId",[16,[15,"q"],"gtm.uniqueEventId"],"deferrable",true]],[52,"t",[8,"percent_scrolled",[16,[15,"q"],"gtm.scrollThreshold"]]],["p",[15,"s"]],["n",["m"],[15,"g"],[15,"t"],[15,"s"]]],[15,"o"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_site_search",[46,"a"],[52,"b",["require","getQueryParameters"]],[52,"c",["require","internal.sendGtagEvent"]],[52,"d",["require","getContainerVersion"]],[52,"e",[15,"__module_ccdEmSiteSearchActivity"]],[52,"f",[2,[15,"e"],"A",[7,[17,[15,"a"],"searchQueryParams"],[15,"b"]]]],[52,"g",[30,[17,[15,"a"],"instanceDestinationId"],[17,["d"],"containerId"]]],[52,"h",[8,"deferrable",true,"eventId",[17,[15,"a"],"gtmEventId"],"eventMetadata",[8,"em_event",true]]],[22,[15,"f"],[46,[53,[52,"i",[39,[28,[28,[17,[15,"a"],"includeParams"]]],[2,[15,"e"],"B",[7,[15,"f"],[17,[15,"a"],"additionalQueryParams"],[15,"b"]]],[8]]],["c",[15,"g"],"view_search_results",[15,"i"],[15,"h"]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_em_video",[46,"a"],[50,"r",[46,"s"],[52,"t",[8]],[43,[15,"t"],[15,"k"],true],[43,[15,"t"],[15,"e"],true],[43,[15,"s"],"eventMetadata",[15,"t"]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","templateStorage"]],[52,"d",[15,"__module_ccdEmVideoActivity"]],[52,"e","speculative"],[52,"f","ae_block_video"],[52,"g","video_start"],[52,"h","video_progress"],[52,"i","video_complete"],[52,"j","isRegistered"],[52,"k","em_event"],[52,"l",[17,[15,"a"],"instanceDestinationId"]],[22,["b",[15,"l"],[15,"f"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[2,[15,"d"],"A",[7,[15,"l"],[17,[15,"a"],"includeParams"]]],[22,[2,[15,"c"],"getItem",[7,[15,"j"]]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"m",["require","internal.addDataLayerEventListener"]],[52,"n",["require","internal.enableAutoEventOnYouTubeActivity"]],[52,"o",["require","internal.getDestinationIds"]],[52,"p",["require","internal.sendGtagEvent"]],[52,"q",["n",[8,"captureComplete",true,"captureStart",true,"progressThresholdsPercent",[7,10,25,50,75]]]],[22,[28,[15,"q"]],[46,[53,[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[2,[15,"c"],"setItem",[7,[15,"j"],true]],["m","gtm.video",[51,"",[7,"s","t"],["t"],[52,"u",[16,[15,"s"],"gtm.videoStatus"]],[41,"v"],[22,[20,[15,"u"],"start"],[46,[53,[3,"v",[15,"g"]]]],[46,[22,[20,[15,"u"],"progress"],[46,[53,[3,"v",[15,"h"]]]],[46,[22,[20,[15,"u"],"complete"],[46,[53,[3,"v",[15,"i"]]]],[46,[53,[36]]]]]]]],[52,"w",[8,"video_current_time",[16,[15,"s"],"gtm.videoCurrentTime"],"video_duration",[16,[15,"s"],"gtm.videoDuration"],"video_percent",[16,[15,"s"],"gtm.videoPercent"],"video_provider",[16,[15,"s"],"gtm.videoProvider"],"video_title",[16,[15,"s"],"gtm.videoTitle"],"video_url",[16,[15,"s"],"gtm.videoUrl"],"visible",[16,[15,"s"],"gtm.videoVisible"]]],[52,"x",[8,"eventId",[16,[15,"s"],"gtm.uniqueEventId"],"deferrable",true]],["r",[15,"x"]],["p",["o"],[15,"v"],[15,"w"],[15,"x"]]],[15,"q"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_first",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_last",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ccd_ga_regscope",[46,"a"],[52,"b",[15,"__module_ccdGaRegionScopedSettings"]],[52,"c",[2,[15,"b"],"B",[7,[15,"a"]]]],[2,[15,"b"],"A",[7,[15,"a"],[15,"c"]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__ogt_1p_data_v2",[46,"a"],[50,"q",[46,"v","w"],[52,"x",[7]],[52,"y",[2,[15,"b"],"keys",[7,[15,"v"]]]],[65,"z",[15,"y"],[46,[53,[52,"aA",[30,[16,[15,"v"],[15,"z"]],[7]]],[52,"aB",[39,[18,[17,[15,"aA"],"length"],0],"1","0"]],[52,"aC",[39,["r",[15,"w"],[15,"z"]],"1","0"]],[2,[15,"x"],"push",[7,[0,[0,[0,[16,[15,"p"],[15,"z"]],"-"],[15,"aB"]],[15,"aC"]]]]]]],[36,[2,[15,"x"],"join",[7,"~"]]]],[50,"r",[46,"v","w"],[22,[28,[15,"v"]],[46,[53,[36,false]]]],[38,[15,"w"],[46,"email","phone_number","first_name","last_name","street","city","region","postal_code","country"],[46,[5,[46,[36,[28,[28,[16,[15,"v"],"email"]]]]]],[5,[46,[36,[28,[28,[16,[15,"v"],"phone_number"]]]]]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46]],[5,[46,[36,["s",[15,"v"],[15,"w"]]]]],[9,[46,[36,false]]]]]],[50,"s",[46,"v","w"],[36,[1,[28,[28,[16,[15,"v"],"address"]]],[28,[28,[16,[16,[15,"v"],"address"],[15,"w"]]]]]]],[50,"t",[46,"v","w","x","y"],[22,[20,[16,[15,"w"],"type"],[15,"x"]],[46,[53,[22,[28,[15,"v"]],[46,[53,[3,"v",[8]]]]],[22,[28,[16,[15,"v"],[15,"x"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],"userData"]],[52,"z",[8,"mode","a"]],[22,[16,[15,"w"],"tagName"],[46,[53,[43,[15,"z"],"location",[16,[15,"w"],"tagName"]]]]],[22,[16,[15,"w"],"querySelector"],[46,[53,[43,[15,"z"],"selector",[16,[15,"w"],"querySelector"]]]]],[43,[15,"y"],[15,"x"],[15,"z"]]]]]]]],[36,[15,"v"]]],[50,"u",[46,"v","w","x"],[22,[28,[16,[15,"a"],[15,"x"]]],[46,[36]]],[43,[15,"v"],[15,"w"],[8,"value",[16,[15,"a"],[15,"x"]]]]],[22,[28,[17,[15,"a"],"isEnabled"]],[46,[53,[2,[15,"a"],"gtmOnSuccess",[7]],[36]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.isFeatureEnabled"]],[52,"d",[15,"__module_featureFlags"]],[52,"e",["require","internal.getDestinationIds"]],[52,"f",["require","internal.getProductSettingsParameter"]],[52,"g",["require","internal.detectUserProvidedData"]],[52,"h",["require","queryPermission"]],[52,"i",["require","internal.setRemoteConfigParameter"]],[52,"j",["require","internal.registerCcdCallback"]],[52,"k",[15,"__module_metadataSchema"]],[52,"l","_z"],[52,"m",["c",[17,[15,"d"],"EM"]]],[52,"n",[30,["e"],[7]]],[52,"o",[8,"enable_code",true]],[52,"p",[8,"email","1","phone_number","2","first_name","3","last_name","4","country","5","postal_code","6","street","7","city","8","region","9"]],[22,[17,[15,"a"],"isAutoEnabled"],[46,[53,[52,"v",[7]],[22,[1,[17,[15,"a"],"autoCollectExclusionSelectors"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[46,[53,[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[17,[15,"a"],"autoCollectExclusionSelectors"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[17,[16,[17,[15,"a"],"autoCollectExclusionSelectors"],[15,"y"]],"exclusionSelector"]],[22,[15,"z"],[46,[53,[2,[15,"v"],"push",[7,[15,"z"]]]]]]]]]]]]],[52,"w",[30,["c",[17,[15,"d"],"AC"]],[17,[15,"a"],"isAutoCollectPiiEnabledFlag"]]],[52,"x",[39,[17,[15,"a"],"isAutoCollectPiiEnabledFlag"],[17,[15,"a"],"autoEmailEnabled"],true]],[43,[15,"o"],"auto_detect",[8,"email",[15,"x"],"phone",[1,[15,"w"],[17,[15,"a"],"autoPhoneEnabled"]],"address",[1,[15,"w"],[17,[15,"a"],"autoAddressEnabled"]],"exclude_element_selectors",[15,"v"]]]]]],[22,[17,[15,"a"],"isManualEnabled"],[46,[53,[52,"v",[8]],[22,[17,[15,"a"],"manualEmailEnabled"],[46,[53,["u",[15,"v"],"email","emailValue"]]]],[22,[17,[15,"a"],"manualPhoneEnabled"],[46,[53,["u",[15,"v"],"phone","phoneValue"]]]],[22,[17,[15,"a"],"manualAddressEnabled"],[46,[53,[52,"w",[8]],["u",[15,"w"],"first_name","firstNameValue"],["u",[15,"w"],"last_name","lastNameValue"],["u",[15,"w"],"street","streetValue"],["u",[15,"w"],"city","cityValue"],["u",[15,"w"],"region","regionValue"],["u",[15,"w"],"country","countryValue"],["u",[15,"w"],"postal_code","postalCodeValue"],[43,[15,"v"],"name_and_address",[7,[15,"w"]]]]]],[43,[15,"o"],"selectors",[15,"v"]]]]],[65,"v",[15,"n"],[46,[53,["i",[15,"v"],"user_data_settings",[15,"o"]],[52,"w",[16,[15,"o"],"auto_detect"]],[22,[28,[15,"w"]],[46,[53,[6]]]],[52,"x",[51,"",[7,"y"],[52,"z",[2,[15,"y"],"getMetadata",[7,[17,[15,"k"],"AL"]]]],[22,[15,"z"],[46,[53,[36,[15,"z"]]]]],[52,"aA",[1,["c",[17,[15,"d"],"CW"]],[20,[2,[15,"v"],"indexOf",[7,"G-"]],0]]],[41,"aB"],[22,["h","detect_user_provided_data","auto"],[46,[53,[3,"aB",["g",[8,"excludeElementSelectors",[16,[15,"w"],"exclude_element_selectors"],"fieldFilters",[8,"email",[16,[15,"w"],"email"],"phone",[16,[15,"w"],"phone"],"address",[16,[15,"w"],"address"]],"performDataLayerSearch",[15,"aA"]]]]]]],[52,"aC",[1,[15,"aB"],[16,[15,"aB"],"elements"]]],[52,"aD",[8]],[52,"aE",[8]],[22,[1,[15,"aC"],[18,[17,[15,"aC"],"length"],0]],[46,[53,[41,"aF"],[41,"aG"],[3,"aG",[8]],[53,[41,"aH"],[3,"aH",0],[63,[7,"aH"],[23,[15,"aH"],[17,[15,"aC"],"length"]],[33,[15,"aH"],[3,"aH",[0,[15,"aH"],1]]],[46,[53,[52,"aI",[16,[15,"aC"],[15,"aH"]]],["t",[15,"aD"],[15,"aI"],"email",[15,"aE"]],[22,["c",[17,[15,"d"],"AD"]],[46,[53,["t",[15,"aD"],[15,"aI"],"phone_number",[15,"aE"]],[3,"aF",["t",[15,"aF"],[15,"aI"],"first_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"last_name",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"country",[15,"aG"]]],[3,"aF",["t",[15,"aF"],[15,"aI"],"postal_code",[15,"aG"]]]]]]]]]],[22,[1,[15,"aF"],[28,[16,[15,"aD"],"address"]]],[46,[53,[43,[15,"aD"],"address",[15,"aF"]],[22,[15,"m"],[46,[53,[43,[16,[15,"aD"],"address"],"_tag_metadata",[15,"aG"]]]]]]]]]]],[22,[15,"aA"],[46,[53,[52,"aF",[1,[15,"aB"],[16,[15,"aB"],"dataLayerSearchResults"]]],[22,[15,"aF"],[46,[53,[52,"aG",["q",[15,"aF"],[15,"aD"]]],[22,[15,"aG"],[46,[53,[2,[15,"y"],"setHitData",[7,[15,"l"],[15,"aG"]]]]]]]]]]]],[22,[15,"m"],[46,[53,[22,[30,[16,[15,"aD"],"email"],[16,[15,"aD"],"phone_number"]],[46,[53,[43,[15,"aD"],"_tag_metadata",[15,"aE"]]]]]]]],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AL"],[15,"aD"]]],[36,[15,"aD"]]]],["j",[15,"v"],[51,"",[7,"y"],[2,[15,"y"],"setMetadata",[7,[17,[15,"k"],"AM"],[15,"x"]]]]]]]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__set_product_settings",[46,"a"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",0],[52,"c",1],[52,"d",2],[52,"e",3],[52,"f",4],[52,"g",5],[52,"h",6],[52,"i",7],[52,"j",8],[52,"k",9],[52,"l",10],[52,"m",12],[52,"n",13],[52,"o",16],[52,"p",17],[52,"q",19],[52,"r",20],[52,"s",21],[52,"t",22],[52,"u",23],[52,"v",24],[52,"w",25],[52,"x",26],[52,"y",27],[52,"z",28],[52,"aA",29],[52,"aB",30],[52,"aC",31],[52,"aD",32],[52,"aE",33],[52,"aF",34],[52,"aG",35],[52,"aH",36],[52,"aI",37],[52,"aJ",38],[52,"aK",39],[52,"aL",40],[52,"aM",41],[52,"aN",47],[52,"aO",42],[52,"aP",43],[52,"aQ",44],[52,"aR",45],[52,"aS",46],[52,"aT",48],[52,"aU",49],[52,"aV",52],[52,"aW",53],[52,"aX",54],[52,"aY",56],[52,"aZ",59],[52,"bA",60],[52,"bB",62],[52,"bC",63],[52,"bD",66],[52,"bE",68],[52,"bF",69],[52,"bG",71],[52,"bH",72],[52,"bI",75],[52,"bJ",78],[52,"bK",83],[52,"bL",84],[52,"bM",87],[52,"bN",88],[52,"bO",89],[52,"bP",90],[52,"bQ",91],[52,"bR",92],[52,"bS",93],[52,"bT",94],[52,"bU",95],[52,"bV",96],[52,"bW",97],[52,"bX",100],[52,"bY",101],[52,"bZ",102],[52,"cA",103],[52,"cB",104],[52,"cC",106],[52,"cD",107],[52,"cE",108],[52,"cF",109],[52,"cG",111],[52,"cH",112],[52,"cI",113],[52,"cJ",114],[52,"cK",115],[52,"cL",116],[52,"cM",117],[52,"cN",118],[52,"cO",119],[52,"cP",120],[52,"cQ",121],[52,"cR",122],[52,"cS",123],[52,"cT",125],[52,"cU",126],[52,"cV",127],[52,"cW",128],[52,"cX",129],[52,"cY",130],[52,"cZ",131],[52,"dA",132],[52,"dB",133],[52,"dC",134],[52,"dD",135],[52,"dE",136],[52,"dF",137],[52,"dG",138],[52,"dH",139],[52,"dI",140],[52,"dJ",141],[52,"dK",142],[52,"dL",143],[52,"dM",144],[52,"dN",145],[52,"dO",146],[52,"dP",147],[52,"dQ",148],[52,"dR",149],[52,"dS",152],[52,"dT",153],[52,"dU",154],[52,"dV",155],[52,"dW",156],[52,"dX",157],[52,"dY",158],[52,"dZ",159],[52,"eA",160],[52,"eB",162],[52,"eC",164],[52,"eD",165],[52,"eE",167],[52,"eF",168],[52,"eG",169],[52,"eH",170],[52,"eI",171],[52,"eJ",174],[52,"eK",175],[52,"eL",176],[52,"eM",177],[52,"eN",178],[52,"eO",180],[52,"eP",182],[52,"eQ",183],[52,"eR",185],[52,"eS",186],[52,"eT",187],[52,"eU",188],[52,"eV",189],[52,"eW",190],[52,"eX",191],[52,"eY",192],[52,"eZ",193],[52,"fA",194],[52,"fB",195],[52,"fC",196],[52,"fD",197],[52,"fE",198],[52,"fF",199],[52,"fG",200],[52,"fH",201],[52,"fI",202],[52,"fJ",203],[52,"fK",204],[36,[8,"E",[15,"f"],"F",[15,"g"],"ES",[15,"eT"],"EU",[15,"eV"],"FH",[15,"fI"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"EE",[15,"eF"],"P",[15,"q"],"FD",[15,"fE"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"EA",[15,"eB"],"AK",[15,"aL"],"AL",[15,"aM"],"EZ",[15,"fA"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AM",[15,"aN"],"AS",[15,"aT"],"FJ",[15,"fK"],"EX",[15,"eY"],"AT",[15,"aU"],"ET",[15,"eU"],"AV",[15,"aW"],"AW",[15,"aX"],"AU",[15,"aV"],"AX",[15,"aY"],"ED",[15,"eE"],"AY",[15,"aZ"],"EG",[15,"eH"],"EI",[15,"eJ"],"EV",[15,"eW"],"AZ",[15,"bA"],"EB",[15,"eC"],"EM",[15,"eN"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"BD",[15,"bE"],"BE",[15,"bF"],"EW",[15,"eX"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"EN",[15,"eO"],"BI",[15,"bJ"],"EK",[15,"eL"],"BJ",[15,"bK"],"EJ",[15,"eK"],"BK",[15,"bL"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"BT",[15,"bU"],"BU",[15,"bV"],"EC",[15,"eD"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BL",[15,"bM"],"FE",[15,"fF"],"BY",[15,"bZ"],"BZ",[15,"cA"],"FC",[15,"fD"],"CA",[15,"cB"],"EO",[15,"eP"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"CG",[15,"cH"],"FI",[15,"fJ"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CL",[15,"cM"],"CK",[15,"cL"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"FF",[15,"fG"],"EL",[15,"eM"],"EY",[15,"eZ"],"CR",[15,"cS"],"EQ",[15,"eR"],"EH",[15,"eI"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"ER",[15,"eS"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"EF",[15,"eG"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"],"DG",[15,"dH"],"DH",[15,"dI"],"DI",[15,"dJ"],"DJ",[15,"dK"],"DK",[15,"dL"],"FG",[15,"fH"],"DL",[15,"dM"],"DM",[15,"dN"],"FA",[15,"fB"],"FB",[15,"fC"],"DN",[15,"dO"],"B",[15,"c"],"D",[15,"e"],"C",[15,"d"],"DO",[15,"dP"],"DP",[15,"dQ"],"DQ",[15,"dR"],"DR",[15,"dS"],"DS",[15,"dT"],"A",[15,"b"],"DT",[15,"dU"],"DU",[15,"dV"],"DV",[15,"dW"],"DW",[15,"dX"],"DX",[15,"dY"],"EP",[15,"eQ"],"DY",[15,"dZ"],"DZ",[15,"eA"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_activities",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"c","d"],[36,[39,[15,"d"],["d",[15,"c"]],[15,"c"]]]],[36,[8,"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","gclgb"],[52,"t","gclid"],[52,"u","gclgs"],[52,"v","gcllp"],[52,"w","gclst"],[52,"x","ads_data_redaction"],[52,"y","allow_ad_personalization_signals"],[52,"z","allow_direct_google_requests"],[52,"aA","allow_google_signals"],[52,"aB","auid"],[52,"aC","discount"],[52,"aD","aw_feed_country"],[52,"aE","aw_feed_language"],[52,"aF","items"],[52,"aG","aw_merchant_id"],[52,"aH","aw_basket_type"],[52,"aI","client_id"],[52,"aJ","conversion_id"],[52,"aK","conversion_linker"],[52,"aL","conversion_api"],[52,"aM","cookie_deprecation"],[52,"aN","cookie_expires"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","shipping"],[52,"aX","engagement_time_msec"],[52,"aY","estimated_delivery_date"],[52,"aZ","event_developer_id_string"],[52,"bA","event"],[52,"bB","event_timeout"],[52,"bC","first_party_collection"],[52,"bD","gdpr_applies"],[52,"bE","google_analysis_params"],[52,"bF","_google_ng"],[52,"bG","gpp_sid"],[52,"bH","gpp_string"],[52,"bI","gsa_experiment_id"],[52,"bJ","gtag_event_feature_usage"],[52,"bK","iframe_state"],[52,"bL","ignore_referrer"],[52,"bM","is_passthrough"],[52,"bN","_lps"],[52,"bO","language"],[52,"bP","merchant_feed_label"],[52,"bQ","merchant_feed_language"],[52,"bR","merchant_id"],[52,"bS","new_customer"],[52,"bT","page_hostname"],[52,"bU","page_path"],[52,"bV","page_referrer"],[52,"bW","page_title"],[52,"bX","_platinum_request_status"],[52,"bY","restricted_data_processing"],[52,"bZ","screen_resolution"],[52,"cA","search_term"],[52,"cB","send_page_view"],[52,"cC","server_container_url"],[52,"cD","session_duration"],[52,"cE","session_engaged_time"],[52,"cF","session_id"],[52,"cG","_shared_user_id"],[52,"cH","topmost_url"],[52,"cI","transaction_id"],[52,"cJ","transport_url"],[52,"cK","update"],[52,"cL","_user_agent_architecture"],[52,"cM","_user_agent_bitness"],[52,"cN","_user_agent_full_version_list"],[52,"cO","_user_agent_mobile"],[52,"cP","_user_agent_model"],[52,"cQ","_user_agent_platform"],[52,"cR","_user_agent_platform_version"],[52,"cS","_user_agent_wow64"],[52,"cT","user_data_auto_latency"],[52,"cU","user_data_auto_meta"],[52,"cV","user_data_auto_multi"],[52,"cW","user_data_auto_selectors"],[52,"cX","user_data_auto_status"],[52,"cY","user_data_mode"],[52,"cZ","user_id"],[52,"dA","user_properties"],[52,"dB","us_privacy_string"],[52,"dC","value"],[52,"dD","_fpm_parameters"],[52,"dE","_host_name"],[52,"dF","_in_page_command"],[52,"dG","non_personalized_ads"],[52,"dH","conversion_label"],[52,"dI","page_location"],[52,"dJ","global_developer_id_string"],[52,"dK","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"DG",[15,"dH"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"],"AP",[15,"aQ"],"AQ",[15,"aR"],"AR",[15,"aS"],"AS",[15,"aT"],"AT",[15,"aU"],"AU",[15,"aV"],"AV",[15,"aW"],"AW",[15,"aX"],"AX",[15,"aY"],"AY",[15,"aZ"],"AZ",[15,"bA"],"BA",[15,"bB"],"BB",[15,"bC"],"BC",[15,"bD"],"DI",[15,"dJ"],"BD",[15,"bE"],"BE",[15,"bF"],"BF",[15,"bG"],"BG",[15,"bH"],"BH",[15,"bI"],"BI",[15,"bJ"],"BJ",[15,"bK"],"BK",[15,"bL"],"BL",[15,"bM"],"BM",[15,"bN"],"BN",[15,"bO"],"BO",[15,"bP"],"BP",[15,"bQ"],"BQ",[15,"bR"],"BR",[15,"bS"],"BS",[15,"bT"],"DH",[15,"dI"],"BT",[15,"bU"],"BU",[15,"bV"],"BV",[15,"bW"],"BW",[15,"bX"],"BX",[15,"bY"],"BY",[15,"bZ"],"BZ",[15,"cA"],"CA",[15,"cB"],"CB",[15,"cC"],"CC",[15,"cD"],"CD",[15,"cE"],"CE",[15,"cF"],"CF",[15,"cG"],"DJ",[15,"dK"],"CG",[15,"cH"],"CH",[15,"cI"],"CI",[15,"cJ"],"CJ",[15,"cK"],"CK",[15,"cL"],"CL",[15,"cM"],"CM",[15,"cN"],"CN",[15,"cO"],"CO",[15,"cP"],"CP",[15,"cQ"],"CQ",[15,"cR"],"CR",[15,"cS"],"CS",[15,"cT"],"CT",[15,"cU"],"CU",[15,"cV"],"CV",[15,"cW"],"CW",[15,"cX"],"CX",[15,"cY"],"CY",[15,"cZ"],"CZ",[15,"dA"],"DA",[15,"dB"],"DB",[15,"dC"],"DC",[15,"dD"],"DD",[15,"dE"],"DE",[15,"dF"],"DF",[15,"dG"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","hit_type"],[52,"m","hit_type_override"],[52,"n","is_conversion"],[52,"o","is_external_event"],[52,"p","is_first_visit"],[52,"q","is_first_visit_conversion"],[52,"r","is_fpm_encryption"],[52,"s","is_fpm_split"],[52,"t","is_gcp_conversion"],[52,"u","is_google_signals_allowed"],[52,"v","is_server_side_destination"],[52,"w","is_session_start"],[52,"x","is_session_start_conversion"],[52,"y","is_sgtm_ga_ads_conversion_study_control_group"],[52,"z","is_sgtm_prehit"],[52,"aA","is_split_conversion"],[52,"aB","is_syn"],[52,"aC","prehit_for_retry"],[52,"aD","redact_ads_data"],[52,"aE","redact_click_ids"],[52,"aF","send_ccm_parallel_ping"],[52,"aG","send_user_data_hit"],[52,"aH","speculative"],[52,"aI","syn_or_mod"],[52,"aJ","transient_ecsid"],[52,"aK","transmission_type"],[52,"aL","user_data"],[52,"aM","user_data_from_automatic"],[52,"aN","user_data_from_automatic_getter"],[52,"aO","user_data_from_code"],[52,"aP","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"D",[15,"e"],"E",[15,"f"],"F",[15,"g"],"G",[15,"h"],"H",[15,"i"],"I",[15,"j"],"J",[15,"k"],"K",[15,"l"],"L",[15,"m"],"M",[15,"n"],"N",[15,"o"],"O",[15,"p"],"P",[15,"q"],"Q",[15,"r"],"R",[15,"s"],"S",[15,"t"],"T",[15,"u"],"U",[15,"v"],"V",[15,"w"],"W",[15,"x"],"X",[15,"y"],"Y",[15,"z"],"Z",[15,"aA"],"AA",[15,"aB"],"AB",[15,"aC"],"AC",[15,"aD"],"AD",[15,"aE"],"AE",[15,"aF"],"AF",[15,"aG"],"AG",[15,"aH"],"AH",[15,"aI"],"AI",[15,"aJ"],"AJ",[15,"aK"],"AK",[15,"aL"],"AL",[15,"aM"],"AM",[15,"aN"],"AN",[15,"aO"],"AO",[15,"aP"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmSiteSearchActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"b",[46,"d","e"],[52,"f",[2,[30,[15,"d"],""],"split",[7,","]]],[53,[41,"g"],[3,"g",0],[63,[7,"g"],[23,[15,"g"],[17,[15,"f"],"length"]],[33,[15,"g"],[3,"g",[0,[15,"g"],1]]],[46,[53,[52,"h",["e",[2,[16,[15,"f"],[15,"g"]],"trim",[7]]]],[22,[21,[15,"h"],[44]],[46,[53,[36,[15,"h"]]]]]]]]]],[50,"c",[46,"d","e","f"],[52,"g",[8,"search_term",[15,"d"]]],[52,"h",[2,[30,[15,"e"],""],"split",[7,","]]],[53,[41,"i"],[3,"i",0],[63,[7,"i"],[23,[15,"i"],[17,[15,"h"],"length"]],[33,[15,"i"],[3,"i",[0,[15,"i"],1]]],[46,[53,[52,"j",[2,[16,[15,"h"],[15,"i"]],"trim",[7]]],[52,"k",["f",[15,"j"]]],[22,[21,[15,"k"],[44]],[46,[53,[43,[15,"g"],[0,"q_",[15,"j"]],[15,"k"]]]]]]]]],[36,[15,"g"]]],[36,[8,"B",[15,"c"],"A",[15,"b"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmDownloadActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_text",[44]]],[2,[15,"j"],"setHitData",[7,"file_name",[44]]],[2,[15,"j"],"setHitData",[7,"file_extension",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_downloads"],[52,"f","file_download"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmFormActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k","l"],[22,[20,[15,"k"],[44]],[46,[53,[3,"k",[20,[2,[15,"j"],"indexOf",[7,"AW-"]],0]]]]],["c",[15,"j"],[51,"",[7,"m"],[52,"n",[2,[15,"m"],"getEventName",[7]]],[52,"o",[30,[20,[15,"n"],[15,"g"]],[20,[15,"n"],[15,"f"]]]],[22,[30,[28,[15,"o"]],[28,[2,[15,"m"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[22,[1,[28,[15,"k"]],[2,[15,"m"],"getMetadata",[7,[15,"h"]]]],[46,[53,[2,[15,"m"],"abort",[7]],[36]]]],[2,[15,"m"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"l"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_id",[44]]],[2,[15,"m"],"setHitData",[7,"form_name",[44]]],[2,[15,"m"],"setHitData",[7,"form_destination",[44]]],[2,[15,"m"],"setHitData",[7,"form_length",[44]]],[22,[20,[15,"n"],[15,"f"]],[46,[53,[2,[15,"m"],"setHitData",[7,"form_submit_text",[44]]]]],[46,[22,[20,[15,"n"],[15,"g"]],[46,[53,[2,[15,"m"],"setHitData",[7,"first_field_id",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_name",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_type",[44]]],[2,[15,"m"],"setHitData",[7,"first_field_position",[44]]]]]]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_form"],[52,"f","form_submit"],[52,"g","form_start"],[52,"h","form_event_canceled"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmOutboundClickActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"link_id",[44]]],[2,[15,"j"],"setHitData",[7,"link_classes",[44]]],[2,[15,"j"],"setHitData",[7,"link_url",[44]]],[2,[15,"j"],"setHitData",[7,"link_domain",[44]]],[2,[15,"j"],"setHitData",[7,"outbound",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_outbound_click"],[52,"f","click"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmPageViewActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j"],["c",[15,"j"],[51,"",[7,"k"],[22,[30,[21,[2,[15,"k"],"getEventName",[7]],[15,"h"]],[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"g"]],[46,[53,[2,[15,"k"],"abort",[7]],[36]]]],[22,[28,[2,[15,"k"],"getMetadata",[7,[17,[15,"e"],"Y"]]]],[46,[53,["d",[15,"j"],[17,[15,"f"],"BU"],[2,[15,"k"],"getHitData",[7,[17,[15,"f"],"BU"]]]]]]],[2,[15,"k"],"setMetadata",[7,[17,[15,"e"],"AG"],false]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",["require","internal.setRemoteConfigParameter"]],[52,"e",[15,"__module_metadataSchema"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g","ae_block_history"],[52,"h",[17,[15,"f"],"O"]],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmScrollActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"g",[46,"h","i"],["c",[15,"h"],[51,"",[7,"j"],[22,[30,[21,[2,[15,"j"],"getEventName",[7]],[15,"f"]],[28,[2,[15,"j"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"h"],[15,"e"]],[46,[53,[2,[15,"j"],"abort",[7]],[36]]]],[2,[15,"j"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"i"]],[46,[53,[2,[15,"j"],"setHitData",[7,"percent_scrolled",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_scroll"],[52,"f","scroll"],[36,[8,"A",[15,"g"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdEmVideoActivity",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"i",[46,"j","k"],["c",[15,"j"],[51,"",[7,"l"],[52,"m",[2,[15,"l"],"getEventName",[7]]],[52,"n",[30,[30,[20,[15,"m"],[15,"f"]],[20,[15,"m"],[15,"g"]]],[20,[15,"m"],[15,"h"]]]],[22,[30,[28,[15,"n"]],[28,[2,[15,"l"],"getMetadata",[7,[17,[15,"d"],"G"]]]]],[46,[53,[36]]]],[22,["b",[15,"j"],[15,"e"]],[46,[53,[2,[15,"l"],"abort",[7]],[36]]]],[2,[15,"l"],"setMetadata",[7,[17,[15,"d"],"AG"],false]],[22,[28,[15,"k"]],[46,[53,[2,[15,"l"],"setHitData",[7,"video_current_time",[44]]],[2,[15,"l"],"setHitData",[7,"video_duration",[44]]],[2,[15,"l"],"setHitData",[7,"video_percent",[44]]],[2,[15,"l"],"setHitData",[7,"video_provider",[44]]],[2,[15,"l"],"setHitData",[7,"video_title",[44]]],[2,[15,"l"],"setHitData",[7,"video_url",[44]]],[2,[15,"l"],"setHitData",[7,"visible",[44]]]]]]]]],[52,"b",["require","internal.getProductSettingsParameter"]],[52,"c",["require","internal.registerCcdCallback"]],[52,"d",[15,"__module_metadataSchema"]],[52,"e","ae_block_video"],[52,"f","video_start"],[52,"g","video_progress"],[52,"h","video_complete"],[36,[8,"A",[15,"i"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_ccdGaRegionScopedSettings",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"m",[46,"p","q","r"],[50,"w",[46,"y"],[52,"z",[16,[15,"l"],[15,"y"]]],[22,[28,[15,"z"]],[46,[36]]],[53,[41,"aA"],[3,"aA",0],[63,[7,"aA"],[23,[15,"aA"],[17,[15,"z"],"length"]],[33,[15,"aA"],[3,"aA",[0,[15,"aA"],1]]],[46,[53,[52,"aB",[16,[15,"z"],[15,"aA"]]],["t",[15,"s"],[17,[15,"aB"],"name"],[17,[15,"aB"],"value"]]]]]]],[50,"x",[46,"y"],[22,[30,[28,[15,"u"]],[21,[17,[15,"u"],"length"],2]],[46,[53,[36,false]]]],[41,"z"],[3,"z",[16,[15,"y"],[15,"v"]]],[22,[20,[15,"z"],[44]],[46,[53,[3,"z",[16,[15,"y"],[15,"u"]]]]]],[36,[28,[28,[15,"z"]]]]],[22,[28,[15,"q"]],[46,[36]]],[52,"s",[30,[17,[15,"p"],"instanceDestinationId"],[17,["c"],"containerId"]]],[52,"t",["h",[15,"f"],[15,"r"]]],[52,"u",[13,[41,"$0"],[3,"$0",["h",[15,"d"],[15,"r"]]],["$0"]]],[52,"v",[13,[41,"$0"],[3,"$0",["h",[15,"e"],[15,"r"]]],["$0"]]],[53,[41,"y"],[3,"y",0],[63,[7,"y"],[23,[15,"y"],[17,[15,"q"],"length"]],[33,[15,"y"],[3,"y",[0,[15,"y"],1]]],[46,[53,[52,"z",[16,[15,"q"],[15,"y"]]],[22,[30,[17,[15,"z"],"disallowAllRegions"],["x",[17,[15,"z"],"disallowedRegions"]]],[46,[53,["w",[17,[15,"z"],"redactFieldGroup"]]]]]]]]]],[50,"n",[46,"p"],[52,"q",[8]],[22,[28,[15,"p"]],[46,[36,[15,"q"]]]],[52,"r",[2,[15,"p"],"split",[7,","]]],[53,[41,"s"],[3,"s",0],[63,[7,"s"],[23,[15,"s"],[17,[15,"r"],"length"]],[33,[15,"s"],[3,"s",[0,[15,"s"],1]]],[46,[53,[52,"t",[2,[16,[15,"r"],[15,"s"]],"trim",[7]]],[22,[28,[15,"t"]],[46,[6]]],[52,"u",[2,[15,"t"],"split",[7,"-"]]],[52,"v",[16,[15,"u"],0]],[52,"w",[39,[20,[17,[15,"u"],"length"],2],[15,"t"],[44]]],[22,[30,[28,[15,"v"]],[21,[17,[15,"v"],"length"],2]],[46,[53,[6]]]],[22,[1,[21,[15,"w"],[44]],[30,[23,[17,[15,"w"],"length"],4],[18,[17,[15,"w"],"length"],6]]],[46,[53,[6]]]],[43,[15,"q"],[15,"t"],true]]]]],[36,[15,"q"]]],[50,"o",[46,"p"],[22,[28,[17,[15,"p"],"settingsTable"]],[46,[36,[7]]]],[52,"q",[8]],[53,[41,"r"],[3,"r",0],[63,[7,"r"],[23,[15,"r"],[17,[17,[15,"p"],"settingsTable"],"length"]],[33,[15,"r"],[3,"r",[0,[15,"r"],1]]],[46,[53,[52,"s",[16,[17,[15,"p"],"settingsTable"],[15,"r"]]],[52,"t",[17,[15,"s"],"redactFieldGroup"]],[22,[28,[16,[15,"l"],[15,"t"]]],[46,[6]]],[43,[15,"q"],[15,"t"],[8,"redactFieldGroup",[15,"t"],"disallowAllRegions",false,"disallowedRegions",[8]]],[52,"u",[16,[15,"q"],[15,"t"]]],[22,[17,[15,"s"],"disallowAllRegions"],[46,[53,[43,[15,"u"],"disallowAllRegions",true],[6]]]],[43,[15,"u"],"disallowedRegions",["n",[17,[15,"s"],"disallowedRegions"]]]]]]],[36,[2,[15,"b"],"values",[7,[15,"q"]]]]],[52,"b",["require","Object"]],[52,"c",["require","getContainerVersion"]],[52,"d",["require","internal.getCountryCode"]],[52,"e",["require","internal.getRegionCode"]],[52,"f",["require","internal.setRemoteConfigParameter"]],[52,"g",[15,"__module_activities"]],[52,"h",[17,[15,"g"],"A"]],[41,"i"],[41,"j"],[41,"k"],[52,"l",[8,"GOOGLE_SIGNALS",[7,[8,"name","allow_google_signals","value",false]],"DEVICE_AND_GEO",[7,[8,"name","geo_granularity","value",true],[8,"name","redact_device_info","value",true]]]],[36,[8,"A",[15,"m"],"B",[15,"o"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__c":{"2":true,"4":true}
,
"__ccd_auto_redact":{"2":true,"4":true}
,
"__ccd_conversion_marking":{"2":true,"4":true}
,
"__ccd_em_download":{"2":true,"4":true}
,
"__ccd_em_form":{"2":true,"4":true}
,
"__ccd_em_outbound_click":{"2":true,"4":true}
,
"__ccd_em_page_view":{"2":true,"4":true}
,
"__ccd_em_scroll":{"2":true,"4":true}
,
"__ccd_em_site_search":{"2":true,"4":true}
,
"__ccd_em_video":{"2":true,"4":true}
,
"__ccd_ga_first":{"2":true,"4":true}
,
"__ccd_ga_last":{"2":true,"4":true}
,
"__ccd_ga_regscope":{"2":true,"4":true}
,
"__e":{"2":true,"4":true}
,
"__ogt_1p_data_v2":{"2":true}
,
"__set_product_settings":{"2":true,"4":true}


}
,"blob":{"1":"1"}
,"permissions":{
"__c":{}
,
"__ccd_auto_redact":{}
,
"__ccd_conversion_marking":{}
,
"__ccd_em_download":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_form":{"access_template_storage":{},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.formInteract","gtm.formSubmit"]},"detect_form_submit_events":{"allowWaitForTags":""},"detect_form_interaction_events":{}}
,
"__ccd_em_outbound_click":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.linkClick"]},"access_template_storage":{},"detect_link_click_events":{"allowWaitForTags":""}}
,
"__ccd_em_page_view":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.historyChange-v2"]},"access_template_storage":{},"detect_history_change_events":{}}
,
"__ccd_em_scroll":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.scrollDepth"]},"access_template_storage":{},"detect_scroll_events":{}}
,
"__ccd_em_site_search":{"get_url":{"urlParts":"any","queriesAllowed":"any"},"read_container_data":{}}
,
"__ccd_em_video":{"listen_data_layer":{"accessType":"specific","allowedEvents":["gtm.video"]},"access_template_storage":{},"detect_youtube_activity_events":{"allowFixMissingJavaScriptApi":false}}
,
"__ccd_ga_first":{}
,
"__ccd_ga_last":{}
,
"__ccd_ga_regscope":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__ogt_1p_data_v2":{"detect_user_provided_data":{"limitDataSources":true,"allowAutoDataSources":true,"allowManualDataSources":false,"allowCodeDataSources":false}}
,
"__set_product_settings":{}


}



,"security_groups":{
"google":[
"__c"
,
"__ccd_auto_redact"
,
"__ccd_conversion_marking"
,
"__ccd_em_download"
,
"__ccd_em_form"
,
"__ccd_em_outbound_click"
,
"__ccd_em_page_view"
,
"__ccd_em_scroll"
,
"__ccd_em_site_search"
,
"__ccd_em_video"
,
"__ccd_ga_first"
,
"__ccd_ga_last"
,
"__ccd_ga_regscope"
,
"__e"
,
"__ogt_1p_data_v2"
,
"__set_product_settings"

]


}



};




var k,ca=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},da=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ea=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=ea(this),ha=function(a,b){if(b)a:{for(var c=fa,d=a.split("."),e=0;e<d.length-1;e++){var f=d[e];if(!(f in c))break a;c=c[f]}var g=d[d.length-1],h=c[g],m=b(h);m!=h&&m!=null&&da(c,g,{configurable:!0,writable:!0,value:m})}};
ha("Symbol",function(a){if(a)return a;var b=function(f,g){this.D=f;da(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.D};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});var ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ma;
if(typeof Object.setPrototypeOf=="function")ma=Object.setPrototypeOf;else{var oa;a:{var pa={a:!0},qa={};try{qa.__proto__=pa;oa=qa.a;break a}catch(a){}oa=!1}ma=oa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ra=ma,sa=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(ra)ra(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.uq=b.prototype},l=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ca(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},ta=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ua=function(a){return a instanceof Array?a:ta(l(a))},wa=function(a){return va(a,a)},va=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},xa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};ha("Object.assign",function(a){return a||xa});
var ya=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var za=this||self,Aa=function(a,b){function c(){}c.prototype=b.prototype;a.uq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.rr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ba=function(a,b){this.type=a;this.data=b};var Ca=function(){this.map=new Map;this.D=new Set};k=Ca.prototype;k.get=function(a){return this.map.get(String(a))};k.set=function(a,b){this.D.has(a)||this.map.set(String(a),b)};k.yl=function(a,b){this.set(a,b);this.D.add(a)};k.has=function(a){return this.map.has(String(a))};k.remove=function(a){this.D.has(a)||this.map.delete(String(a))};var Da=function(a,b){switch(b){case 1:return a.map.keys();case 2:return a.map.values();case 3:return a.map.entries();default:return[]}};
Ca.prototype.xa=function(){return Da(this,1)};Ca.prototype.Yb=function(){return Da(this,2)};Ca.prototype.Gb=function(){return Da(this,3)};var Ea=function(){this.map={};this.D={}};k=Ea.prototype;k.get=function(a){return this.map["dust."+a]};k.set=function(a,b){var c="dust."+a;this.D.hasOwnProperty(c)||(this.map[c]=b)};k.yl=function(a,b){this.set(a,b);this.D["dust."+a]=!0};k.has=function(a){return this.map.hasOwnProperty("dust."+a)};k.remove=function(a){var b="dust."+a;this.D.hasOwnProperty(b)||delete this.map[b]};
var Ga=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ea.prototype.xa=function(){return Ga(this,1)};Ea.prototype.Yb=function(){return Ga(this,2)};Ea.prototype.Gb=function(){return Ga(this,3)};var Ha=function(){};Ha.prototype.reset=function(){};var Ia=[],Ja={};function Ka(a){return Ia[a]===void 0?!1:Ia[a]};var La=function(a,b){this.R=a;this.parent=b;this.D=this.J=void 0;this.Rc=!1;this.O=function(c,d,e){return c.apply(d,e)};this.values=Ka(14)?new Ca:new Ea};La.prototype.add=function(a,b){Ma(this,a,b,!1)};var Ma=function(a,b,c,d){a.Rc||(d?a.values.yl(b,c):a.values.set(b,c))};La.prototype.set=function(a,b){this.Rc||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
La.prototype.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};La.prototype.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};var Na=function(a){var b=new La(a.R,a);a.J&&(b.J=a.J);b.O=a.O;b.D=a.D;return b};La.prototype.te=function(){return this.R};La.prototype.eb=function(){this.Rc=!0};var Oa=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.wm=a;this.Zl=c===void 0?!1:c;this.debugInfo=[];this.D=b};sa(Oa,Error);var Pa=function(a){return a instanceof Oa?a:new Oa(a,void 0,!0)};function Qa(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Sa(a,e.value),c instanceof Ba);e=d.next());return c}function Sa(a,b){try{var c=l(b),d=c.next().value,e=ta(c),f=a.get(String(d));if(!f||typeof f.invoke!=="function")throw Pa(Error("Attempting to execute non-function "+b[0]+"."));return f.invoke.apply(f,[a].concat(ua(e)))}catch(h){var g=a.J;g&&g(h,b.context?{id:b[0],line:b.context.line}:null);throw h;}};var Ta=function(){this.J=new Ha;this.D=new La(this.J)};k=Ta.prototype;k.te=function(){return this.J};k.execute=function(a){return this.Lj([a].concat(ua(ya.apply(1,arguments))))};k.Lj=function(){for(var a,b=l(ya.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Sa(this.D,c.value);return a};k.lo=function(a){var b=ya.apply(1,arguments),c=Na(this.D);c.D=a;for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Sa(c,f.value);return d};k.eb=function(){this.D.eb()};var Ua=function(){this.Da=!1;this.ba=new Ea};k=Ua.prototype;k.get=function(a){return this.ba.get(a)};k.set=function(a,b){this.Da||this.ba.set(a,b)};k.has=function(a){return this.ba.has(a)};k.remove=function(a){this.Da||this.ba.remove(a)};k.xa=function(){return this.ba.xa()};k.Yb=function(){return this.ba.Yb()};k.Gb=function(){return this.ba.Gb()};k.eb=function(){this.Da=!0};k.Rc=function(){return this.Da};function Va(){for(var a=Wa,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Ya(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var Wa,Za;function $a(a){Wa=Wa||Ya();Za=Za||Va();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(Wa[m],Wa[n],Wa[p],Wa[q])}return b.join("")}
function ab(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=Za[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}Wa=Wa||Ya();Za=Za||Va();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var cb={};function db(a,b){cb[a]=cb[a]||[];cb[a][b]=!0}function eb(){cb.GTAG_EVENT_FEATURE_CHANNEL=fb}function gb(a){var b=cb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return $a(c.join("")).replace(/\.+$/,"")}function hb(){for(var a=[],b=cb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function jb(){}function kb(a){return typeof a==="function"}function lb(a){return typeof a==="string"}function mb(a){return typeof a==="number"&&!isNaN(a)}function nb(a){return Array.isArray(a)?a:[a]}function ob(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function pb(a,b){if(!mb(a)||!mb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function qb(a,b){for(var c=new rb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function sb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function tb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function ub(a){return Math.round(Number(a))||0}function vb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function wb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function xb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function yb(){return new Date(Date.now())}function zb(){return yb().getTime()}var rb=function(){this.prefix="gtm.";this.values={}};rb.prototype.set=function(a,b){this.values[this.prefix+a]=b};rb.prototype.get=function(a){return this.values[this.prefix+a]};rb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ab(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Bb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Cb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Db(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Eb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Fb(a,b){var c=y;b=b||[];for(var d=c,e=0;e<a.length-1;e++){if(!d.hasOwnProperty(a[e]))return;d=d[a[e]];if(b.indexOf(d)>=0)return}return d}function Gb(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Hb=/^\w{1,9}$/;function Ib(a,b){a=a||{};b=b||",";var c=[];sb(a,function(d,e){Hb.test(d)&&e&&c.push(d)});return c.join(b)}function Jb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Kb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Lb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Mb(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Nb=globalThis.trustedTypes,Ob;function Pb(){var a=null;if(!Nb)return a;try{var b=function(c){return c};a=Nb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Qb(){Ob===void 0&&(Ob=Pb());return Ob};var Rb=function(a){this.D=a};Rb.prototype.toString=function(){return this.D+""};function Sb(a){var b=a,c=Qb(),d=c?c.createScriptURL(b):b;return new Rb(d)}function Tb(a){if(a instanceof Rb)return a.D;throw Error("");};var Ub=wa([""]),Vb=va(["\x00"],["\\0"]),Wb=va(["\n"],["\\n"]),Xb=va(["\x00"],["\\u0000"]);function Yb(a){return a.toString().indexOf("`")===-1}Yb(function(a){return a(Ub)})||Yb(function(a){return a(Vb)})||Yb(function(a){return a(Wb)})||Yb(function(a){return a(Xb)});var Zb=function(a){this.D=a};Zb.prototype.toString=function(){return this.D};var $b=function(a){this.Rp=a};function ac(a){return new $b(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var bc=[ac("data"),ac("http"),ac("https"),ac("mailto"),ac("ftp"),new $b(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function cc(a){var b;b=b===void 0?bc:b;if(a instanceof Zb)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof $b&&d.Rp(a))return new Zb(a)}}var dc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function ec(a){var b;if(a instanceof Zb)if(a instanceof Zb)b=a.D;else throw Error("");else b=dc.test(a)?a:void 0;return b};function fc(a,b){var c=ec(b);c!==void 0&&(a.action=c)};function hc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var ic=function(a){this.D=a};ic.prototype.toString=function(){return this.D+""};var kc=function(){this.D=jc[0].toLowerCase()};kc.prototype.toString=function(){return this.D};function lc(a,b){var c=[new kc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof kc)g=f.D;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var mc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function nc(a){return a===null?"null":a===void 0?"undefined":a};var y=window,oc=window.history,A=document,pc=navigator;function qc(){var a;try{a=pc.serviceWorker}catch(b){return}return a}var rc=A.currentScript,sc=rc&&rc.src;function tc(a,b){var c=y[a];y[a]=c===void 0?b:c;return y[a]}function uc(a){return(pc.userAgent||"").indexOf(a)!==-1}function vc(){return uc("Firefox")||uc("FxiOS")}function wc(){return(uc("GSA")||uc("GoogleApp"))&&(uc("iPhone")||uc("iPad"))}function xc(){return uc("Edg/")||uc("EdgA/")||uc("EdgiOS/")}
var yc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},zc={onload:1,src:1,width:1,height:1,style:1};function Ac(a,b,c){b&&sb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Bc(a,b,c,d,e){var f=A.createElement("script");Ac(f,d,yc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Sb(nc(a));f.src=Tb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=A.getElementsByTagName("script")[0]||A.body||A.head;r.parentNode.insertBefore(f,r)}return f}
function Cc(){if(sc){var a=sc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Dc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=A.createElement("iframe"),h=!0);Ac(g,c,zc);d&&sb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=A.body&&A.body.lastChild||A.body||A.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Ec(a,b,c,d){return Fc(a,b,c,d)}function Ic(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Jc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function D(a){y.setTimeout(a,0)}function Kc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Lc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Mc(a){var b=A.createElement("div"),c=b,d,e=nc("A<div>"+a+"</div>"),f=Qb(),g=f?f.createHTML(e):e;d=new ic(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof ic)h=d.D;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Nc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Oc(a,b,c){var d;try{d=pc.sendBeacon&&pc.sendBeacon(a)}catch(e){db("TAGGING",15)}d?b==null||b():Fc(a,b,c)}function Pc(a,b){try{return pc.sendBeacon(a,b)}catch(c){db("TAGGING",15)}return!1}var Qc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Rc(a,b,c,d,e){if(Sc()){var f=Object.assign({},Qc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=y.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Hh)return e==null||e(),!1;if(b){var h=
Pc(a,b);h?d==null||d():e==null||e();return h}Tc(a,d,e);return!0}function Sc(){return typeof y.fetch==="function"}function Uc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Vc(){var a=y.performance;if(a&&kb(a.now))return a.now()}
function Wc(){var a,b=y.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function Xc(){return y.performance||void 0}function Yc(){var a=y.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Fc=function(a,b,c,d){var e=new Image(1,1);Ac(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Tc=Oc;function Zc(a,b){return this.evaluate(a)&&this.evaluate(b)}function $c(a,b){return this.evaluate(a)===this.evaluate(b)}function ad(a,b){return this.evaluate(a)||this.evaluate(b)}function bd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function cd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function dd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=y.location.href;d instanceof Ua&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var ed=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,fd=function(a){if(a==null)return String(a);var b=ed.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},gd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},hd=function(a){if(!a||fd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!gd(a,"constructor")&&!gd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
gd(a,b)},id=function(a,b){var c=b||(fd(a)=="array"?[]:{}),d;for(d in a)if(gd(a,d)){var e=a[d];fd(e)=="array"?(fd(c[d])!="array"&&(c[d]=[]),c[d]=id(e,c[d])):hd(e)?(hd(c[d])||(c[d]={}),c[d]=id(e,c[d])):c[d]=e}return c};function jd(a){if(a==void 0||Array.isArray(a)||hd(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function kd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var ld=function(a){a=a===void 0?[]:a;this.ba=new Ea;this.values=[];this.Da=!1;for(var b in a)a.hasOwnProperty(b)&&(kd(b)?this.values[Number(b)]=a[Number(b)]:this.ba.set(b,a[b]))};k=ld.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof ld?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Da)if(a==="length"){if(!kd(b))throw Pa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else kd(a)?this.values[Number(a)]=b:this.ba.set(a,b)};k.get=function(a){return a==="length"?this.length():kd(a)?this.values[Number(a)]:this.ba.get(a)};k.length=function(){return this.values.length};k.xa=function(){for(var a=this.ba.xa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Yb=function(){for(var a=this.ba.Yb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.Gb=function(){for(var a=this.ba.Gb(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){kd(a)?delete this.values[Number(a)]:this.Da||this.ba.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,ua(ya.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=ya.apply(2,arguments);return b===void 0&&c.length===0?new ld(this.values.splice(a)):new ld(this.values.splice.apply(this.values,[a,b||0].concat(ua(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ua(ya.apply(0,arguments)))};k.has=function(a){return kd(a)&&this.values.hasOwnProperty(a)||this.ba.has(a)};k.eb=function(){this.Da=!0;Object.freeze(this.values)};k.Rc=function(){return this.Da};
function md(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var nd=function(a,b){this.functionName=a;this.se=b;this.ba=new Ea;this.Da=!1};k=nd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new ld(this.xa())};k.invoke=function(a){return this.se.call.apply(this.se,[new od(this,a)].concat(ua(ya.apply(1,arguments))))};k.Kb=function(a){var b=ya.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ua(b)))}catch(c){}};k.get=function(a){return this.ba.get(a)};
k.set=function(a,b){this.Da||this.ba.set(a,b)};k.has=function(a){return this.ba.has(a)};k.remove=function(a){this.Da||this.ba.remove(a)};k.xa=function(){return this.ba.xa()};k.Yb=function(){return this.ba.Yb()};k.Gb=function(){return this.ba.Gb()};k.eb=function(){this.Da=!0};k.Rc=function(){return this.Da};var pd=function(a,b){nd.call(this,a,b)};sa(pd,nd);var qd=function(a,b){nd.call(this,a,b)};sa(qd,nd);var od=function(a,b){this.se=a;this.M=b};
od.prototype.evaluate=function(a){var b=this.M;return Array.isArray(a)?Sa(b,a):a};od.prototype.getName=function(){return this.se.getName()};od.prototype.te=function(){return this.M.te()};var rd=function(){this.map=new Map};rd.prototype.set=function(a,b){this.map.set(a,b)};rd.prototype.get=function(a){return this.map.get(a)};var sd=function(){this.keys=[];this.values=[]};sd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};sd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function td(){try{return Map?new rd:new sd}catch(a){return new sd}};var ud=function(a){if(a instanceof ud)return a;if(jd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};ud.prototype.getValue=function(){return this.value};ud.prototype.toString=function(){return String(this.value)};var wd=function(a){this.promise=a;this.Da=!1;this.ba=new Ea;this.ba.set("then",vd(this));this.ba.set("catch",vd(this,!0));this.ba.set("finally",vd(this,!1,!0))};k=wd.prototype;k.get=function(a){return this.ba.get(a)};k.set=function(a,b){this.Da||this.ba.set(a,b)};k.has=function(a){return this.ba.has(a)};k.remove=function(a){this.Da||this.ba.remove(a)};k.xa=function(){return this.ba.xa()};k.Yb=function(){return this.ba.Yb()};k.Gb=function(){return this.ba.Gb()};
var vd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new pd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof pd||(d=void 0);e instanceof pd||(e=void 0);var f=Na(this.M),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new ud(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new wd(h)})};wd.prototype.eb=function(){this.Da=!0};wd.prototype.Rc=function(){return this.Da};function xd(a,b,c){var d=td(),e=function(g,h){for(var m=g.xa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof ld){var m=[];d.set(g,m);for(var n=g.xa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof wd)return g.promise.then(function(u){return xd(u,b,1)},function(u){return Promise.reject(xd(u,b,1))});if(g instanceof Ua){var q={};d.set(g,q);e(g,q);return q}if(g instanceof pd){var r=function(){for(var u=
ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=yd(u[w],b,c);var x=new La(b?b.te():new Ha);b&&(x.D=b.D);return f(g.invoke.apply(g,[x].concat(ua(v))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof ud&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function yd(a,b,c){var d=td(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||tb(g)){var m=new ld;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(hd(g)){var p=new Ua;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new pd("",function(){for(var u=ya.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=xd(this.evaluate(u[w]),b,c);return f((0,this.M.O)(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new ud(g)};return f(a)};var zd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof ld)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new ld(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new ld(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new ld(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ua(ya.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Pa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Pa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Pa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Pa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=md(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new ld(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=md(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ua(ya.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ua(ya.apply(1,arguments)))}};var Ad={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Bd=new Ba("break"),Cd=new Ba("continue");function Dd(a,b){return this.evaluate(a)+this.evaluate(b)}function Ed(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Fd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof ld))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Pa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=xd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Pa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Ad.hasOwnProperty(e)){var m=2;m=1;var n=xd(f,void 0,m);return yd(d[e].apply(d,n),this.M)}throw Pa(Error("TypeError: "+e+" is not a function"));}if(d instanceof ld){if(d.has(e)){var p=d.get(String(e));if(p instanceof pd){var q=md(f);return p.invoke.apply(p,[this.M].concat(ua(q)))}throw Pa(Error("TypeError: "+e+" is not a function"));}if(zd.supportedMethods.indexOf(e)>=
0){var r=md(f);return zd[e].call.apply(zd[e],[d,this.M].concat(ua(r)))}}if(d instanceof pd||d instanceof Ua||d instanceof wd){if(d.has(e)){var t=d.get(e);if(t instanceof pd){var u=md(f);return t.invoke.apply(t,[this.M].concat(ua(u)))}throw Pa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof pd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof ud&&e==="toString")return d.toString();throw Pa(Error("TypeError: Object has no '"+
e+"' property."));}function Gd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.M;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Hd(){var a=ya.apply(0,arguments),b=Na(this.M),c=Qa(b,a);if(c instanceof Ba)return c}function Id(){return Bd}function Jd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ba)return d}}
function Kd(){for(var a=this.M,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);Ma(a,c,d,!0)}}}function Ld(){return Cd}function Md(a,b){return new Ba(a,this.evaluate(b))}function Nd(a,b){for(var c=ya.apply(2,arguments),d=new ld,e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(ua(c));this.M.add(a,this.evaluate(g))}function Od(a,b){return this.evaluate(a)/this.evaluate(b)}
function Pd(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof ud,f=d instanceof ud;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function Qd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Rd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Qa(f,d);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}}}
function Sd(a,b,c){if(typeof b==="string")return Rd(a,function(){return b.length},function(f){return f},c);if(b instanceof Ua||b instanceof wd||b instanceof ld||b instanceof pd){var d=b.xa(),e=d.length;return Rd(a,function(){return e},function(f){return d[f]},c)}}function Td(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){g.set(d,h);return g},e,f)}
function Ud(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){var m=Na(g);Ma(m,d,h,!0);return m},e,f)}function Vd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Sd(function(h){var m=Na(g);m.add(d,h);return m},e,f)}function Wd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Xd(function(h){g.set(d,h);return g},e,f)}
function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Xd(function(h){var m=Na(g);Ma(m,d,h,!0);return m},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.M;return Xd(function(h){var m=Na(g);m.add(d,h);return m},e,f)}
function Xd(a,b,c){if(typeof b==="string")return Rd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof ld)return Rd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Pa(Error("The value is not iterable."));}
function $d(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof ld))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.M,h=this.evaluate(d),m=Na(g);for(e(g,m);Sa(m,b);){var n=Qa(m,h);if(n instanceof Ba){if(n.type==="break")break;if(n.type==="return")return n}var p=Na(g);e(m,p);Sa(p,c);m=p}}
function ae(a,b){var c=ya.apply(2,arguments),d=this.M,e=this.evaluate(b);if(!(e instanceof ld))throw Error("Error: non-List value given for Fn argument names.");return new pd(a,function(){return function(){var f=ya.apply(0,arguments),g=Na(d);g.D===void 0&&(g.D=this.M.D);for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new ld(h));var r=Qa(g,c);if(r instanceof Ba)return r.type===
"return"?r.data:r}}())}function be(a){var b=this.evaluate(a),c=this.M;if(ce&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function de(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Pa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ua||d instanceof wd||d instanceof ld||d instanceof pd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:kd(e)&&(c=d[e]);else if(d instanceof ud)return;return c}function ee(a,b){return this.evaluate(a)>this.evaluate(b)}function fe(a,b){return this.evaluate(a)>=this.evaluate(b)}
function ge(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof ud&&(c=c.getValue());d instanceof ud&&(d=d.getValue());return c===d}function he(a,b){return!ge.call(this,a,b)}function ie(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Qa(this.M,d);if(e instanceof Ba)return e}var ce=!1;
function je(a,b){return this.evaluate(a)<this.evaluate(b)}function ke(a,b){return this.evaluate(a)<=this.evaluate(b)}function le(){for(var a=new ld,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function me(){for(var a=new Ua,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ne(a,b){return this.evaluate(a)%this.evaluate(b)}
function oe(a,b){return this.evaluate(a)*this.evaluate(b)}function pe(a){return-this.evaluate(a)}function qe(a){return!this.evaluate(a)}function re(a,b){return!Pd.call(this,a,b)}function se(){return null}function te(a,b){return this.evaluate(a)||this.evaluate(b)}function ue(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function ve(a){return this.evaluate(a)}function we(){return ya.apply(0,arguments)}function xe(a){return new Ba("return",this.evaluate(a))}
function ye(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Pa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof pd||d instanceof ld||d instanceof Ua)&&d.set(String(e),f);return f}function ze(a,b){return this.evaluate(a)-this.evaluate(b)}
function Ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Ba){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ba&&(g.type==="return"||g.type==="continue")))return g}
function Be(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Ce(a){var b=this.evaluate(a);return b instanceof pd?"function":typeof b}function De(){for(var a=this.M,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Ee(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Qa(this.M,e);if(f instanceof Ba){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Qa(this.M,e);if(g instanceof Ba){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Fe(a){return~Number(this.evaluate(a))}function Ge(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function He(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ie(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Je(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ke(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Le(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Me(){}
function Ne(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ba)return d}catch(h){if(!(h instanceof Oa&&h.Zl))throw h;var e=Na(this.M);a!==""&&(h instanceof Oa&&(h=h.wm),e.add(a,new ud(h)));var f=this.evaluate(c),g=Qa(e,f);if(g instanceof Ba)return g}}function Oe(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Oa&&f.Zl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ba)return e;if(c)throw c;if(d instanceof Ba)return d};var Qe=function(){this.D=new Ta;Pe(this)};Qe.prototype.execute=function(a){return this.D.Lj(a)};var Pe=function(a){var b=function(c,d){var e=new qd(String(c),d);e.eb();a.D.D.set(String(c),e)};b("map",me);b("and",Zc);b("contains",bd);b("equals",$c);b("or",ad);b("startsWith",cd);b("variable",dd)};var Se=function(){this.J=!1;this.D=new Ta;Re(this);this.J=!0};Se.prototype.execute=function(a){return Te(this.D.Lj(a))};var Ue=function(a,b,c){return Te(a.D.lo(b,c))};Se.prototype.eb=function(){this.D.eb()};
var Re=function(a){var b=function(c,d){var e=String(c),f=new qd(e,d);f.eb();a.D.D.set(e,f)};b(0,Dd);b(1,Ed);b(2,Fd);b(3,Gd);b(56,Je);b(57,Ge);b(58,Fe);b(59,Le);b(60,He);b(61,Ie);b(62,Ke);b(53,Hd);b(4,Id);b(5,Jd);b(68,Ne);b(52,Kd);b(6,Ld);b(49,Md);b(7,le);b(8,me);b(9,Jd);b(50,Nd);b(10,Od);b(12,Pd);b(13,Qd);b(67,Oe);b(51,ae);b(47,Td);b(54,Ud);b(55,Vd);b(63,$d);b(64,Wd);b(65,Yd);b(66,Zd);b(15,be);b(16,de);b(17,de);b(18,ee);b(19,fe);b(20,ge);b(21,he);b(22,ie);b(23,je);b(24,ke);b(25,ne);b(26,oe);b(27,
pe);b(28,qe);b(29,re);b(45,se);b(30,te);b(32,ue);b(33,ue);b(34,ve);b(35,ve);b(46,we);b(36,xe);b(43,ye);b(37,ze);b(38,Ae);b(39,Be);b(40,Ce);b(44,Me);b(41,De);b(42,Ee)};Se.prototype.te=function(){return this.D.te()};function Te(a){if(a instanceof Ba||a instanceof pd||a instanceof ld||a instanceof Ua||a instanceof wd||a instanceof ud||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var Ve=function(a){this.message=a};function We(a){a.Br=!0;return a};var Xe=We(function(a){return typeof a==="string"});function Ye(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new Ve("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function Ze(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var $e=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function af(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+Ye(e)+c}a<<=2;d||(a|=32);return c=""+Ye(a|b)+c}
function cf(a,b){var c;var d=a.Uc,e=a.Sc;d===void 0?c="":(e||(e=0),c=""+af(1,1)+Ye(d<<2|e));var f=a.Yl,g=a.Mo,h="4"+c+(f?""+af(2,1)+Ye(f):"")+(g?""+af(12,1)+Ye(g):""),m,n=a.Mj;m=n&&$e.test(n)?""+af(3,2)+n:"";var p,q=a.Ij;p=q?""+af(4,1)+Ye(q):"";var r;var t=a.ctid;if(t&&b){var u=af(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var x=v[1];r=""+u+Ye(1+x.length)+(a.lm||0)+x}}else r="";var z=a.tq,B=a.oe,C=a.Oa,F=a.Fr,G=h+m+p+r+(z?""+af(6,1)+Ye(z):"")+(B?""+af(7,3)+Ye(B.length)+
B:"")+(C?""+af(8,3)+Ye(C.length)+C:"")+(F?""+af(9,3)+Ye(F.length)+F:""),I;var K=a.am;K=K===void 0?{}:K;for(var U=[],Q=l(Object.keys(K)),na=Q.next();!na.done;na=Q.next()){var T=na.value;U[Number(T)]=K[T]}if(U.length){var ba=af(10,3),aa;if(U.length===0)aa=Ye(0);else{for(var W=[],ka=0,ja=!1,la=0;la<U.length;la++){ja=!0;var Ra=la%6;U[la]&&(ka|=1<<Ra);Ra===5&&(W.push(Ye(ka)),ka=0,ja=!1)}ja&&W.push(Ye(ka));aa=W.join("")}var Xa=aa;I=""+ba+Ye(Xa.length)+Xa}else I="";var Fa=a.xm;return G+I+(Fa?""+af(11,3)+
Ye(Fa.length)+Fa:"")};var df=function(){function a(b){return{toString:function(){return b}}}return{Vm:a("consent"),bk:a("convert_case_to"),dk:a("convert_false_to"),ek:a("convert_null_to"),fk:a("convert_true_to"),gk:a("convert_undefined_to"),Gq:a("debug_mode_metadata"),Ha:a("function"),Di:a("instance_name"),oo:a("live_only"),po:a("malware_disabled"),METADATA:a("metadata"),so:a("original_activity_id"),Xq:a("original_vendor_template_id"),Wq:a("once_on_load"),ro:a("once_per_event"),Al:a("once_per_load"),Zq:a("priority_override"),
gr:a("respected_consent_types"),Jl:a("setup_tags"),rh:a("tag_id"),Ql:a("teardown_tags")}}();var Af;var Bf=[],Cf=[],Df=[],Ef=[],Ff=[],Gf,Hf,If;function Jf(a){If=If||a}
function Kf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Bf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Ef.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Df.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Lf(p[r])}Cf.push(p)}}
function Lf(a){}var Mf,Nf=[],Of=[];function Pf(a,b){var c={};c[df.Ha]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Qf(a,b,c){try{return Hf(Rf(a,b,c))}catch(d){JSON.stringify(a)}return 2}function Sf(a){var b=a[df.Ha];if(!b)throw Error("Error: No function name given for function call.");return!!Gf[b]}
var Rf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Tf(a[e],b,c));return d},Tf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Tf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Bf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[df.Di]);try{var m=Rf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Uf(m,{event:b,index:f,type:2,
name:h});Mf&&(d=Mf.No(d,m))}catch(z){b.logMacroError&&b.logMacroError(z,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Tf(a[n],b,c)]=Tf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Tf(a[q],b,c);If&&(p=p||If.Op(r));d.push(r)}return If&&p?If.So(d):d.join("");case "escape":d=Tf(a[1],b,c);if(If&&Array.isArray(a[1])&&a[1][0]==="macro"&&If.Pp(a))return If.cq(d);d=String(d);for(var t=2;t<a.length;t++)lf[a[t]]&&(d=lf[a[t]](d));return d;
case "tag":var u=a[1];if(!Ef[u])throw Error("Unable to resolve tag reference "+u+".");return{im:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[df.Ha]=a[1];var w=Qf(v,b,c),x=!!a[4];return x||w!==2?x!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Uf=function(a,b){var c=a[df.Ha],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Gf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Nf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Eb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Bf[q];break;case 1:r=Ef[q];break;default:n="";break a}var t=r&&r[df.Di];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Of.indexOf(c)===-1){Of.push(c);
var x=zb();u=e(g);var z=zb()-x,B=zb();v=Af(c,h,b);w=z-(zb()-B)}else if(e&&(u=e(g)),!e||f)v=Af(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),jd(u)?(Array.isArray(u)?Array.isArray(v):hd(u)?hd(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Vf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};sa(Vf,Error);Vf.prototype.getMessage=function(){return this.message};function Wf(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)Wf(a[c],b[c])}};function Xf(){return function(a,b){var c;var d=Yf;a instanceof Oa?(a.D=d,c=a):c=new Oa(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function Yf(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)mb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function Zf(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=$f(a),f=0;f<Cf.length;f++){var g=Cf[f],h=ag(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Ef.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function ag(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function $f(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Qf(Df[c],a));return b[c]}};function bg(a,b){b[df.bk]&&typeof a==="string"&&(a=b[df.bk]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(df.ek)&&a===null&&(a=b[df.ek]);b.hasOwnProperty(df.gk)&&a===void 0&&(a=b[df.gk]);b.hasOwnProperty(df.fk)&&a===!0&&(a=b[df.fk]);b.hasOwnProperty(df.dk)&&a===!1&&(a=b[df.dk]);return a};var cg=function(){this.D={}},eg=function(a,b){var c=dg.D,d;(d=c.D)[a]!=null||(d[a]=[]);c.D[a].push(function(){return b.apply(null,ua(ya.apply(0,arguments)))})};function fg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Vf(c,d,g);}}
function gg(a,b,c){return function(d){if(d){var e=a.D[d],f=a.D.all;if(e||f){var g=c.apply(void 0,[d].concat(ua(ya.apply(1,arguments))));fg(e,b,d,g);fg(f,b,d,g)}}}};var kg=function(){var a=data.permissions||{},b=hg.ctid,c=this;this.J={};this.D=new cg;var d={},e={},f=gg(this.D,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ua(ya.apply(1,arguments)))):{}});sb(a,function(g,h){function m(p){var q=ya.apply(1,arguments);if(!n[p])throw ig(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ua(q)))}var n={};sb(h,function(p,q){var r=jg(p,q);n[p]=r.assert;d[p]||(d[p]=r.U);r.Wl&&!e[p]&&(e[p]=r.Wl)});c.J[g]=function(p,
q){var r=n[p];if(!r)throw ig(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ua(t.slice(1))))}})},lg=function(a){return dg.J[a]||function(){}};
function jg(a,b){var c=Pf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=ig;try{return Uf(c)}catch(d){return{assert:function(e){throw new Vf(e,{},"Permission "+e+" is unknown.");},U:function(){throw new Vf(a,{},"Permission "+a+" is unknown.");}}}}function ig(a,b,c){return new Vf(a,b,c)};var mg=!1;var ng={};ng.Om=vb('');ng.bp=vb('');
var rg=function(a){var b={},c=0;sb(a,function(e,f){if(f!=null){var g=(""+f).replace(/~/g,"~~");if(og.hasOwnProperty(e))b[og[e]]=g;else if(pg.hasOwnProperty(e)){var h=pg[e];b.hasOwnProperty(h)||(b[h]=g)}else if(e==="category")for(var m=g.split("/",5),n=0;n<m.length;n++){var p=b,q=qg[n],r=m[n];p.hasOwnProperty(q)||(p[q]=r)}else if(c<27){var t=String.fromCharCode(c<10?48+c:65+c-10);b["k"+t]=(""+String(e)).replace(/~/g,"~~");b["v"+t]=g;c++}}});var d=[];sb(b,function(e,f){d.push(""+e+f)});return d.join("~")},
og={item_id:"id",item_name:"nm",item_brand:"br",item_category:"ca",item_category2:"c2",item_category3:"c3",item_category4:"c4",item_category5:"c5",item_variant:"va",price:"pr",quantity:"qt",coupon:"cp",item_list_name:"ln",index:"lp",item_list_id:"li",discount:"ds",affiliation:"af",promotion_id:"pi",promotion_name:"pn",creative_name:"cn",creative_slot:"cs",location_id:"lo"},pg={id:"id",name:"nm",brand:"br",variant:"va",list_name:"ln",list_position:"lp",list:"ln",position:"lp",creative:"cn"},qg=["ca",
"c2","c3","c4","c5"];function sg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var tg=[];function ug(a){switch(a){case 1:return 0;case 38:return 11;case 53:return 1;case 54:return 2;case 52:return 7;case 75:return 3;case 103:return 12;case 197:return 13;case 203:return 14;case 114:return 10;case 115:return 4;case 116:return 5;case 135:return 9;case 136:return 6}}function vg(a,b){tg[a]=b;var c=ug(a);c!==void 0&&(Ia[c]=b)}function E(a){vg(a,!0)}E(39);E(34);E(35);E(36);
E(56);E(145);E(153);E(144);E(120);
E(5);E(111);E(139);
E(87);E(92);E(117);E(159);
E(132);E(20);E(72);
E(113);E(154);E(116);
vg(23,!1),E(24);Ja[1]=sg('1',6E4);Ja[3]=sg('10',1);Ja[2]=sg('',50);
E(29);wg(26,25);E(37);
E(9);E(91);E(123);
E(157);E(158);E(71);
E(136);E(127);
E(27);E(69);
E(135);E(95);E(38);
E(103);E(112);E(63);E(152);
E(101);
E(122);E(121);
E(108);E(134);
E(115);E(96);E(31);
E(22);E(97);E(19);E(12);
E(28);
E(90);
E(59);E(13);
E(167);
E(175);E(176);
E(180);
E(182);E(185);
E(187);
E(192);E(194);E(201);function H(a){return!!tg[a]}function wg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?E(b):E(a)};
var xg=function(){this.events=[];this.D="";this.sa={};this.baseUrl="";this.O=0;this.R=this.J=!1;this.endpoint=0;H(89)&&(this.R=!0)};xg.prototype.add=function(a){return this.T(a)?(this.events.push(a),this.D=a.J,this.sa=a.sa,this.baseUrl=a.baseUrl,this.O+=a.R,this.J=a.O,this.endpoint=a.endpoint,this.destinationId=a.destinationId,this.ia=a.eventId,this.la=a.priorityId,!0):!1};xg.prototype.T=function(a){return this.events.length?this.events.length>=20||a.R+this.O>=16384?!1:this.baseUrl===a.baseUrl&&this.J===
a.O&&this.Ca(a):!0};xg.prototype.Ca=function(a){var b=this;if(!this.R)return this.D===a.J;var c=Object.keys(this.sa);return c.length===Object.keys(a.sa).length&&c.every(function(d){return a.sa.hasOwnProperty(d)&&String(b.sa[d])===String(a.sa[d])})};var yg={},zg=(yg.uaa=!0,yg.uab=!0,yg.uafvl=!0,yg.uamb=!0,yg.uam=!0,yg.uap=!0,yg.uapv=!0,yg.uaw=!0,yg);
var Cg=function(a,b){var c=a.events;if(c.length===1)return Ag(c[0],b);var d=[];a.D&&d.push(a.D);for(var e={},f=0;f<c.length;f++)sb(c[f].Jd,function(t,u){u!=null&&(e[t]=e[t]||{},e[t][String(u)]=e[t][String(u)]+1||1)});var g={};sb(e,function(t,u){var v,w=-1,x=0;sb(u,function(z,B){x+=B;var C=(z.length+t.length+2)*(B-1);C>w&&(v=z,w=C)});x===c.length&&(g[t]=v)});Bg(g,d);b&&d.push("_s="+b);for(var h=d.join("&"),m=[],n={},p=0;p<c.length;n={xj:void 0},p++){var q=[];n.xj={};sb(c[p].Jd,function(t){return function(u,
v){g[u]!==""+v&&(t.xj[u]=v)}}(n));c[p].D&&q.push(c[p].D);Bg(n.xj,q);m.push(q.join("&"))}var r=m.join("\r\n");return{params:h,body:r}},Ag=function(a,b){var c=[];a.J&&c.push(a.J);b&&c.push("_s="+b);Bg(a.Jd,c);var d=!1;a.D&&(c.push(a.D),d=!0);var e=c.join("&"),f="",g=e.length+a.baseUrl.length+1;d&&g>2048&&(f=c.pop(),e=c.join("&"));return{params:e,body:f}},Bg=function(a,b){sb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))})};var Dg=function(a){var b=[];sb(a,function(c,d){d!=null&&b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")},Eg=function(a,b,c,d,e,f,g,h){this.baseUrl=b;this.endpoint=c;this.destinationId=f;this.eventId=g;this.priorityId=h;this.sa=a.sa;this.Jd=a.Jd;this.ij=a.ij;this.O=d;this.J=Dg(a.sa);this.D=Dg(a.ij);this.R=this.D.length;if(e&&this.R>16384)throw Error("EVENT_TOO_LARGE");};
var Hg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Fg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Gg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Eb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Gg=/^[a-z$_][\w-$]*$/i,Fg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Ig=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Jg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Kg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Lg=new rb;function Mg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Lg.get(e);f||(f=new RegExp(b,d),Lg.set(e,f));return f.test(a)}catch(g){return!1}}function Ng(a,b){return String(a).indexOf(String(b))>=0}
function Og(a,b){return String(a)===String(b)}function Pg(a,b){return Number(a)>=Number(b)}function Qg(a,b){return Number(a)<=Number(b)}function Rg(a,b){return Number(a)>Number(b)}function Sg(a,b){return Number(a)<Number(b)}function Tg(a,b){return Eb(String(a),String(b))};var $g=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ah={Fn:"function",PixieMap:"Object",List:"Array"};
function bh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=$g.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof pd?n="Fn":m instanceof ld?n="List":m instanceof Ua?n="PixieMap":m instanceof wd?n="PixiePromise":m instanceof ud&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ah[n]||n)+", which does not match required type ")+
((ah[h]||h)+"."));}}}function J(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof pd?d.push("function"):g instanceof ld?d.push("Array"):g instanceof Ua?d.push("Object"):g instanceof wd?d.push("Promise"):g instanceof ud?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function ch(a){return a instanceof Ua}function dh(a){return ch(a)||a===null||eh(a)}
function fh(a){return a instanceof pd}function gh(a){return fh(a)||a===null||eh(a)}function hh(a){return a instanceof ld}function ih(a){return a instanceof ud}function jh(a){return typeof a==="string"}function kh(a){return jh(a)||a===null||eh(a)}function lh(a){return typeof a==="boolean"}function mh(a){return lh(a)||eh(a)}function nh(a){return lh(a)||a===null||eh(a)}function oh(a){return typeof a==="number"}function eh(a){return a===void 0};function ph(a){return""+a}
function qh(a,b){var c=[];return c};function rh(a,b){var c=new pd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Pa(g);}});c.eb();return c}
function sh(a,b){var c=new Ua,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];kb(e)?c.set(d,rh(a+"_"+d,e)):hd(e)?c.set(d,sh(a+"_"+d,e)):(mb(e)||lb(e)||typeof e==="boolean")&&c.set(d,e)}c.eb();return c};function th(a,b){if(!jh(a))throw J(this.getName(),["string"],arguments);if(!kh(b))throw J(this.getName(),["string","undefined"],arguments);var c={},d=new Ua;return d=sh("AssertApiSubject",
c)};function uh(a,b){if(!kh(b))throw J(this.getName(),["string","undefined"],arguments);if(a instanceof wd)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ua;return d=sh("AssertThatSubject",c)};function vh(a){return function(){for(var b=ya.apply(0,arguments),c=[],d=this.M,e=0;e<b.length;++e)c.push(xd(b[e],d));return yd(a.apply(null,c))}}function wh(){for(var a=Math,b=xh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=vh(a[e].bind(a)))}return c};function yh(a){return a!=null&&Eb(a,"__cvt_")};function zh(a){var b;return b};function Ah(a){var b;if(!jh(a))throw J(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Bh(a){try{return encodeURI(a)}catch(b){}};function Ch(a){try{return encodeURIComponent(String(a))}catch(b){}};
var Dh=function(a,b){for(var c=0;c<b.length;c++){if(a===void 0)return;a=a[b[c]]}return a},Eh=function(a,b){var c=b.preHit;if(c){var d=a[0];switch(d){case "hitData":return a.length<2?void 0:Dh(c.getHitData(a[1]),a.slice(2));case "metadata":return a.length<2?void 0:Dh(c.getMetadata(a[1]),a.slice(2));case "eventName":return c.getEventName();case "destinationId":return c.getDestinationId();default:throw Error(d+" is not a valid field that can be accessed\n                      from PreHit data.");}}},
Gh=function(a,b){if(a){if(a.contextValue!==void 0){var c;a:{var d=a.contextValue,e=d.keyParts;if(e&&e.length!==0){var f=d.namespaceType;switch(f){case 1:c=Eh(e,b);break a;case 2:var g=b.macro;c=g?g[e[0]]:void 0;break a;default:throw Error("Unknown Namespace Type used: "+f);}}c=void 0}return c}if(a.booleanExpressionValue!==void 0)return Fh(a.booleanExpressionValue,b);if(a.booleanValue!==void 0)return!!a.booleanValue;if(a.stringValue!==void 0)return String(a.stringValue);if(a.integerValue!==void 0)return Number(a.integerValue);
if(a.doubleValue!==void 0)return Number(a.doubleValue);throw Error("Unknown field used for variable of type ExpressionValue:"+a);}},Fh=function(a,b){var c=a.args;if(!Array.isArray(c)||c.length===0)throw Error('Invalid boolean expression format. Expected "args":'+c+" property to\n         be non-empty array.");var d=function(g){return Gh(g,b)};switch(a.type){case 1:for(var e=0;e<c.length;e++)if(d(c[e]))return!0;return!1;case 2:for(var f=0;f<c.length;f++)if(!d(c[f]))return!1;return c.length>0;case 3:return!d(c[0]);
case 4:return Mg(d(c[0]),d(c[1]),!1);case 5:return Og(d(c[0]),d(c[1]));case 6:return Tg(d(c[0]),d(c[1]));case 7:return Jg(d(c[0]),d(c[1]));case 8:return Ng(d(c[0]),d(c[1]));case 9:return Sg(d(c[0]),d(c[1]));case 10:return Qg(d(c[0]),d(c[1]));case 11:return Rg(d(c[0]),d(c[1]));case 12:return Pg(d(c[0]),d(c[1]));case 13:return Kg(d(c[0]),String(d(c[1])));default:throw Error('Invalid boolean expression format. Expected "type" property tobe a positive integer which is less than 14.');}};function Hh(a){if(!kh(a))throw J(this.getName(),["string|undefined"],arguments);};function Ih(a,b){if(!oh(a)||!oh(b))throw J(this.getName(),["number","number"],arguments);return pb(a,b)};function Jh(){return(new Date).getTime()};function Kh(a){if(a===null)return"null";if(a instanceof ld)return"array";if(a instanceof pd)return"function";if(a instanceof ud){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Lh(a){function b(c){return function(d){try{return c(d)}catch(e){(mg||ng.Om)&&a.call(this,e.message)}}}return{parse:b(function(c){return yd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(xd(c))}),publicName:"JSON"}};function Mh(a){return ub(xd(a,this.M))};function Nh(a){return Number(xd(a,this.M))};function Oh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Ph(a,b,c){var d=null,e=!1;return e?d:null};var xh="floor ceil round max min abs pow sqrt".split(" ");function Qh(){var a={};return{qp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Im:function(b,c){a[b]=c},reset:function(){a={}}}}function Rh(a,b){return function(){return pd.prototype.invoke.apply(a,[b].concat(ua(ya.apply(0,arguments))))}}
function Sh(a,b){if(!jh(a))throw J(this.getName(),["string","any"],arguments);}
function Th(a,b){if(!jh(a)||!ch(b))throw J(this.getName(),["string","PixieMap"],arguments);};var Uh={};var Vh=function(a){var b=new Ua;if(a instanceof ld)for(var c=a.xa(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof pd)for(var f=a.xa(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var m=0;m<a.length;m++)b.set(m,a[m]);return b};
Uh.keys=function(a){bh(this.getName(),arguments);if(a instanceof ld||a instanceof pd||typeof a==="string")a=Vh(a);if(a instanceof Ua||a instanceof wd)return new ld(a.xa());return new ld};
Uh.values=function(a){bh(this.getName(),arguments);if(a instanceof ld||a instanceof pd||typeof a==="string")a=Vh(a);if(a instanceof Ua||a instanceof wd)return new ld(a.Yb());return new ld};
Uh.entries=function(a){bh(this.getName(),arguments);if(a instanceof ld||a instanceof pd||typeof a==="string")a=Vh(a);if(a instanceof Ua||a instanceof wd)return new ld(a.Gb().map(function(b){return new ld(b)}));return new ld};
Uh.freeze=function(a){(a instanceof Ua||a instanceof wd||a instanceof ld||a instanceof pd)&&a.eb();return a};Uh.delete=function(a,b){if(a instanceof Ua&&!a.Rc())return a.remove(b),!0;return!1};function L(a,b){var c=ya.apply(2,arguments),d=a.M.D;if(!d)throw Error("Missing program state.");if(d.iq){try{d.Xl.apply(null,[b].concat(ua(c)))}catch(e){throw db("TAGGING",21),e;}return}d.Xl.apply(null,[b].concat(ua(c)))};var Wh=function(){this.J={};this.D={};this.O=!0;};Wh.prototype.get=function(a,b){var c=this.contains(a)?this.J[a]:void 0;return c};Wh.prototype.contains=function(a){return this.J.hasOwnProperty(a)};
Wh.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.D.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.J[a]=c?void 0:kb(b)?rh(a,b):sh(a,b)};function Xh(a,b){var c=void 0;return c};function Yh(){var a={};
return a};var M={m:{Na:"ad_personalization",V:"ad_storage",W:"ad_user_data",ja:"analytics_storage",fc:"region",ka:"consent_updated",wg:"wait_for_update",jn:"app_remove",kn:"app_store_refund",ln:"app_store_subscription_cancel",mn:"app_store_subscription_convert",nn:"app_store_subscription_renew",on:"consent_update",kk:"add_payment_info",lk:"add_shipping_info",Md:"add_to_cart",Nd:"remove_from_cart",mk:"view_cart",Wc:"begin_checkout",Od:"select_item",jc:"view_item_list",Fc:"select_promotion",kc:"view_promotion",
mb:"purchase",Pd:"refund",ub:"view_item",nk:"add_to_wishlist",pn:"exception",qn:"first_open",rn:"first_visit",ra:"gtag.config",Ab:"gtag.get",sn:"in_app_purchase",Xc:"page_view",tn:"screen_view",un:"session_start",vn:"source_update",wn:"timing_complete",xn:"track_social",Qd:"user_engagement",yn:"user_id_update",Fe:"gclid_link_decoration_source",Ge:"gclid_storage_source",mc:"gclgb",nb:"gclid",pk:"gclid_len",Rd:"gclgs",Sd:"gcllp",Td:"gclst",Aa:"ads_data_redaction",He:"gad_source",Ie:"gad_source_src",
Yc:"gclid_url",qk:"gclsrc",Je:"gbraid",Ud:"wbraid",Fa:"allow_ad_personalization_signals",Cg:"allow_custom_scripts",Ke:"allow_direct_google_requests",Dg:"allow_display_features",Eg:"allow_enhanced_conversions",Mb:"allow_google_signals",ob:"allow_interest_groups",zn:"app_id",An:"app_installer_id",Bn:"app_name",Cn:"app_version",Nb:"auid",Dn:"auto_detection_enabled",Zc:"aw_remarketing",Th:"aw_remarketing_only",Fg:"discount",Gg:"aw_feed_country",Hg:"aw_feed_language",wa:"items",Ig:"aw_merchant_id",rk:"aw_basket_type",
Le:"campaign_content",Me:"campaign_id",Ne:"campaign_medium",Oe:"campaign_name",Pe:"campaign",Qe:"campaign_source",Re:"campaign_term",Ob:"client_id",sk:"rnd",Uh:"consent_update_type",En:"content_group",Gn:"content_type",Pb:"conversion_cookie_prefix",Se:"conversion_id",Qa:"conversion_linker",Vh:"conversion_linker_disabled",bd:"conversion_api",Jg:"cookie_deprecation",pb:"cookie_domain",qb:"cookie_expires",wb:"cookie_flags",dd:"cookie_name",Qb:"cookie_path",ib:"cookie_prefix",Gc:"cookie_update",Vd:"country",
Va:"currency",Wh:"customer_buyer_stage",Te:"customer_lifetime_value",Xh:"customer_loyalty",Yh:"customer_ltv_bucket",Ue:"custom_map",Zh:"gcldc",ed:"dclid",tk:"debug_mode",qa:"developer_id",Hn:"disable_merchant_reported_purchases",fd:"dc_custom_params",In:"dc_natural_search",uk:"dynamic_event_settings",vk:"affiliation",Kg:"checkout_option",ai:"checkout_step",wk:"coupon",Ve:"item_list_name",bi:"list_name",Jn:"promotions",We:"shipping",di:"tax",Lg:"engagement_time_msec",Mg:"enhanced_client_id",Ng:"enhanced_conversions",
xk:"enhanced_conversions_automatic_settings",Og:"estimated_delivery_date",ei:"euid_logged_in_state",Xe:"event_callback",Kn:"event_category",Rb:"event_developer_id_string",Ln:"event_label",gd:"event",Pg:"event_settings",Qg:"event_timeout",Mn:"description",Nn:"fatal",On:"experiments",fi:"firebase_id",Wd:"first_party_collection",Rg:"_x_20",oc:"_x_19",yk:"fledge_drop_reason",zk:"fledge",Ak:"flight_error_code",Bk:"flight_error_message",Ck:"fl_activity_category",Dk:"fl_activity_group",gi:"fl_advertiser_id",
Ek:"fl_ar_dedupe",Ye:"match_id",Fk:"fl_random_number",Gk:"tran",Hk:"u",Sg:"gac_gclid",Xd:"gac_wbraid",Ik:"gac_wbraid_multiple_conversions",Jk:"ga_restrict_domain",hi:"ga_temp_client_id",Pn:"ga_temp_ecid",hd:"gdpr_applies",Kk:"geo_granularity",Hc:"value_callback",qc:"value_key",rc:"google_analysis_params",Yd:"_google_ng",Zd:"google_signals",Lk:"google_tld",Ze:"gpp_sid",af:"gpp_string",Tg:"groups",Mk:"gsa_experiment_id",bf:"gtag_event_feature_usage",Nk:"gtm_up",Ic:"iframe_state",cf:"ignore_referrer",
ii:"internal_traffic_results",Ok:"_is_fpm",Jc:"is_legacy_converted",Kc:"is_legacy_loaded",Ug:"is_passthrough",jd:"_lps",xb:"language",Vg:"legacy_developer_id_string",Ra:"linker",ae:"accept_incoming",sc:"decorate_forms",na:"domains",Lc:"url_position",df:"merchant_feed_label",ef:"merchant_feed_language",ff:"merchant_id",Pk:"method",Qn:"name",Qk:"navigation_type",hf:"new_customer",Wg:"non_interaction",Rn:"optimize_id",Rk:"page_hostname",jf:"page_path",Wa:"page_referrer",Bb:"page_title",Sk:"passengers",
Tk:"phone_conversion_callback",Sn:"phone_conversion_country_code",Uk:"phone_conversion_css_class",Tn:"phone_conversion_ids",Vk:"phone_conversion_number",Wk:"phone_conversion_options",Un:"_platinum_request_status",Vn:"_protected_audience_enabled",kf:"quantity",Xg:"redact_device_info",ji:"referral_exclusion_definition",Jq:"_request_start_time",Tb:"restricted_data_processing",Wn:"retoken",Xn:"sample_rate",ki:"screen_name",Mc:"screen_resolution",Xk:"_script_source",Yn:"search_term",rb:"send_page_view",
kd:"send_to",ld:"server_container_url",lf:"session_duration",Yg:"session_engaged",li:"session_engaged_time",uc:"session_id",Zg:"session_number",nf:"_shared_user_id",pf:"delivery_postal_code",Kq:"_tag_firing_delay",Lq:"_tag_firing_time",Mq:"temporary_client_id",mi:"_timezone",ni:"topmost_url",Zn:"tracking_id",oi:"traffic_type",Xa:"transaction_id",vc:"transport_url",Yk:"trip_type",nd:"update",Cb:"url_passthrough",Zk:"uptgs",qf:"_user_agent_architecture",rf:"_user_agent_bitness",tf:"_user_agent_full_version_list",
uf:"_user_agent_mobile",vf:"_user_agent_model",wf:"_user_agent_platform",xf:"_user_agent_platform_version",yf:"_user_agent_wow64",Ya:"user_data",ri:"user_data_auto_latency",si:"user_data_auto_meta",ui:"user_data_auto_multi",wi:"user_data_auto_selectors",xi:"user_data_auto_status",Ub:"user_data_mode",ah:"user_data_settings",Sa:"user_id",Vb:"user_properties",al:"_user_region",zf:"us_privacy_string",Ga:"value",bl:"wbraid_multiple_conversions",rd:"_fpm_parameters",Bi:"_host_name",ql:"_in_page_command",
rl:"_ip_override",vl:"_is_passthrough_cid",wc:"non_personalized_ads",Ni:"_sst_parameters",nc:"conversion_label",Ba:"page_location",Sb:"global_developer_id_string",md:"tc_privacy_string"}};var Zh={},$h=(Zh[M.m.ka]="gcu",Zh[M.m.mc]="gclgb",Zh[M.m.nb]="gclaw",Zh[M.m.pk]="gclid_len",Zh[M.m.Rd]="gclgs",Zh[M.m.Sd]="gcllp",Zh[M.m.Td]="gclst",Zh[M.m.Nb]="auid",Zh[M.m.Fg]="dscnt",Zh[M.m.Gg]="fcntr",Zh[M.m.Hg]="flng",Zh[M.m.Ig]="mid",Zh[M.m.rk]="bttype",Zh[M.m.Ob]="gacid",Zh[M.m.nc]="label",Zh[M.m.bd]="capi",Zh[M.m.Jg]="pscdl",Zh[M.m.Va]="currency_code",Zh[M.m.Wh]="clobs",Zh[M.m.Te]="vdltv",Zh[M.m.Xh]="clolo",Zh[M.m.Yh]="clolb",Zh[M.m.tk]="_dbg",Zh[M.m.Og]="oedeld",Zh[M.m.Rb]="edid",Zh[M.m.yk]=
"fdr",Zh[M.m.zk]="fledge",Zh[M.m.Sg]="gac",Zh[M.m.Xd]="gacgb",Zh[M.m.Ik]="gacmcov",Zh[M.m.hd]="gdpr",Zh[M.m.Sb]="gdid",Zh[M.m.Yd]="_ng",Zh[M.m.Ze]="gpp_sid",Zh[M.m.af]="gpp",Zh[M.m.Mk]="gsaexp",Zh[M.m.bf]="_tu",Zh[M.m.Ic]="frm",Zh[M.m.Ug]="gtm_up",Zh[M.m.jd]="lps",Zh[M.m.Vg]="did",Zh[M.m.df]="fcntr",Zh[M.m.ef]="flng",Zh[M.m.ff]="mid",Zh[M.m.hf]=void 0,Zh[M.m.Bb]="tiba",Zh[M.m.Tb]="rdp",Zh[M.m.uc]="ecsid",Zh[M.m.nf]="ga_uid",Zh[M.m.pf]="delopc",Zh[M.m.md]="gdpr_consent",Zh[M.m.Xa]="oid",Zh[M.m.Zk]=
"uptgs",Zh[M.m.qf]="uaa",Zh[M.m.rf]="uab",Zh[M.m.tf]="uafvl",Zh[M.m.uf]="uamb",Zh[M.m.vf]="uam",Zh[M.m.wf]="uap",Zh[M.m.xf]="uapv",Zh[M.m.yf]="uaw",Zh[M.m.ri]="ec_lat",Zh[M.m.si]="ec_meta",Zh[M.m.ui]="ec_m",Zh[M.m.wi]="ec_sel",Zh[M.m.xi]="ec_s",Zh[M.m.Ub]="ec_mode",Zh[M.m.Sa]="userId",Zh[M.m.zf]="us_privacy",Zh[M.m.Ga]="value",Zh[M.m.bl]="mcov",Zh[M.m.Bi]="hn",Zh[M.m.ql]="gtm_ee",Zh[M.m.wc]="npa",Zh[M.m.Se]=null,Zh[M.m.Mc]=null,Zh[M.m.xb]=null,Zh[M.m.wa]=null,Zh[M.m.Ba]=null,Zh[M.m.Wa]=null,Zh[M.m.ni]=
null,Zh[M.m.rd]=null,Zh[M.m.Fe]=null,Zh[M.m.Ge]=null,Zh[M.m.rc]=null,Zh);function ai(a,b){if(a){var c=a.split("x");c.length===2&&(bi(b,"u_w",c[0]),bi(b,"u_h",c[1]))}}
function ci(a){var b=di;b=b===void 0?ei:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(fi(q.value)),r.push(fi(q.quantity)),r.push(fi(q.item_id)),r.push(fi(q.start_date)),r.push(fi(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ei(a){return gi(a.item_id,a.id,a.item_name)}function gi(){for(var a=l(ya.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function hi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function bi(a,b,c){c===void 0||c===null||c===""&&!zg[b]||(a[b]=c)}function fi(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var ii={},ki={lq:ji};function li(a,b){var c=ii[b],d=c.Km;if(!(ii[b].active||ii[b].percent>50||c.percent<=0||(a.studies||{})[d])){var e=a.studies||{};e[d]=!0;a.studies=e;ki.lq(a,b)}}function ji(a,b){var c=ii[b];if(!(pb(0,9999)<c.percent*2*100))return a;mi(a,{experimentId:c.experimentId,controlId:c.controlId,experimentCallback:function(){}});return a}
function mi(a,b){if((a.exp||{})[b.experimentId])b.experimentCallback();else if(!(a.exp||{})[b.controlId]){var c;a:{for(var d=!1,e=!1,f=0;d===e;)if(d=pb(0,1)===0,e=pb(0,1)===0,f++,f>30){c=void 0;break a}c=d}var g=c;if(g!==void 0)if(g){b.experimentCallback();var h=a.exp||{};h[b.experimentId]=!0;a.exp=h}else{var m=a.exp||{};m[b.controlId]=!0;a.exp=m}}};var N={K:{Uj:"call_conversion",X:"conversion",ao:"floodlight",Bf:"ga_conversion",Ji:"landing_page",Ia:"page_view",oa:"remarketing",Ua:"user_data_lead",La:"user_data_web"}};function pi(a){return qi?A.querySelectorAll(a):null}
function ri(a,b){if(!qi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!A.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var si=!1;
if(A.querySelectorAll)try{var ti=A.querySelectorAll(":root");ti&&ti.length==1&&ti[0]==A.documentElement&&(si=!0)}catch(a){}var qi=si;function ui(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function vi(){this.blockSize=-1};function wi(a,b){this.blockSize=-1;this.blockSize=64;this.O=za.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.J=0;this.D=[];this.ia=a;this.T=b;this.la=za.Int32Array?new Int32Array(64):Array(64);xi===void 0&&(za.Int32Array?xi=new Int32Array(yi):xi=yi);this.reset()}Aa(wi,vi);for(var zi=[],Ai=0;Ai<63;Ai++)zi[Ai]=0;var Bi=[].concat(128,zi);
wi.prototype.reset=function(){this.R=this.J=0;var a;if(za.Int32Array)a=new Int32Array(this.T);else{var b=this.T,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.D=a};
var Ci=function(a){for(var b=a.O,c=a.la,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.D[0]|0,n=a.D[1]|0,p=a.D[2]|0,q=a.D[3]|0,r=a.D[4]|0,t=a.D[5]|0,u=a.D[6]|0,v=a.D[7]|0,w=0;w<64;w++){var x=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,z=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(xi[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+z|0;q=p;p=n;n=m;m=z+x|0}a.D[0]=a.D[0]+m|0;a.D[1]=a.D[1]+n|0;a.D[2]=a.D[2]+p|0;a.D[3]=a.D[3]+q|0;a.D[4]=a.D[4]+r|0;a.D[5]=a.D[5]+t|0;a.D[6]=a.D[6]+u|0;a.D[7]=a.D[7]+v|0};
wi.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.J;if(typeof a==="string")for(;c<b;)this.O[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ci(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.O[d++]=g;d==this.blockSize&&(Ci(this),d=0)}else throw Error("message must be string or array");
}this.J=d;this.R+=b};wi.prototype.digest=function(){var a=[],b=this.R*8;this.J<56?this.update(Bi,56-this.J):this.update(Bi,this.blockSize-(this.J-56));for(var c=63;c>=56;c--)this.O[c]=b&255,b/=256;Ci(this);for(var d=0,e=0;e<this.ia;e++)for(var f=24;f>=0;f-=8)a[d++]=this.D[e]>>f&255;return a};
var yi=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],xi;function Di(){wi.call(this,8,Ei)}Aa(Di,wi);var Ei=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Fi=/^[0-9A-Fa-f]{64}$/;function Gi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Hi(a){if(a===""||a==="e0")return Promise.resolve(a);var b;if((b=y.crypto)==null?0:b.subtle){if(Fi.test(a))return Promise.resolve(a);try{var c=Gi(a);return y.crypto.subtle.digest("SHA-256",c).then(function(d){return Ii(d,y)}).catch(function(){return"e2"})}catch(d){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Ii(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Ji=[],Ki;function Li(a){Ki?Ki(a):Ji.push(a)}function Mi(a,b){if(!H(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Li(a),b):c}function Ni(a,b){if(!H(190))return b;var c=Oi(a,"");return c!==b?(Li(a),b):c}function Oi(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Pi(a,b){if(!H(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Li(a),b)}function Qi(){Ki=Ri;for(var a=l(Ji),b=a.next();!b.done;b=a.next())Ki(b.value);Ji.length=0};var Si={fn:'256',gn:'100',hn:'1000',fo:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',ho:'US-CO',Bo:Ni(44,'101509157~103116026~103200004~103233427~103351869~103351871~104684208~104684211~104718208~104766473~104766475~104791498~104791500')},Ti={Xo:Number(Si.fn)||0,Yo:Number(Si.gn)||0,ap:Number(Si.hn)||0,vp:Si.fo.split("~"),wp:Si.ho.split("~"),Dq:Si.Bo};Object.assign({},Ti);function O(a){db("GTM",a)};
var Yi=function(a,b){var c=["tv.1"],d=Ui(a);if(d)return c.push(d),{cb:!1,Nj:c.join("~"),rg:{}};var e={},f=0;var g=Vi(a,function(p,q,r){var t=p.value,u;if(r){var v=q+"__"+f++;u="${userData."+v+"|sha256}";e[v]=t}else u=encodeURIComponent(encodeURIComponent(t));var w;c.push(""+q+((w=p.index)!=null?w:"")+"."+u)}).cb;var h=c.join("~"),m={userData:e},n=b===3;return b===2||n?{cb:g,Nj:h,rg:m,Zo:n?"tv.9~${"+(h+
"|encryptRsa}"):"tv.1~${"+(h+"|encrypt}"),encryptionKeyString:n?Wi():Xi()}:{cb:g,Nj:h,rg:m}},$i=function(a){if(!(a!=null&&Object.keys(a).length>0))return!1;var b=Zi(a);return Vi(b,function(){}).cb},Vi=function(a,b){b=b===void 0?function(){}:b;for(var c=!1,d=!1,e=l(a),f=e.next();!f.done;f=e.next()){var g=f.value;if(g.value){var h=aj[g.name];if(h){var m=bj(g);m&&(c=!0);d=!0;b(g,h,m)}}}return{cb:d,nj:c}},bj=function(a){var b=cj(a.name),c=/^e\d+$/.test(a.value),d;if(d=b&&!c){var e=a.value;d=!(dj.test(e)||
Fi.test(e))}return d},cj=function(a){return ej.indexOf(a)!==-1},Xi=function(){return'{\x22keys\x22:[{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BG904dy9WmhVIIN2dcA0nntGq1UgZbXnZdLdnazmhgnynSb4scGZ7898AB4JTEXhKpZyt/bI4c6pv3QFJCN8jcc\x3d\x22,\x22version\x22:0},\x22id\x22:\x22ab6723b9-2511-4f81-9cbd-e0d149a278cf\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BLpjiodJVqbXHjCX08Lld8KPCcDftXT1PLNwYFUZ4lewt6oPzk+zFS+SpTiA5B2AVGOEwVdDQjlCYjTdZbjyC8I\x3d\x22,\x22version\x22:0},\x22id\x22:\x22a3673cdc-b970-4045-a499-51d8c4352595\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BPMyGNvjmGlpCP2STM1WnoBMxEofvl4D/0l1ue1ScTFUEMAHqLbVq+4pAyr7nbTE7dXqK/3mnLAseTs8HwmR/LY\x3d\x22,\x22version\x22:0},\x22id\x22:\x228a8cc618-03a2-4904-889f-e39aef13e0b1\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BNORn+FghIYcBSP8FBXiQP9YIqpHq19J+Ww2IutW21aKRqE9/NO/EzSP6w7wzBvfPLWqHoe5MaG7WF5AmcP2tLs\x3d\x22,\x22version\x22:0},\x22id\x22:\x226d842905-6792-4050-9c1d-6b8b58dae2db\x22},{\x22hpkePublicKey\x22:{\x22params\x22:{\x22aead\x22:\x22AES_128_GCM\x22,\x22kdf\x22:\x22HKDF_SHA256\x22,\x22kem\x22:\x22DHKEM_P256_HKDF_SHA256\x22},\x22publicKey\x22:\x22BCbwodqwmiKhapTrvDCT/5oMNvQVr+cSmWFdVdydYAcUYqXw85kcCKqybq8MuinPMHgeHdVdwVKdc2w7boKPY0E\x3d\x22,\x22version\x22:0},\x22id\x22:\x225500c479-6e55-424a-b045-7a6d3f36b679\x22}]}'},hj=function(a){if(y.Promise){var b=void 0;return b}},mj=function(a,b,c,d,e){if(y.Promise)try{var f=Zi(a),g=ij(f,e).then(jj);return g}catch(p){}},oj=function(a){try{return jj(nj(Zi(a)))}catch(b){}},gj=function(a,b){var c=void 0;return c},jj=function(a){var b=a.Tc,c=a.time,d=["tv.1"],e=Ui(b);if(e)return d.push(e),{Ib:encodeURIComponent(d.join("~")),nj:!1,cb:!1,time:c,mj:!0};var f=b.filter(function(n){return!bj(n)}),g=Vi(f,function(n,p){var q=n.value,r=n.index;r!==void 0&&(p+=r);d.push(p+"."+q)}),h=g.nj,m=g.cb;return{Ib:encodeURIComponent(d.join("~")),nj:h,cb:m,time:c,mj:!1}},Ui=function(a){if(a.length===1&&a[0].name==="error_code")return aj.error_code+
"."+a[0].value},lj=function(a){if(a.length===1&&a[0].name==="error_code")return!1;for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;if(aj[d.name]&&d.value)return!0}return!1},Zi=function(a){function b(r,t,u,v){var w=pj(r);w!==""&&(Fi.test(w)?h.push({name:t,value:w,index:v}):h.push({name:t,value:u(w),index:v}))}function c(r,t){var u=r;if(lb(u)||Array.isArray(u)){u=nb(r);for(var v=0;v<u.length;++v){var w=pj(u[v]),x=Fi.test(w);t&&!x&&O(89);!t&&x&&O(88)}}}function d(r,t){var u=r[t];c(u,!1);var v=
qj[t];r[v]&&(r[t]&&O(90),u=r[v],c(u,!0));return u}function e(r,t,u){for(var v=nb(d(r,t)),w=0;w<v.length;++w)b(v[w],t,u)}function f(r,t,u,v){var w=d(r,t);b(w,t,u,v)}function g(r){return function(t){O(64);return r(t)}}var h=[];if(y.location.protocol!=="https:")return h.push({name:"error_code",value:"e3",index:void 0}),h;e(a,"email",rj);e(a,"phone_number",sj);e(a,"first_name",g(tj));e(a,"last_name",g(tj));var m=a.home_address||{};e(m,"street",g(uj));e(m,"city",g(uj));e(m,"postal_code",g(vj));e(m,"region",
g(uj));e(m,"country",g(vj));for(var n=nb(a.address||{}),p=0;p<n.length;p++){var q=n[p];f(q,"first_name",tj,p);f(q,"last_name",tj,p);f(q,"street",uj,p);f(q,"city",uj,p);f(q,"postal_code",vj,p);f(q,"region",uj,p);f(q,"country",vj,p)}return h},wj=function(a){var b=a?Zi(a):[];return jj({Tc:b})},xj=function(a){return a&&a!=null&&Object.keys(a).length>0&&y.Promise?Zi(a).some(function(b){return b.value&&cj(b.name)&&!Fi.test(b.value)}):!1},pj=function(a){return a==null?"":lb(a)?xb(String(a)):"e0"},vj=function(a){return a.replace(yj,
"")},tj=function(a){return uj(a.replace(/\s/g,""))},uj=function(a){return xb(a.replace(zj,"").toLowerCase())},sj=function(a){a=a.replace(/[\s-()/.]/g,"");a.charAt(0)!=="+"&&(a="+"+a);return Aj.test(a)?a:"e0"},rj=function(a){var b=a.toLowerCase().split("@");if(b.length===2){var c=b[0];/^(gmail|googlemail)\./.test(b[1])&&(c=c.replace(/\./g,""));c=c+"@"+b[1];if(Bj.test(c))return c}return"e0"},nj=function(a){var b=Vc();try{a.forEach(function(e){if(e.value&&cj(e.name)){var f;var g=e.value,h=y;if(g===""||
g==="e0"||Fi.test(g))f=g;else try{var m=new Di;m.update(Gi(g));f=Ii(m.digest(),h)}catch(n){f="e2"}e.value=f}});var c={Tc:a};if(b!==void 0){var d=Vc();b&&d&&(c.time=Math.round(d)-Math.round(b))}return c}catch(e){return{Tc:[]}}},ij=function(a,b){if(!a.some(function(d){return d.value&&cj(d.name)}))return Promise.resolve({Tc:a});if(!y.Promise)return Promise.resolve({Tc:[]});var c=b?Vc():void 0;return Promise.all(a.map(function(d){return d.value&&cj(d.name)?Hi(d.value).then(function(e){d.value=e}):Promise.resolve()})).then(function(){var d=
{Tc:a};if(c!==void 0){var e=Vc();c&&e!==void 0&&(d.time=Math.round(e)-Math.round(c))}return d}).catch(function(){return{Tc:[]}})},zj=/[0-9`~!@#$%^&*()_\-+=:;<>,.?|/\\[\]]/g,Bj=/^\S+@\S+\.\S+$/,Aj=/^\+\d{10,15}$/,yj=/[.~]/g,dj=/^[0-9A-Za-z_-]{43}$/,Cj={},aj=(Cj.email="em",Cj.phone_number="pn",Cj.first_name="fn",Cj.last_name="ln",Cj.street="sa",Cj.city="ct",Cj.region="rg",Cj.country="co",Cj.postal_code="pc",Cj.error_code="ec",Cj),Dj={},qj=(Dj.email="sha256_email_address",Dj.phone_number="sha256_phone_number",
Dj.first_name="sha256_first_name",Dj.last_name="sha256_last_name",Dj.street="sha256_street",Dj);var ej=Object.freeze(["email","phone_number","first_name","last_name","street"]);
var Ej={},Fj=(Ej[M.m.ob]=1,Ej[M.m.ld]=2,Ej[M.m.vc]=2,Ej[M.m.Aa]=3,Ej[M.m.Te]=4,Ej[M.m.Cg]=5,Ej[M.m.Gc]=6,Ej[M.m.ib]=6,Ej[M.m.pb]=6,Ej[M.m.dd]=6,Ej[M.m.Qb]=6,Ej[M.m.wb]=6,Ej[M.m.qb]=7,Ej[M.m.Tb]=9,Ej[M.m.Dg]=10,Ej[M.m.Mb]=11,Ej),Gj={},Hj=(Gj.unknown=13,Gj.standard=14,Gj.unique=15,Gj.per_session=16,Gj.transactions=17,Gj.items_sold=18,Gj);var fb=[];function Ij(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Fj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Fj[f],h=b;h=h===void 0?!1:h;db("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(fb[g]=!0)}}};var Jj=function(){this.D=new Set;this.J=new Set},Lj=function(a){var b=Kj.ia;a=a===void 0?[]:a;var c=[].concat(ua(b.D)).concat([].concat(ua(b.J))).concat(a);c.sort(function(d,e){return d-e});return c},Mj=function(){var a=[].concat(ua(Kj.ia.D));a.sort(function(b,c){return b-c});return a},Nj=function(){var a=Kj.ia,b=Ti.Dq;a.D=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.D.add(e)}};var Oj={},Pj=Ni(14,"56g0"),Qj=Pi(15,Number("0")),Rj=Ni(19,"dataLayer");Ni(20,"");Ni(16,"ChAI8OrOwgYQ2ufixpzDyp8TEiUAS79Zaz6lZQI2XncYZM3/i+pLnUXpSn+NdwpveLEBfSe1ESs7GgJpMw\x3d\x3d");var Sj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Tj={__paused:1,__tg:1},Uj;for(Uj in Sj)Sj.hasOwnProperty(Uj)&&(Tj[Uj]=1);var Vj=Mi(11,vb("true")),Wj=!1,Xj,Yj=!1;Yj=!0;
Xj=Yj;var Zj,ak=!1;Zj=ak;Oj.Bg=Ni(3,"www.googletagmanager.com");var bk=""+Oj.Bg+(Xj?"/gtag/js":"/gtm.js"),ck=null,dk=null,ek={},fk={};Oj.Wm=Mi(2,vb("true"));var gk="";Oj.Oi=gk;
var Kj=new function(){this.ia=new Jj;this.D=this.O=!1;this.J=0;this.Ca=this.Za=this.Eb=this.T="";this.la=this.R=!1};function hk(){var a;a=a===void 0?[]:a;return Lj(a).join("~")}function ik(){var a=Kj.T.length;return Kj.T[a-1]==="/"?Kj.T.substring(0,a-1):Kj.T}function jk(){return Kj.D?H(84)?Kj.J===0:Kj.J!==1:!1}function kk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var lk=new rb,mk={},nk={},qk={name:Rj,set:function(a,b){id(Gb(a,b),mk);ok()},get:function(a){return pk(a,2)},reset:function(){lk=new rb;mk={};ok()}};function pk(a,b){return b!=2?lk.get(a):rk(a)}function rk(a,b){var c=a.split(".");b=b||[];for(var d=mk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function sk(a,b){nk.hasOwnProperty(a)||(lk.set(a,b),id(Gb(a,b),mk),ok())}
function tk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=pk(c,1);if(Array.isArray(d)||hd(d))d=id(d,null);nk[c]=d}}function ok(a){sb(nk,function(b,c){lk.set(b,c);id(Gb(b),mk);id(Gb(b,c),mk);a&&delete nk[b]})}function uk(a,b){var c,d=(b===void 0?2:b)!==1?rk(a):lk.get(a);fd(d)==="array"||fd(d)==="object"?c=id(d,null):c=d;return c};
var wk=function(a){for(var b=[],c=Object.keys(vk),d=0;d<c.length;d++){var e=c[d],f=vk[e],g=void 0,h=(g=a[e])!=null?g:"0";b.push(f+"-"+h)}return b.join("~")},xk=function(a,b){return a||b?a&&!b?"1":!a&&b?"2":"3":"0"},yk=function(a,b,c,d){if(!c)return!1;for(var e=String(c.value),f,g=e.replace(/\["?'?/g,".").replace(/"?'?\]/g,"").split(",").map(function(w){return w.trim()}).filter(function(w){return w&&!Eb(w,"#")&&!Eb(w,".")}),h=0;h<g.length;h++){var m=g[h];if(Eb(m,"dataLayer."))f=pk(m.substring(10));
else{var n=m.split(".");f=y[n.shift()];for(var p=0;p<n.length;p++)f=f&&f[n[p]]}if(f!==void 0)break}if(f===void 0&&qi)try{var q=pi(e);if(q&&q.length>0){f=[];for(var r=0;r<q.length&&r<(b==="email"||b==="phone_number"?5:1);r++)f.push(Lc(q[r])||xb(q[r].value));f=f.length===1?f[0]:f}}catch(w){O(149)}if(H(60)){for(var t,u=0;u<g.length&&(t=pk(g[u]),t===void 0);u++);var v=f!==void 0;d[b]=xk(t!==void 0,v);v||(f=t)}return f?(a[b]=f,!0):!1},zk=function(a,b){b=b===void 0?{}:b;if(a){var c={},d=!1;d=yk(c,"email",
a.email,b)||d;d=yk(c,"phone_number",a.phone,b)||d;c.address=[];for(var e=a.name_and_address||[],f=0;f<e.length;f++){var g={};d=yk(g,"first_name",e[f].first_name,b)||d;d=yk(g,"last_name",e[f].last_name,b)||d;d=yk(g,"street",e[f].street,b)||d;d=yk(g,"city",e[f].city,b)||d;d=yk(g,"region",e[f].region,b)||d;d=yk(g,"country",e[f].country,b)||d;d=yk(g,"postal_code",e[f].postal_code,b)||d;c.address.push(g)}return d?c:void 0}},Ak=function(a,b){switch(a.enhanced_conversions_mode){case "manual":if(b&&hd(b))return b;
var c=a.enhanced_conversions_manual_var;if(c!==void 0)return c;var d=y.enhanced_conversion_data;d&&db("GTAG_EVENT_FEATURE_CHANNEL",8);return d;case "automatic":return zk(a[M.m.xk])}},Bk=function(a){return hd(a)?!!a.enable_code:!1},vk={email:"1",phone_number:"2",first_name:"3",last_name:"4",country:"5",postal_code:"6",street:"7",city:"8",region:"9"};var Fk=/:[0-9]+$/,Gk=/^\d+\.fls\.doubleclick\.net$/;function Hk(a,b,c,d){var e=Ik(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Ik(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=ta(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Jk(a){try{return decodeURIComponent(a)}catch(b){}}function Kk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Lk(a.protocol)||Lk(y.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:y.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||y.location.hostname).replace(Fk,"").toLowerCase());return Mk(a,b,c,d,e)}
function Mk(a,b,c,d,e){var f,g=Lk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Nk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Fk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||db("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Hk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Lk(a){return a?a.replace(":","").toLowerCase():""}function Nk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Ok={},Pk=0;
function Qk(a){var b=Ok[a];if(!b){var c=A.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||db("TAGGING",1),d="/"+d);var e=c.hostname.replace(Fk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Pk<5&&(Ok[a]=b,Pk++)}return b}function Rk(a,b,c){var d=Qk(a);return Lb(b,d,c)}
function Sk(a){var b=Qk(y.location.href),c=Kk(b,"host",!1);if(c&&c.match(Gk)){var d=Kk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Tk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Uk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Vk(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Qk(""+c+b).href}}function Wk(a,b){if(jk()||Kj.O)return Vk(a,b)}
function Xk(){return!!Oj.Oi&&Oj.Oi.split("@@").join("")!=="SGTM_TOKEN"}function Yk(a){for(var b=l([M.m.ld,M.m.vc]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function Zk(a,b,c){c=c===void 0?"":c;if(!jk())return a;var d=b?Tk[a]||"":"";d==="/gs"&&(c="");return""+ik()+d+c}function $k(a){if(!jk())return a;for(var b=l(Uk),c=b.next();!c.done;c=b.next())if(Eb(a,""+ik()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function al(a){var b=String(a[df.Ha]||"").replace(/_/g,"");return Eb(b,"cvt")?"cvt":b}var bl=y.location.search.indexOf("?gtm_latency=")>=0||y.location.search.indexOf("&gtm_latency=")>=0;var cl={jq:Pi(27,Number("0.005000")),Vo:Pi(42,Number("0.010000"))},dl=Math.random(),el=bl||dl<Number(cl.jq),fl=bl||dl>=1-Number(cl.Vo);var gl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},hl=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var il,jl;a:{for(var kl=["CLOSURE_FLAGS"],ll=za,ml=0;ml<kl.length;ml++)if(ll=ll[kl[ml]],ll==null){jl=null;break a}jl=ll}var nl=jl&&jl[610401301];il=nl!=null?nl:!1;function ol(){var a=za.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var pl,ql=za.navigator;pl=ql?ql.userAgentData||null:null;function rl(a){if(!il||!pl)return!1;for(var b=0;b<pl.brands.length;b++){var c=pl.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function sl(a){return ol().indexOf(a)!=-1};function tl(){return il?!!pl&&pl.brands.length>0:!1}function ul(){return tl()?!1:sl("Opera")}function vl(){return sl("Firefox")||sl("FxiOS")}function wl(){return tl()?rl("Chromium"):(sl("Chrome")||sl("CriOS"))&&!(tl()?0:sl("Edge"))||sl("Silk")};var xl=function(a){xl[" "](a);return a};xl[" "]=function(){};var yl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function zl(){return il?!!pl&&!!pl.platform:!1}function Al(){return sl("iPhone")&&!sl("iPod")&&!sl("iPad")}function Bl(){Al()||sl("iPad")||sl("iPod")};ul();tl()||sl("Trident")||sl("MSIE");sl("Edge");!sl("Gecko")||ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")||sl("Trident")||sl("MSIE")||sl("Edge");ol().toLowerCase().indexOf("webkit")!=-1&&!sl("Edge")&&sl("Mobile");zl()||sl("Macintosh");zl()||sl("Windows");(zl()?pl.platform==="Linux":sl("Linux"))||zl()||sl("CrOS");zl()||sl("Android");Al();sl("iPad");sl("iPod");Bl();ol().toLowerCase().indexOf("kaios");var Cl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{xl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Dl=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},El=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Fl=function(a){if(y.top==y)return 0;if(a===void 0?0:a){var b=y.location.ancestorOrigins;
if(b)return b[b.length-1]==y.location.origin?1:2}return Cl(y.top)?1:2},Gl=function(a){a=a===void 0?document:a;return a.createElement("img")},Hl=function(){for(var a=y,b=a;a&&a!=a.parent;)a=a.parent,Cl(a)&&(b=a);return b};function Il(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Jl(){return Il("join-ad-interest-group")&&kb(pc.joinAdInterestGroup)}
function Kl(a,b,c){var d=Ja[3]===void 0?1:Ja[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=A.querySelector(e);g&&(f=[g])}else f=Array.from(A.querySelectorAll(e))}catch(r){}var h;a:{try{h=A.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Ja[2]===void 0?50:Ja[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&zb()-q<(Ja[1]===void 0?6E4:Ja[1])?(db("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ll(f[0]);else{if(n)return db("TAGGING",10),!1}else f.length>=d?Ll(f[0]):n&&Ll(m[0]);Dc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:zb()});return!0}function Ll(a){try{a.parentNode.removeChild(a)}catch(b){}};function Ml(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Nl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};vl();Al()||sl("iPod");sl("iPad");!sl("Android")||wl()||vl()||ul()||sl("Silk");wl();!sl("Safari")||wl()||(tl()?0:sl("Coast"))||ul()||(tl()?0:sl("Edge"))||(tl()?rl("Microsoft Edge"):sl("Edg/"))||(tl()?rl("Opera"):sl("OPR"))||vl()||sl("Silk")||sl("Android")||Bl();var Ol={},Pl=null,Ql=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Pl){Pl={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Ol[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Pl[q]===void 0&&(Pl[q]=p)}}}for(var r=Ol[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var x=b[v],
z=b[v+1],B=b[v+2],C=r[x>>2],F=r[(x&3)<<4|z>>4],G=r[(z&15)<<2|B>>6],I=r[B&63];t[w++]=""+C+F+G+I}var K=0,U=u;switch(b.length-v){case 2:K=b[v+1],U=r[(K&15)<<2]||u;case 1:var Q=b[v];t[w]=""+r[Q>>2]+r[(Q&3)<<4|K>>4]+U+u}return t.join("")};var Rl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Sl=/#|$/,Tl=function(a,b){var c=a.search(Sl),d=Rl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return yl(a.slice(d,e!==-1?e:0))},Ul=/[?&]($|#)/,Vl=function(a,b,c){for(var d,e=a.search(Sl),f=0,g,h=[];(g=Rl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Ul,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function Wl(a,b,c,d,e,f){var g=Tl(c,"fmt");if(d){var h=Tl(c,"random"),m=Tl(c,"label")||"";if(!h)return!1;var n=Ql(yl(m)+":"+yl(h));if(!Ml(a,n,d))return!1}g&&Number(g)!==4&&(c=Vl(c,"rfmt",g));var p=Vl(c,"fmt",4);Bc(p,function(){a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},e,f,b.getElementsByTagName("script")[0].parentElement||void 0);return!0};var Xl={},Yl=(Xl[1]={},Xl[2]={},Xl[3]={},Xl[4]={},Xl);function Zl(a,b,c){var d=$l(b,c);if(d){var e=Yl[b][d];e||(e=Yl[b][d]=[]);e.push(Object.assign({},a))}}function am(a,b){var c=$l(a,b);if(c){var d=Yl[a][c];d&&(Yl[a][c]=d.filter(function(e){return!e.Em}))}}function bm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function $l(a,b){var c=b;if(b[0]==="/"){var d;c=((d=y.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function cm(a){var b=ya.apply(1,arguments);fl&&(Zl(a,2,b[0]),Zl(a,3,b[0]));Oc.apply(null,ua(b))}function dm(a){var b=ya.apply(1,arguments);fl&&Zl(a,2,b[0]);return Pc.apply(null,ua(b))}function em(a){var b=ya.apply(1,arguments);fl&&Zl(a,3,b[0]);Ec.apply(null,ua(b))}
function fm(a){var b=ya.apply(1,arguments),c=b[0];fl&&(Zl(a,2,c),Zl(a,3,c));return Rc.apply(null,ua(b))}function gm(a){var b=ya.apply(1,arguments);fl&&Zl(a,1,b[0]);Bc.apply(null,ua(b))}function hm(a){var b=ya.apply(1,arguments);b[0]&&fl&&Zl(a,4,b[0]);Dc.apply(null,ua(b))}function im(a){var b=ya.apply(1,arguments);fl&&Zl(a,1,b[2]);return Wl.apply(null,ua(b))}function jm(a){var b=ya.apply(1,arguments);fl&&Zl(a,4,b[0]);Kl.apply(null,ua(b))};var km=/gtag[.\/]js/,lm=/gtm[.\/]js/,mm=!1;function nm(a){if(mm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(km.test(c))return"3";if(lm.test(c))return"2"}return"0"};function om(a,b){var c=pm();c.pending||(c.pending=[]);ob(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function qm(){var a=y.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var rm=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.siloed=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=qm()};
function pm(){var a=tc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new rm,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.siloed||(c.siloed=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=qm());return c};var sm={},tm=!1,um=void 0,hg={ctid:Ni(5,"G-5S1SQJV11Q"),canonicalContainerId:Ni(6,"187644217"),ym:Ni(10,"G-5S1SQJV11Q|GT-MBLWDKSM"),zm:Ni(9,"G-5S1SQJV11Q")};sm.he=Mi(7,vb(""));function vm(){return sm.he&&wm().some(function(a){return a===hg.ctid})}function xm(){var a=ym();return tm?a.map(zm):a}function Am(){var a=wm();return tm?a.map(zm):a}
function Bm(){var a=Am();if(!tm)for(var b=l([].concat(ua(a))),c=b.next();!c.done;c=b.next()){var d=zm(c.value),e=pm().destination[d];e&&e.state!==0||a.push(d)}return a}function Cm(){return Dm(hg.ctid)}function Em(){return Dm(hg.canonicalContainerId||"_"+hg.ctid)}function ym(){return hg.ym?hg.ym.split("|"):[hg.ctid]}function wm(){return hg.zm?hg.zm.split("|").filter(function(a){return H(108)?a.indexOf("GTM-")!==0:!0}):[]}function Fm(){var a=Gm(Hm()),b=a&&a.parent;if(b)return Gm(b)}
function Gm(a){var b=pm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function Dm(a){return tm?zm(a):a}function zm(a){return"siloed_"+a}function Im(a){a=String(a);return Eb(a,"siloed_")?a.substring(7):a}function Jm(){if(Kj.R){var a=pm();if(a.siloed){for(var b=[],c=ym().map(zm),d=wm().map(zm),e={},f=0;f<a.siloed.length;e={xh:void 0},f++)e.xh=a.siloed[f],!tm&&ob(e.xh.isDestination?d:c,function(g){return function(h){return h===g.xh.ctid}}(e))?tm=!0:b.push(e.xh);a.siloed=b}}}
function Km(){var a=pm();if(a.pending){for(var b,c=[],d=!1,e=xm(),f=um?um:Bm(),g={},h=0;h<a.pending.length;g={mg:void 0},h++)g.mg=a.pending[h],ob(g.mg.target.isDestination?f:e,function(m){return function(n){return n===m.mg.target.ctid}}(g))?d||(b=g.mg.onLoad,d=!0):c.push(g.mg);a.pending=c;if(b)try{b(Em())}catch(m){}}}
function Lm(){var a=hg.ctid,b=xm(),c=Bm();um=c;for(var d=function(n,p){var q={canonicalContainerId:hg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};rc&&(q.scriptElement=rc);sc&&(q.scriptSource=sc);if(Fm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Kj.D,x=Qk(v),z=w?x.pathname:""+x.hostname+x.pathname,B=A.scripts,C="",F=0;F<B.length;++F){var G=B[F];if(!(G.innerHTML.length===
0||!w&&G.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(z)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(F);break b}C=String(F)}}if(C){t=C;break b}}t=void 0}var I=t;if(I){mm=!0;r=I;break a}}var K=[].slice.call(A.scripts);r=q.scriptElement?String(K.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=nm(q)}var U=p?e.destination:e.container,Q=U[n];Q?(p&&Q.state===0&&O(93),Object.assign(Q,q)):U[n]=q},e=pm(),f=l(b),g=f.next();!g.done;g=
f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Em()]={};Km()}function Mm(){var a=Em();return!!pm().canonical[a]}function Nm(a){return!!pm().container[a]}function Om(a){var b=pm().destination[a];return!!b&&!!b.state}function Hm(){return{ctid:Cm(),isDestination:sm.he}}function Pm(a,b,c){b.siloed&&Qm({ctid:a,isDestination:!1});var d=Hm();pm().container[a]={state:1,context:b,parent:d};om({ctid:a,isDestination:!1},c)}
function Qm(a){var b=pm();(b.siloed=b.siloed||[]).push(a)}function Rm(){var a=pm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Sm(){var a={};sb(pm().destination,function(b,c){c.state===0&&(a[Im(b)]=c)});return a}function Tm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Um(){for(var a=pm(),b=l(xm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1}
function Vm(a){var b=pm();return b.destination[a]?1:b.destination[zm(a)]?2:0};var Wm={Ka:{be:0,fe:1,Ki:2}};Wm.Ka[Wm.Ka.be]="FULL_TRANSMISSION";Wm.Ka[Wm.Ka.fe]="LIMITED_TRANSMISSION";Wm.Ka[Wm.Ka.Ki]="NO_TRANSMISSION";var Xm={Z:{Db:0,Ea:1,Ec:2,Nc:3}};Xm.Z[Xm.Z.Db]="NO_QUEUE";Xm.Z[Xm.Z.Ea]="ADS";Xm.Z[Xm.Z.Ec]="ANALYTICS";Xm.Z[Xm.Z.Nc]="MONITORING";function Ym(){var a=tc("google_tag_data",{});return a.ics=a.ics||new Zm}var Zm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.D=[]};
Zm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;db("TAGGING",19);b==null?db("TAGGING",18):$m(this,a,b==="granted",c,d,e,f,g)};Zm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)$m(this,a[d],void 0,void 0,"","",b,c)};
var $m=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&lb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&y.setTimeout(function(){m[b]===t&&t.quiet&&(db("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Zm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())an(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())an(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&lb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.D.push({consentTypes:a,se:b})};var an=function(a,b){for(var c=0;c<a.D.length;++c){var d=a.D[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Am=!0)}};Zm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.D.length;++c){var d=this.D[c];if(d.Am){d.Am=!1;try{d.se({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var bn=!1,cn=!1,dn={},en={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(dn.ad_storage=1,dn.analytics_storage=1,dn.ad_user_data=1,dn.ad_personalization=1,dn),usedContainerScopedDefaults:!1};function fn(a){var b=Ym();b.accessedAny=!0;return(lb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,en)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function gn(a){var b=Ym();b.accessedAny=!0;return b.getConsentState(a,en)}function hn(a){var b=Ym();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function jn(){if(!Ka(8))return!1;var a=Ym();a.accessedAny=!0;if(a.active)return!0;if(!en.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(en.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(en.containerScopedDefaults[c.value]!==1)return!0;return!1}function kn(a,b){Ym().addListener(a,b)}
function ln(a,b){Ym().notifyListeners(a,b)}function mn(a,b){function c(){for(var e=0;e<b.length;e++)if(!hn(b[e]))return!0;return!1}if(c()){var d=!1;kn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function nn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];fn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=lb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),kn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):y.setTimeout(function(){m(c())},500)}}))};var on={},pn=(on[Xm.Z.Db]=Wm.Ka.be,on[Xm.Z.Ea]=Wm.Ka.be,on[Xm.Z.Ec]=Wm.Ka.be,on[Xm.Z.Nc]=Wm.Ka.be,on),qn=function(a,b){this.D=a;this.consentTypes=b};qn.prototype.isConsentGranted=function(){switch(this.D){case 0:return this.consentTypes.every(function(a){return fn(a)});case 1:return this.consentTypes.some(function(a){return fn(a)});default:hc(this.D,"consentsRequired had an unknown type")}};
var rn={},sn=(rn[Xm.Z.Db]=new qn(0,[]),rn[Xm.Z.Ea]=new qn(0,["ad_storage"]),rn[Xm.Z.Ec]=new qn(0,["analytics_storage"]),rn[Xm.Z.Nc]=new qn(1,["ad_storage","analytics_storage"]),rn);var un=function(a){var b=this;this.type=a;this.D=[];kn(sn[a].consentTypes,function(){tn(b)||b.flush()})};un.prototype.flush=function(){for(var a=l(this.D),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.D=[]};var tn=function(a){return pn[a.type]===Wm.Ka.Ki&&!sn[a.type].isConsentGranted()},vn=function(a,b){tn(a)?a.D.push(b):b()},wn=new Map;function xn(a){wn.has(a)||wn.set(a,new un(a));return wn.get(a)};var yn="https://"+Ni(21,"www.googletagmanager.com"),zn="/td?id="+hg.ctid,An="v t pid dl tdp exp".split(" "),Bn=["mcc"],Cn={},Dn={},En=!1,Fn=void 0;function Gn(a,b,c){Dn[a]=b;(c===void 0||c)&&Hn(a)}function Hn(a,b){Cn[a]!==void 0&&(b===void 0||!b)||Eb(hg.ctid,"GTM-")&&a==="mcc"||(Cn[a]=!0)}
function In(a){a=a===void 0?!1:a;var b=Object.keys(Cn).filter(function(c){return Cn[c]===!0&&Dn[c]!==void 0&&(a||!Bn.includes(c))}).map(function(c){var d=Dn[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+Zk(yn)+zn+(""+b+"&z=0")}function Jn(){Object.keys(Cn).forEach(function(a){An.indexOf(a)<0&&(Cn[a]=!1)})}
function Kn(a){a=a===void 0?!1:a;if(Kj.la&&fl&&hg.ctid){var b=xn(Xm.Z.Nc);if(tn(b))En||(En=!0,vn(b,Kn));else{var c=In(a),d={destinationId:hg.ctid,endpoint:61};a?fm(d,c,void 0,{Hh:!0},void 0,function(){em(d,c+"&img=1")}):em(d,c);Jn();En=!1}}}var Ln={};function Mn(a){var b=String(a);Ln.hasOwnProperty(b)||(Ln[b]=!0,Gn("csp",Object.keys(Ln).join("~")),Hn("csp",!0),Fn===void 0&&H(171)&&(Fn=y.setTimeout(function(){var c=Cn.csp;Cn.csp=!0;var d=In(!1);Cn.csp=c;Bc(d+"&script=1");Fn=void 0},500)))}
function Nn(){Object.keys(Cn).filter(function(a){return Cn[a]&&!An.includes(a)}).length>0&&Kn(!0)}var On=pb();function Pn(){On=pb()}function Qn(){Gn("v","3");Gn("t","t");Gn("pid",function(){return String(On)});Gn("exp",hk());Ic(y,"pagehide",Nn);y.setInterval(Pn,864E5)};var Rn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Sn=[M.m.ld,M.m.vc,M.m.Wd,M.m.Ob,M.m.uc,M.m.Sa,M.m.Ra,M.m.ib,M.m.pb,M.m.Qb],Tn=!1,Un=!1,Vn={},Wn={};function Xn(){!Un&&Tn&&(Rn.some(function(a){return en.containerScopedDefaults[a]!==1})||Yn("mbc"));Un=!0}function Yn(a){fl&&(Gn(a,"1"),Kn())}function Zn(a,b){if(!Vn[b]&&(Vn[b]=!0,Wn[b]))for(var c=l(Sn),d=c.next();!d.done;d=c.next())if(P(a,d.value)){Yn("erc");break}};function $n(a){db("HEALTH",a)};var ao={aa:{Tm:"aw_user_data_cache",Ph:"cookie_deprecation_label",bo:"fl_user_data_cache",eo:"ga4_user_data_cache",Cf:"ip_geo_data_cache",Ei:"ip_geo_fetch_in_progress",zl:"nb_data",vo:"page_experiment_ids",Lf:"pt_data",Bl:"pt_listener_set",Il:"service_worker_endpoint",Kl:"shared_user_id",Ll:"shared_user_id_requested",ph:"shared_user_id_source"}};var bo=function(a){return We(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(ao.aa);
function co(a,b){b=b===void 0?!1:b;if(bo(a)){var c,d,e=(d=(c=tc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function eo(a,b){var c=co(a,!0);c&&c.set(b)}function fo(a){var b;return(b=co(a))==null?void 0:b.get()}function go(a){var b={},c=co(a);if(!c){c=co(a,!0);if(!c)return;c.set(b)}return c.get()}function ho(a,b){if(typeof b==="function"){var c;return(c=co(a,!0))==null?void 0:c.subscribe(b)}}function io(a,b){var c=co(a);return c?c.unsubscribe(b):!1};var jo={pp:Ni(22,"eyIwIjoiQlIiLCIxIjoiQlItTUEiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20uYnIiLCI0IjoiIiwiNSI6ZmFsc2UsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0")},ko={},lo=!1;function mo(){function a(){c!==void 0&&io(ao.aa.Cf,c);try{var e=fo(ao.aa.Cf);ko=JSON.parse(e)}catch(f){O(123),$n(2),ko={}}lo=!0;b()}var b=no,c=void 0,d=fo(ao.aa.Cf);d?a(d):(c=ho(ao.aa.Cf,a),oo())}
function oo(){function a(c){eo(ao.aa.Cf,c||"{}");eo(ao.aa.Ei,!1)}if(!fo(ao.aa.Ei)){eo(ao.aa.Ei,!0);var b="";try{y.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function po(){var a=jo.pp;try{return JSON.parse(ab(a))}catch(b){return O(123),$n(2),{}}}function qo(){return ko["0"]||""}function ro(){return ko["1"]||""}function so(){var a=!1;a=!!ko["2"];return a}function to(){return ko["6"]!==!1}function uo(){var a="";a=ko["4"]||"";return a}
function vo(){var a=!1;a=!!ko["5"];return a}function wo(){var a="";a=ko["3"]||"";return a};var xo={},yo=Object.freeze((xo[M.m.Fa]=1,xo[M.m.Dg]=1,xo[M.m.Eg]=1,xo[M.m.Mb]=1,xo[M.m.wa]=1,xo[M.m.pb]=1,xo[M.m.qb]=1,xo[M.m.wb]=1,xo[M.m.dd]=1,xo[M.m.Qb]=1,xo[M.m.ib]=1,xo[M.m.Gc]=1,xo[M.m.Ue]=1,xo[M.m.qa]=1,xo[M.m.uk]=1,xo[M.m.Xe]=1,xo[M.m.Pg]=1,xo[M.m.Qg]=1,xo[M.m.Wd]=1,xo[M.m.Jk]=1,xo[M.m.rc]=1,xo[M.m.Zd]=1,xo[M.m.Lk]=1,xo[M.m.Tg]=1,xo[M.m.ii]=1,xo[M.m.Jc]=1,xo[M.m.Kc]=1,xo[M.m.Ra]=1,xo[M.m.ji]=1,xo[M.m.Tb]=1,xo[M.m.rb]=1,xo[M.m.kd]=1,xo[M.m.ld]=1,xo[M.m.lf]=1,xo[M.m.li]=1,xo[M.m.pf]=1,xo[M.m.vc]=
1,xo[M.m.nd]=1,xo[M.m.ah]=1,xo[M.m.Vb]=1,xo[M.m.rd]=1,xo[M.m.Ni]=1,xo));Object.freeze([M.m.Ba,M.m.Wa,M.m.Bb,M.m.xb,M.m.ki,M.m.Sa,M.m.fi,M.m.En]);
var zo={},Ao=Object.freeze((zo[M.m.jn]=1,zo[M.m.kn]=1,zo[M.m.ln]=1,zo[M.m.mn]=1,zo[M.m.nn]=1,zo[M.m.qn]=1,zo[M.m.rn]=1,zo[M.m.sn]=1,zo[M.m.un]=1,zo[M.m.Qd]=1,zo)),Bo={},Co=Object.freeze((Bo[M.m.kk]=1,Bo[M.m.lk]=1,Bo[M.m.Md]=1,Bo[M.m.Nd]=1,Bo[M.m.mk]=1,Bo[M.m.Wc]=1,Bo[M.m.Od]=1,Bo[M.m.jc]=1,Bo[M.m.Fc]=1,Bo[M.m.kc]=1,Bo[M.m.mb]=1,Bo[M.m.Pd]=1,Bo[M.m.ub]=1,Bo[M.m.nk]=1,Bo)),Do=Object.freeze([M.m.Fa,M.m.Ke,M.m.Mb,M.m.Gc,M.m.Wd,M.m.cf,M.m.rb,M.m.nd]),Eo=Object.freeze([].concat(ua(Do))),Fo=Object.freeze([M.m.qb,
M.m.Qg,M.m.lf,M.m.li,M.m.Lg]),Go=Object.freeze([].concat(ua(Fo))),Ho={},Io=(Ho[M.m.V]="1",Ho[M.m.ja]="2",Ho[M.m.W]="3",Ho[M.m.Na]="4",Ho),Jo={},Ko=Object.freeze((Jo.search="s",Jo.youtube="y",Jo.playstore="p",Jo.shopping="h",Jo.ads="a",Jo.maps="m",Jo));function Lo(a){return typeof a!=="object"||a===null?{}:a}function Mo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function No(a){if(a!==void 0&&a!==null)return Mo(a)}function Oo(a){return typeof a==="number"?a:No(a)};function Po(a){return a&&a.indexOf("pending:")===0?Qo(a.substr(8)):!1}function Qo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=zb();return b<c+3E5&&b>c-9E5};var Ro=!1,So=!1,To=!1,Uo=0,Vo=!1,Wo=[];function Xo(a){if(Uo===0)Vo&&Wo&&(Wo.length>=100&&Wo.shift(),Wo.push(a));else if(Yo()){var b=Ni(41,'google.tagmanager.ta.prodqueue'),c=tc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Zo(){$o();Jc(A,"TAProdDebugSignal",Zo)}function $o(){if(!So){So=!0;ap();var a=Wo;Wo=void 0;a==null||a.forEach(function(b){Xo(b)})}}
function ap(){var a=A.documentElement.getAttribute("data-tag-assistant-prod-present");Qo(a)?Uo=1:!Po(a)||Ro||To?Uo=2:(To=!0,Ic(A,"TAProdDebugSignal",Zo,!1),y.setTimeout(function(){$o();Ro=!0},200))}function Yo(){if(!Vo)return!1;switch(Uo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var bp=!1;function cp(a,b){var c=ym(),d=wm();if(Yo()){var e=dp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Xo(e)}}
function ep(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.ab;e=a.isBatched;var f;if(f=Yo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=dp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Xo(h)}}function fp(a){Yo()&&ep(a())}
function dp(a,b){b=b===void 0?{}:b;b.groupId=gp;var c,d=b,e={publicId:hp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'1',messageType:a};c.containerProduct=bp?"OGT":"GTM";c.key.targetRef=ip;return c}var hp="",ip={ctid:"",isDestination:!1},gp;
function jp(a){var b=hg.ctid,c=vm();Uo=0;Vo=!0;ap();gp=a;hp=b;bp=Xj;ip={ctid:b,isDestination:c}};var kp=[M.m.V,M.m.ja,M.m.W,M.m.Na],lp,mp;function np(a){var b=a[M.m.fc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)sb(a,function(d){return function(e,f){if(e!==M.m.fc){var g=Mo(f),h=b[d.cg],m=qo(),n=ro();cn=!0;bn&&db("TAGGING",20);Ym().declare(e,g,h,m,n)}}}(c))}
function op(a){Xn();!mp&&lp&&Yn("crc");mp=!0;var b=a[M.m.wg];b&&O(41);var c=a[M.m.fc];c?O(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)sb(a,function(e){return function(f,g){if(f!==M.m.fc&&f!==M.m.wg){var h=No(g),m=c[e.dg],n=Number(b),p=qo(),q=ro();n=n===void 0?0:n;bn=!0;cn&&db("TAGGING",20);Ym().default(f,h,m,p,q,n,en)}}}(d))}
function pp(a){en.usedContainerScopedDefaults=!0;var b=a[M.m.fc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(ro())&&!c.includes(qo()))return}sb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}en.usedContainerScopedDefaults=!0;en.containerScopedDefaults[d]=e==="granted"?3:2})}
function qp(a,b){Xn();lp=!0;sb(a,function(c,d){var e=Mo(d);bn=!0;cn&&db("TAGGING",20);Ym().update(c,e,en)});ln(b.eventId,b.priorityId)}function rp(a){a.hasOwnProperty("all")&&(en.selectedAllCorePlatformServices=!0,sb(Ko,function(b){en.corePlatformServices[b]=a.all==="granted";en.usedCorePlatformServices=!0}));sb(a,function(b,c){b!=="all"&&(en.corePlatformServices[b]=c==="granted",en.usedCorePlatformServices=!0)})}function sp(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return fn(b)})}
function tp(a,b){kn(a,b)}function up(a,b){nn(a,b)}function vp(a,b){mn(a,b)}function wp(){var a=[M.m.V,M.m.Na,M.m.W];Ym().waitForUpdate(a,500,en)}function xp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Ym().clearTimeout(d,void 0,en)}ln()}function yp(){if(!Zj)for(var a=to()?kk(Kj.Za):kk(Kj.Eb),b=0;b<kp.length;b++){var c=kp[b],d=c,e=a[c]?"granted":"denied";Ym().implicit(d,e)}};var zp=!1,Ap=[];function Bp(){if(!zp){zp=!0;for(var a=Ap.length-1;a>=0;a--)Ap[a]();Ap=[]}};var Cp=y.google_tag_manager=y.google_tag_manager||{};function Dp(a,b){return Cp[a]=Cp[a]||b()}function Ep(){var a=Cm(),b=Gp;Cp[a]=Cp[a]||b}function Hp(){var a=Cp.sequence||1;Cp.sequence=a+1;return a};function Ip(){if(Cp.pscdl!==void 0)fo(ao.aa.Ph)===void 0&&eo(ao.aa.Ph,Cp.pscdl);else{var a=function(c){Cp.pscdl=c;eo(ao.aa.Ph,c)},b=function(){a("error")};try{pc.cookieDeprecationLabel?(a("pending"),pc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Jp=0;function Kp(a){fl&&a===void 0&&Jp===0&&(Gn("mcc","1"),Jp=1)};var Lp={Af:{Xm:"cd",Ym:"ce",Zm:"cf",bn:"cpf",dn:"cu"}};var Mp=/^(?:siloed_)?(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Np=/\s/;
function Op(a,b){if(lb(a)){a=xb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Mp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Np.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Pp(a,b){for(var c={},d=0;d<a.length;++d){var e=Op(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Qp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Rp={},Qp=(Rp[0]=0,Rp[1]=1,Rp[2]=2,Rp[3]=0,Rp[4]=1,Rp[5]=0,Rp[6]=0,Rp[7]=0,Rp);var Sp=Number('')||500,Tp={},Up={},Vp={initialized:11,complete:12,interactive:13},Wp={},Xp=Object.freeze((Wp[M.m.rb]=!0,Wp)),Yp=void 0;function Zp(a,b){if(b.length&&fl){var c;(c=Tp)[a]!=null||(c[a]=[]);Up[a]!=null||(Up[a]=[]);var d=b.filter(function(e){return!Up[a].includes(e)});Tp[a].push.apply(Tp[a],ua(d));Up[a].push.apply(Up[a],ua(d));!Yp&&d.length>0&&(Hn("tdc",!0),Yp=y.setTimeout(function(){Kn();Tp={};Yp=void 0},Sp))}}
function $p(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function aq(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;fd(t)==="object"?u=t[r]:fd(t)==="array"&&(u=t[r]);return u===void 0?Xp[r]:u},f=$p(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=fd(m)==="object"||fd(m)==="array",q=fd(n)==="object"||fd(n)==="array";if(p&&q)aq(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function bq(){Gn("tdc",function(){Yp&&(y.clearTimeout(Yp),Yp=void 0);var a=[],b;for(b in Tp)Tp.hasOwnProperty(b)&&a.push(b+"*"+Tp[b].join("."));return a.length?a.join("!"):void 0},!1)};var cq=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.D=c;this.T=d;this.O=e;this.R=f;this.J=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},dq=function(a,b){var c=[];switch(b){case 3:c.push(a.D);c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 2:c.push(a.D);break;case 1:c.push(a.T);c.push(a.O);c.push(a.R);c.push(a.J);break;case 4:c.push(a.D),c.push(a.T),c.push(a.O),c.push(a.R)}return c},P=function(a,b,c,d){for(var e=l(dq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},eq=function(a){for(var b={},c=dq(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
cq.prototype.getMergedValues=function(a,b,c){function d(n){hd(n)&&sb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=dq(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var fq=function(a){for(var b=[M.m.Pe,M.m.Le,M.m.Me,M.m.Ne,M.m.Oe,M.m.Qe,M.m.Re],c=dq(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},gq=function(a,b){this.eventId=a;this.priorityId=b;this.J={};this.T={};this.D={};this.O={};this.ia={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},hq=function(a,
b){a.J=b;return a},iq=function(a,b){a.T=b;return a},jq=function(a,b){a.D=b;return a},kq=function(a,b){a.O=b;return a},lq=function(a,b){a.ia=b;return a},mq=function(a,b){a.R=b;return a},nq=function(a,b){a.eventMetadata=b||{};return a},oq=function(a,b){a.onSuccess=b;return a},pq=function(a,b){a.onFailure=b;return a},qq=function(a,b){a.isGtmEvent=b;return a},rq=function(a){return new cq(a.eventId,a.priorityId,a.J,a.T,a.D,a.O,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var R={C:{Rj:"accept_by_default",vg:"add_tag_timing",Lh:"allow_ad_personalization",Tj:"batch_on_navigation",Vj:"client_id_source",Be:"consent_event_id",Ce:"consent_priority_id",Fq:"consent_state",ka:"consent_updated",Vc:"conversion_linker_enabled",za:"cookie_options",yg:"create_dc_join",zg:"create_fpm_geo_join",Ag:"create_fpm_signals_join",Ld:"create_google_join",Ee:"em_event",Iq:"endpoint_for_debug",jk:"enhanced_client_id_source",Sh:"enhanced_match_result",od:"euid_mode_enabled",jb:"event_start_timestamp_ms",
kl:"event_usage",eh:"extra_tag_experiment_ids",Pq:"add_parameter",zi:"attribution_reporting_experiment",Ai:"counting_method",fh:"send_as_iframe",Qq:"parameter_order",gh:"parsed_target",co:"ga4_collection_subdomain",ol:"gbraid_cookie_marked",fa:"hit_type",sd:"hit_type_override",jo:"is_config_command",Df:"is_consent_update",Ef:"is_conversion",sl:"is_ecommerce",ud:"is_external_event",Fi:"is_fallback_aw_conversion_ping_allowed",Ff:"is_first_visit",tl:"is_first_visit_conversion",hh:"is_fl_fallback_conversion_flow_allowed",
ce:"is_fpm_encryption",ih:"is_fpm_split",de:"is_gcp_conversion",Gi:"is_google_signals_allowed",vd:"is_merchant_center",jh:"is_new_to_site",kh:"is_server_side_destination",ee:"is_session_start",wl:"is_session_start_conversion",Tq:"is_sgtm_ga_ads_conversion_study_control_group",Uq:"is_sgtm_prehit",xl:"is_sgtm_service_worker",Hi:"is_split_conversion",ko:"is_syn",Gf:"join_id",Ii:"join_elapsed",Hf:"join_timer_sec",ie:"tunnel_updated",Yq:"prehit_for_retry",ar:"promises",er:"record_aw_latency",xc:"redact_ads_data",
je:"redact_click_ids",wo:"remarketing_only",Gl:"send_ccm_parallel_ping",oh:"send_fledge_experiment",hr:"send_ccm_parallel_test_ping",Mf:"send_to_destinations",Mi:"send_to_targets",Hl:"send_user_data_hit",kb:"source_canonical_id",Ja:"speculative",Ml:"speculative_in_message",Nl:"suppress_script_load",Ol:"syn_or_mod",Rl:"transient_ecsid",Nf:"transmission_type",Ta:"user_data",kr:"user_data_from_automatic",lr:"user_data_from_automatic_getter",me:"user_data_from_code",sh:"user_data_from_manual",Tl:"user_data_mode",
Of:"user_id_updated"}};var sq={Sm:Number("5"),Hr:Number("")},tq=[],uq=!1;function vq(a){tq.push(a)}var wq="?id="+hg.ctid,xq=void 0,yq={},zq=void 0,Aq=new function(){var a=5;sq.Sm>0&&(a=sq.Sm);this.J=a;this.D=0;this.O=[]},Bq=1E3;
function Cq(a,b){var c=xq;if(c===void 0)if(b)c=Hp();else return"";for(var d=[Zk("https://www.googletagmanager.com"),"/a",wq],e=l(tq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Kd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Dq(){if(Kj.la&&(zq&&(y.clearTimeout(zq),zq=void 0),xq!==void 0&&Eq)){var a=xn(Xm.Z.Nc);if(tn(a))uq||(uq=!0,vn(a,Dq));else{var b;if(!(b=yq[xq])){var c=Aq;b=c.D<c.J?!1:zb()-c.O[c.D%c.J]<1E3}if(b||Bq--<=0)O(1),yq[xq]=!0;else{var d=Aq,e=d.D++%d.J;d.O[e]=zb();var f=Cq(!0);em({destinationId:hg.ctid,endpoint:56,eventId:xq},f);uq=Eq=!1}}}}function Fq(){if(el&&Kj.la){var a=Cq(!0,!0);em({destinationId:hg.ctid,endpoint:56,eventId:xq},a)}}var Eq=!1;
function Gq(a){yq[a]||(a!==xq&&(Dq(),xq=a),Eq=!0,zq||(zq=y.setTimeout(Dq,500)),Cq().length>=2022&&Dq())}var Hq=pb();function Iq(){Hq=pb()}function Jq(){return[["v","3"],["t","t"],["pid",String(Hq)]]};var Kq={};function Lq(a,b,c){el&&a!==void 0&&(Kq[a]=Kq[a]||[],Kq[a].push(c+b),Gq(a))}function Mq(a){var b=a.eventId,c=a.Kd,d=[],e=Kq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Kq[b];return d};function Nq(a,b,c,d){var e=Op(Dm(a),!0);e&&Oq.register(e,b,c,d)}function Pq(a,b,c,d){var e=Op(c,d.isGtmEvent);e&&(Wj&&(d.deferrable=!0),Oq.push("event",[b,a],e,d))}function Qq(a,b,c,d){var e=Op(c,d.isGtmEvent);e&&Oq.push("get",[a,b],e,d)}function Rq(a){var b=Op(Dm(a),!0),c;b?c=Sq(Oq,b).D:c={};return c}function Tq(a,b){var c=Op(Dm(a),!0);c&&Uq(Oq,c,b)}
var Vq=function(){this.T={};this.D={};this.J={};this.ia=null;this.R={};this.O=!1;this.status=1},Wq=function(a,b,c,d){this.J=zb();this.D=b;this.args=c;this.messageContext=d;this.type=a},Xq=function(){this.destinations={};this.D={};this.commands=[]},Sq=function(a,b){var c=b.destinationId;tm||(c=Im(c));return a.destinations[c]=a.destinations[c]||new Vq},Yq=function(a,b,c,d){if(d.D){var e=Sq(a,d.D),f=e.ia;if(f){var g=d.D.id;tm||(g=Im(g));var h=id(c,null),m=id(e.T[g],null),n=id(e.R,null),p=id(e.D,null),
q=id(a.D,null),r={};if(el)try{r=id(mk,null)}catch(x){O(72)}var t=d.D.prefix,u=function(x){Lq(d.messageContext.eventId,t,x)},v=rq(qq(pq(oq(nq(lq(kq(mq(jq(iq(hq(new gq(d.messageContext.eventId,d.messageContext.priorityId),h),m),n),p),q),r),d.messageContext.eventMetadata),function(){if(u){var x=u;u=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(u){var x=u;u=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),
w=function(){try{Lq(d.messageContext.eventId,t,"1");var x=d.type,z=d.D.id;if(fl&&x==="config"){var B,C=(B=Op(z))==null?void 0:B.ids;if(!(C&&C.length>1)){var F,G=tc("google_tag_data",{});G.td||(G.td={});F=G.td;var I=id(v.R);id(v.D,I);var K=[],U;for(U in F)F.hasOwnProperty(U)&&aq(F[U],I).length&&K.push(U);K.length&&(Zp(z,K),db("TAGGING",Vp[A.readyState]||14));F[z]=I}}f(d.D.id,b,d.J,v)}catch(Q){Lq(d.messageContext.eventId,t,"4")}};b==="gtag.get"?w():vn(e.la,w)}}};
Xq.prototype.register=function(a,b,c,d){var e=Sq(this,a);e.status!==3&&(e.ia=b,e.status=3,e.la=xn(c),Uq(this,a,d||{}),this.flush())};
Xq.prototype.push=function(a,b,c,d){c!==void 0&&(Sq(this,c).status===1&&(Sq(this,c).status=2,this.push("require",[{}],c,{})),Sq(this,c).O&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.C.Mf]||(d.eventMetadata[R.C.Mf]=[c.destinationId]),d.eventMetadata[R.C.Mi]||(d.eventMetadata[R.C.Mi]=[c.id]));this.commands.push(new Wq(a,c,b,d));d.deferrable||this.flush()};
Xq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Pc:void 0,yh:void 0}){var f=this.commands[0],g=f.D;if(f.messageContext.deferrable)!g||Sq(this,g).O?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Sq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];sb(h,function(t,u){id(Gb(t,u),b.D)});Ij(h,!0);break;case "config":var m=Sq(this,g);
e.Pc={};sb(f.args[0],function(t){return function(u,v){id(Gb(u,v),t.Pc)}}(e));var n=!!e.Pc[M.m.nd];delete e.Pc[M.m.nd];var p=g.destinationId===g.id;Ij(e.Pc,!0);n||(p?m.R={}:m.T[g.id]={});m.O&&n||Yq(this,M.m.ra,e.Pc,f);m.O=!0;p?id(e.Pc,m.R):(id(e.Pc,m.T[g.id]),O(70));d=!0;break;case "event":e.yh={};sb(f.args[0],function(t){return function(u,v){id(Gb(u,v),t.yh)}}(e));Ij(e.yh);Yq(this,f.args[1],e.yh,f);break;case "get":var q={},r=(q[M.m.qc]=f.args[0],q[M.m.Hc]=f.args[1],q);Yq(this,M.m.Ab,r,f)}this.commands.shift();
Zq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Zq=function(a,b){if(b.type!=="require")if(b.D)for(var c=Sq(a,b.D).J[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.J)for(var g=f.J[b.type]||[],h=0;h<g.length;h++)g[h]()}},Uq=function(a,b,c){var d=id(c,null);id(Sq(a,b).D,d);Sq(a,b).D=d},Oq=new Xq;function $q(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function ar(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function br(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Gl(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=mc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}ar(e,"load",f);ar(e,"error",f)};$q(e,"load",f);$q(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function cr(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Dl(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});dr(c,b)}
function dr(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else br(c,a,b===void 0?!1:b,d===void 0?!1:d)};var er=function(){this.ia=this.ia;this.R=this.R};er.prototype.ia=!1;er.prototype.dispose=function(){this.ia||(this.ia=!0,this.O())};er.prototype[Symbol.dispose]=function(){this.dispose()};er.prototype.addOnDisposeCallback=function(a,b){this.ia?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};er.prototype.O=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function fr(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var gr=function(a,b){b=b===void 0?{}:b;er.call(this);this.D=null;this.la={};this.Eb=0;this.T=null;this.J=a;var c;this.Za=(c=b.timeoutMs)!=null?c:500;var d;this.Ca=(d=b.vr)!=null?d:!1};sa(gr,er);gr.prototype.O=function(){this.la={};this.T&&(ar(this.J,"message",this.T),delete this.T);delete this.la;delete this.J;delete this.D;er.prototype.O.call(this)};var ir=function(a){return typeof a.J.__tcfapi==="function"||hr(a)!=null};
gr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ca},d=hl(function(){return a(c)}),e=0;this.Za!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Za));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=fr(c),c.internalBlockOnErrors=b.Ca,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{jr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};gr.prototype.removeEventListener=function(a){a&&a.listenerId&&jr(this,"removeEventListener",null,a.listenerId)};
var lr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=kr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&kr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?kr(a.purpose.legitimateInterests,
b)&&kr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},kr=function(a,b){return!(!a||!a[b])},jr=function(a,b,c,d){c||(c=function(){});var e=a.J;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(hr(a)){mr(a);var g=++a.Eb;a.la[g]=c;if(a.D){var h={};a.D.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},hr=function(a){if(a.D)return a.D;a.D=El(a.J,"__tcfapiLocator");return a.D},mr=function(a){if(!a.T){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.la[d.callId](d.returnValue,d.success)}catch(e){}};a.T=b;$q(a.J,"message",b)}},nr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=fr(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(cr({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var or={1:0,3:0,4:0,7:3,9:3,10:3};function pr(){return Dp("tcf",function(){return{}})}var qr=function(){return new gr(y,{timeoutMs:-1})};
function rr(){var a=pr(),b=qr();ir(b)&&!sr()&&!tr()&&O(124);if(!a.active&&ir(b)){sr()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Ym().active=!0,a.tcString="tcunavailable");wp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)ur(a),xp([M.m.V,M.m.Na,M.m.W]),Ym().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,tr()&&(a.active=!0),!vr(c)||sr()||tr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in or)or.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(vr(c)){var g={},h;for(h in or)if(or.hasOwnProperty(h))if(h==="1"){var m,n=c,p={op:!0};p=p===void 0?{}:p;m=nr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.op)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?lr(n,"1",0):!0:!1;g["1"]=m}else g[h]=lr(c,h,or[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[M.m.V]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(xp([M.m.V,M.m.Na,M.m.W]),Ym().active=!0):(r[M.m.Na]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[M.m.W]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":xp([M.m.W]),qp(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:wr()||""}))}}else xp([M.m.V,M.m.Na,M.m.W])})}catch(c){ur(a),xp([M.m.V,M.m.Na,M.m.W]),Ym().active=!0}}}
function ur(a){a.type="e";a.tcString="tcunavailable"}function vr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function sr(){return y.gtag_enable_tcf_support===!0}function tr(){return pr().enableAdvertiserConsentMode===!0}function wr(){var a=pr();if(a.active)return a.tcString}function xr(){var a=pr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function yr(a){if(!or.hasOwnProperty(String(a)))return!0;var b=pr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var zr=[M.m.V,M.m.ja,M.m.W,M.m.Na],Ar={},Br=(Ar[M.m.V]=1,Ar[M.m.ja]=2,Ar);function Cr(a){if(a===void 0)return 0;switch(P(a,M.m.Fa)){case void 0:return 1;case !1:return 3;default:return 2}}function Dr(){return H(182)?(H(183)?Ti.vp:Ti.wp).indexOf(ro())!==-1&&pc.globalPrivacyControl===!0:ro()==="US-CO"&&pc.globalPrivacyControl===!0}
function Er(a){if(Dr())return!1;var b=Cr(a);if(b===3)return!1;switch(gn(M.m.Na)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}function Fr(){return jn()||!fn(M.m.V)||!fn(M.m.ja)}function Gr(){var a={},b;for(b in Br)Br.hasOwnProperty(b)&&(a[Br[b]]=gn(b));return"G1"+Ze(a[1]||0)+Ze(a[2]||0)}var Hr={},Ir=(Hr[M.m.V]=0,Hr[M.m.ja]=1,Hr[M.m.W]=2,Hr[M.m.Na]=3,Hr);
function Jr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Kr(a){for(var b="1",c=0;c<zr.length;c++){var d=b,e,f=zr[c],g=en.delegatedConsentTypes[f];e=g===void 0?0:Ir.hasOwnProperty(g)?12|Ir[g]:8;var h=Ym();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Jr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Jr(m.declare)<<4|Jr(m.default)<<2|Jr(m.update)])}var n=b,p=(Dr()?1:0)<<3,q=(jn()?1:0)<<2,r=Cr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[en.containerScopedDefaults.ad_storage<<4|en.containerScopedDefaults.analytics_storage<<2|en.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(en.usedContainerScopedDefaults?1:0)<<2|en.containerScopedDefaults.ad_personalization]}
function Lr(){if(!fn(M.m.W))return"-";for(var a=Object.keys(Ko),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=en.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Ko[m])}(en.usedCorePlatformServices?en.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Mr(){return to()||(sr()||tr())&&xr()==="1"?"1":"0"}function Nr(){return(to()?!0:!(!sr()&&!tr())&&xr()==="1")||!fn(M.m.W)}
function Or(){var a="0",b="0",c;var d=pr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=pr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;to()&&(h|=1);xr()==="1"&&(h|=2);sr()&&(h|=4);var m;var n=pr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Ym().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Pr(){return ro()==="US-CO"};function Qr(){var a=!1;return a};var Rr;function Sr(){if(sc===null)return 0;var a=Xc();if(!a)return 0;var b=a.getEntriesByName(sc,"resource")[0];if(!b)return 0;switch(b.xr){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Tr={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Ur(a){a=a===void 0?{}:a;var b=hg.ctid.split("-")[0].toUpperCase(),c={ctid:hg.ctid,Ij:Qj,Mj:Pj,lm:sm.he?2:1,tq:a.Hm,oe:hg.canonicalContainerId};if(H(204)){var d;c.Mo=(d=Rr)!=null?d:Rr=Sr()}c.oe!==a.Oa&&(c.Oa=a.Oa);var e=Fm();c.xm=e?e.canonicalContainerId:void 0;Xj?(c.Uc=Tr[b],c.Uc||(c.Uc=0)):c.Uc=Zj?13:10;Kj.D?(c.Sc=0,c.Yl=2):Kj.O?c.Sc=1:Qr()?c.Sc=2:c.Sc=3;var f={};f[6]=tm;Kj.J===2?f[7]=!0:Kj.J===1&&(f[2]=!0);if(sc){var g=Kk(Qk(sc),"host");g&&(f[8]=g.match(/^(www\.)?googletagmanager\.com$/)===
null)}c.am=f;return cf(c,a.th)}
function Vr(){if(!H(192))return Ur();if(H(193))return cf({Ij:Qj,Mj:Pj});var a=hg.ctid.split("-")[0].toUpperCase(),b={ctid:hg.ctid,Ij:Qj,Mj:Pj,lm:sm.he?2:1,oe:hg.canonicalContainerId},c=Fm();b.xm=c?c.canonicalContainerId:void 0;Xj?(b.Uc=Tr[a],b.Uc||(b.Uc=0)):b.Uc=Zj?13:10;Kj.D?(b.Sc=0,b.Yl=2):Kj.O?b.Sc=1:Qr()?b.Sc=2:b.Sc=3;var d={};d[6]=tm;Kj.J===2?d[7]=!0:Kj.J===1&&(d[2]=!0);if(sc){var e=Kk(Qk(sc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.am=d;return cf(b)};function Wr(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var Xr={P:{xo:0,Sj:1,xg:2,Yj:3,Nh:4,Wj:5,Xj:6,Zj:7,Oh:8,il:9,fl:10,yi:11,jl:12,bh:13,nl:14,Jf:15,uo:16,ke:17,Ri:18,Si:19,Ti:20,Pl:21,Ui:22,Qh:23,ik:24}};Xr.P[Xr.P.xo]="RESERVED_ZERO";Xr.P[Xr.P.Sj]="ADS_CONVERSION_HIT";Xr.P[Xr.P.xg]="CONTAINER_EXECUTE_START";Xr.P[Xr.P.Yj]="CONTAINER_SETUP_END";Xr.P[Xr.P.Nh]="CONTAINER_SETUP_START";Xr.P[Xr.P.Wj]="CONTAINER_BLOCKING_END";Xr.P[Xr.P.Xj]="CONTAINER_EXECUTE_END";Xr.P[Xr.P.Zj]="CONTAINER_YIELD_END";Xr.P[Xr.P.Oh]="CONTAINER_YIELD_START";Xr.P[Xr.P.il]="EVENT_EXECUTE_END";
Xr.P[Xr.P.fl]="EVENT_EVALUATION_END";Xr.P[Xr.P.yi]="EVENT_EVALUATION_START";Xr.P[Xr.P.jl]="EVENT_SETUP_END";Xr.P[Xr.P.bh]="EVENT_SETUP_START";Xr.P[Xr.P.nl]="GA4_CONVERSION_HIT";Xr.P[Xr.P.Jf]="PAGE_LOAD";Xr.P[Xr.P.uo]="PAGEVIEW";Xr.P[Xr.P.ke]="SNIPPET_LOAD";Xr.P[Xr.P.Ri]="TAG_CALLBACK_ERROR";Xr.P[Xr.P.Si]="TAG_CALLBACK_FAILURE";Xr.P[Xr.P.Ti]="TAG_CALLBACK_SUCCESS";Xr.P[Xr.P.Pl]="TAG_EXECUTE_END";Xr.P[Xr.P.Ui]="TAG_EXECUTE_START";Xr.P[Xr.P.Qh]="CUSTOM_PERFORMANCE_START";Xr.P[Xr.P.ik]="CUSTOM_PERFORMANCE_END";var Yr=[],Zr={},$r={};var as=["1"];function bs(a){return a.origin!=="null"};function cs(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ka(10)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};function ds(a,b,c,d){if(!es(d))return[];if(Yr.includes("1")){var e;(e=Xc())==null||e.mark("1-"+Xr.P.Qh+"-"+($r["1"]||0))}var f=cs(a,String(b||fs()),c);if(Yr.includes("1")){var g="1-"+Xr.P.ik+"-"+($r["1"]||0),h={start:"1-"+Xr.P.Qh+"-"+($r["1"]||0),end:g},m;(m=Xc())==null||m.mark(g);var n,p,q=(p=(n=Xc())==null?void 0:n.measure(g,h))==null?void 0:p.duration;q!==void 0&&($r["1"]=($r["1"]||0)+1,Zr["1"]=q+(Zr["1"]||0))}return f}
function gs(a,b,c,d,e){if(es(e)){var f=hs(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=is(f,function(g){return g.Wo},b);if(f.length===1)return f[0];f=is(f,function(g){return g.Yp},c);return f[0]}}}function js(a,b,c,d){var e=fs(),f=window;bs(f)&&(f.document.cookie=a);var g=fs();return e!==g||c!==void 0&&ds(b,g,!1,d).indexOf(c)>=0}
function ks(a,b,c,d){function e(w,x,z){if(z==null)return delete h[x],w;h[x]=z;return w+"; "+x+"="+z}function f(w,x){if(x==null)return w;h[x]=!0;return w+"; "+x}if(!es(c.Cc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=ls(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Up);g=e(g,"samesite",c.kq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ms(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!ns(u,c.path)&&js(v,a,b,c.Cc))return 0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return ns(n,c.path)?1:js(g,a,b,c.Cc)?0:1}function os(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");return ks(a,b,c)}
function is(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function hs(a,b,c){for(var d=[],e=ds(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Oo:e[f],Po:g.join("."),Wo:Number(n[0])||1,Yp:Number(n[1])||1})}}}return d}function ls(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var ps=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,qs=/(^|\.)doubleclick\.net$/i;function ns(a,b){return a!==void 0&&(qs.test(window.document.location.hostname)||b==="/"&&ps.test(a))}function rs(a){if(!a)return 1;var b=a;Ka(7)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function ss(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function ts(a,b){var c=""+rs(a),d=ss(b);d>1&&(c+="-"+d);return c}
var fs=function(){return bs(window)?window.document.cookie:""},es=function(a){return a&&Ka(8)?(Array.isArray(a)?a:[a]).every(function(b){return hn(b)&&fn(b)}):!0},ms=function(){var a=[],b=window.document.location.hostname.split(".");if(b.length===4){var c=b[b.length-1];if(Number(c).toString()===c)return["none"]}for(var d=b.length-2;d>=0;d--)a.push(b.slice(d).join("."));var e=window.document.location.hostname;qs.test(e)||ps.test(e)||a.push("none");return a};function us(a){var b=Math.round(Math.random()*2147483647);return a?String(b^Wr(a)&2147483647):String(b)}function vs(a){return[us(a),Math.round(zb()/1E3)].join(".")}function ws(a,b,c,d,e){var f=rs(b),g;return(g=gs(a,f,ss(c),d,e))==null?void 0:g.Po};function xs(a,b,c,d){var e,f=Number(a.Ac!=null?a.Ac:void 0);f!==0&&(e=new Date((b||zb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Cc:d}};var ys=["ad_storage","ad_user_data"];function zs(a,b){if(!a)return db("TAGGING",32),10;if(b===null||b===void 0||b==="")return db("TAGGING",33),11;var c=As(!1);if(c.error!==0)return db("TAGGING",34),c.error;if(!c.value)return db("TAGGING",35),2;c.value[a]=b;var d=Bs(c);d!==0&&db("TAGGING",36);return d}
function Cs(a){if(!a)return db("TAGGING",27),{error:10};var b=As();if(b.error!==0)return db("TAGGING",29),b;if(!b.value)return db("TAGGING",30),{error:2};if(!(a in b.value))return db("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(db("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function As(a){a=a===void 0?!0:a;if(!fn(ys))return db("TAGGING",43),{error:3};try{if(!y.localStorage)return db("TAGGING",44),{error:1}}catch(f){return db("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=y.localStorage.getItem("_gcl_ls")}catch(f){return db("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return db("TAGGING",47),{error:12}}}catch(f){return db("TAGGING",48),{error:8}}if(b.schema!=="gcl")return db("TAGGING",49),{error:4};
if(b.version!==1)return db("TAGGING",50),{error:5};try{var e=Ds(b);a&&e&&Bs({value:b,error:0})}catch(f){return db("TAGGING",48),{error:8}}return{value:b,error:0}}
function Ds(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,db("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Ds(a[e.value])||c;return c}return!1}
function Bs(a){if(a.error)return a.error;if(!a.value)return db("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return db("TAGGING",52),6}try{y.localStorage.setItem("_gcl_ls",c)}catch(d){return db("TAGGING",53),7}return 0};function Es(){if(!Fs())return-1;var a=Gs();return a!==-1&&Hs(a+1)?a+1:-1}function Gs(){if(!Fs())return-1;var a=Cs("gcl_ctr");if(!a||a.error!==0||!a.value||typeof a.value!=="object")return-1;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return-1;var c=b.value.value;return c==null||Number.isNaN(c)?-1:Number(c)}catch(d){return-1}}function Fs(){return fn(["ad_storage","ad_user_data"])?!0:!1}
function Hs(a,b){b=b||{};var c=zb();return zs("gcl_ctr",{value:{value:a,creationTimeMs:c},expires:Number(xs(b,c,!0).expires)})===0?!0:!1};var Is;function Js(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ks,d=Ls,e=Ms();if(!e.init){Ic(A,"mousedown",a);Ic(A,"keyup",a);Ic(A,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Ns(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ms().decorators.push(f)}
function Os(a,b,c){for(var d=Ms().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==A.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Cb(e,g.callback())}}return e}
function Ms(){var a=tc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Ps=/(.*?)\*(.*?)\*(.*)/,Qs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Rs=/^(?:www\.|m\.|amp\.)+/,Ss=/([^?#]+)(\?[^#]*)?(#.*)?/;function Ts(a){var b=Ss.exec(a);if(b)return{Bj:b[1],query:b[2],fragment:b[3]}}function Us(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Vs(a,b){var c=[pc.userAgent,(new Date).getTimezoneOffset(),pc.userLanguage||pc.language,Math.floor(zb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Is)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Is=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Is[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Ws(a){return function(b){var c=Qk(y.location.href),d=c.search.replace("?",""),e=Hk(d,"_gl",!1,!0)||"";b.query=Xs(e)||{};var f=Kk(c,"fragment"),g;var h=-1;if(Eb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Xs(g||"")||{};a&&Ys(c,d,f)}}function Zs(a,b){var c=Us(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Ys(a,b,c){function d(g,h){var m=Zs("_gl",g);m.length&&(m=h+m);return m}if(oc&&oc.replaceState){var e=Us("_gl");if(e.test(b)||e.test(c)){var f=Kk(a,"path");b=d(b,"?");c=d(c,"#");oc.replaceState({},"",""+f+b+c)}}}function $s(a,b){var c=Ws(!!b),d=Ms();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Cb(e,f.query),a&&Cb(e,f.fragment));return e}
var Xs=function(a){try{var b=at(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=ab(d[e+1]);c[f]=g}db("TAGGING",6);return c}}catch(h){db("TAGGING",8)}};function at(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Ps.exec(d);if(f){c=f;break a}d=Jk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Vs(h,p)){m=!0;break a}m=!1}if(m)return h;db("TAGGING",7)}}}
function bt(a,b,c,d,e){function f(p){p=Zs(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Ts(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.Bj+h+m}
function ct(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var x=n[w];x!==void 0&&x===x&&x!==null&&x.toString()!=="[object Object]"&&(v.push(w),v.push($a(String(x))))}var z=v.join("*");u=["1",Vs(z),z].join("*");d?(Ka(3)||Ka(1)||!p)&&dt("_gl",u,a,p,q):et("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Os(b,1,d),f=Os(b,2,d),g=Os(b,4,d),h=Os(b,3,d);c(e,!1,!1);c(f,!0,!1);Ka(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
ft(m,h[m],a)}function ft(a,b,c){c.tagName.toLowerCase()==="a"?et(a,b,c):c.tagName.toLowerCase()==="form"&&dt(a,b,c)}function et(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ka(5)||d)){var h=y.location.href,m=Ts(c.href),n=Ts(h);g=!(m&&n&&m.Bj===n.Bj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=bt(a,b,c.href,d,e);dc.test(p)&&(c.href=p)}}
function dt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=bt(a,b,f,d,e);dc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=A.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ks(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||ct(e,e.hostname)}}catch(g){}}function Ls(a){try{var b=a.getAttribute("action");if(b){var c=Kk(Qk(b),"host");ct(a,c)}}catch(d){}}function gt(a,b,c,d){Js();var e=c==="fragment"?2:1;d=!!d;Ns(a,b,e,d,!1);e===2&&db("TAGGING",23);d&&db("TAGGING",24)}
function ht(a,b){Js();Ns(a,[Mk(y.location,"host",!0)],b,!0,!0)}function it(){var a=A.location.hostname,b=Qs.exec(A.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Jk(f[2])||"":Jk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Rs,""),m=e.replace(Rs,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function jt(a,b){return a===!1?!1:a||b||it()};var kt=["1"],lt={},mt={};function nt(a,b){b=b===void 0?!0:b;var c=ot(a.prefix);if(lt[c])pt(a);else if(qt(c,a.path,a.domain)){var d=mt[ot(a.prefix)]||{id:void 0,Fh:void 0};b&&rt(a,d.id,d.Fh);pt(a)}else{var e=Sk("auiddc");if(e)db("TAGGING",17),lt[c]=e;else if(b){var f=ot(a.prefix),g=vs();st(f,g,a);qt(c,a.path,a.domain);pt(a,!0)}}}
function pt(a,b){if((b===void 0?0:b)&&Fs()){var c=As(!1);c.error!==0?db("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Bs(c)!==0&&db("TAGGING",41)):db("TAGGING",40):db("TAGGING",39)}Fs()&&Gs()===-1&&Hs(0,a)}function rt(a,b,c){var d=ot(a.prefix),e=lt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(zb()/1E3)));st(d,h,a,g*1E3)}}}}
function st(a,b,c,d){var e;e=["1",ts(c.domain,c.path),b].join(".");var f=xs(c,d);f.Cc=tt();os(a,e,f)}function qt(a,b,c){var d=ws(a,b,c,kt,tt());if(!d)return!1;ut(a,d);return!0}function ut(a,b){var c=b.split(".");c.length===5?(lt[a]=c.slice(0,2).join("."),mt[a]={id:c.slice(2,4).join("."),Fh:Number(c[4])||0}):c.length===3?mt[a]={id:c.slice(0,2).join("."),Fh:Number(c[2])||0}:lt[a]=b}function ot(a){return(a||"_gcl")+"_au"}
function vt(a){function b(){fn(c)&&a()}var c=tt();mn(function(){b();fn(c)||nn(b,c)},c)}function wt(a){var b=$s(!0),c=ot(a.prefix);vt(function(){var d=b[c];if(d){ut(c,d);var e=Number(lt[c].split(".")[1])*1E3;if(e){db("TAGGING",16);var f=xs(a,e);f.Cc=tt();var g=["1",ts(a.domain,a.path),d].join(".");os(c,g,f)}}})}function xt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=ws(a,e.path,e.domain,kt,tt());h&&(g[a]=h);return g};vt(function(){gt(f,b,c,d)})}
function tt(){return["ad_storage","ad_user_data"]};function zt(a){for(var b=[],c=A.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Pj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function At(a,b){var c=zt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Pj]||(d[c[e].Pj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Pj].push(g)}}return d};var Bt={},Ct=(Bt.k={da:/^[\w-]+$/},Bt.b={da:/^[\w-]+$/,Jj:!0},Bt.i={da:/^[1-9]\d*$/},Bt.h={da:/^\d+$/},Bt.t={da:/^[1-9]\d*$/},Bt.d={da:/^[A-Za-z0-9_-]+$/},Bt.j={da:/^\d+$/},Bt.u={da:/^[1-9]\d*$/},Bt.l={da:/^[01]$/},Bt.o={da:/^[1-9]\d*$/},Bt.g={da:/^[01]$/},Bt.s={da:/^.+$/},Bt);var Dt={},Ht=(Dt[5]={Kh:{2:Et},uj:"2",uh:["k","i","b","u"]},Dt[4]={Kh:{2:Et,GCL:Ft},uj:"2",uh:["k","i","b"]},Dt[2]={Kh:{GS2:Et,GS1:Gt},uj:"GS2",uh:"sogtjlhd".split("")},Dt);function It(a,b,c){var d=Ht[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Kh[e];if(f)return f(a,b)}}}
function Et(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Ht[b];if(f){for(var g=f.uh,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Ct[p];r&&(r.Jj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Jt(a,b,c){var d=Ht[b];if(d)return[d.uj,c||"1",Kt(a,b)].join(".")}
function Kt(a,b){var c=Ht[b];if(c){for(var d=[],e=l(c.uh),f=e.next();!f.done;f=e.next()){var g=f.value,h=Ct[g];if(h){var m=a[g];if(m!==void 0)if(h.Jj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Ft(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Gt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Lt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Mt(a,b,c){if(Ht[b]){for(var d=[],e=ds(a,void 0,void 0,Lt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=It(g.value,b,c);h&&d.push(Nt(h))}return d}}function Ot(a,b,c,d,e){d=d||{};var f=ts(d.domain,d.path),g=Jt(b,c,f);if(!g)return 1;var h=xs(d,e,void 0,Lt.get(c));return os(a,g,h)}function Pt(a,b){var c=b.da;return typeof c==="function"?c(a):c.test(a)}
function Nt(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=Ct[e];d.Tf?d.Tf.Jj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Pt(h,g.Tf)}}(d)):void 0:typeof f==="string"&&Pt(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Qt=function(){this.value=0};Qt.prototype.set=function(a){return this.value|=1<<a};var Rt=function(a,b){b<=0||(a.value|=1<<b-1)};Qt.prototype.get=function(){return this.value};Qt.prototype.clear=function(a){this.value&=~(1<<a)};Qt.prototype.clearAll=function(){this.value=0};Qt.prototype.equals=function(a){return this.value===a.value};function St(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Tt(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]}
function Ut(a){if(!a||a.length<50||a.length>200)return!1;var b=St(a),c;if(b)a:{if(b&&b.length!==0){var d=0;try{for(;d<b.length;){var e=Tt(b,d);if(e===void 0)break;var f=l(e),g=f.next().value,h=f.next().value,m=g,n=h,p=m&7;if(m>>3===16382){if(p!==0)break;var q=Tt(b,n);if(q===void 0)break;c=l(q).next().value===1;break a}var r;b:{var t=void 0;switch(p){case 0:r=(t=Tt(b,n))==null?void 0:t[1];break b;case 1:r=n+8;break b;case 2:var u=Tt(b,n);if(u===void 0)break;var v=l(u),w=v.next().value;r=v.next().value+
w;break b;case 5:r=n+4;break b}r=void 0}var x=r;if(x===void 0||x>b.length)break;d=x}}catch(z){}}c=!1}else c=!1;return c};function Vt(){var a=String,b=y.location.hostname,c=y.location.pathname,d=b=Mb(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Mb(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(Wr((""+b+e).toLowerCase()))};var Wt={},Xt=(Wt.gclid=!0,Wt.dclid=!0,Wt.gbraid=!0,Wt.wbraid=!0,Wt),Yt=/^\w+$/,Zt=/^[\w-]+$/,$t={},au=($t.aw="_aw",$t.dc="_dc",$t.gf="_gf",$t.gp="_gp",$t.gs="_gs",$t.ha="_ha",$t.ag="_ag",$t.gb="_gb",$t),bu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,cu=/^www\.googleadservices\.com$/;function du(){return["ad_storage","ad_user_data"]}function eu(a){return!Ka(8)||fn(a)}function fu(a,b){function c(){var d=eu(b);d&&a();return d}mn(function(){c()||nn(c,b)},b)}
function gu(a){return hu(a).map(function(b){return b.gclid})}function iu(a){return ju(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function ju(a){var b=ku(a.prefix),c=lu("gb",b),d=lu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=hu(c).map(e("gb")),g=mu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function nu(a,b,c,d,e,f){var g=ob(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Ed=f),g.labels=ou(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Ed:f})}function mu(a){for(var b=Mt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=g.k,m=g.b,n=pu(f);if(n){var p=void 0;Ka(9)&&(p=f.u);nu(c,"2",h,n,m||[],p)}}return c.sort(function(q,r){return r.timestamp-q.timestamp})}
function hu(a){for(var b=[],c=ds(a,A.cookie,void 0,du()),d=l(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);if(f!=null){var g=f;nu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ru(b)}function su(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function tu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ma&&b.Ma&&h.Ma.equals(b.Ma)&&(e=h)}if(d){var m,n,p=(m=d.Ma)!=null?m:new Qt,q=(n=b.Ma)!=null?n:new Qt;p.value|=q.value;d.Ma=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Ed=b.Ed);d.labels=su(d.labels||[],b.labels||[]);d.zb=su(d.zb||[],b.zb||[])}else c&&e?Object.assign(e,b):a.push(b)}
function uu(a){if(!a)return new Qt;var b=new Qt;if(a===1)return Rt(b,2),Rt(b,3),b;Rt(b,a);return b}
function vu(){var a=Cs("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Zt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Qt;typeof e==="number"?g=uu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ma:g,zb:[2]}}catch(h){return null}}
function wu(){var a=Cs("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Zt))return b;var f=new Qt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ma:f,zb:[2]});return b},[])}catch(b){return null}}
function xu(a){for(var b=[],c=ds(a,A.cookie,void 0,du()),d=l(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);f!=null&&(f.Ed=void 0,f.Ma=new Qt,f.zb=[1],tu(b,f))}var g=vu();g&&(g.Ed=void 0,g.zb=g.zb||[2],tu(b,g));if(Ka(12)){var h=wu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Ed=void 0;p.zb=p.zb||[2];tu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ru(b)}
function ou(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function ku(a){return a&&typeof a==="string"&&a.match(Yt)?a:"_gcl"}function yu(a,b){if(a){var c={value:a,Ma:new Qt};Rt(c.Ma,b);return c}}
function zu(a,b,c,d){var e=Qk(a),f=Kk(e,"query",!1,void 0,"gclsrc"),g=yu(Kk(e,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!g||!f)){var h=e.hash.replace("#","");g||(g=yu(Hk(h,"gclid",!1),3));f||(f=Hk(h,"gclsrc",!1))}var m;if(d&&!Ut((m=g)==null?void 0:m.value)){var n;a:{for(var p=Ik(Kk(e,"query")),q=l(Object.keys(p)),r=q.next();!r.done;r=q.next()){var t=r.value;if(!Xt[t]){var u=p[t][0]||"";if(Ut(u)){n=u;break a}}}n=void 0}var v=n,w;v&&v!==((w=g)==null?void 0:w.value)&&(g=yu(v,7))}return!g||f!==void 0&&
f!=="aw"&&f!=="aw.ds"?[]:[g]}function Au(a,b){var c=Qk(a),d=Kk(c,"query",!1,void 0,"gclid"),e=Kk(c,"query",!1,void 0,"gclsrc"),f=Kk(c,"query",!1,void 0,"wbraid");f=Kb(f);var g=Kk(c,"query",!1,void 0,"gbraid"),h=Kk(c,"query",!1,void 0,"gad_source"),m=Kk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Hk(n,"gclid",!1);e=e||Hk(n,"gclsrc",!1);f=f||Hk(n,"wbraid",!1);g=g||Hk(n,"gbraid",!1);h=h||Hk(n,"gad_source",!1)}return Bu(d,e,m,f,g,h)}
function Cu(){return Au(y.location.href,!0)}
function Bu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Zt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Zt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Zt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Zt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Du(a){for(var b=Cu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Au(y.document.referrer,!1),b.gad_source=void 0);Eu(b,!1,a)}
function Fu(a){Du(a);var b=zu(y.location.href,!0,!1,Ka(13)?Gu(Hu()):!1);b.length||(b=zu(y.document.referrer,!1,!0,!1));if(b.length){var c=b[0];a=a||{};var d=zb(),e=xs(a,d,!0),f=du(),g=function(){eu(f)&&e.expires!==void 0&&zs("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ma.get()},expires:Number(e.expires)})};mn(function(){g();eu(f)||nn(g,f)},f)}}
function Iu(a,b,c){c=c||{};var d=zb(),e=xs(c,d,!0),f=du(),g=function(){if(eu(f)&&e.expires!==void 0){var h=wu()||[];tu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ma:uu(b)},!0);zs("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ma?m.Ma.get():0},expires:Number(m.expires)}}))}};mn(function(){eu(f)?g():nn(g,f)},f)}
function Eu(a,b,c,d,e){c=c||{};e=e||[];var f=ku(c.prefix),g=d||zb(),h=Math.round(g/1E3),m=du(),n=!1,p=!1,q=function(){if(eu(m)){var r=xs(c,g,!0);r.Cc=m;for(var t=function(K,U){var Q=lu(K,f);Q&&(os(Q,U,r),K!=="gb"&&(n=!0))},u=function(K){var U=["GCL",h,K];e.length>0&&U.push(e.join("."));return U.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var x=w.value;a[x]&&t(x,u(a[x][0]))}if(!n&&a.gb){var z=a.gb[0],B=lu("gb",f);!b&&hu(B).some(function(K){return K.gclid===z&&K.labels&&
K.labels.length>0})||t("gb",u(z))}}if(!p&&a.gbraid&&eu("ad_storage")&&(p=!0,!n)){var C=a.gbraid,F=lu("ag",f);if(b||!mu(F).some(function(K){return K.gclid===C&&K.labels&&K.labels.length>0})){var G={},I=(G.k=C,G.i=""+h,G.b=e,G);Ot(F,I,5,c,g)}}Ju(a,f,g,c)};mn(function(){q();eu(m)||nn(q,m)},m)}
function Ju(a,b,c,d){if(a.gad_source!==void 0&&eu("ad_storage")){if(Ka(4)){var e=Wc();if(e==="r"||e==="h")return}var f=a.gad_source,g=lu("gs",b);if(g){var h=Math.floor((zb()-(Vc()||0))/1E3),m;if(Ka(9)){var n=Vt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p)}else{var q={};m=(q.k=f,q.i=""+h,q)}Ot(g,m,5,d,c)}}}
function Ku(a,b){var c=$s(!0);fu(function(){for(var d=ku(b.prefix),e=0;e<a.length;++e){var f=a[e];if(au[f]!==void 0){var g=lu(f,d),h=c[g];if(h){var m=Math.min(Lu(h),zb()),n;b:{for(var p=m,q=ds(g,A.cookie,void 0,du()),r=0;r<q.length;++r)if(Lu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=xs(b,m,!0);t.Cc=du();os(g,h,t)}}}}Eu(Bu(c.gclid,c.gclsrc),!1,b)},du())}
function Mu(a){var b=["ag"],c=$s(!0),d=ku(a.prefix);fu(function(){for(var e=0;e<b.length;++e){var f=lu(b[e],d);if(f){var g=c[f];if(g){var h=It(g,5);if(h){var m=pu(h);m||(m=zb());var n;a:{for(var p=m,q=Mt(f,5),r=0;r<q.length;++r)if(pu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Ot(f,h,5,a,m)}}}}},["ad_storage"])}function lu(a,b){var c=au[a];if(c!==void 0)return b+c}function Lu(a){return Nu(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function pu(a){return a?(Number(a.i)||0)*1E3:0}function qu(a){var b=Nu(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Nu(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Zt.test(a[2])?[]:a}
function Ou(a,b,c,d,e){if(Array.isArray(b)&&bs(y)){var f=ku(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=lu(a[m],f);if(n){var p=ds(n,A.cookie,void 0,du());p.length&&(h[n]=p.sort()[p.length-1])}}return h};fu(function(){gt(g,b,c,d)},du())}}
function Pu(a,b,c,d){if(Array.isArray(a)&&bs(y)){var e=["ag"],f=ku(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=lu(e[m],f);if(!n)return{};var p=Mt(n,5);if(p.length){var q=p.sort(function(r,t){return pu(t)-pu(r)})[0];h[n]=Jt(q,5)}}return h};fu(function(){gt(g,a,b,c)},["ad_storage"])}}function ru(a){return a.filter(function(b){return Zt.test(b.gclid)})}
function Qu(a,b){if(bs(y)){for(var c=ku(b.prefix),d={},e=0;e<a.length;e++)au[a[e]]&&(d[a[e]]=au[a[e]]);fu(function(){sb(d,function(f,g){var h=ds(c+g,A.cookie,void 0,du());h.sort(function(t,u){return Lu(u)-Lu(t)});if(h.length){var m=h[0],n=Lu(m),p=Nu(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Nu(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Eu(q,!0,b,n,p)}})},du())}}
function Ru(a){var b=["ag"],c=["gbraid"];fu(function(){for(var d=ku(a.prefix),e=0;e<b.length;++e){var f=lu(b[e],d);if(!f)break;var g=Mt(f,5);if(g.length){var h=g.sort(function(q,r){return pu(r)-pu(q)})[0],m=pu(h),n=h.b,p={};p[c[e]]=h.k;Eu(p,!0,a,m,n)}}},["ad_storage"])}function Su(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Tu(a){function b(h,m,n){n&&(h[m]=n)}if(jn()){var c=Cu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:$s(!1)._gs);if(Su(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);ht(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);ht(function(){return g},1)}}}
function Uu(a){if(!Ka(1))return null;var b=$s(!0).gad_source;if(b!=null)return y.location.hash="",b;if(Ka(2)){var c=Qk(y.location.href);b=Kk(c,"query",!1,void 0,"gad_source");if(b!=null)return b;var d=Cu();if(Su(d,a))return"0"}return null}function Vu(a){var b=Uu(a);b!=null&&ht(function(){var c={};return c.gad_source=b,c},4)}
function Wu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}function Xu(a,b,c,d){var e=[];c=c||{};if(!eu(du()))return e;var f=hu(a),g=Wu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=xs(c,p,!0);r.Cc=du();os(a,q,r)}return e}
function Yu(a,b){var c=[];b=b||{};var d=ju(b),e=Wu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ku(b.prefix),n=lu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},x=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Ot(n,x,5,b,u)}else if(h.type==="gb"){var z=[q,v,r].concat(t||[],[a]).join("."),B=xs(b,u,!0);B.Cc=du();os(n,z,B)}}return c}
function Zu(a,b){var c=ku(b),d=lu(a,c);if(!d)return 0;var e;e=a==="ag"?mu(d):hu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function $u(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function av(a){var b=Math.max(Zu("aw",a),$u(eu(du())?At():{})),c=Math.max(Zu("gb",a),$u(eu(du())?At("_gac_gb",!0):{}));c=Math.max(c,Zu("ag",a));return c>b}
function Gu(a){return bu.test(a)||cu.test(a)}function Hu(){return A.referrer?Kk(Qk(A.referrer),"host"):""};
var bv=function(a,b){b=b===void 0?!1:b;var c=Dp("ads_pageview",function(){return{}});if(c[a])return!1;b||(c[a]=!0);return!0},cv=function(a){return Rk(a,"gclid dclid gbraid wbraid gclaw gcldc gclha gclgf gclgb _gl".split(" "),"0")},jv=function(a,b,c,d,e){var f=ku(a.prefix);if(bv(f,!0)){var g=Cu(),h=[],m=g.gclid,n=g.dclid,p=g.gclsrc||"aw",q=dv(),r=q.Yf,t=q.jm;!m||p!=="aw.ds"&&p!=="aw"&&p!=="ds"&&p!=="3p.ds"||h.push({gclid:m,zd:p});n&&h.push({gclid:n,zd:"ds"});h.length===2&&O(147);h.length===0&&g.wbraid&&
h.push({gclid:g.wbraid,zd:"gb"});h.length===0&&p==="aw.ds"&&h.push({gclid:"",zd:"aw.ds"});ev(function(){var u=sp(fv());if(u){nt(a);var v=[],w=u?lt[ot(a.prefix)]:void 0;w&&v.push("auid="+w);if(sp(M.m.W)){e&&v.push("userId="+e);var x=fo(ao.aa.Kl);if(x===void 0)eo(ao.aa.Ll,!0);else{var z=fo(ao.aa.ph);v.push("ga_uid="+z+"."+x)}}var B=Hu(),C=u||!d?h:[];C.length===0&&Gu(B)&&C.push({gclid:"",zd:""});if(C.length!==0||r!==void 0){B&&v.push("ref="+encodeURIComponent(B));var F=gv();v.push("url="+encodeURIComponent(F));
v.push("tft="+zb());var G=Vc();G!==void 0&&v.push("tfd="+Math.round(G));var I=Fl(!0);v.push("frm="+I);r!==void 0&&v.push("gad_source="+encodeURIComponent(r));t!==void 0&&v.push("gad_source_src="+encodeURIComponent(t.toString()));if(!c){var K={};c=rq(hq(new gq(0),(K[M.m.Fa]=Oq.D[M.m.Fa],K)))}v.push("gtm="+Ur({Oa:b}));Fr()&&v.push("gcs="+Gr());v.push("gcd="+Kr(c));Nr()&&v.push("dma_cps="+Lr());v.push("dma="+Mr());Er(c)?v.push("npa=0"):v.push("npa=1");Pr()&&v.push("_ng=1");ir(qr())&&v.push("tcfd="+Or());
var U=xr();U&&v.push("gdpr="+U);var Q=wr();Q&&v.push("gdpr_consent="+Q);H(23)&&v.push("apve=0");H(123)&&$s(!1)._up&&v.push("gtm_up=1");hk()&&v.push("tag_exp="+hk());if(C.length>0)for(var na=0;na<C.length;na++){var T=C[na],ba=T.gclid,aa=T.zd;if(!hv(a.prefix,aa+"."+ba,w!==void 0)){var W=iv+"?"+v.join("&");ba!==""?W=aa==="gb"?W+"&wbraid="+ba:W+"&gclid="+ba+"&gclsrc="+aa:aa==="aw.ds"&&(W+="&gclsrc=aw.ds");Oc(W)}}else if(r!==void 0&&!hv(a.prefix,"gad",w!==void 0)){var ka=iv+"?"+v.join("&");Oc(ka)}}}})}},
hv=function(a,b,c){var d=Dp("joined_auid",function(){return{}}),e=(c?a||"_gcl":"")+"."+b;if(d[e])return!0;d[e]=!0;return!1},dv=function(){var a=Qk(y.location.href),b=void 0,c=void 0,d=Kk(a,"query",!1,void 0,"gad_source"),e,f=a.hash.replace("#","").match(kv);e=f?f[1]:void 0;d&&e?(b=d,c=1):d?(b=d,c=2):e&&(b=e,c=3);return{Yf:b,jm:c}},gv=function(){var a=Fl(!1)===1?y.top.location.href:y.location.href;return a=a.replace(/[\?#].*$/,"")},lv=function(a){var b=[];sb(a,function(c,d){d=ru(d);for(var e=[],f=
0;f<d.length;f++)e.push(d[f].gclid);e.length&&b.push(c+":"+e.join(","))});return b.join(";")},nv=function(a,b){return mv("dc",a,b)},ov=function(a,b){return mv("aw",a,b)},mv=function(a,b,c){if(a==="aw"||a==="dc"||a==="gb"){var d=Sk("gcl"+a);if(d)return d.split(".")}var e=ku(b);if(e==="_gcl"){var f=!sp(fv())&&c,g;g=Cu()[a]||[];if(g.length>0)return f?["0"]:g}var h=lu(a,e);return h?gu(h):[]},ev=function(a){var b=fv();vp(function(){a();sp(b)||nn(a,b)},b)},fv=function(){return[M.m.V,M.m.W]},iv=Ni(36,'https://adservice.google.com/pagead/regclk'),
kv=/^gad_source[_=](\d+)$/;function pv(){return Dp("dedupe_gclid",function(){return vs()})};var qv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,rv=/^www.googleadservices.com$/;function sv(a){a||(a=tv());return a.Cq?!1:a.Dp||a.Ep||a.Hp||a.Fp||a.Yf||a.np||a.Gp||a.tp?!0:!1}function tv(){var a={},b=$s(!0);a.Cq=!!b._up;var c=Cu();a.Dp=c.aw!==void 0;a.Ep=c.dc!==void 0;a.Hp=c.wbraid!==void 0;a.Fp=c.gbraid!==void 0;a.Gp=c.gclsrc==="aw.ds";a.Yf=dv().Yf;var d=A.referrer?Kk(Qk(A.referrer),"host"):"";a.tp=qv.test(d);a.np=rv.test(d);return a};function uv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function vv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function wv(){return["ad_storage","ad_user_data"]}function xv(a){if(H(38)&&!fo(ao.aa.zl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{uv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(eo(ao.aa.zl,function(d){d.gclid&&Iu(d.gclid,5,a)}),vv(c)||O(178))})}catch(c){O(177)}};mn(function(){eu(wv())?b():nn(b,wv())},wv())}};var yv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function zv(a){a.data.action==="gcl_transfer"&&a.data.gadSource?eo(ao.aa.Lf,{gadSource:a.data.gadSource}):O(173)}
function Av(a,b){if(H(a)){if(fo(ao.aa.Lf))return O(176),ao.aa.Lf;if(fo(ao.aa.Bl))return O(170),ao.aa.Lf;var c=Hl();if(!c)O(171);else if(c.opener){var d=function(g){if(yv.includes(g.origin)){a===119?zv(g):a===200&&(zv(g),g.data.gclid&&Iu(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);ar(c,"message",d)}else O(172)};if($q(c,"message",d)){eo(ao.aa.Bl,!0);for(var e=l(yv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);O(174);return ao.aa.Lf}O(175)}}}
;var Bv=function(){this.D=this.gppString=void 0};Bv.prototype.reset=function(){this.D=this.gppString=void 0};var Cv=new Bv;var Dv=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Ev=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Fv=/^\d+\.fls\.doubleclick\.net$/,Gv=/;gac=([^;?]+)/,Hv=/;gacgb=([^;?]+)/;
function Iv(a,b){if(Fv.test(A.location.host)){var c=A.location.href.match(b);return c&&c.length===2&&c[1].match(Dv)?Jk(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Jv(a,b,c){for(var d=eu(du())?At("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Xu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{mp:f?e.join(";"):"",lp:Iv(d,Hv)}}function Kv(a){var b=A.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Ev)?b[1]:void 0}
function Lv(a){var b=Ka(9),c={},d,e,f;Fv.test(A.location.host)&&(d=Kv("gclgs"),e=Kv("gclst"),b&&(f=Kv("gcllp")));if(d&&e&&(!b||f))c.zh=d,c.Bh=e,c.Ah=f;else{var g=zb(),h=mu((a||"_gcl")+"_gs"),m=h.map(function(q){return q.gclid}),n=h.map(function(q){return g-q.timestamp}),p=[];b&&(p=h.map(function(q){return q.Ed}));m.length>0&&n.length>0&&(!b||p.length>0)&&(c.zh=m.join("."),c.Bh=n.join("."),b&&p.length>0&&(c.Ah=p.join(".")))}return c}
function Mv(a,b,c,d){d=d===void 0?!1:d;if(Fv.test(A.location.host)){var e=Kv(c);if(e){if(d){var f=new Qt;Rt(f,2);Rt(f,3);return e.split(".").map(function(h){return{gclid:h,Ma:f,zb:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?xu(g):hu(g)}if(b==="wbraid")return hu((a||"_gcl")+"_gb");if(b==="braids")return ju({prefix:a})}return[]}function Nv(a){return Fv.test(A.location.host)?!(Kv("gclaw")||Kv("gac")):av(a)}
function Ov(a,b,c){var d;d=c?Yu(a,b):Xu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Pv(){var a=y.__uspapi;if(kb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};
var Uv=function(a){if(a.eventName===M.m.ra&&S(a,R.C.fa)===N.K.Ia)if(H(24)){V(a,R.C.je,P(a.F,M.m.Aa)!=null&&P(a.F,M.m.Aa)!==!1&&!sp([M.m.V,M.m.W]));var b=Qv(a),c=P(a.F,M.m.Qa)!==!1;c||X(a,M.m.Vh,"1");var d=ku(b.prefix),e=S(a,R.C.kh);if(!S(a,R.C.ka)&&!S(a,R.C.Of)&&!S(a,R.C.ie)){var f=P(a.F,M.m.Cb),g=P(a.F,M.m.Ra)||{};Rv({pe:c,xe:g,Ae:f,Qc:b});if(!e&&!bv(d)){a.isAborted=!0;return}}if(e)a.isAborted=!0;else{X(a,M.m.gd,M.m.Xc);if(S(a,R.C.ka))X(a,M.m.gd,M.m.on),X(a,M.m.ka,"1");else if(S(a,R.C.Of))X(a,M.m.gd,
M.m.yn);else if(S(a,R.C.ie))X(a,M.m.gd,M.m.vn);else{var h=Cu();X(a,M.m.Yc,h.gclid);X(a,M.m.ed,h.dclid);X(a,M.m.qk,h.gclsrc);Sv(a,M.m.Yc)||Sv(a,M.m.ed)||(X(a,M.m.Ud,h.wbraid),X(a,M.m.Je,h.gbraid));X(a,M.m.Wa,Hu());X(a,M.m.Ba,gv());if(H(27)&&sc){var m=Kk(Qk(sc),"host");m&&X(a,M.m.Xk,m)}if(!S(a,R.C.ie)){var n=dv(),p=n.jm;X(a,M.m.He,n.Yf);X(a,M.m.Ie,p)}X(a,M.m.Ic,Fl(!0));var q=tv();sv(q)&&X(a,M.m.jd,"1");X(a,M.m.sk,pv());$s(!1)._up==="1"&&X(a,M.m.Nk,"1")}Tn=!0;X(a,M.m.Bb);X(a,M.m.Nb);var r=sp([M.m.V,
M.m.W]);r&&(X(a,M.m.Bb,Tv()),c&&(nt(b),X(a,M.m.Nb,lt[ot(b.prefix)])));X(a,M.m.mc);X(a,M.m.nb);if(!Sv(a,M.m.Yc)&&!Sv(a,M.m.ed)&&Nv(d)){var t=iu(b);t.length>0&&X(a,M.m.mc,t.join("."))}else if(!Sv(a,M.m.Ud)&&r){var u=gu(d+"_aw");u.length>0&&X(a,M.m.nb,u.join("."))}H(31)&&X(a,M.m.Qk,Wc());a.F.isGtmEvent&&(a.F.D[M.m.Fa]=Oq.D[M.m.Fa]);Er(a.F)?X(a,M.m.wc,!1):X(a,M.m.wc,!0);V(a,R.C.vg,!0);var v=Pv();v!==void 0&&X(a,M.m.zf,v||"error");var w=xr();w&&X(a,M.m.hd,w);if(H(137))try{var x=Intl.DateTimeFormat().resolvedOptions().timeZone;
X(a,M.m.mi,x||"-")}catch(F){X(a,M.m.mi,"e")}var z=wr();z&&X(a,M.m.md,z);var B=Cv.gppString;B&&X(a,M.m.af,B);var C=Cv.D;C&&X(a,M.m.Ze,C);V(a,R.C.Ja,!1)}}else a.isAborted=!0},Qv=function(a){var b={prefix:P(a.F,M.m.Pb)||P(a.F,M.m.ib),domain:P(a.F,M.m.pb),Ac:P(a.F,M.m.qb),flags:P(a.F,M.m.wb)};a.F.isGtmEvent&&(b.path=P(a.F,M.m.Qb));return b},Vv=function(a,b){var c,d,e,f,g,h,m,n;c=a.pe;d=a.xe;e=a.Ae;f=a.Oa;g=a.F;h=a.ye;m=a.yr;n=a.Qm;Rv({pe:c,xe:d,Ae:e,Qc:b});c&&m!==!0&&(n!=null?n=String(n):n=void 0,jv(b,
f,g,h,n))},Wv=function(a,b){if(!S(a,R.C.ie)){var c=Av(119);if(c){var d=fo(c),e=function(g){V(a,R.C.ie,!0);var h=Sv(a,M.m.He),m=Sv(a,M.m.Ie);X(a,M.m.He,String(g.gadSource));X(a,M.m.Ie,6);V(a,R.C.ka);V(a,R.C.Of);X(a,M.m.ka);b();X(a,M.m.He,h);X(a,M.m.Ie,m);V(a,R.C.ie,!1)};if(d)e(d);else{var f=void 0;f=ho(c,function(g,h){e(h);io(c,f)})}}}},Rv=function(a){var b,c,d,e;b=a.pe;c=a.xe;d=a.Ae;e=a.Qc;b&&(jt(c[M.m.ae],!!c[M.m.na])&&(Ku(Xv,e),Mu(e),wt(e)),Fl()!==2?(Fu(e),xv(e),Av(200,e)):Du(e),Qu(Xv,e),Ru(e));
c[M.m.na]&&(Ou(Xv,c[M.m.na],c[M.m.Lc],!!c[M.m.sc],e.prefix),Pu(c[M.m.na],c[M.m.Lc],!!c[M.m.sc],e.prefix),xt(ot(e.prefix),c[M.m.na],c[M.m.Lc],!!c[M.m.sc],e),xt("FPAU",c[M.m.na],c[M.m.Lc],!!c[M.m.sc],e));d&&(H(101)?Tu(Yv):Tu(Zv));Vu(Zv)},$v=function(a,b,c,d){var e,f,g;e=a.Rm;f=a.callback;g=a.qm;if(typeof f==="function")if(e===M.m.nb&&g===void 0){var h=d(b.prefix,c);h.length===0?f(void 0):h.length===1?f(h[0]):f(h)}else e===M.m.Nb?(O(65),nt(b,!1),f(lt[ot(b.prefix)])):f(g)},aw=function(a,b){Array.isArray(b)||
(b=[b]);var c=S(a,R.C.fa);return b.indexOf(c)>=0},Xv=["aw","dc","gb"],Zv=["aw","dc","gb","ag"],Yv=["aw","dc","gb","ag","gad_source"];function bw(a){var b=P(a.F,M.m.Kc),c=P(a.F,M.m.Jc);b&&!c?(a.eventName!==M.m.ra&&a.eventName!==M.m.Qd&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}function cw(a){var b=sp(M.m.V)?Cp.pscdl:"denied";b!=null&&X(a,M.m.Jg,b)}function dw(a){var b=Fl(!0);X(a,M.m.Ic,b)}function ew(a){Pr()&&X(a,M.m.Yd,1)}
function Tv(){var a=A.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Jk(a.substring(0,b))===void 0;)b--;return Jk(a.substring(0,b))||""}function fw(a){gw(a,Lp.Af.Ym,P(a.F,M.m.qb))}function gw(a,b,c){Sv(a,M.m.rd)||X(a,M.m.rd,{});Sv(a,M.m.rd)[b]=c}function hw(a){V(a,R.C.Nf,Xm.Z.Ea)}function iw(a){var b=gb("GTAG_EVENT_FEATURE_CHANNEL");b&&(X(a,M.m.bf,b),eb())}function jw(a){var b=a.F.getMergedValues(M.m.rc);b&&a.mergeHitDataForKey(M.m.rc,b)}
function kw(a,b){b=b===void 0?!1:b;if(H(108)){var c=S(a,R.C.Mf);if(c)if(c.indexOf(a.target.destinationId)<0){if(V(a,R.C.Rj,!1),b||!lw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else V(a,R.C.Rj,!0)}}function mw(a){fl&&(Tn=!0,a.eventName===M.m.ra?Zn(a.F,a.target.id):(S(a,R.C.Ee)||(Wn[a.target.id]=!0),Kp(S(a,R.C.kb))))};function ww(a,b,c,d){var e=Cc(),f;if(e===1)a:{var g=bk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=A.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==y.location.protocol?a:b)+c};function Iw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Sv(a,b)},setHitData:function(b,c){X(a,b,c)},setHitDataIfNotDefined:function(b,c){Sv(a,b)===void 0&&X(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return S(a,b)},setMetadata:function(b,c){V(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.F,b)},yb:function(){return a},getHitKeys:function(){return Object.keys(a.D)},getMergedValues:function(b){return a.F.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return hd(c)?a.mergeHitDataForKey(b,c):!1}}};var Kw=function(a){var b=Jw[tm?a.target.destinationId:Im(a.target.destinationId)];if(!a.isAborted&&b)for(var c=Iw(a),d=0;d<b.length;++d){try{b[d](c)}catch(e){a.isAborted=!0}if(a.isAborted)break}},Lw=function(a,b){var c=Jw[a];c||(c=Jw[a]=[]);c.push(b)},Jw={};function Nw(a,b){return arguments.length===1?Ow("set",a):Ow("set",a,b)}function Pw(a,b){return arguments.length===1?Ow("config",a):Ow("config",a,b)}function Qw(a,b,c){c=c||{};c[M.m.kd]=a;return Ow("event",b,c)}function Ow(){return arguments};var Sw=function(){this.messages=[];this.D=[]};Sw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=Object.assign({},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.D.length;g++)try{this.D[g](f)}catch(h){}};Sw.prototype.listen=function(a){this.D.push(a)};
Sw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Sw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Tw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.C.kb]=hg.canonicalContainerId;Uw().enqueue(a,b,c)}
function Vw(){var a=Ww;Uw().listen(a)}function Uw(){return Dp("mb",function(){return new Sw})};var Xw,Yw=!1;function Zw(){Yw=!0;Xw=Xw||{}}function $w(a){Yw||Zw();return Xw[a]};function ax(){var a=y.screen;return{width:a?a.width:0,height:a?a.height:0}}
function bx(a){if(A.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!y.getComputedStyle)return!0;var c=y.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=y.getComputedStyle(d,null))}return!1}
var dx=function(a){var b=cx(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},cx=function(){var a=A.body,b=A.documentElement||a&&a.parentElement,c,d;if(A.compatMode&&A.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var gx=function(a){if(ex){if(a>=0&&a<fx.length&&fx[a]){var b;(b=fx[a])==null||b.disconnect();fx[a]=void 0}}else y.clearInterval(a)},jx=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(ex){var e=!1;D(function(){e||hx(a,b,c)()});return ix(function(f){e=!0;for(var g={eg:0};g.eg<f.length;g={eg:g.eg},g.eg++)D(function(h){return function(){a(f[h.eg])}}(g))},
b,c)}return y.setInterval(hx(a,b,c),1E3)},hx=function(a,b,c){function d(h,m){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:m,intersectionRect:n,isIntersecting:m>0,rootBounds:n,target:h,time:zb()};D(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,m){return h-m});return function(){for(var h=0;h<b.length;h++){var m=dx(b[h]);if(m>e[h])for(;f[h]<c.length-1&&m>=c[f[h]+1];)d(b[h],m),f[h]++;
else if(m<e[h])for(;f[h]>=0&&m<=c[f[h]];)d(b[h],m),f[h]--;e[h]=m}}},ix=function(a,b,c){for(var d=new y.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<fx.length;f++)if(!fx[f])return fx[f]=d,f;return fx.push(d)-1},fx=[],ex=!(!y.IntersectionObserver||!y.IntersectionObserverEntry);
var lx=function(a){return a.tagName+":"+a.isVisible+":"+a.ma.length+":"+kx.test(a.ma)},zx=function(a){a=a||{ve:!0,we:!0,Jh:void 0};a.Xb=a.Xb||{email:!0,phone:!1,address:!1};var b=mx(a),c=nx[b];if(c&&zb()-c.timestamp<200)return c.result;var d=ox(),e=d.status,f=[],g,h,m=[];if(!H(33)){if(a.Xb&&a.Xb.email){var n=px(d.elements);f=qx(n,a&&a.Uf);g=rx(f);n.length>10&&(e="3")}!a.Jh&&g&&(f=[g]);for(var p=0;p<f.length;p++)m.push(sx(f[p],!!a.ve,!!a.we));m=m.slice(0,10)}else if(a.Xb){}g&&(h=sx(g,!!a.ve,!!a.we));var F={elements:m,
Ej:h,status:e};nx[b]={timestamp:zb(),result:F};return F},Ax=function(a,b){if(a){var c=a.trim().replaceAll(/\s+/g,"").replaceAll(/(\d{2,})\./g,"$1").replaceAll(/-/g,"").replaceAll(/\((\d+)\)/g,"$1");if(b&&c.match(/^\+?\d{3,7}$/))return c;c.charAt(0)!=="+"&&(c="+"+c);if(c.match(/^\+\d{10,15}$/))return c}},Cx=function(a){var b=Bx(/^(\w|[- ])+$/)(a);if(!b)return b;var c=b.replaceAll(/[- ]+/g,"");return c.length>10?void 0:c},Bx=function(a){return function(b){var c=b.match(a);return c?c[0].trim().toLowerCase():
void 0}},yx=function(a,b,c){var d=a.element,e={ma:a.ma,type:a.ya,tagName:d.tagName};b&&(e.querySelector=Dx(d));c&&(e.isVisible=!bx(d));return e},sx=function(a,b,c){return yx({element:a.element,ma:a.ma,ya:xx.hc},b,c)},mx=function(a){var b=!(a==null||!a.ve)+"."+!(a==null||!a.we);a&&a.Uf&&a.Uf.length&&(b+="."+a.Uf.join("."));a&&a.Xb&&(b+="."+a.Xb.email+"."+a.Xb.phone+"."+a.Xb.address);return b},rx=function(a){if(a.length!==0){var b;b=Ex(a,function(c){return!Fx.test(c.ma)});b=Ex(b,function(c){return c.element.tagName.toUpperCase()===
"INPUT"});b=Ex(b,function(c){return!bx(c.element)});return b[0]}},qx=function(a,b){if(!b||b.length===0)return a;for(var c=[],d=0;d<a.length;d++){for(var e=!0,f=0;f<b.length;f++){var g=b[f];if(g&&ri(a[d].element,g)){e=!1;break}}e&&c.push(a[d])}return c},Ex=function(a,b){if(a.length<=1)return a;var c=a.filter(b);return c.length===0?a:c},Dx=function(a){var b;if(a===A.body)b="body";else{var c;if(a.id)c="#"+a.id;else{var d;if(a.parentElement){var e;a:{var f=a.parentElement;if(f){for(var g=0;g<f.childElementCount;g++)if(f.children[g]===
a){e=g+1;break a}e=-1}else e=1}d=Dx(a.parentElement)+">:nth-child("+e.toString()+")"}else d="";c=d}b=c}return b},px=function(a){for(var b=[],c=0;c<a.length;c++){var d=a[c],e=d.textContent;d.tagName.toUpperCase()==="INPUT"&&d.value&&(e=d.value);if(e){var f=e.match(Gx);if(f){var g=f[0],h;if(y.location){var m=Mk(y.location,"host",!0);h=g.toLowerCase().indexOf(m)>=0}else h=!1;h||b.push({element:d,ma:g})}}}return b},ox=function(){var a=[],b=A.body;if(!b)return{elements:a,status:"4"};for(var c=b.querySelectorAll("*"),
d=0;d<c.length&&d<1E4;d++){var e=c[d];if(!(Hx.indexOf(e.tagName.toUpperCase())>=0)&&e.children instanceof HTMLCollection){for(var f=!1,g=0;g<e.childElementCount&&g<1E4;g++)if(!(Ix.indexOf(e.children[g].tagName.toUpperCase())>=0)){f=!0;break}(!f||H(33)&&Jx.indexOf(e.tagName)!==-1)&&a.push(e)}}return{elements:a,status:c.length>1E4?"2":"1"}},Gx=/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}/i,kx=/@(gmail|googlemail)\./i,Fx=/support|noreply/i,Hx="SCRIPT STYLE IMG SVG PATH BR NOSCRIPT TEXTAREA".split(" "),Ix=
["BR"],Kx=sg('',2),xx={hc:"1",xd:"2",pd:"3",wd:"4",De:"5",Kf:"6",mh:"7",Qi:"8",Mh:"9",Li:"10"},nx={},Jx=["INPUT","SELECT"],Lx=Bx(/^([^\x00-\x40\x5b-\x60\x7b-\xff]|[.-]|\s)+$/);
var jy=function(a,b,c){var d={};a.mergeHitDataForKey(M.m.Ni,(d[b]=c,d))},ky=function(a,b){var c=lw(a,M.m.Pg,a.F.J[M.m.Pg]);if(c&&c[b||a.eventName]!==void 0)return c[b||a.eventName]},ly=function(a){var b=S(a,R.C.Ta);if(hd(b))return b},my=function(a){if(S(a,R.C.vd)||!Yk(a.F))return!1;if(!P(a.F,M.m.ld)){var b=P(a.F,M.m.Wd);return b===!0||b==="true"}return!0},ny=function(a){return lw(a,M.m.Zd,P(a.F,M.m.Zd))||!!lw(a,"google_ng",!1)};var dg;var oy=Number('')||5,py=Number('')||50,qy=pb();
var sy=function(a,b){a&&(ry("sid",a.targetId,b),ry("cc",a.clientCount,b),ry("tl",a.totalLifeMs,b),ry("hc",a.heartbeatCount,b),ry("cl",a.clientLifeMs,b))},ry=function(a,b,c){b!=null&&c.push(a+"="+b)},ty=function(){var a=A.referrer;if(a){var b;return Kk(Qk(a),"host")===((b=y.location)==null?void 0:b.host)?1:2}return 0},uy="https://"+Ni(21,"www.googletagmanager.com")+"/a?",wy=function(){this.T=vy;this.O=0};wy.prototype.J=function(a,b,c,d){var e=ty(),f,
g=[];f=y===y.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&ry("si",a.gg,g);ry("m",0,g);ry("iss",f,g);ry("if",c,g);sy(b,g);d&&ry("fm",encodeURIComponent(d.substring(0,py)),g);this.R(g);};wy.prototype.D=function(a,b,c,d,e){var f=[];ry("m",1,f);ry("s",a,f);ry("po",ty(),f);b&&(ry("st",b.state,f),ry("si",b.gg,f),ry("sm",b.qg,f));sy(c,f);ry("c",d,f);e&&ry("fm",encodeURIComponent(e.substring(0,
py)),f);this.R(f);};wy.prototype.R=function(a){a=a===void 0?[]:a;!el||this.O>=oy||(ry("pid",qy,a),ry("bc",++this.O,a),a.unshift("ctid="+hg.ctid+"&t=s"),this.T(""+uy+a.join("&")))};var xy=Number('')||500,yy=Number('')||5E3,zy=Number('20')||10,Ay=Number('')||5E3;function By(a){return a.performance&&a.performance.now()||Date.now()}
var Cy=function(a,b){var c;var d=function(e,f,g){g=g===void 0?{tm:function(){},vm:function(){},sm:function(){},onFailure:function(){}}:g;this.Do=e;this.D=f;this.O=g;this.ia=this.la=this.heartbeatCount=this.Co=0;this.nh=!1;this.J={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=By(this.D);this.qg=By(this.D);this.T=10};d.prototype.init=function(){this.R(1);this.Ca()};d.prototype.getState=function(){return{state:this.state,
gg:Math.round(By(this.D)-this.gg),qg:Math.round(By(this.D)-this.qg)}};d.prototype.R=function(e){this.state!==e&&(this.state=e,this.qg=By(this.D))};d.prototype.Sl=function(){return String(this.Co++)};d.prototype.Ca=function(){var e=this;this.heartbeatCount++;this.Za({type:0,clientId:this.id,requestId:this.Sl(),maxDelay:this.qh()},function(f){if(f.type===0){var g;if(((g=f.failure)==null?void 0:g.failureType)!=null)if(f.stats&&(e.stats=f.stats),e.ia++,f.isDead||e.ia>zy){var h=f.isDead&&f.failure.failureType;
e.T=h||10;e.R(4);e.Ao();var m,n;(n=(m=e.O).sm)==null||n.call(m,{failureType:h||10,data:f.failure.data})}else e.R(3),e.Ul();else{if(e.heartbeatCount>f.stats.heartbeatCount+zy){e.heartbeatCount=f.stats.heartbeatCount;var p,q;(q=(p=e.O).onFailure)==null||q.call(p,{failureType:13})}e.stats=f.stats;var r=e.state;e.R(2);if(r!==2)if(e.nh){var t,u;(u=(t=e.O).vm)==null||u.call(t)}else{e.nh=!0;var v,w;(w=(v=e.O).tm)==null||w.call(v)}e.ia=0;e.Eo();e.Ul()}}})};d.prototype.qh=function(){return this.state===2?
yy:xy};d.prototype.Ul=function(){var e=this;this.D.setTimeout(function(){e.Ca()},Math.max(0,this.qh()-(By(this.D)-this.la)))};d.prototype.Ho=function(e,f,g){var h=this;this.Za({type:1,clientId:this.id,requestId:this.Sl(),command:e},function(m){if(m.type===1)if(m.result)f(m.result);else{var n,p,q,r={failureType:(q=(n=m.failure)==null?void 0:n.failureType)!=null?q:12,data:(p=m.failure)==null?void 0:p.data},t,u;(u=(t=h.O).onFailure)==null||u.call(t,r);g(r)}})};d.prototype.Za=function(e,f){var g=this;
if(this.state===4)e.failure={failureType:this.T},f(e);else{var h=this.state!==2&&e.type!==0,m=e.requestId,n,p=this.D.setTimeout(function(){var r=g.J[m];r&&g.If(r,7)},(n=e.maxDelay)!=null?n:Ay),q={request:e,Gm:f,Bm:h,Tp:p};this.J[m]=q;h||this.sendRequest(q)}};d.prototype.sendRequest=function(e){this.la=By(this.D);e.Bm=!1;this.Do(e.request)};d.prototype.Eo=function(){for(var e=l(Object.keys(this.J)),f=e.next();!f.done;f=e.next()){var g=this.J[f.value];g.Bm&&this.sendRequest(g)}};d.prototype.Ao=function(){for(var e=
l(Object.keys(this.J)),f=e.next();!f.done;f=e.next())this.If(this.J[f.value],this.T)};d.prototype.If=function(e,f){this.Eb(e);var g=e.request;g.failure={failureType:f};e.Gm(g)};d.prototype.Eb=function(e){delete this.J[e.request.requestId];this.D.clearTimeout(e.Tp)};d.prototype.Bp=function(e){this.la=By(this.D);var f=this.J[e.requestId];if(f)this.Eb(f),f.Gm(e);else{var g,h;(h=(g=this.O).onFailure)==null||h.call(g,{failureType:14})}};c=new d(a,y,b);return c};var Dy;
var Ey=function(){Dy||(Dy=new wy);return Dy},vy=function(a){vn(xn(Xm.Z.Nc),function(){Fc(a)})},Fy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Gy=function(a){var b=a,c=Kj.Ca;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Hy=function(a){var b=fo(ao.aa.Il);return b&&b[a]},Iy=function(a,
b,c,d,e){var f=this;this.J=d;this.T=this.R=!1;this.ia=null;this.initTime=c;this.D=15;this.O=this.Ro(a);y.setTimeout(function(){f.initialize()},1E3);D(function(){f.Lp(a,b,e)})};k=Iy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.J.D(this.D,{state:this.getState(),gg:this.initTime,qg:Math.round(zb())-this.initTime},void 0,a.commandType),c({failureType:this.D})):this.O.Ho(a,b,c)};k.getState=function(){return this.O.getState().state};k.Lp=function(a,b,c){var d=y.location.origin,e=this,
f=Dc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Fy(h):"",p;H(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Dc(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ia=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.O.Bp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.D=11,this.J.J(void 0,void 0,this.D,r.toString())}};k.Ro=function(a){var b=this,c=Cy(function(d){var e;(e=b.ia)==null||e.postMessage(d,a.origin)},{tm:function(){b.R=!0;b.J.J(c.getState(),c.stats)},vm:function(){},sm:function(d){b.R?(b.D=(d==null?void 0:d.failureType)||10,b.J.D(b.D,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.D=(d==null?void 0:
d.failureType)||4,b.J.J(c.getState(),c.stats,b.D,d==null?void 0:d.data))},onFailure:function(d){b.D=d.failureType;b.J.D(b.D,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.T||this.O.init();this.T=!0};function Jy(){var a=gg(dg.D,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ky(a,b){var c=Math.round(zb());b=b===void 0?!1:b;var d=y.location.origin;if(!d||!Jy()||H(168))return;jk()&&(a=""+d+ik()+"/_/service_worker");var e=Gy(a);if(e===null||Hy(e.origin))return;if(!qc()){Ey().J(void 0,void 0,6);return}var f=new Iy(e,!!a,c||Math.round(zb()),Ey(),b);go(ao.aa.Il)[e.origin]=f;}
var Ly=function(a,b,c,d){var e;if((e=Hy(a))==null||!e.delegate){var f=qc()?16:6;Ey().D(f,void 0,void 0,b.commandType);d({failureType:f});return}Hy(a).delegate(b,c,d);};
function My(a,b,c,d,e){var f=Gy();if(f===null){d(qc()?16:6);return}var g,h=(g=Hy(f.origin))==null?void 0:g.initTime,m=Math.round(zb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);Ly(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Ny(a,b,c,d){var e=Gy(a);if(e===null){d("_is_sw=f"+(qc()?16:6)+"te");return}var f=b?1:0,g=Math.round(zb()),h,m=(h=Hy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;H(169)&&(p=!0);Ly(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:y.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Hy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Oy(a){if(H(10)||jk()||Kj.O||Yk(a.F)||H(168))return;Ky(void 0,H(131));};var Py="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Qy(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Ry(){var a=y.google_tag_data,b;if(a!=null&&a.uach){var c=a.uach,d=Object.assign({},c);c.fullVersionList&&(d.fullVersionList=c.fullVersionList.slice(0));b=d}else b=null;return b}function Sy(){var a,b;return(b=(a=y.google_tag_data)==null?void 0:a.uach_promise)!=null?b:null}
function Ty(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Uy(){var a=y;if(!Ty(a))return null;var b=Qy(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Py).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var Wy=function(a,b){if(a)for(var c=Vy(a),d=l(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;X(b,f,c[f])}},Vy=function(a){var b={};b[M.m.qf]=a.architecture;b[M.m.rf]=a.bitness;a.fullVersionList&&(b[M.m.tf]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[M.m.uf]=a.mobile?"1":"0";b[M.m.vf]=a.model;b[M.m.wf]=a.platform;b[M.m.xf]=a.platformVersion;b[M.m.yf]=a.wow64?"1":"0";return b},Xy=function(a){var b=0,c=function(g,
h){try{a(g,h)}catch(m){}},d=Ry();if(d)c(d);else{var e=Sy();if(e){b=Math.min(Math.max(isFinite(b)?b:0,0),1E3);var f=y.setTimeout(function(){c.hg||(c.hg=!0,O(106),c(null,Error("Timeout")))},b);e.then(function(g){c.hg||(c.hg=!0,O(104),y.clearTimeout(f),c(g))}).catch(function(g){c.hg||(c.hg=!0,O(105),y.clearTimeout(f),c(null,g))})}else c(null)}},Zy=function(){if(Ty(y)&&(Yy=zb(),!Sy())){var a=Uy();a&&(a.then(function(){O(95)}),a.catch(function(){O(96)}))}},Yy;function $y(a){var b=a.location.href;if(a===a.top)return{url:b,Qp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Qp:c}};var Oz=function(){return H(90)?uo():""},Pz=function(){var a;H(90)&&uo()!==""&&(a=uo());return"https://"+(a?a+".":"")+"analytics.google.com/g/collect"},Qz=function(){var a="www";H(90)&&uo()&&(a=uo());return"https://"+a+".google-analytics.com/g/collect"};function Rz(a,b){var c=!!jk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ik()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?H(187)?Oz()?Pz():""+ik()+"/ag/g/c":Oz().toLowerCase()==="region1"?""+ik()+"/r1ag/g/c":""+ik()+"/ag/g/c":Pz();case 16:if(c){if(H(187))return Oz()?Qz():
""+ik()+"/ga/g/c";var d=Oz().toLowerCase()==="region1"?"/r1ga/g/c":"/ga/g/c";return""+ik()+d}return Qz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?ik()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ik()+"/d/pagead/form-data":H(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Io+".fls.doubleclick.net/activityi;";
case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ik()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return H(180)?c&&b.Cd?ik()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion":c?b.Cd?ik()+"/as/d/ccm/conversion":ik()+"/as/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ik()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";
case 23:return H(180)?c&&b.Cd?ik()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion":c?b.Cd?ik()+"/g/d/ccm/conversion":ik()+"/g/ccm/conversion":"https://www.google.com/ccm/conversion";case 21:return H(180)?c&&b.Cd?ik()+"/d/ccm/form-data":H(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data":c?b.Cd?ik()+"/d/ccm/form-data":ik()+"/ccm/form-data":H(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 55:case 27:case 30:case 36:case 54:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:hc(a,"Unknown endpoint")}};function Sz(a){a=a===void 0?[]:a;return Lj(a).join("~")}function Tz(){if(!H(118))return"";var a,b;return(((a=Gm(Hm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Uz(a,b){b&&sb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};
var Wz=function(a,b){for(var c={},d=function(p,q){var r;r=q===!0?"1":q===!1?"0":encodeURIComponent(String(q));c[p]=r},e=l(Object.keys(a.D)),f=e.next();!f.done;f=e.next()){var g=f.value,h=Sv(a,g),m=Vz[g];m&&h!==void 0&&h!==""&&(!S(a,R.C.je)||g!==M.m.Yc&&g!==M.m.ed&&g!==M.m.Ud&&g!==M.m.Je||(h="0"),d(m,h))}d("gtm",Ur({Oa:S(a,R.C.kb)}));Fr()&&d("gcs",Gr());d("gcd",Kr(a.F));Nr()&&d("dma_cps",Lr());d("dma",Mr());ir(qr())&&d("tcfd",Or());Sz()&&d("tag_exp",Sz());Tz()&&d("ptag_exp",Tz());if(S(a,R.C.vg)){d("tft",
zb());var n=Vc();n!==void 0&&d("tfd",Math.round(n))}H(24)&&d("apve","1");(H(25)||H(26))&&d("apvf",Sc()?H(26)?"f":"sb":"nf");pn[Xm.Z.Ea]!==Wm.Ka.fe||sn[Xm.Z.Ea].isConsentGranted()||(c.limited_ads="1");b(c)},Xz=function(a,b,c){var d=b.F;ep({targetId:b.target.destinationId,request:{url:a,parameterEncoding:2,endpoint:c},ab:{eventId:d.eventId,priorityId:d.priorityId},wh:{eventId:S(b,R.C.Be),priorityId:S(b,R.C.Ce)}})},Yz=function(a,b,c){var d={destinationId:b.target.destinationId,endpoint:c,eventId:b.F.eventId,
priorityId:b.F.priorityId};Xz(a,b,c);fm(d,a,void 0,{Hh:!0,method:"GET"},function(){},function(){em(d,a+"&img=1")})},Zz=function(a){var b=xc()||vc()?"www.google.com":"www.googleadservices.com",c=[];sb(a,function(d,e){d==="dl"?c.push("url="+e):d==="dr"?c.push("ref="+e):d==="uid"?c.push("userId="+e):c.push(d+"="+e)});return"https://"+b+"/pagead/set_partitioned_cookie?"+c.join("&")},$z=function(a){Wz(a,function(b){if(S(a,R.C.fa)===N.K.Ia){var c=[];H(28)&&a.target.destinationId&&c.push("tid="+a.target.destinationId);
sb(b,function(r,t){c.push(r+"="+t)});var d=sp([M.m.V,M.m.W])?45:46,e=Rz(d)+"?"+c.join("&");Xz(e,a,d);var f=a.F,g={destinationId:a.target.destinationId,endpoint:d,eventId:f.eventId,priorityId:f.priorityId};if(H(26)&&Sc()){fm(g,e,void 0,{Hh:!0},function(){},function(){em(g,e+"&img=1")});var h=sp([M.m.V,M.m.W]),m=Sv(a,M.m.jd)==="1",n=Sv(a,M.m.Vh)==="1";if(h&&m&&!n){var p=Zz(b),q=xc()||vc()?58:57;Yz(p,a,q)}}else dm(g,e)||em(g,e+"&img=1");if(kb(a.F.onSuccess))a.F.onSuccess()}})},aA={},Vz=(aA[M.m.ka]="gcu",
aA[M.m.mc]="gclgb",aA[M.m.nb]="gclaw",aA[M.m.He]="gad_source",aA[M.m.Ie]="gad_source_src",aA[M.m.Yc]="gclid",aA[M.m.qk]="gclsrc",aA[M.m.Je]="gbraid",aA[M.m.Ud]="wbraid",aA[M.m.Nb]="auid",aA[M.m.sk]="rnd",aA[M.m.Vh]="ncl",aA[M.m.Zh]="gcldc",aA[M.m.ed]="dclid",aA[M.m.Rb]="edid",aA[M.m.gd]="en",aA[M.m.hd]="gdpr",aA[M.m.Sb]="gdid",aA[M.m.Yd]="_ng",aA[M.m.Ze]="gpp_sid",aA[M.m.af]="gpp",aA[M.m.bf]="_tu",aA[M.m.Nk]="gtm_up",aA[M.m.Ic]="frm",aA[M.m.jd]="lps",aA[M.m.Vg]="did",aA[M.m.Qk]="navt",aA[M.m.Ba]=
"dl",aA[M.m.Wa]="dr",aA[M.m.Bb]="dt",aA[M.m.Xk]="scrsrc",aA[M.m.nf]="ga_uid",aA[M.m.md]="gdpr_consent",aA[M.m.mi]="u_tz",aA[M.m.Sa]="uid",aA[M.m.zf]="us_privacy",aA[M.m.wc]="npa",aA);var bA={};bA.P=Xr.P;var cA={Vq:"L",yo:"S",mr:"Y",Eq:"B",Oq:"E",Sq:"I",jr:"TC",Rq:"HTC"},dA={yo:"S",Nq:"V",Hq:"E",ir:"tag"},eA={},fA=(eA[bA.P.Si]="6",eA[bA.P.Ti]="5",eA[bA.P.Ri]="7",eA);function gA(){function a(c,d){var e=gb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var hA=!1;function yA(a){}
function zA(a){}function AA(){}
function BA(a){}function CA(a){}
function DA(a){}
function EA(){}function FA(a,b){}
function GA(a,b,c){}
function HA(){};var IA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function JA(a,b,c,d,e,f,g){var h=Object.assign({},IA);c&&(h.body=c,h.method="POST");Object.assign(h,e);y.fetch(b,h).then(function(m){if(!m.ok)g==null||g();else if(m.body){var n=m.body.getReader(),p=new TextDecoder;return new Promise(function(q){function r(){n.read().then(function(t){var u;u=t.done;var v=p.decode(t.value,{stream:!u});KA(d,v);u?(f==null||f(),q()):r()}).catch(function(){q()})}r()})}}).catch(function(){g?g():H(128)&&(b+="&_z=retryFetch",c?dm(a,b,c):cm(a,b))})};var LA=function(a){this.R=a;this.D=""},MA=function(a,b){a.J=b;return a},NA=function(a,b){a.O=b;return a},KA=function(a,b){b=a.D+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}OA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.D=b},PA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};OA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},OA=function(a,b){b&&(QA(b.send_pixel,b.options,a.R),QA(b.create_iframe,b.options,a.J),QA(b.fetch,b.options,a.O))};function RA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function QA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=hd(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};function EB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};function FB(a,b,c){c=c===void 0?!1:c;GB().addRestriction(0,a,b,c)}function HB(a,b,c){c=c===void 0?!1:c;GB().addRestriction(1,a,b,c)}function IB(){var a=Em();return GB().getRestrictions(1,a)}var JB=function(){this.container={};this.D={}},KB=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
JB.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.D[b]){var e=KB(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
JB.prototype.getRestrictions=function(a,b){var c=KB(this,b);if(a===0){var d,e;return[].concat(ua((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ua((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ua((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ua((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
JB.prototype.getExternalRestrictions=function(a,b){var c=KB(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};JB.prototype.removeExternalRestrictions=function(a){var b=KB(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.D[a]=!0};function GB(){return Dp("r",function(){return new JB})};var LB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),MB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},NB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},OB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function PB(){var a=pk("gtm.allowlist")||pk("gtm.whitelist");a&&O(9);Xj&&(a=["google","gtagfl","lcl","zone","cmpPartners"]);LB.test(y.location&&y.location.hostname)&&(Xj?O(116):(O(117),QB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Db(wb(a),MB),c=pk("gtm.blocklist")||pk("gtm.blacklist");c||(c=pk("tagTypeBlacklist"))&&O(3);c?O(8):c=[];LB.test(y.location&&y.location.hostname)&&(c=wb(c),c.push("nonGooglePixels","nonGoogleScripts","sandboxedScripts"));
wb(c).indexOf("google")>=0&&O(2);var d=c&&Db(wb(c),NB),e={};return function(f){var g=f&&f[df.Ha];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=fk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(Xj&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=qb(d,h||[]);t&&O(10);q=t}}var u=!m||
q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Xj&&h.indexOf("cmpPartners")>=0?!RB():b&&b.indexOf("sandboxedScripts")!==-1?0:qb(d,OB))&&(u=!0);return e[g]=u}}function RB(){var a=gg(dg.D,Cm(),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var QB=!1;QB=!0;
function SB(){tm&&FB(Em(),function(a){var b=Pf(a.entityId),c;if(Sf(b)){var d=b[df.Ha];if(!d)throw Error("Error: No function name given for function call.");var e=Gf[d];c=!!e&&!!e.runInSiloedMode}else c=!!EB(b[df.Ha],4);return c})};function TB(a,b,c,d,e){if(!UB()){var f=d.siloed?zm(a):a;if(!Nm(f)){d.loadExperiments=Mj();Pm(f,d,e);var g=VB(a),h=function(){pm().container[f]&&(pm().container[f].state=3);WB()},m={destinationId:f,endpoint:0};if(jk())gm(m,ik()+"/"+g,void 0,h);else{var n=Eb(a,"GTM-"),p=Xk(),q=c?"/gtag/js":"/gtm.js",r=Wk(b,q+g);if(!r){var t=Oj.Bg+q;p&&sc&&n&&(t=sc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);r=ww("https://","http://",t+g)}gm(m,r,void 0,h)}}}}
function WB(){Rm()||sb(Sm(),function(a,b){XB(a,b.transportUrl,b.context);O(92)})}
function XB(a,b,c,d){if(!UB()){var e=c.siloed?zm(a):a;if(!Om(e))if(c.loadExperiments||(c.loadExperiments=Mj()),Rm()){var f;(f=pm().destination)[e]!=null||(f[e]={state:0,transportUrl:b,context:c,parent:Hm()});pm().destination[e].state=0;om({ctid:e,isDestination:!0},d);O(91)}else{c.siloed&&Qm({ctid:e,isDestination:!0});var g;(g=pm().destination)[e]!=null||(g[e]={context:c,state:1,parent:Hm()});pm().destination[e].state=1;om({ctid:e,isDestination:!0},d);var h={destinationId:e,endpoint:0};if(jk())gm(h,
ik()+("/gtd"+VB(a,!0)));else{var m="/gtag/destination"+VB(a,!0),n=Wk(b,m);n||(n=ww("https://","http://",Oj.Bg+m));gm(h,n)}}}}function VB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Rj!=="dataLayer"&&(c+="&l="+Rj);if(!Eb(a,"GTM-")||b)c=H(130)?c+(jk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Vr();Xk()&&(c+="&sign="+Oj.Oi);var d=Kj.J;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!H(191)&&Mj().join("~")&&(c+="&tag_exp="+Mj().join("~"));return c}
function UB(){if(Qr()){return!0}return!1};var YB=function(){this.J=0;this.D={}};YB.prototype.addListener=function(a,b,c){var d=++this.J;this.D[a]=this.D[a]||{};this.D[a][String(d)]={listener:b,bc:c};return d};YB.prototype.removeListener=function(a,b){var c=this.D[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var $B=function(a,b){var c=[];sb(ZB.D[a],function(d,e){c.indexOf(e.listener)<0&&(e.bc===void 0||b.indexOf(e.bc)>=0)&&c.push(e.listener)});return c};function aC(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:Cm()}};var cC=function(a,b){this.D=!1;this.R=[];this.eventData={tags:[]};this.T=!1;this.J=this.O=0;bC(this,a,b)},dC=function(a,b,c,d){if(Tj.hasOwnProperty(b)||b==="__zone")return-1;var e={};hd(d)&&(e=id(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},eC=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},fC=function(a){if(!a.D){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.D=!0;a.R.length=0}},bC=function(a,b,c){b!==void 0&&a.Pf(b);c&&y.setTimeout(function(){fC(a)},
Number(c))};cC.prototype.Pf=function(a){var b=this,c=Bb(function(){D(function(){a(Cm(),b.eventData)})});this.D?c():this.R.push(c)};var gC=function(a){a.O++;return Bb(function(){a.J++;a.T&&a.J>=a.O&&fC(a)})},hC=function(a){a.T=!0;a.J>=a.O&&fC(a)};var iC={};function jC(){return y[kC()]}
function kC(){return y.GoogleAnalyticsObject||"ga"}function nC(){var a=Cm();}
function oC(a,b){return function(){var c=jC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var uC=["es","1"],vC={},wC={};function xC(a,b){if(el){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";vC[a]=[["e",c],["eid",a]];Gq(a)}}function yC(a){var b=a.eventId,c=a.Kd;if(!vC[b])return[];var d=[];wC[b]||d.push(uC);d.push.apply(d,ua(vC[b]));c&&(wC[b]=!0);return d};var zC={},AC={},BC={};function CC(a,b,c,d){el&&H(120)&&((d===void 0?0:d)?(BC[b]=BC[b]||0,++BC[b]):c!==void 0?(AC[a]=AC[a]||{},AC[a][b]=Math.round(c)):(zC[a]=zC[a]||{},zC[a][b]=(zC[a][b]||0)+1))}function DC(a){var b=a.eventId,c=a.Kd,d=zC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete zC[b];return e.length?[["md",e.join(".")]]:[]}
function EC(a){var b=a.eventId,c=a.Kd,d=AC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete AC[b];return e.length?[["mtd",e.join(".")]]:[]}function FC(){for(var a=[],b=l(Object.keys(BC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+BC[d])}return a.length?[["mec",a.join(".")]]:[]};var GC={},HC={};function IC(a,b,c){if(el&&b){var d=al(b);GC[a]=GC[a]||[];GC[a].push(c+d);var e=(Sf(b)?"1":"2")+d;HC[a]=HC[a]||[];HC[a].push(e);Gq(a)}}function JC(a){var b=a.eventId,c=a.Kd,d=[],e=GC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=HC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete GC[b],delete HC[b]);return d};function KC(a,b,c,d){var e=Ef[a],f=LC(a,b,c,d);if(!f)return null;var g=Tf(e[df.Jl],c,[]);if(g&&g.length){var h=g[0];f=KC(h.index,{onSuccess:f,onFailure:h.im===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function LC(a,b,c,d){function e(){function w(){$n(3);var I=zb()-G;IC(c.id,f,"7");eC(c.Oc,C,"exception",I);H(109)&&GA(c,f,bA.P.Ri);F||(F=!0,h())}if(f[df.po])h();else{var x=Rf(f,c,[]),z=x[df.Vm];if(z!=null)for(var B=0;B<z.length;B++)if(!sp(z[B])){h();return}var C=dC(c.Oc,String(f[df.Ha]),Number(f[df.rh]),x[df.METADATA]),F=!1;x.vtp_gtmOnSuccess=function(){if(!F){F=!0;var I=zb()-G;IC(c.id,Ef[a],"5");eC(c.Oc,C,"success",I);H(109)&&GA(c,f,bA.P.Ti);g()}};x.vtp_gtmOnFailure=function(){if(!F){F=!0;var I=zb()-
G;IC(c.id,Ef[a],"6");eC(c.Oc,C,"failure",I);H(109)&&GA(c,f,bA.P.Si);h()}};x.vtp_gtmTagId=f.tag_id;x.vtp_gtmEventId=c.id;c.priorityId&&(x.vtp_gtmPriorityId=c.priorityId);IC(c.id,f,"1");H(109)&&FA(c,f);var G=zb();try{Uf(x,{event:c,index:a,type:1})}catch(I){w(I)}H(109)&&GA(c,f,bA.P.Pl)}}var f=Ef[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Tf(f[df.Ql],c,[]);if(n&&n.length){var p=n[0],q=KC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.im===
2?m:q}if(f[df.Al]||f[df.ro]){var r=f[df.Al]?Ff:c.wq,t=g,u=h;if(!r[a]){var v=MC(a,r,Bb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function MC(a,b,c){var d=[],e=[];b[a]=NC(d,e,c);return{onSuccess:function(){b[a]=OC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=PC;for(var f=0;f<e.length;f++)e[f]()}}}function NC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function OC(a){a()}function PC(a,b){b()};var SC=function(a,b){for(var c=[],d=0;d<Ef.length;d++)if(a[d]){var e=Ef[d];var f=gC(b.Oc);try{var g=KC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[df.Ha];if(!h)throw Error("Error: No function name given for function call.");var m=Gf[h];c.push({Mm:d,priorityOverride:(m?m.priorityOverride||0:0)||EB(e[df.Ha],1)||0,execute:g})}else QC(d,b),f()}catch(p){f()}}c.sort(RC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function TC(a,b){if(!ZB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=$B(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=gC(b);try{d[e](a,f)}catch(g){f()}}return!0}function RC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Mm,h=b.Mm;f=g>h?1:g<h?-1:0}return f}
function QC(a,b){if(el){var c=function(d){var e=b.isBlocked(Ef[d])?"3":"4",f=Tf(Ef[d][df.Jl],b,[]);f&&f.length&&c(f[0].index);IC(b.id,Ef[d],e);var g=Tf(Ef[d][df.Ql],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var UC=!1,ZB;function VC(){ZB||(ZB=new YB);return ZB}
function WC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(H(109)){}if(d==="gtm.js"){if(UC)return!1;UC=!0}var e=!1,f=IB(),g=id(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}xC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:XC(g,e),wq:[],logMacroError:function(){O(6);$n(0)},cachedModelValues:YC(),Oc:new cC(function(){if(H(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};H(120)&&el&&(n.reportMacroDiscrepancy=CC);H(109)&&CA(n.id);var p=Zf(n);H(109)&&DA(n.id);e&&(p=ZC(p));H(109)&&BA(b);var q=SC(p,n),r=TC(a,n.Oc);hC(n.Oc);d!=="gtm.js"&&d!=="gtm.sync"||nC();return $C(p,q)||r}function YC(){var a={};a.event=uk("event",1);a.ecommerce=uk("ecommerce",1);a.gtm=uk("gtm");a.eventModel=uk("eventModel");return a}
function XC(a,b){var c=PB();return function(d){if(c(d))return!0;var e=d&&d[df.Ha];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Em();f=GB().getRestrictions(0,g);var h=a;b&&(h=id(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=fk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function ZC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Ef[c][df.Ha]);if(Sj[d]||Ef[c][df.so]!==void 0||EB(d,2))b[c]=!0}return b}function $C(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Ef[c]&&!Tj[String(Ef[c][df.Ha])])return!0;return!1};function aD(){VC().addListener("gtm.init",function(a,b){Kj.la=!0;Kn();b()})};var bD=!1,cD=0,dD=[];function eD(a){if(!bD){var b=A.createEventObject,c=A.readyState==="complete",d=A.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){bD=!0;for(var e=0;e<dD.length;e++)D(dD[e])}dD.push=function(){for(var f=ya.apply(0,arguments),g=0;g<f.length;g++)D(f[g]);return 0}}}function fD(){if(!bD&&cD<140){cD++;try{var a,b;(b=(a=A.documentElement).doScroll)==null||b.call(a,"left");eD()}catch(c){y.setTimeout(fD,50)}}}
function gD(){bD=!1;cD=0;if(A.readyState==="interactive"&&!A.createEventObject||A.readyState==="complete")eD();else{Ic(A,"DOMContentLoaded",eD);Ic(A,"readystatechange",eD);if(A.createEventObject&&A.documentElement.doScroll){var a=!0;try{a=!y.frameElement}catch(b){}a&&fD()}Ic(y,"load",eD)}}function hD(a){bD?a():dD.push(a)};var iD={},jD={};function kD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Dj:void 0,lj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Dj=Op(g,b),e.Dj){var h=um?um:Bm();ob(h,function(r){return function(t){return r.Dj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=iD[g]||[];e.lj={};m.forEach(function(r){return function(t){r.lj[t]=!0}}(e));for(var n=xm(),p=0;p<n.length;p++)if(e.lj[n[p]]){c=c.concat(Am());break}var q=jD[g]||[];q.length&&(c=c.concat(q))}}return{wj:c,Vp:d}}
function lD(a){sb(iD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function mD(a){sb(jD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var nD=!1,oD=!1;function pD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=id(b,null),b[M.m.Xe]&&(d.eventCallback=b[M.m.Xe]),b[M.m.Qg]&&(d.eventTimeout=b[M.m.Qg]));return d}function qD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Hp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function rD(a,b){var c=a&&a[M.m.kd];c===void 0&&(c=pk(M.m.kd,2),c===void 0&&(c="default"));if(lb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?lb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=kD(d,b.isGtmEvent),f=e.wj,g=e.Vp;if(g.length)for(var h=sD(a),m=0;m<g.length;m++){var n=Op(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q;if(!(q=Eb(p,"siloed_"))){var r=n.destinationId,t=pm().destination[r];q=!!t&&t.state===0}q||XB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var u=
f.concat(g);return{wj:Pp(f,b.isGtmEvent),Jo:Pp(u,b.isGtmEvent)}}}var tD=void 0,uD=void 0;function vD(a,b,c){var d=id(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=id(b,null);id(c,e);Tw(Pw(xm()[0],e),a.eventId,d)}function sD(a){for(var b=l([M.m.ld,M.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Oq.D[d];if(e)return e}}
var wD={config:function(a,b){var c=qD(a,b);if(!(a.length<2)&&lb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!hd(a[2])||a.length>3)return;d=a[2]}var e=Op(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!sm.he){var m=Gm(Hm());if(Tm(m)){var n=m.parent,p=n.isDestination;h={Xp:Gm(n),Sp:p};break a}}h=void 0}var q=h;q&&(f=q.Xp,g=q.Sp);xC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Am().indexOf(r)===-1:xm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[M.m.Kc]){var u=sD(d);if(t)XB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;tD?vD(b,v,tD):uD||(uD=id(v,null))}else TB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var w;var x=d;uD?(vD(b,uD,x),w=!1):(!x[M.m.nd]&&Vj&&tD||(tD=id(x,null)),w=!0);w&&f.containers&&f.containers.join(",");return}fl&&(Jp===1&&(Cn.mcc=!1),Jp=2);if(Vj&&!t&&!d[M.m.nd]){var z=oD;oD=!0;if(z)return}nD||O(43);if(!b.noTargetGroup)if(t){mD(e.id);
var B=e.id,C=d[M.m.Tg]||"default";C=String(C).split(",");for(var F=0;F<C.length;F++){var G=jD[C[F]]||[];jD[C[F]]=G;G.indexOf(B)<0&&G.push(B)}}else{lD(e.id);var I=e.id,K=d[M.m.Tg]||"default";K=K.toString().split(",");for(var U=0;U<K.length;U++){var Q=iD[K[U]]||[];iD[K[U]]=Q;Q.indexOf(I)<0&&Q.push(I)}}delete d[M.m.Tg];var na=b.eventMetadata||{};na.hasOwnProperty(R.C.ud)||(na[R.C.ud]=!b.fromContainerExecution);b.eventMetadata=na;delete d[M.m.Xe];for(var T=t?[e.id]:Am(),ba=0;ba<T.length;ba++){var aa=
d,W=T[ba],ka=id(b,null),ja=Op(W,ka.isGtmEvent);ja&&Oq.push("config",[aa],ja,ka)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=qD(a,b),d=a[1],e={},f=Lo(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===M.m.wg?Array.isArray(h)?NaN:Number(h):g===M.m.fc?(Array.isArray(h)?h:[h]).map(Mo):No(h)}b.fromContainerExecution||(e[M.m.W]&&O(139),e[M.m.Na]&&O(140));d==="default"?op(e):d==="update"?qp(e,c):d==="declare"&&b.fromContainerExecution&&np(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&lb(c)){var d=void 0;if(a.length>2){if(!hd(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=pD(c,d),f=qD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=rD(d,b);if(m){var n=m.wj,p=m.Jo,q,r,t;if(!tm&&H(108)){q=p.map(function(I){return I.id});r=p.map(function(I){return I.destinationId});t=n.map(function(I){return I.id});for(var u=l(um?um:Bm()),v=u.next();!v.done;v=u.next()){var w=v.value;
!Eb(w,"siloed_")&&r.indexOf(w)<0&&r.indexOf(zm(w))<0&&t.push(w)}}else q=n.map(function(I){return I.id}),r=n.map(function(I){return I.destinationId}),t=q;xC(g,c);for(var x=l(t),z=x.next();!z.done;z=x.next()){var B=z.value,C=id(b,null),F=id(d,null);delete F[M.m.Xe];var G=C.eventMetadata||{};G.hasOwnProperty(R.C.ud)||(G[R.C.ud]=!C.fromContainerExecution);G[R.C.Mi]=q.slice();G[R.C.Mf]=r.slice();C.eventMetadata=G;Pq(c,F,B,C)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[M.m.kd]=q.join(","):delete e.eventModel[M.m.kd];
nD||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[R.C.Ol]&&(b.noGtmEvent=!0);e.eventModel[M.m.Jc]&&(b.noGtmEvent=!0);return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&lb(a[1])&&lb(a[2])&&kb(a[3])){var c=Op(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){nD||O(43);var f=sD();if(ob(Am(),function(h){return c.destinationId===h})){qD(a,b);var g={};id((g[M.m.qc]=d,g[M.m.Hc]=e,g),null);Qq(d,function(h){D(function(){e(h)})},c.id,b)}else XB(c.destinationId,f,{source:4,
fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){nD=!0;var c=qD(a,b),d=c.eventId,e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&lb(a[1])&&kb(a[2])){if(eg(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](Cm(),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===2&&hd(a[1])?c=id(a[1],null):a.length===
3&&lb(a[1])&&(c={},hd(a[2])||Array.isArray(a[2])?c[a[1]]=id(a[2],null):c[a[1]]=a[2]);if(c){var d=qD(a,b),e=d.eventId,f=d.priorityId;id(c,null);var g=id(c,null);Oq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},xD={policy:!0};var zD=function(a){if(yD(a))return a;this.value=a};zD.prototype.getUntrustedMessageValue=function(){return this.value};var yD=function(a){return!a||fd(a)!=="object"||hd(a)?!1:"getUntrustedMessageValue"in a};zD.prototype.getUntrustedMessageValue=zD.prototype.getUntrustedMessageValue;var AD=!1,BD=[];function CD(){if(!AD){AD=!0;for(var a=0;a<BD.length;a++)D(BD[a])}}function DD(a){AD?D(a):BD.push(a)};var ED=0,FD={},GD=[],HD=[],ID=!1,JD=!1;function KD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function LD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return MD(a)}function ND(a,b){if(!mb(b)||b<0)b=0;var c=Cp[Rj],d=0,e=!1,f=void 0;f=y.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(y.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function OD(a,b){var c=a._clear||b.overwriteModelFields;sb(a,function(e,f){e!=="_clear"&&(c&&sk(e),sk(e,f))});ck||(ck=a["gtm.start"]);var d=a["gtm.uniqueEventId"];if(!a.event)return!1;typeof d!=="number"&&(d=Hp(),a["gtm.uniqueEventId"]=d,sk("gtm.uniqueEventId",d));return WC(a)}function PD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(tb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function QD(){var a;if(HD.length)a=HD.shift();else if(GD.length)a=GD.shift();else return;var b;var c=a;if(ID||!PD(c.message))b=c;else{ID=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Hp(),f=Hp(),c.message["gtm.uniqueEventId"]=Hp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};GD.unshift(n,c);b=h}return b}
function RD(){for(var a=!1,b;!JD&&(b=QD());){JD=!0;delete mk.eventModel;ok();var c=b,d=c.message,e=c.messageContext;if(d==null)JD=!1;else{e.fromContainerExecution&&tk();try{if(kb(d))try{d.call(qk)}catch(u){}else if(Array.isArray(d)){if(lb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=pk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(u){}}}else{var n=void 0;if(tb(d))a:{if(d.length&&lb(d[0])){var p=wD[d[0]];if(p&&(!e.fromContainerExecution||!xD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;n&&(a=OD(n,e)||a)}}finally{e.fromContainerExecution&&ok(!0);var q=d["gtm.uniqueEventId"];if(typeof q==="number"){for(var r=FD[String(q)]||[],t=0;t<r.length;t++)HD.push(SD(r[t]));r.length&&HD.sort(KD);delete FD[String(q)];q>ED&&(ED=q)}JD=!1}}}return!a}
function TD(){if(H(109)){var a=!Kj.R;}var c=RD();if(H(109)){}try{var e=Cm(),f=y[Rj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Ww(a){if(ED<a.notBeforeEventId){var b=String(a.notBeforeEventId);FD[b]=FD[b]||[];FD[b].push(a)}else HD.push(SD(a)),HD.sort(KD),D(function(){JD||RD()})}function SD(a){return{message:a.message,messageContext:a.messageContext}}
function UD(){function a(f){var g={};if(yD(f)){var h=f;f=yD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=tc(Rj,[]),c=Cp[Rj]=Cp[Rj]||{};c.pruned===!0&&O(83);FD=Uw().get();Vw();hD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});DD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Cp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new zD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});GD.push.apply(GD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return RD()&&p};var e=b.slice(0).map(function(f){return a(f)});GD.push.apply(GD,e);if(!Kj.R){if(H(109)){}D(TD)}}var MD=function(a){return y[Rj].push(a)};function VD(a){MD(a)};function WD(){var a,b=Qk(y.location.href);(a=b.hostname+b.pathname)&&Gn("dl",encodeURIComponent(a));var c;var d=hg.ctid;if(d){var e=sm.he?1:0,f,g=Gm(Hm());f=g&&g.context;c=d+";"+hg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Gn("tdp",h);var m=Fl(!0);m!==void 0&&Gn("frm",String(m))};function XD(){(Yo()||fl)&&y.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){O(179);var b=bm(a.effectiveDirective);if(b){var c;var d=$l(b,a.blockedURI);c=d?Yl[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.Em){p.Em=!0;if(H(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Yo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Yo()){var u=dp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Xo(u)}}}Mn(p.endpoint)}}am(b,a.blockedURI)}}}}})};function YD(){var a;var b=Fm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Gn("pcid",e)};var ZD=/^(https?:)?\/\//;
function $D(){var a;var b=Gm(Hm());if(b){for(;b.parent;){var c=Gm(b.parent);if(!c)break;b=c}a=b}else a=void 0;var d=a;if(d){var e;a:{var f,g=(f=d.scriptElement)==null?void 0:f.src;if(g){var h;try{var m;h=(m=Xc())==null?void 0:m.getEntriesByType("resource")}catch(u){}if(h){for(var n=-1,p=l(h),q=p.next();!q.done;q=p.next()){var r=q.value;if(r.initiatorType==="script"&&(n+=1,r.name.replace(ZD,"")===g.replace(ZD,""))){e=n;break a}}O(146)}else O(145)}e=void 0}var t=e;t!==void 0&&(d.canonicalContainerId&&
Gn("rtg",String(d.canonicalContainerId)),Gn("slo",String(t)),Gn("hlo",d.htmlLoadOrder||"-1"),Gn("lst",String(d.loadScriptType||"0")))}else O(144)};function aE(){var a=[],b=Number('1')||0,c=function(){var f=!1;return f}();a.push({Lm:195,Km:195,experimentId:104527906,controlId:104527907,percent:b,active:c,dj:1});var d=Number('1')||0,e=function(){var f=!1;
return f}();a.push({Lm:196,Km:196,experimentId:104528500,controlId:104528501,percent:d,active:e,dj:0});return a};var bE={};function cE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Kj.ia.J.add(Number(c.value))}function dE(){if(H(194))for(var a=l(aE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Lm;ii[d]=c;if(c.dj===1){var e=d,f=go(ao.aa.vo);li(f,e);cE(f)}else if(c.dj===0){var g=bE;li(g,d);cE(g)}}};

function yE(){};var zE=function(){};zE.prototype.toString=function(){return"undefined"};var AE=new zE;function HE(a,b){function c(g){var h=Qk(g),m=Kk(h,"protocol"),n=Kk(h,"host",!0),p=Kk(h,"port"),q=Kk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function IE(a){return JE(a)?1:0}
function JE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=id(a,{});id({arg1:c[d],any_of:void 0},e);if(IE(e))return!0}return!1}switch(a["function"]){case "_cn":return Ng(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Ig.length;g++){var h=Ig[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Jg(b,c);case "_eq":return Og(b,c);case "_ge":return Pg(b,c);case "_gt":return Rg(b,c);case "_lc":return Kg(b,c);case "_le":return Qg(b,
c);case "_lt":return Sg(b,c);case "_re":return Mg(b,c,a.ignore_case);case "_sw":return Tg(b,c);case "_um":return HE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var KE=function(a,b,c,d){er.call(this);this.nh=b;this.If=c;this.Eb=d;this.Za=new Map;this.qh=0;this.la=new Map;this.Ca=new Map;this.T=void 0;this.J=a};sa(KE,er);KE.prototype.O=function(){delete this.D;this.Za.clear();this.la.clear();this.Ca.clear();this.T&&(ar(this.J,"message",this.T),delete this.T);delete this.J;delete this.Eb;er.prototype.O.call(this)};
var LE=function(a){if(a.D)return a.D;a.If&&a.If(a.J)?a.D=a.J:a.D=El(a.J,a.nh);var b;return(b=a.D)!=null?b:null},NE=function(a,b,c){if(LE(a))if(a.D===a.J){var d=a.Za.get(b);d&&d(a.D,c)}else{var e=a.la.get(b);if(e&&e.vj){ME(a);var f=++a.qh;a.Ca.set(f,{Ih:e.Ih,Uo:e.om(c),persistent:b==="addEventListener"});a.D.postMessage(e.vj(c,f),"*")}}},ME=function(a){a.T||(a.T=function(b){try{var c;c=a.Eb?a.Eb(b):void 0;if(c){var d=c.aq,e=a.Ca.get(d);if(e){e.persistent||a.Ca.delete(d);var f;(f=e.Ih)==null||f.call(e,
e.Uo,c.payload)}}}catch(g){}},$q(a.J,"message",a.T))};var OE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},PE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},QE={om:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Ih:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},RE={om:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Ih:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function SE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,aq:b.__gppReturn.callId}}
var TE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;er.call(this);this.caller=new KE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},SE);this.caller.Za.set("addEventListener",OE);this.caller.la.set("addEventListener",QE);this.caller.Za.set("removeEventListener",PE);this.caller.la.set("removeEventListener",RE);this.timeoutMs=c!=null?c:500};sa(TE,er);TE.prototype.O=function(){this.caller.dispose();er.prototype.O.call(this)};
TE.prototype.addEventListener=function(a){var b=this,c=hl(function(){a(UE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);NE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(VE,!0);return}a(WE,!0)}}})};
TE.prototype.removeEventListener=function(a){NE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var WE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},UE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},VE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function XE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Cv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Cv.D=d}}function YE(){try{var a=new TE(y,{timeoutMs:-1});LE(a.caller)&&a.addEventListener(XE)}catch(b){}};function ZE(){var a=[["cv",Oi(1)],["rv",Pj],["tc",Ef.filter(function(b){return b}).length]];Qj&&a.push(["x",Qj]);hk()&&a.push(["tag_exp",hk()]);return a};var $E={};function Ri(a){$E[a]=($E[a]||0)+1}function aF(){for(var a=[],b=l(Object.keys($E)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+$E[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var bF={},cF={};function dF(a){var b=a.eventId,c=a.Kd,d=[],e=bF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=cF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete bF[b],delete cF[b]);return d};function eF(){return!1}function fF(){var a={};return function(b,c,d){}};function gF(){var a=hF;return function(b,c,d){var e=d&&d.event;iF(c);var f=yh(b)?void 0:1,g=new Ua;sb(c,function(r,t){var u=yd(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.D.D.J=Xf();var h={Xl:lg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Pf:e!==void 0?function(r){e.Oc.Pf(r)}:void 0,Hb:function(){return b},log:function(){},hp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},iq:!!EB(b,3),originalEventData:e==null?void 0:
e.originalEventData};e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(eF()){var m=fF(),n,p;h.tb={Oj:[],Qf:{},Zb:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Gh:Qh()};h.log=function(r){var t=ya.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=
Ue(a,h,[b,g]);a.D.D.J=void 0;q instanceof Ba&&(q.type==="return"?q=q.data:q=void 0);return xd(q,void 0,f)}}function iF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;kb(b)&&(a.gtmOnSuccess=function(){D(b)});kb(c)&&(a.gtmOnFailure=function(){D(c)})};function jF(a){}jF.N="internal.addAdsClickIds";function kF(a,b){var c=this;}kF.publicName="addConsentListener";var lF=!1;function mF(a){for(var b=0;b<a.length;++b)if(lF)try{a[b]()}catch(c){O(77)}else a[b]()}function nF(a,b,c){var d=this,e;if(!jh(a)||!fh(b)||!kh(c))throw J(this.getName(),["string","function","string|undefined"],arguments);mF([function(){L(d,"listen_data_layer",a)}]);e=VC().addListener(a,xd(b),c===null?void 0:c);return e}nF.N="internal.addDataLayerEventListener";function oF(a,b,c){}oF.publicName="addDocumentEventListener";function pF(a,b,c,d){}pF.publicName="addElementEventListener";function qF(a){return a.M.D};function rF(a){}rF.publicName="addEventCallback";
var sF=function(a){return typeof a==="string"?a:String(Hp())},vF=function(a,b){tF(a,"init",!1)||(uF(a,"init",!0),b())},tF=function(a,b,c){var d=wF(a);return Ab(d,b,c)},xF=function(a,b,c,d){var e=wF(a),f=Ab(e,b,d);e[b]=c(f)},uF=function(a,b,c){wF(a)[b]=c},wF=function(a){var b=Dp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},yF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":Uc(a,"className"),"gtm.elementId":a.for||Kc(a,"id")||"","gtm.elementTarget":a.formTarget||
Uc(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||Uc(a,"href")||a.src||a.code||a.codebase||"";return d};
var AF=function(a,b,c){if(!a.elements)return 0;for(var d=b.dataset[c],e=0,f=1;e<a.elements.length;e++){var g=a.elements[e];if(zF(g)){if(g.dataset[c]===d)return f;f++}}return 0},BF=function(a){if(a.form){var b;return((b=a.form)==null?0:b.tagName)?a.form:A.getElementById(a.form)}return Nc(a,["form"],100)},zF=function(a){var b=a.tagName.toLowerCase();return CF.indexOf(b)<0||b==="input"&&DF.indexOf(a.type.toLowerCase())>=0?!1:!0},CF=["input","select","textarea"],DF=["button","hidden","image","reset",
"submit"];
function HF(a){}HF.N="internal.addFormAbandonmentListener";function IF(a,b,c,d){}
IF.N="internal.addFormData";var JF={},KF=[],LF={},MF=0,NF=0;
var PF=function(){Ic(A,"change",function(a){for(var b=0;b<KF.length;b++)KF[b](a)});Ic(y,"pagehide",function(){OF()})},OF=function(){sb(LF,function(a,b){var c=JF[a];c&&sb(b,function(d,e){QF(e,c)})})},TF=function(a,b){var c=""+a;if(JF[c])JF[c].push(b);else{var d=[b];JF[c]=d;var e=LF[c];e||(e={},LF[c]=e);KF.push(function(f){var g=f.target;if(g){var h=BF(g);if(h){var m=RF(h,"gtmFormInteractId",function(){return MF++}),n=RF(g,"gtmFormInteractFieldId",function(){return NF++}),p=e[m];p?(p.Dc&&(y.clearTimeout(p.Dc),
p.ac.dataset.gtmFormInteractFieldId!==n&&QF(p,d)),p.ac=g,SF(p,d,a)):(e[m]={form:h,ac:g,sequenceNumber:0,Dc:null},SF(e[m],d,a))}}})}},QF=function(a,b){var c=a.form,d=a.ac,e=yF(c,"gtm.formInteract"),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name");e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldPosition"]=AF(c,d,"gtmFormInteractFieldId");e["gtm.interactSequenceNumber"]=a.sequenceNumber;
e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name");e["gtm.interactedFormFieldType"]=d.getAttribute("type");for(var g=0;g<b.length;g++)b[g](e);a.sequenceNumber++;a.Dc=null},SF=function(a,b,c){c?a.Dc=y.setTimeout(function(){QF(a,b)},c):QF(a,b)},RF=function(a,b,c){var d=a.dataset[b];if(d)return d;d=String(c());return a.dataset[b]=d};
function UF(a,b){if(!fh(a)||!dh(b))throw J(this.getName(),["function","Object|undefined"],arguments);var c=xd(b)||{},d=Number(c.interval);if(!d||d<0)d=0;var e=xd(a),f;tF("pix.fil","init")?f=tF("pix.fil","reg"):(PF(),f=TF,uF("pix.fil","reg",TF),uF("pix.fil","init",!0));f(d,e);}UF.N="internal.addFormInteractionListener";
var WF=function(a,b,c){var d=yF(a,"gtm.formSubmit");d["gtm.interactedFormName"]=a.getAttribute("name");d["gtm.interactedFormLength"]=a.length;d["gtm.willOpenInCurrentWindow"]=!b&&VF(a);c&&c.value&&(d["gtm.formSubmitButtonText"]=c.value);var e=a.action;e&&e.tagName&&(e=a.cloneNode(!1).action);d["gtm.elementUrl"]=e;d["gtm.formCanceled"]=b;return d},XF=function(a,b){var c=tF("pix.fsl",a?"nv.mwt":"mwt",0);y.setTimeout(b,c)},YF=function(a,b,c,d,e){var f=tF("pix.fsl",c?"nv.mwt":"mwt",0),g=tF("pix.fsl",
c?"runIfCanceled":"runIfUncanceled",[]);if(!g.length)return!0;var h=WF(a,c,e);O(121);if(h["gtm.elementUrl"]==="https://www.facebook.com/tr/")return O(122),!0;if(d&&f){for(var m=Jb(b,g.length),n=0;n<g.length;++n)g[n](h,m);return m.done}for(var p=0;p<g.length;++p)g[p](h,function(){});return!0},ZF=function(){var a=[],b=function(c){return ob(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);return d?d.button:null}}},
VF=function(a){var b=Uc(a,"target");return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},$F=function(){var a=ZF(),b=HTMLFormElement.prototype.submit;Ic(A,"click",function(c){var d=c.target;if(d){var e=Nc(d,["button","input"],100);if(e&&(e.type==="submit"||e.type==="image")&&e.name&&Kc(e,"value")){var f=BF(e);f&&a.store(f,e)}}},!1);Ic(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=VF(d)&&!e,g=a.get(d),h=!0,m=function(){if(h){var n,
p={};g&&(n=A.createElement("input"),n.type="hidden",n.name=g.name,n.value=g.value,d.appendChild(n),g.getAttribute("formaction")&&(p.action=d.getAttribute("action"),fc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(p.enctype=d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(p.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(p.validate=d.getAttribute("validate"),
d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(p.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);n&&(d.removeChild(n),p.hasOwnProperty("action")&&fc(d,p.action),p.hasOwnProperty("enctype")&&d.setAttribute("enctype",p.enctype),p.hasOwnProperty("method")&&d.setAttribute("method",p.method),p.hasOwnProperty("validate")&&d.setAttribute("validate",p.validate),p.hasOwnProperty("target")&&d.setAttribute("target",
p.target))}};if(YF(d,m,e,f,g))return h=!1,c.returnValue;XF(e,m);e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1);return!1},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0,e=function(){d&&b.call(c)};YF(c,e,!1,VF(c))?(b.call(c),d=!1):XF(!1,e)}};
function aG(a,b){if(!fh(a)||!dh(b))throw J(this.getName(),["function","Object|undefined"],arguments);var c=xd(b,this.M,1)||{},d=c.waitForCallbacks,e=c.waitForCallbacksTimeout,f=c.checkValidation;e=e&&e>0?e:2E3;var g=xd(a,this.M,1);if(d){var h=function(n){return Math.max(e,n)};xF("pix.fsl","mwt",h,0);f||xF("pix.fsl","nv.mwt",h,0)}var m=function(n){n.push(g);return n};xF("pix.fsl","runIfUncanceled",m,[]);f||xF("pix.fsl","runIfCanceled",
m,[]);tF("pix.fsl","init")||($F(),uF("pix.fsl","init",!0));}aG.N="internal.addFormSubmitListener";
function fG(a){}fG.N="internal.addGaSendListener";function gG(a){if(!a)return{};var b=a.hp;return aC(b.type,b.index,b.name)}function hG(a){return a?{originatingEntity:gG(a)}:{}};function pG(a){var b=Cp.zones;return b?b.getIsAllowedFn(xm(),a):function(){return!0}}function qG(){var a=Cp.zones;a&&a.unregisterChild(xm())}
function rG(){HB(Em(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Cp.zones;return c?c.isActive(xm(),b):!0});FB(Em(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return pG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var sG=function(a,b){this.tagId=a;this.oe=b};
function tG(a,b){var c=this,d=void 0;
return d}tG.N="internal.loadGoogleTag";function uG(a){return new pd("",function(b){var c=this.evaluate(b);if(c instanceof pd)return new pd("",function(){var d=ya.apply(0,arguments),e=this,f=id(qF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=Na(this.M);h.D=f;return c.Kb.apply(c,[h].concat(ua(g)))})})};function vG(a,b,c){var d=this;}vG.N="internal.addGoogleTagRestriction";var wG={},xG=[];
function EG(a,b){}
EG.N="internal.addHistoryChangeListener";function FG(a,b,c){}FG.publicName="addWindowEventListener";function GG(a,b){return!0}GG.publicName="aliasInWindow";function HG(a,b,c){}HG.N="internal.appendRemoteConfigParameter";function IG(a){var b;return b}
IG.publicName="callInWindow";function JG(a){}JG.publicName="callLater";function KG(a){}KG.N="callOnDomReady";function LG(a){}LG.N="callOnWindowLoad";function MG(a,b){var c;return c}MG.N="internal.computeGtmParameter";function NG(a,b){var c=this;}NG.N="internal.consentScheduleFirstTry";function OG(a,b){var c=this;}OG.N="internal.consentScheduleRetry";function PG(a){var b;return b}PG.N="internal.copyFromCrossContainerData";function QG(a,b){var c;var d=yd(c,this.M,yh(qF(this).Hb())?2:1);d===void 0&&c!==void 0&&O(45);return d}QG.publicName="copyFromDataLayer";
function RG(a){var b=void 0;return b}RG.N="internal.copyFromDataLayerCache";function SG(a){var b;return b}SG.publicName="copyFromWindow";function TG(a){var b=void 0;return yd(b,this.M,1)}TG.N="internal.copyKeyFromWindow";var UG=function(a){return a===Xm.Z.Ea&&pn[a]===Wm.Ka.fe&&!sp(M.m.V)};var VG=function(){return"0"},WG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];H(102)&&b.push("gbraid");return Rk(a,b,"0")};var XG={},YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH=(vH[M.m.Sa]=(XG[2]=[UG],XG),vH[M.m.nf]=(YG[2]=[UG],YG),vH[M.m.Ye]=(ZG[2]=[UG],ZG),vH[M.m.ri]=($G[2]=[UG],$G),vH[M.m.si]=(aH[2]=[UG],aH),vH[M.m.ui]=(bH[2]=[UG],bH),vH[M.m.wi]=(cH[2]=[UG],cH),vH[M.m.xi]=(dH[2]=[UG],dH),vH[M.m.Ub]=(eH[2]=[UG],eH),vH[M.m.qf]=(fH[2]=[UG],fH),vH[M.m.rf]=(gH[2]=[UG],gH),vH[M.m.tf]=(hH[2]=[UG],hH),vH[M.m.uf]=(iH[2]=
[UG],iH),vH[M.m.vf]=(jH[2]=[UG],jH),vH[M.m.wf]=(kH[2]=[UG],kH),vH[M.m.xf]=(lH[2]=[UG],lH),vH[M.m.yf]=(mH[2]=[UG],mH),vH[M.m.nb]=(nH[1]=[UG],nH),vH[M.m.Yc]=(oH[1]=[UG],oH),vH[M.m.ed]=(pH[1]=[UG],pH),vH[M.m.Ud]=(qH[1]=[UG],qH),vH[M.m.Je]=(rH[1]=[function(a){return H(102)&&UG(a)}],rH),vH[M.m.fd]=(sH[1]=[UG],sH),vH[M.m.Ba]=(tH[1]=[UG],tH),vH[M.m.Wa]=(uH[1]=[UG],uH),vH),xH={},yH=(xH[M.m.nb]=VG,xH[M.m.Yc]=VG,xH[M.m.ed]=VG,xH[M.m.Ud]=VG,xH[M.m.Je]=VG,xH[M.m.fd]=function(a){if(!hd(a))return{};var b=id(a,
null);delete b.match_id;return b},xH[M.m.Ba]=WG,xH[M.m.Wa]=WG,xH),zH={},AH={},BH=(AH[R.C.Ta]=(zH[2]=[UG],zH),AH),CH={};var DH=function(a,b,c,d){this.D=a;this.O=b;this.R=c;this.T=d};DH.prototype.getValue=function(a){a=a===void 0?Xm.Z.Db:a;if(!this.O.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.T(this.D):this.D};DH.prototype.J=function(){return fd(this.D)==="array"||hd(this.D)?id(this.D,null):this.D};
var EH=function(){},FH=function(a,b){this.conditions=a;this.D=b},GH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new DH(c,e,g,a.D[b]||EH)},HH,IH;var JH=function(a,b,c){this.eventName=b;this.F=c;this.D={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;V(this,g,d[g])}},Sv=function(a,b){var c,d;return(c=a.D[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,S(a,R.C.Nf))},X=function(a,b,c){var d=a.D,e;c===void 0?e=void 0:(HH!=null||(HH=new FH(wH,yH)),e=GH(HH,b,c));d[b]=e};
JH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.D[a])==null?void 0:(e=d.J)==null?void 0:e.call(d);if(!c)return X(this,a,b),!0;if(!hd(c))return!1;X(this,a,Object.assign(c,b));return!0};var KH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.D)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.D[e])==null?void 0:(h=(g=f).J)==null?void 0:h.call(g)}return b};
JH.prototype.copyToHitData=function(a,b,c){var d=P(this.F,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&lb(d)&&H(92))try{d=c(d)}catch(e){}d!==void 0&&X(this,a,d)};
var S=function(a,b){var c=a.metadata[b];if(b===R.C.Nf){var d;return c==null?void 0:(d=c.J)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,S(a,R.C.Nf))},V=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(IH!=null||(IH=new FH(BH,CH)),e=GH(IH,b,c));d[b]=e},LH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).J)==null?void 0:
h.call(g)}return b},lw=function(a,b,c){var d=a.target.destinationId;tm||(d=Im(d));var e=$w(d);return e&&e[b]!==void 0?e[b]:c};function MH(a,b){var c;if(!ch(a)||!dh(b))throw J(this.getName(),["Object","Object|undefined"],arguments);var d=xd(b)||{},e=xd(a,this.M,1).yb(),f=e.F;d.omitEventContext&&(f=rq(new gq(e.F.eventId,e.F.priorityId)));var g=new JH(e.target,e.eventName,f);if(!d.omitHitData)for(var h=KH(e),m=l(Object.keys(h)),n=m.next();!n.done;n=m.next()){var p=n.value;X(g,p,h[p])}if(d.omitMetadata)g.metadata={};else for(var q=LH(e),r=l(Object.keys(q)),t=r.next();!t.done;t=
r.next()){var u=t.value;V(g,u,q[u])}g.isAborted=e.isAborted;c=yd(Iw(g),this.M,1);return c}MH.N="internal.copyPreHit";function NH(a,b){var c=null;return yd(c,this.M,2)}NH.publicName="createArgumentsQueue";function OH(a){return yd(function(c){var d=jC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
jC(),n=m&&m.getByName&&m.getByName(f);return(new y.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.M,1)}OH.N="internal.createGaCommandQueue";function PH(a){return yd(function(){if(!kb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.M,
yh(qF(this).Hb())?2:1)}PH.publicName="createQueue";function QH(a,b){var c=null;if(!jh(a)||!kh(b))throw J(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new ud(new RegExp(a,d))}catch(e){}return c}QH.N="internal.createRegex";function RH(a){}RH.N="internal.declareConsentState";function SH(a){var b="";return b}SH.N="internal.decodeUrlHtmlEntities";function TH(a,b,c){var d;return d}TH.N="internal.decorateUrlWithGaCookies";function UH(){}UH.N="internal.deferCustomEvents";function VH(a){var b;L(this,"detect_user_provided_data","auto");var c=xd(a)||{},d=zx({ve:!!c.includeSelector,we:!!c.includeVisibility,Uf:c.excludeElementSelectors,Xb:c.fieldFilters,Jh:!!c.selectMultipleElements});b=new Ua;var e=new ld;b.set("elements",e);for(var f=d.elements,g=0;g<f.length;g++)e.push(WH(f[g]));d.Ej!==void 0&&b.set("preferredEmailElement",WH(d.Ej));b.set("status",d.status);if(H(129)&&c.performDataLayerSearch&&!/Mobile|iPhone|iPad|iPod|Android|IEMobile/.test(pc&&
pc.userAgent||"")){}return b}
var XH=function(a){switch(a){case xx.hc:return"email";case xx.xd:return"phone_number";case xx.pd:return"first_name";case xx.wd:return"last_name";case xx.Qi:return"street";case xx.Mh:return"city";case xx.Li:return"region";case xx.Kf:return"postal_code";case xx.De:return"country"}},WH=function(a){var b=new Ua;b.set("userData",a.ma);b.set("tagName",a.tagName);a.querySelector!==void 0&&b.set("querySelector",a.querySelector);a.isVisible!==void 0&&b.set("isVisible",a.isVisible);if(H(33)){}else switch(a.type){case xx.hc:b.set("type","email")}return b};VH.N="internal.detectUserProvidedData";
function $H(a,b){return f}$H.N="internal.enableAutoEventOnClick";var cI=function(a){if(!aI){var b=function(){var c=A.body;if(c)if(bI)(new MutationObserver(function(){for(var e=0;e<aI.length;e++)D(aI[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Ic(c,"DOMNodeInserted",function(){d||(d=!0,D(function(){d=!1;for(var e=0;e<aI.length;e++)D(aI[e])}))})}};aI=[];A.body?b():D(b)}aI.push(a)},bI=!!y.MutationObserver,aI;
function hI(a,b){return p}hI.N="internal.enableAutoEventOnElementVisibility";function iI(){}iI.N="internal.enableAutoEventOnError";var jI={},kI=[],lI={},mI=0,nI=0;
var pI=function(){sb(lI,function(a,b){var c=jI[a];c&&sb(b,function(d,e){oI(e,c)})})},sI=function(a,b){var c=""+b;if(jI[c])jI[c].push(a);else{var d=[a];jI[c]=d;var e=lI[c];e||(e={},lI[c]=e);kI.push(function(f){var g=f.target;if(g){var h=BF(g);if(h){var m=qI(h,"gtmFormInteractId",function(){return mI++}),n=qI(g,"gtmFormInteractFieldId",function(){return nI++});if(m!==null&&n!==null){var p=e[m];p?(p.Dc&&(y.clearTimeout(p.Dc),p.ac.getAttribute("data-gtm-form-interact-field-id")!==n&&oI(p,d)),p.ac=g,rI(p,
d,b)):(e[m]={form:h,ac:g,sequenceNumber:0,Dc:null},rI(e[m],d,b))}}}})}},oI=function(a,b){var c=a.form,d=a.ac,e=yF(c,"gtm.formInteract",b),f=c.action;f&&f.tagName&&(f=c.cloneNode(!1).action);e["gtm.elementUrl"]=f;e["gtm.interactedFormName"]=c.getAttribute("name")!=null?c.getAttribute("name"):void 0;e["gtm.interactedFormLength"]=c.length;e["gtm.interactedFormField"]=d;e["gtm.interactedFormFieldId"]=d.id;e["gtm.interactedFormFieldName"]=d.getAttribute("name")!=null?d.getAttribute("name"):void 0;e["gtm.interactedFormFieldPosition"]=
AF(c,d,"gtmFormInteractFieldId");e["gtm.interactedFormFieldType"]=d.getAttribute("type")!=null?d.getAttribute("type"):void 0;e["gtm.interactSequenceNumber"]=a.sequenceNumber;MD(e);a.sequenceNumber++;a.Dc=null},rI=function(a,b,c){c?a.Dc=y.setTimeout(function(){oI(a,b)},c):oI(a,b)},qI=function(a,b,c){var d;try{if(d=a.dataset[b])return d;d=String(c());a.dataset[b]=d}catch(e){d=null}return d};
function tI(a,b){var c=this;if(!dh(a))throw J(this.getName(),["Object|undefined","any"],arguments);mF([function(){L(c,"detect_form_interaction_events")}]);var d=sF(b),e=a&&Number(a.get("interval"));e>0&&isFinite(e)||(e=0);if(tF("fil","init",!1)){var f=tF("fil","reg");if(f)f(d,e);else throw Error("Failed to register trigger: "+d);}else Ic(A,"change",function(g){for(var h=0;h<kI.length;h++)kI[h](g)}),Ic(y,"pagehide",function(){pI()}),
sI(d,e),uF("fil","reg",sI),uF("fil","init",!0);return d}tI.N="internal.enableAutoEventOnFormInteraction";
var uI=function(a,b,c,d,e){var f=tF("fsl",c?"nv.mwt":"mwt",0),g;g=c?tF("fsl","nv.ids",[]):tF("fsl","ids",[]);if(!g.length)return!0;var h=yF(a,"gtm.formSubmit",g),m=a.action;m&&m.tagName&&(m=a.cloneNode(!1).action);O(121);if(m==="https://www.facebook.com/tr/")return O(122),!0;h["gtm.elementUrl"]=m;h["gtm.formCanceled"]=c;a.getAttribute("name")!=null&&(h["gtm.interactedFormName"]=a.getAttribute("name"));e&&(h["gtm.formSubmitElement"]=e,h["gtm.formSubmitElementText"]=e.value);if(d&&f){if(!LD(h,ND(b,
f),f))return!1}else LD(h,function(){},f||2E3);return!0},vI=function(){var a=[],b=function(c){return ob(a,function(d){return d.form===c})};return{store:function(c,d){var e=b(c);e?e.button=d:a.push({form:c,button:d})},get:function(c){var d=b(c);if(d)return d.button}}},wI=function(a){var b=a.target;return b&&b!=="_self"&&b!=="_parent"&&b!=="_top"?!1:!0},xI=function(){var a=vI(),b=HTMLFormElement.prototype.submit;Ic(A,"click",function(c){var d=c.target;if(d){var e=Nc(d,["button","input"],100);if(e&&(e.type===
"submit"||e.type==="image")&&e.name&&Kc(e,"value")){var f=BF(e);f&&a.store(f,e)}}},!1);Ic(A,"submit",function(c){var d=c.target;if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=wI(d)&&!e,g=a.get(d),h=!0;if(uI(d,function(){if(h){var m=null,n={};g&&(m=A.createElement("input"),m.type="hidden",m.name=g.name,m.value=g.value,d.appendChild(m),g.hasAttribute("formaction")&&(n.action=d.getAttribute("action"),fc(d,g.getAttribute("formaction"))),g.hasAttribute("formenctype")&&(n.enctype=
d.getAttribute("enctype"),d.setAttribute("enctype",g.getAttribute("formenctype"))),g.hasAttribute("formmethod")&&(n.method=d.getAttribute("method"),d.setAttribute("method",g.getAttribute("formmethod"))),g.hasAttribute("formvalidate")&&(n.validate=d.getAttribute("validate"),d.setAttribute("validate",g.getAttribute("formvalidate"))),g.hasAttribute("formtarget")&&(n.target=d.getAttribute("target"),d.setAttribute("target",g.getAttribute("formtarget"))));b.call(d);m&&(d.removeChild(m),n.hasOwnProperty("action")&&
fc(d,n.action),n.hasOwnProperty("enctype")&&d.setAttribute("enctype",n.enctype),n.hasOwnProperty("method")&&d.setAttribute("method",n.method),n.hasOwnProperty("validate")&&d.setAttribute("validate",n.validate),n.hasOwnProperty("target")&&d.setAttribute("target",n.target))}},e,f,g))h=!1;else return e||(c.preventDefault&&c.preventDefault(),c.returnValue=!1),!1;return c.returnValue},!1);HTMLFormElement.prototype.submit=function(){var c=this,d=!0;uI(c,function(){d&&b.call(c)},!1,wI(c))&&(b.call(c),d=
!1)}};
function yI(a,b){var c=this;if(!dh(a))throw J(this.getName(),["Object|undefined","any"],arguments);var d=a&&a.get("waitForTags");mF([function(){L(c,"detect_form_submit_events",{waitForTags:!!d})}]);var e=a&&a.get("checkValidation"),f=sF(b);if(d){var g=Number(a.get("waitForTagsTimeout"));g>0&&isFinite(g)||(g=2E3);var h=function(n){return Math.max(g,n)};xF("fsl","mwt",h,0);e||xF("fsl","nv.mwt",h,0)}var m=function(n){n.push(f);
return n};xF("fsl","ids",m,[]);e||xF("fsl","nv.ids",m,[]);tF("fsl","init",!1)||(xI(),uF("fsl","init",!0));return f}yI.N="internal.enableAutoEventOnFormSubmit";
function DI(){var a=this;}DI.N="internal.enableAutoEventOnGaSend";var EI={},FI=[];
var HI=function(a,b){var c=""+b;if(EI[c])EI[c].push(a);else{var d=[a];EI[c]=d;var e=GI("gtm.historyChange-v2"),f=-1;FI.push(function(g){f>=0&&y.clearTimeout(f);b?f=y.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},GI=function(a){var b=y.location.href,c={source:null,state:y.history.state||null,url:Nk(Qk(b)),hb:Kk(Qk(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.hb!==d.hb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.hb,
"gtm.newUrlFragment":d.hb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;MD(h)}}},II=function(a,b){var c=y.history,d=c[a];if(kb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=y.location.href;b({source:a,state:e,url:Nk(Qk(h)),hb:Kk(Qk(h),"fragment")})}}catch(e){}},KI=function(a){y.addEventListener("popstate",function(b){var c=JI(b);a({source:"popstate",state:b.state,url:Nk(Qk(c)),hb:Kk(Qk(c),
"fragment")})})},LI=function(a){y.addEventListener("hashchange",function(b){var c=JI(b);a({source:"hashchange",state:null,url:Nk(Qk(c)),hb:Kk(Qk(c),"fragment")})})},JI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||y.location.href};
function MI(a,b){var c=this;if(!dh(a))throw J(this.getName(),["Object|undefined","any"],arguments);mF([function(){L(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!tF(d,"init",!1)){var g;d==="ehl"?(g=function(m){for(var n=0;n<FI.length;n++)FI[n](m)},f=sF(b),HI(f,e),uF(d,"reg",HI)):g=GI("gtm.historyChange");LI(g);KI(g);II("pushState",
g);II("replaceState",g);uF(d,"init",!0)}else if(d==="ehl"){var h=tF(d,"reg");h&&(f=sF(b),h(f,e))}d==="hl"&&(f=void 0);return f}MI.N="internal.enableAutoEventOnHistoryChange";var NI=["http://","https://","javascript:","file://"];
var OI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=Uc(b,"href");if(c.indexOf(":")!==-1&&!NI.some(function(h){return Eb(c,h)}))return!1;var d=c.indexOf("#"),e=Uc(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=Nk(Qk(c)),g=Nk(Qk(y.location.href));return f!==g}return!0},PI=function(a,b){for(var c=Kk(Qk((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||Uc(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},QI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.D||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Nc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=tF("lcl",e?"nv.mwt":"mwt",0),g;g=e?tF("lcl","nv.ids",[]):tF("lcl","ids",[]);for(var h=[],m=0;m<g.length;m++){var n=g[m],p=tF("lcl","aff.map",{})[n];p&&!PI(p,d)||h.push(n)}if(h.length){var q=OI(c,d),r=yF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Lc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!ob(String(Uc(d,"rel")||"").split(" "),function(x){return x.toLowerCase()==="noreferrer"}),u=y[(Uc(d,"target")||"_self").substring(1)],v=!0,w=ND(function(){var x;if(x=v&&u){var z;a:if(t){var B;try{B=new MouseEvent(c.type,{bubbles:!0})}catch(C){if(!A.createEvent){z=!1;break a}B=A.createEvent("MouseEvents");B.initEvent(c.type,!0,!0)}B.D=!0;c.target.dispatchEvent(B);z=!0}else z=!1;x=!z}x&&(u.location.href=Uc(d,
"href"))},f);if(LD(r,w,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else LD(r,function(){},f||2E3);return!0}}}var b=0;Ic(A,"click",a,!1);Ic(A,"auxclick",a,!1)};
function RI(a,b){var c=this;if(!dh(a))throw J(this.getName(),["Object|undefined","any"],arguments);var d=xd(a);mF([function(){L(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=sF(b);if(e){var m=Number(d.waitForTagsTimeout);m>0&&isFinite(m)||(m=2E3);var n=function(q){return Math.max(m,q)};xF("lcl","mwt",n,0);f||xF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};xF("lcl","ids",p,[]);f||xF("lcl","nv.ids",p,[]);g&&xF("lcl","aff.map",function(q){q[h]=g;return q},{});tF("lcl","init",!1)||(QI(),uF("lcl","init",!0));return h}RI.N="internal.enableAutoEventOnLinkClick";var SI,TI;
var UI=function(a){return tF("sdl",a,{})},VI=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];xF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},YI=function(){function a(){WI();XI(a,!0)}return a},ZI=function(){function a(){f?e=y.setTimeout(a,c):(e=0,WI(),XI(b));f=!1}function b(){d&&SI();e?f=!0:(e=y.setTimeout(a,c),uF("sdl","pending",!0))}var c=250,d=!1;A.scrollingElement&&A.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
XI=function(a,b){tF("sdl","init",!1)&&!$I()&&(b?Jc(y,"scrollend",a):Jc(y,"scroll",a),Jc(y,"resize",a),uF("sdl","init",!1))},WI=function(){var a=SI(),b=a.depthX,c=a.depthY,d=b/TI.scrollWidth*100,e=c/TI.scrollHeight*100;aJ(b,"horiz.pix","PIXELS","horizontal");aJ(d,"horiz.pct","PERCENT","horizontal");aJ(c,"vert.pix","PIXELS","vertical");aJ(e,"vert.pct","PERCENT","vertical");uF("sdl","pending",!1)},aJ=function(a,b,c,d){var e=UI(b),f={},g;for(g in e)if(f={ze:f.ze},f.ze=g,e.hasOwnProperty(f.ze)){var h=
Number(f.ze);if(!(a<h)){var m={};VD((m.event="gtm.scrollDepth",m["gtm.scrollThreshold"]=h,m["gtm.scrollUnits"]=c.toLowerCase(),m["gtm.scrollDirection"]=d,m["gtm.triggers"]=e[f.ze].join(","),m));xF("sdl",b,function(n){return function(p){delete p[n.ze];return p}}(f),{})}}},cJ=function(){xF("sdl","scr",function(a){a||(a=A.scrollingElement||A.body&&A.body.parentNode);return TI=a},!1);xF("sdl","depth",function(a){a||(a=bJ());return SI=a},!1)},bJ=function(){var a=0,b=0;return function(){var c=cx(),d=c.height;
a=Math.max(TI.scrollLeft+c.width,a);b=Math.max(TI.scrollTop+d,b);return{depthX:a,depthY:b}}},$I=function(){return!!(Object.keys(UI("horiz.pix")).length||Object.keys(UI("horiz.pct")).length||Object.keys(UI("vert.pix")).length||Object.keys(UI("vert.pct")).length)};
function dJ(a,b){var c=this;if(!ch(a))throw J(this.getName(),["Object","any"],arguments);mF([function(){L(c,"detect_scroll_events")}]);cJ();if(!TI)return;var d=sF(b),e=xd(a);switch(e.horizontalThresholdUnits){case "PIXELS":VI(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":VI(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":VI(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":VI(e.verticalThresholds,
d,"vert.pct")}tF("sdl","init",!1)?tF("sdl","pending",!1)||D(function(){WI()}):(uF("sdl","init",!0),uF("sdl","pending",!0),D(function(){WI();if($I()){var f=ZI();"onscrollend"in y?(f=YI(),Ic(y,"scrollend",f)):Ic(y,"scroll",f);Ic(y,"resize",f)}else uF("sdl","init",!1)}));return d}dJ.N="internal.enableAutoEventOnScroll";function eJ(a){return function(){if(a.limit&&a.yj>=a.limit)a.Eh&&y.clearInterval(a.Eh);else{a.yj++;var b=zb();MD({event:a.eventName,"gtm.timerId":a.Eh,"gtm.timerEventNumber":a.yj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Jm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Jm,"gtm.triggers":a.Bq})}}}
function fJ(a,b){
return f}fJ.N="internal.enableAutoEventOnTimer";
var gJ=function(a,b,c){function d(){var g=a();f+=e?(zb()-e)*g.playbackRate/1E3:0;e=zb()}var e=0,f=0;return{createEvent:function(g,h,m){var n=a(),p=n.ej,q=m?Math.round(m):h?Math.round(n.ej*h):Math.round(n.fm),r=h!==void 0?Math.round(h*100):p<=0?0:Math.round(q/p*100),t=A.hidden?!1:dx(c)>=.5;d();var u=void 0;b!==void 0&&(u=[b]);var v=yF(c,"gtm.video",u);v["gtm.videoProvider"]="youtube";v["gtm.videoStatus"]=g;v["gtm.videoUrl"]=n.url;v["gtm.videoTitle"]=n.title;v["gtm.videoDuration"]=Math.round(p);v["gtm.videoCurrentTime"]=
Math.round(q);v["gtm.videoElapsedTime"]=Math.round(f);v["gtm.videoPercent"]=r;v["gtm.videoVisible"]=t;return v},Fm:function(){e=zb()},ne:function(){d()}}};var jc=wa(["data-gtm-yt-inspected-"]),hJ=["www.youtube.com","www.youtube-nocookie.com"],iJ,jJ=!1;
var kJ=function(a,b,c){var d=a.map(function(g){return{fb:g,pg:g,ng:void 0}});if(!b.length)return d;var e=b.map(function(g){return{fb:g*c,pg:void 0,ng:g}});if(!d.length)return e;var f=d.concat(e);f.sort(function(g,h){return g.fb-h.fb});return f},lJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]<0||b.push(a[c]);b.sort(function(d,e){return d-e});return b},mJ=function(a){a=a===void 0?[]:a;for(var b=[],c=0;c<a.length;c++)a[c]>100||a[c]<0||(b[c]=a[c]/100);b.sort(function(d,e){return d-
e});return b},nJ=function(a,b){var c,d;function e(){t=gJ(function(){return{url:w,title:x,ej:v,fm:a.getCurrentTime(),playbackRate:z}},b.bc,a.getIframe());v=0;x=w="";z=1;return f}function f(G){switch(G){case 1:v=Math.round(a.getDuration());w=a.getVideoUrl();if(a.getVideoData){var I=a.getVideoData();x=I?I.title:""}z=a.getPlaybackRate();if(b.Yi){var K=t.createEvent("start");MD(K)}else t.ne();u=kJ(b.Gj,b.Fj,a.getDuration());return g(G);default:return f}}function g(){B=a.getCurrentTime();C=yb().getTime();
t.Fm();r();return h}function h(G){var I;switch(G){case 0:return n(G);case 2:I="pause";case 3:var K=a.getCurrentTime()-B;I=Math.abs((yb().getTime()-C)/1E3*z-K)>1?"seek":I||"buffering";if(a.getCurrentTime())if(b.Xi){var U=t.createEvent(I);MD(U)}else t.ne();q();return m;case -1:return e(G);default:return h}}function m(G){switch(G){case 0:return n(G);case 1:return g(G);case -1:return e(G);default:return m}}function n(){for(;d;){var G=c;y.clearTimeout(d);G()}if(b.Wi){var I=t.createEvent("complete",1);
MD(I)}return e(-1)}function p(){}function q(){d&&(y.clearTimeout(d),d=0,c=p)}function r(){if(u.length&&z!==0){var G=-1,I;do{I=u[0];if(I.fb>a.getDuration())return;G=(I.fb-a.getCurrentTime())/z;if(G<0&&(u.shift(),u.length===0))return}while(G<0);c=function(){d=0;c=p;if(u.length>0&&u[0].fb===I.fb){u.shift();var K=t.createEvent("progress",I.ng,I.pg);MD(K)}r()};d=y.setTimeout(c,G*1E3)}}var t,u=[],v,w,x,z,B,C,F=e(-1);d=0;c=p;return{onStateChange:function(G){F=F(G)},onPlaybackRateChange:function(G){B=a.getCurrentTime();
C=yb().getTime();t.ne();z=G;q();r()}}},pJ=function(a){D(function(){function b(){for(var d=c.getElementsByTagName("iframe"),e=d.length,f=0;f<e;f++)oJ(d[f],a)}var c=A;b();cI(b)})},oJ=function(a,b){if(!a.getAttribute("data-gtm-yt-inspected-"+b.bc)&&(lc(a,"data-gtm-yt-inspected-"+b.bc),qJ(a,b.Xf))){a.id||(a.id=rJ());var c=y.YT,d=c.get(a.id);d||(d=new c.Player(a.id));var e=nJ(d,b),f={},g;for(g in e)f={ig:f.ig},f.ig=g,e.hasOwnProperty(f.ig)&&d.addEventListener(f.ig,function(h){return function(m){return e[h.ig](m.data)}}(f))}},
qJ=function(a,b){var c=a.getAttribute("src");if(sJ(c,"embed/")){if(c.indexOf("enablejsapi=1")>0)return!0;if(b){var d;var e=c.indexOf("?")!==-1?"&":"?";c.indexOf("origin=")>-1?d=c+e+"enablejsapi=1":(iJ||(iJ=A.location.protocol+"//"+A.location.hostname,A.location.port&&(iJ+=":"+A.location.port)),d=c+e+"enablejsapi=1&origin="+encodeURIComponent(iJ));var f;f=Sb(d);a.src=Tb(f).toString();return!0}}return!1},sJ=function(a,b){if(!a)return!1;for(var c=0;c<hJ.length;c++)if(a.indexOf("//"+hJ[c]+"/"+b)>=0)return!0;
return!1},rJ=function(){var a=""+Math.round(Math.random()*1E9);return A.getElementById(a)?rJ():a};
function tJ(a,b){var c=this;var d=function(){pJ(q)};if(!ch(a))throw J(this.getName(),["Object","any"],arguments);mF([function(){L(c,"detect_youtube_activity_events",{fixMissingApi:!!a.get("fixMissingApi")})}]);var e=sF(b),f=!!a.get("captureStart"),g=!!a.get("captureComplete"),h=!!a.get("capturePause"),m=mJ(xd(a.get("progressThresholdsPercent"))),n=lJ(xd(a.get("progressThresholdsTimeInSeconds"))),p=!!a.get("fixMissingApi");
if(!(f||g||h||m.length||n.length))return;var q={Yi:f,Wi:g,Xi:h,Fj:m,Gj:n,Xf:p,bc:e},r=y.YT;if(r)return r.ready&&r.ready(d),e;var t=y.onYouTubeIframeAPIReady;y.onYouTubeIframeAPIReady=function(){t&&t();d()};D(function(){for(var u=A.getElementsByTagName("script"),v=u.length,w=0;w<v;w++){var x=u[w].getAttribute("src");if(sJ(x,"iframe_api")||sJ(x,"player_api"))return e}for(var z=A.getElementsByTagName("iframe"),B=z.length,C=0;C<B;C++)if(!jJ&&qJ(z[C],q.Xf))return Bc("https://www.youtube.com/iframe_api"),
jJ=!0,e});return e}tJ.N="internal.enableAutoEventOnYouTubeActivity";jJ=!1;function uJ(a,b){if(!jh(a)||!dh(b))throw J(this.getName(),["string","Object|undefined"],arguments);var c=b?xd(b):{},d=a,e=!1;var f=JSON.parse(d);if(!f)throw Error("Invalid boolean expression string was given.");e=Fh(f,c);return e}uJ.N="internal.evaluateBooleanExpression";var vJ;function wJ(a){var b=!1;return b}wJ.N="internal.evaluateMatchingRules";function fK(){return yr(7)&&yr(9)&&yr(10)};function aL(a,b,c,d){}aL.N="internal.executeEventProcessor";function bL(a){var b;return yd(b,this.M,1)}bL.N="internal.executeJavascriptString";function cL(a){var b;return b};function dL(a){var b="";return b}dL.N="internal.generateClientId";function eL(a){var b={};return yd(b)}eL.N="internal.getAdsCookieWritingOptions";function fL(a,b){var c=!1;return c}fL.N="internal.getAllowAdPersonalization";function gL(){var a;return a}gL.N="internal.getAndResetEventUsage";function hL(a,b){b=b===void 0?!0:b;var c;return c}hL.N="internal.getAuid";var iL=null;
function jL(){var a=new Ua;L(this,"read_container_data"),H(49)&&iL?a=iL:(a.set("containerId",'G-5S1SQJV11Q'),a.set("version",'1'),a.set("environmentName",''),a.set("debugMode",mg),a.set("previewMode",ng.Om),a.set("environmentMode",ng.bp),a.set("firstPartyServing",jk()||Kj.O),a.set("containerUrl",sc),a.eb(),H(49)&&(iL=a));return a}
jL.publicName="getContainerVersion";function kL(a,b){b=b===void 0?!0:b;var c;return c}kL.publicName="getCookieValues";function lL(){var a="";return a}lL.N="internal.getCorePlatformServicesParam";function mL(){return qo()}mL.N="internal.getCountryCode";function nL(){var a=[];a=Am();return yd(a)}nL.N="internal.getDestinationIds";function oL(a){var b=new Ua;return b}oL.N="internal.getDeveloperIds";function pL(a){var b;return b}pL.N="internal.getEcsidCookieValue";function qL(a,b){var c=null;return c}qL.N="internal.getElementAttribute";function rL(a){var b=null;return b}rL.N="internal.getElementById";function sL(a){var b="";return b}sL.N="internal.getElementInnerText";function tL(a,b){var c=null;return yd(c)}tL.N="internal.getElementProperty";function uL(a){var b;return b}uL.N="internal.getElementValue";function vL(a){var b=0;return b}vL.N="internal.getElementVisibilityRatio";function wL(a){var b=null;return b}wL.N="internal.getElementsByCssSelector";
function xL(a){var b;if(!jh(a))throw J(this.getName(),["string"],arguments);L(this,"read_event_data",a);var c;a:{var d=a,e=qF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],x="",z=l(n),B=z.next();!B.done;B=
z.next()){var C=B.value;C===m?(w.push(x),x=""):x=C===g?x+"\\":C===h?x+".":x+C}x&&w.push(x);for(var F=l(w),G=F.next();!G.done;G=F.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=yd(c,this.M,1);return b}xL.N="internal.getEventData";var yL={};yL.enableConversionAutoDataAnalysis=H(188);yL.enableDecodeUri=H(92);yL.enableGa4OutboundClicksFix=H(96);yL.enableGaAdsConversions=H(122);yL.enableGaAdsConversionsClientId=H(121);yL.enableOverrideAdsCps=H(170);yL.enableUrlDecodeEventUsage=H(139);function zL(){return yd(yL)}zL.N="internal.getFlags";function AL(){var a;return a}AL.N="internal.getGsaExperimentId";function BL(){return new ud(AE)}BL.N="internal.getHtmlId";function CL(a){var b;return b}CL.N="internal.getIframingState";function DL(a,b){var c={};return yd(c)}DL.N="internal.getLinkerValueFromLocation";function EL(){var a=new Ua;return a}EL.N="internal.getPrivacyStrings";function FL(a,b){var c;if(!jh(a)||!jh(b))throw J(this.getName(),["string","string"],arguments);var d=$w(a)||{};c=yd(d[b],this.M);return c}FL.N="internal.getProductSettingsParameter";function GL(a,b){var c;if(!jh(a)||!nh(b))throw J(this.getName(),["string","boolean|undefined"],arguments);L(this,"get_url","query",a);var d=Kk(Qk(y.location.href),"query"),e=Hk(d,a,b);c=yd(e,this.M);return c}GL.publicName="getQueryParameters";function HL(a,b){var c;return c}HL.publicName="getReferrerQueryParameters";function IL(a){var b="";return b}IL.publicName="getReferrerUrl";function JL(){return ro()}JL.N="internal.getRegionCode";function KL(a,b){var c;if(!jh(a)||!jh(b))throw J(this.getName(),["string","string"],arguments);var d=Rq(a);c=yd(d[b],this.M);return c}KL.N="internal.getRemoteConfigParameter";function LL(){var a=new Ua;a.set("width",0);a.set("height",0);return a}LL.N="internal.getScreenDimensions";function ML(){var a="";return a}ML.N="internal.getTopSameDomainUrl";function NL(){var a="";return a}NL.N="internal.getTopWindowUrl";function OL(a){var b="";if(!kh(a))throw J(this.getName(),["string|undefined"],arguments);L(this,"get_url",a);b=Kk(Qk(y.location.href),a);return b}OL.publicName="getUrl";function PL(){L(this,"get_user_agent");return pc.userAgent}PL.N="internal.getUserAgent";function QL(){var a;return a?yd(Vy(a)):a}QL.N="internal.getUserAgentClientHints";var SL=function(a){var b=a.eventName===M.m.Xc&&jn()&&my(a),c=S(a,R.C.xl),d=S(a,R.C.Tj),e=S(a,R.C.Ef),f=S(a,R.C.ee),g=S(a,R.C.yg),h=S(a,R.C.Ld),m=S(a,R.C.zg),n=S(a,R.C.Ag),p=!!ly(a)||!!S(a,R.C.Sh);return!(!Sc()&&pc.sendBeacon===void 0||e||p||f||g||h||n||m||b||c||!d&&RL)},RL=!1;
var TL=function(a){var b=0,c=0;return{start:function(){b=zb()},stop:function(){c=this.get()},get:function(){var d=0;a.qj()&&(d=zb()-b);return d+c}}},UL=function(){this.D=void 0;this.J=0;this.isActive=this.isVisible=this.O=!1;this.T=this.R=void 0};k=UL.prototype;k.mo=function(a){var b=this;if(!this.D){this.O=A.hasFocus();this.isVisible=!A.hidden;this.isActive=!0;var c=function(d,e,f){Ic(d,e,function(g){b.D.stop();f(g);b.qj()&&b.D.start()})};c(y,"focus",function(){b.O=!0});c(y,"blur",function(){b.O=
!1});c(y,"pageshow",function(d){b.isActive=!0;d.persisted&&O(56);b.T&&b.T()});c(y,"pagehide",function(){b.isActive=!1;b.R&&b.R()});c(A,"visibilitychange",function(){b.isVisible=!A.hidden});my(a)&&!vc()&&c(y,"beforeunload",function(){RL=!0});this.Kj(!0);this.J=0}};k.Kj=function(a){if((a===void 0?0:a)||this.D)this.J+=this.Ch(),this.D=TL(this),this.qj()&&this.D.start()};k.Aq=function(a){var b=this.Ch();b>0&&X(a,M.m.Lg,b)};k.Cp=function(a){X(a,M.m.Lg);this.Kj();this.J=0};k.qj=function(){return this.O&&
this.isVisible&&this.isActive};k.rp=function(){return this.J+this.Ch()};k.Ch=function(){return this.D&&this.D.get()||0};k.hq=function(a){this.R=a};k.Dm=function(a){this.T=a};var VL=function(a){db("GA4_EVENT",a)};var WL=function(a){var b=S(a,R.C.kl);if(Array.isArray(b))for(var c=0;c<b.length;c++)VL(b[c]);var d=gb("GA4_EVENT");d&&X(a,"_eu",d)},XL=function(){delete cb.GA4_EVENT};function YL(){return y.gaGlobal=y.gaGlobal||{}}function ZL(){var a=YL();a.hid=a.hid||pb();return a.hid}function $L(a,b){var c=YL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};var aM=["GA1"];
var bM=function(a,b,c){var d=S(a,R.C.Vj);if(d===void 0||c<=d)X(a,M.m.Ob,b),V(a,R.C.Vj,c)},dM=function(a,b){var c=Sv(a,M.m.Ob);if(P(a.F,M.m.Kc)&&P(a.F,M.m.Jc)||b&&c===b)return c;if(c){c=""+c;if(!cM(c,a))return O(31),a.isAborted=!0,"";$L(c,sp(M.m.ja));return c}O(32);a.isAborted=!0;return""},eM=function(a){var b=S(a,R.C.za),c=b.prefix+"_ga",d=ws(b.prefix+"_ga",b.domain,b.path,aM,M.m.ja);if(!d){var e=String(P(a.F,M.m.dd,""));e&&e!==c&&(d=ws(e,b.domain,b.path,aM,M.m.ja))}return d},cM=function(a,b){var c;
var d=S(b,R.C.za),e=d.prefix+"_ga",f=xs(d,void 0,void 0,M.m.ja);if(P(b.F,M.m.Gc)===!1&&eM(b)===a)c=!0;else{var g;g=[aM[0],ts(d.domain,d.path),a].join(".");c=os(e,g,f)!==1}return c};
var fM=function(a){if(a){var b;a:{var c=(Eb(a,"s")&&a.indexOf(".")===-1?"GS2":"GS1")+".1."+a;try{b=It(c,2);break a}catch(d){}b=void 0}return b}},hM=function(a,b){var c;a:{var d=gM,e=Ht[2];if(e){var f,g=rs(b.domain),h=ss(b.path),m=Object.keys(e.Kh),n=Lt.get(2),p;if(f=(p=gs(a,g,h,m,n))==null?void 0:p.Oo){var q=It(f,2,d);c=q?Nt(q):void 0;break a}}c=void 0}if(c){var r=Mt(a,2,gM);if(r&&r.length>1){VL(28);var t;if(r&&r.length!==0){for(var u,v=-Infinity,w=l(r),x=w.next();!x.done;x=w.next()){var z=x.value;
if(z.t!==void 0){var B=Number(z.t);!isNaN(B)&&B>v&&(v=B,u=z)}}t=u}else t=void 0;var C=t;C&&C.t!==c.t&&(VL(32),c=C)}return Kt(c,2)}},gM=function(a){a&&(a==="GS1"?VL(33):a==="GS2"&&VL(34))},iM=function(a){var b=fM(a);if(b){var c=Number(b.o),d=Number(b.t),e=Number(b.j||0);c||VL(29);d||VL(30);isNaN(e)&&VL(31);if(c&&d&&!isNaN(e)){var f=b.h,g=f&&f!=="0"?String(f):void 0,h=b.d?String(b.d):void 0,m={};return m.s=String(b.s),m.o=c,m.g=!!Number(b.g),m.t=d,m.d=h,m.j=e,m.l=b.l==="1",m.h=g,m}}};

var kM=function(a,b,c){if(!b)return a;if(!a)return b;var d=iM(a);if(!d)return b;var e,f=ub((e=P(c.F,M.m.lf))!=null?e:30),g=S(c,R.C.jb);if(!(Math.floor(g/1E3)>d.t+f*60))return a;var h=iM(b);if(!h)return a;h.o=d.o+1;var m;return(m=jM(h))!=null?m:b},mM=function(a,b){var c=S(b,R.C.za),d=lM(b,c),e=fM(a);if(!e)return!1;var f=xs(c||{},void 0,void 0,Lt.get(2));os(d,void 0,f);return Ot(d,e,2,c)!==1},nM=function(a){var b=S(a,R.C.za);return hM(lM(a,b),b)},oM=function(a){var b=S(a,R.C.jb),c={};c.s=Sv(a,M.m.uc);
c.o=Sv(a,M.m.Zg);var d;d=Sv(a,M.m.Yg);var e=(c.g=d,c.t=Math.floor(b/1E3),c.d=S(a,R.C.Gf),c.j=S(a,R.C.Hf)||0,c.l=!!S(a,M.m.ei),c.h=Sv(a,M.m.Mg),c);return jM(e)},jM=function(a){if(a.s&&a.o){var b={},c=(b.s=a.s,b.o=String(a.o),b.g=ub(a.g)?"1":"0",b.t=String(a.t),b.j=String(a.j),b.l=a.l?"1":"0",b.h=a.h||"0",b.d=a.d,b);return Kt(c,2)}},lM=function(a,b){return b.prefix+"_ga_"+a.target.ids[Qp[6]]};
var pM=function(a){var b=P(a.F,M.m.Ra),c=a.F.J[M.m.Ra];if(c===b)return c;var d=id(b,null);c&&c[M.m.na]&&(d[M.m.na]=(d[M.m.na]||[]).concat(c[M.m.na]));return d},qM=function(a,b){var c=$s(!0);return c._up!=="1"?{}:{clientId:c[a],sb:c[b]}},rM=function(a,b,c){var d=$s(!0),e=d[b];e&&(bM(a,e,2),cM(e,a));var f=d[c];f&&mM(f,a);return{clientId:e,sb:f}},sM=function(){var a=Mk(y.location,"host"),b=Mk(Qk(A.referrer),"host");return a&&b?a===b||a.indexOf("."+b)>=0||b.indexOf("."+a)>=0?!0:!1:!1},tM=function(a){if(!P(a.F,
M.m.Cb))return{};var b=S(a,R.C.za),c=b.prefix+"_ga",d=lM(a,b);ht(function(){var e;if(sp("analytics_storage"))e={};else{var f={_up:"1"},g;g=Sv(a,M.m.Ob);e=(f[c]=g,f[d]=oM(a),f)}return e},1);return!sp("analytics_storage")&&sM()?qM(c,d):{}},vM=function(a){var b=pM(a)||{},c=S(a,R.C.za),d=c.prefix+"_ga",e=lM(a,c),f={};jt(b[M.m.ae],!!b[M.m.na])&&(f=rM(a,d,e),f.clientId&&f.sb&&(uM=!0));b[M.m.na]&&gt(function(){var g={},h=eM(a);h&&(g[d]=h);var m=nM(a);m&&(g[e]=m);var n=ds("FPLC",void 0,void 0,M.m.ja);n.length&&
(g._fplc=n[0]);return g},b[M.m.na],b[M.m.Lc],!!b[M.m.sc]);return f},uM=!1;var wM=function(a){if(!S(a,R.C.vd)&&Yk(a.F)){var b=pM(a)||{},c=(jt(b[M.m.ae],!!b[M.m.na])?$s(!0)._fplc:void 0)||(ds("FPLC",void 0,void 0,M.m.ja).length>0?void 0:"0");X(a,"_fplc",c)}};function xM(a){(my(a)||jk())&&X(a,M.m.al,ro()||qo());!my(a)&&jk()&&X(a,M.m.rl,"::")}function yM(a){if(jk()&&!my(a)){var b=H(176);H(187)&&H(201)&&(b=b&&!uo());b&&X(a,M.m.Ok,!0);if(H(78)){fw(a);gw(a,Lp.Af.bn,Oo(P(a.F,M.m.ib)));var c=Lp.Af.dn;var d=P(a.F,M.m.Gc);gw(a,c,d===!0?1:d===!1?0:void 0);gw(a,Lp.Af.Zm,Oo(P(a.F,M.m.wb)));gw(a,Lp.Af.Xm,ts(No(P(a.F,M.m.pb)),No(P(a.F,M.m.Qb))))}}};var AM=function(a,b){Dp("grl",function(){return zM()})(b)||(O(35),a.isAborted=!0)},zM=function(){var a=zb(),b=a+864E5,c=20,d=5E3;return function(e){var f=zb();f>=b&&(b=f+864E5,d=5E3);c=Math.min(c+(f-a)/1E3*5,20);a=f;var g=!1;d<1||c<1||(g=!0,d--,c--);e&&(e.To=d,e.Lo=c);return g}};
var BM=function(a){var b=Sv(a,M.m.Wa);return Kk(Qk(b),"host",!0)},CM=function(a){if(P(a.F,M.m.cf)!==void 0)a.copyToHitData(M.m.cf);else{var b=P(a.F,M.m.ji),c,d;a:{if(uM){var e=pM(a)||{};if(e&&e[M.m.na])for(var f=BM(a),g=e[M.m.na],h=0;h<g.length;h++)if(g[h]instanceof RegExp){if(g[h].test(f)){d=!0;break a}}else if(f.indexOf(g[h])>=0){d=!0;break a}}d=!1}if(!(c=d)){var m;if(m=b)a:{for(var n=b.include_conditions||[],p=BM(a),q=0;q<n.length;q++)if(n[q].test(p)){m=!0;break a}m=!1}c=m}c&&(X(a,M.m.cf,"1"),
VL(4))}};
var DM=function(a,b){Fr()&&(a.gcs=Gr(),S(b,R.C.Df)&&(a.gcu="1"));a.gcd=Kr(b.F);H(97)?a.npa=S(b,R.C.Lh)?"0":"1":Er(b.F)?a.npa="0":a.npa="1";Pr()&&(a._ng="1")},EM=function(a){return sp(M.m.V)&&sp(M.m.ja)?jk()&&S(a,R.C.Gi):!1},FM=function(a){if(S(a,R.C.vd))return{url:Zk("https://www.merchant-center-analytics.goog",void 0,"")+"/mc/collect",endpoint:20};var b=Vk(Yk(a.F),"/g/collect");if(b)return{url:b,endpoint:16};var c=ny(a),d=P(a.F,M.m.Mb),e=c&&!so()&&d!==!1&&fK()&&sp(M.m.V)&&sp(M.m.ja)?17:16;return{url:Rz(e),
endpoint:e}},GM={};GM[M.m.Ob]="cid";GM[M.m.Uh]="gcut";GM[M.m.bd]="are";GM[M.m.Jg]="pscdl";GM[M.m.fi]="_fid";GM[M.m.Kk]="_geo";GM[M.m.Sb]="gdid";GM[M.m.Yd]="_ng";GM[M.m.Ic]="frm";GM[M.m.cf]="ir";GM[M.m.Ok]="fp";GM[M.m.xb]="ul";GM[M.m.Wg]="ni";GM[M.m.Vn]="pae";GM[M.m.Xg]="_rdi";GM[M.m.Mc]="sr";GM[M.m.Zn]="tid";GM[M.m.oi]="tt";GM[M.m.Ub]="ec_mode";GM[M.m.vl]="gtm_up";GM[M.m.qf]=
"uaa";GM[M.m.rf]="uab";GM[M.m.tf]="uafvl";GM[M.m.uf]="uamb";GM[M.m.vf]="uam";GM[M.m.wf]="uap";GM[M.m.xf]="uapv";GM[M.m.yf]="uaw";GM[M.m.al]="ur";GM[M.m.rl]="_uip";GM[M.m.Un]="_prs";GM[M.m.jd]="lps";GM[M.m.Rd]="gclgs";GM[M.m.Td]="gclst";GM[M.m.Sd]="gcllp";var HM={};HM[M.m.Le]="cc";
HM[M.m.Me]="ci";HM[M.m.Ne]="cm";HM[M.m.Oe]="cn";HM[M.m.Qe]="cs";HM[M.m.Re]="ck";HM[M.m.Va]="cu";HM[M.m.bf]="_tu";HM[M.m.Ba]="dl";HM[M.m.Wa]="dr";HM[M.m.Bb]="dt";HM[M.m.Yg]="seg";HM[M.m.uc]="sid";HM[M.m.Zg]="sct";HM[M.m.Sa]="uid";H(145)&&(HM[M.m.jf]="dp");var IM={};IM[M.m.Lg]="_et";IM[M.m.Rb]="edid";H(94)&&(IM._eu="_eu");var JM={};JM[M.m.Le]="cc";JM[M.m.Me]="ci";
JM[M.m.Ne]="cm";JM[M.m.Oe]="cn";JM[M.m.Qe]="cs";JM[M.m.Re]="ck";var KM={},LM=(KM[M.m.Ya]=1,KM),MM=function(a,b,c){function d(T,ba){if(ba!==void 0&&!yo.hasOwnProperty(T)){ba===null&&(ba="");var aa;var W=ba;T!==M.m.Mg?aa=!1:S(a,R.C.od)||my(a)?(e.ecid=W,aa=!0):aa=void 0;if(!aa&&T!==M.m.ei){var ka=ba;ba===!0&&(ka="1");ba===!1&&(ka="0");ka=String(ka);var ja;if(GM[T])ja=GM[T],e[ja]=ka;else if(HM[T])ja=HM[T],g[ja]=ka;else if(IM[T])ja=IM[T],f[ja]=ka;else if(T.charAt(0)==="_")e[T]=ka;else{var la;JM[T]?la=
!0:T!==M.m.Pe?la=!1:(typeof ba!=="object"&&B(T,ba),la=!0);la||B(T,ba)}}}}var e={},f={},g={};e.v="2";e.tid=a.target.destinationId;e.gtm=Ur({Oa:S(a,R.C.kb)});e._p=H(159)?ck:ZL();if(c&&(c.cb||c.mj)&&(H(125)||(e.em=c.Ib),c.Fb)){var h=c.Fb.qe;h&&!H(8)&&(h=h.replace(/./g,"*"));h&&(e.eme=h)}S(a,R.C.Ld)&&(e._gaz=1);DM(e,a);Nr()&&(e.dma_cps=Lr());e.dma=Mr();ir(qr())&&(e.tcfd=Or());Sz()&&(e.tag_exp=Sz());Tz()&&(e.ptag_exp=Tz());var m=Sv(a,M.m.Sb);m&&(e.gdid=m);f.en=String(a.eventName);if(S(a,R.C.Ff)){var n=
S(a,R.C.tl);f._fv=n?2:1}S(a,R.C.jh)&&(f._nsi=1);if(S(a,R.C.ee)){var p=S(a,R.C.wl);f._ss=p?2:1}S(a,R.C.Ef)&&(f._c=1);S(a,R.C.ud)&&(f._ee=1);if(S(a,R.C.sl)){var q=Sv(a,M.m.wa)||P(a.F,M.m.wa);if(Array.isArray(q))for(var r=0;r<q.length&&r<200;r++)f["pr"+(r+1)]=rg(q[r])}var t=Sv(a,M.m.Rb);t&&(f.edid=t);var u=Sv(a,M.m.rc);if(u&&typeof u==="object")for(var v=l(Object.keys(u)),w=v.next();!w.done;w=v.next()){var x=w.value,z=u[x];z!==void 0&&(z===null&&(z=""),f["gap."+x]=String(z))}for(var B=function(T,ba){if(typeof ba!==
"object"||!LM[T]){var aa="ep."+T,W="epn."+T;T=mb(ba)?W:aa;var ka=mb(ba)?aa:W;f.hasOwnProperty(ka)&&delete f[ka];f[T]=String(ba)}},C=l(Object.keys(a.D)),F=C.next();!F.done;F=C.next()){var G=F.value;d(G,Sv(a,G))}(function(T){my(a)&&typeof T==="object"&&sb(T||{},function(ba,aa){typeof aa!=="object"&&(e["sst."+ba]=String(aa))})})(Sv(a,M.m.Ni));Uz(e,Sv(a,M.m.rd));var I=Sv(a,M.m.Vb)||{};P(a.F,M.m.Mb,void 0,4)===!1&&(e.ngs="1");sb(I,function(T,ba){ba!==void 0&&((ba===null&&(ba=""),T!==M.m.Sa||g.uid)?b[T]!==
ba&&(f[(mb(ba)?"upn.":"up.")+String(T)]=String(ba),b[T]=ba):g.uid=String(ba))});if(H(176)){var K=H(187)&&uo();if(jk()&&!K){var U=S(a,R.C.Gf);U?e._gsid=U:e.njid="1"}}else if(EM(a)){var Q=S(a,R.C.Gf);Q?e._gsid=Q:e.njid="1"}var na=FM(a);Eg.call(this,{sa:e,Jd:g,ij:f},na.url,na.endpoint,my(a),void 0,a.target.destinationId,a.F.eventId,a.F.priorityId)};sa(MM,Eg);
var NM=function(a,b){return a.replace(/\$\{([^\}]+)\}/g,function(c,d){return b[d]||c})},OM=function(a){var b={},c="",d=a.pathname.indexOf("/g/collect");d>=0&&(c=a.pathname.substring(0,d));b.transport_url=a.protocol+"//"+a.hostname+c;if(H(186)){var e;try{e=encodeURIComponent(c||"/")}catch(f){e=encodeURIComponent("/")}b.encoded_path=e}return b},PM=function(a,b,c,d,e){var f=0,g=new y.XMLHttpRequest;g.withCredentials=!0;g.onprogress=function(h){if(g.status===200){var m=g.responseText.substring(f);f=h.loaded;
KA(c,m)}};g.onerror=function(){e==null||e()};g.onload=function(){g.status<=399||e==null||e()};g.open(b?"POST":"GET",a);(d==null?0:d.attributionReporting)&&g.setAttributionReporting&&g.setAttributionReporting(d.attributionReporting);g.send(b)},RM=function(a,b,c){var d;return d=NA(MA(new LA(function(e,f){var g=NM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionsrc="");em(a,g,void 0,PA(d,f),h)}),function(e,f){var g=NM(e,b),h=f.dedupe_key;h&&jm(a,g,h)}),function(e,
f){var g=NM(e,b);c&&(g=g.replace("_is_sw=0",c));var h={};f.attribution_reporting&&(h.attributionReporting={eventSourceEligible:!1,triggerEligible:!0});f.process_response?QM(a,g,void 0,d,h,PA(d,f)):fm(a,g,void 0,h,void 0,PA(d,f))})},SM=function(a,b,c,d,e){Zl(a,2,b);var f=RM(a,d,e);QM(a,b,c,f)},QM=function(a,b,c,d,e,f){Sc()?JA(a,b,c,d,e,void 0,f):PM(b,c,d,(e==null?0:e.attributionReporting)?{attributionReporting:e.attributionReporting}:{},f)},TM=function(a,b,c){var d=Qk(b),e=OM(d),f=RA(d);!H(132)||uc("; wv")||
uc("FBAN")||uc("FBAV")||wc()?SM(a,f,c,e):Ny(f,c,e,function(g){SM(a,f,c,e,g)})};var UM={AW:ao.aa.Tm,G:ao.aa.eo,DC:ao.aa.bo};function VM(a){var b=Zi(a);return""+Wr(b.map(function(c){return c.value}).join("!"))}function WM(a){var b=Op(a);return b&&UM[b.prefix]}function XM(a,b){var c=a[b];c&&(c.clearTimerId&&y.clearTimeout(c.clearTimerId),c.clearTimerId=y.setTimeout(function(){delete a[b]},36E5))};
var YM=function(a,b,c,d){var e=a+"?"+b;d?dm(c,e,d):cm(c,e)},$M=function(a,b,c,d,e){var f=b,g=Vc();g!==void 0&&(f+="&tfd="+Math.round(g));b=f;var h=a+"?"+b;ZM&&(d=!Eb(h,Qz())&&!Eb(h,Pz()));if(d&&!RL)TM(e,h,c);else{var m=b;Sc()?fm(e,a+"?"+m,c,{Hh:!0})||YM(a,m,e,c):YM(a,m,e,c)}},aN=function(a,b){function c(w){q.push(w+"="+encodeURIComponent(""+a.sa[w]))}var d=b.mq,e=b.qq,f=b.oq,g=b.nq,h=b.up,m=b.Np,n=b.Mp,p=b.kp;if(d||e||f||g){var q=[];a.sa._ng&&c("_ng");c("tid");c("cid");c("gtm");q.push("aip=1");a.Jd.uid&&
!n&&q.push("uid="+encodeURIComponent(""+a.Jd.uid));c("dma");a.sa.dma_cps!=null&&c("dma_cps");a.sa.gcs!=null&&c("gcs");c("gcd");a.sa.npa!=null&&c("npa");a.sa.frm!=null&&c("frm");d&&(Sz()&&q.push("tag_exp="+Sz()),Tz()&&q.push("ptag_exp="+Tz()),YM("https://stats.g.doubleclick.net/g/collect","v=2&"+q.join("&"),{destinationId:a.destinationId||"",endpoint:19,eventId:a.eventId,priorityId:a.priorityId}),ep({targetId:String(a.sa.tid),request:{url:"https://stats.g.doubleclick.net/g/collect?v=2&"+q.join("&"),
parameterEncoding:2,endpoint:19},ab:b.ab}));if(e&&(Sz()&&q.push("tag_exp="+Sz()),Tz()&&q.push("ptag_exp="+Tz()),q.push("z="+pb()),!m)){var r=h&&Eb(h,"google.")&&h!=="google.com"?"https://www.%/ads/ga-audiences?v=1&t=sr&slf_rd=1&_r=4&".replace("%",h):void 0;if(r){var t=r+q.join("&");em({destinationId:a.destinationId||"",endpoint:47,eventId:a.eventId,priorityId:a.priorityId},t);ep({targetId:String(a.sa.tid),request:{url:t,parameterEncoding:2,endpoint:47},ab:b.ab})}}if(f){var u="https://{ga4CollectionSubdomain.}analytics.google.com/g/s/collect".replace("{ga4CollectionSubdomain.}",
p?p+".":"");q=[];c("_gsid");c("gtm");H(176)&&a.sa._geo&&c("_geo");YM(u,q.join("&"),{destinationId:a.destinationId||"",endpoint:18,eventId:a.eventId,priorityId:a.priorityId});ep({targetId:String(a.sa.tid),request:{url:u+"?"+q.join("&"),parameterEncoding:2,endpoint:18},ab:b.ab})}if(g){var v="https://{ga4CollectionSubdomain.}google-analytics.com/g/collect".replace("{ga4CollectionSubdomain.}",(p||"www")+".");q=[];q.push("v=2");q.push("t=g");c("_gsid");c("gtm");a.sa._geo&&c("_geo");YM(v,q.join("&"),{destinationId:a.destinationId||
"",endpoint:16,eventId:a.eventId,priorityId:a.priorityId});ep({targetId:String(a.sa.tid),request:{url:v+"?"+q.join("&"),parameterEncoding:2,endpoint:16},ab:b.ab})}}},ZM=!1;var bN=function(){this.O=1;this.R={};this.J=-1;this.D=new xg};k=bN.prototype;k.Lb=function(a,b){var c=this,d=new MM(a,this.R,b),e={eventId:a.F.eventId,priorityId:a.F.priorityId},f=SL(a),g,
h;f&&this.D.T(d)||this.flush();var m=f&&this.D.add(d);if(m){if(this.J<0){var n=y.setTimeout,p;my(a)?cN?(cN=!1,p=dN):p=eN:p=5E3;this.J=n.call(y,function(){c.flush()},p)}}else{var q=Ag(d,this.O++),r=q.params,t=q.body;g=r;h=t;$M(d.baseUrl,r,t,d.O,{destinationId:a.target.destinationId,endpoint:d.endpoint,eventId:d.eventId,priorityId:d.priorityId});var u=S(a,R.C.yg),v=S(a,R.C.Ld),w=S(a,R.C.Ag),x=S(a,R.C.zg),z=P(a.F,M.m.ob)!==!1,B=Er(a.F),C={mq:u,qq:v,oq:w,nq:x,up:wo(),qr:z,nr:B,Np:so(),Mp:S(a,R.C.od),
ab:e,F:a.F,kp:uo()};aN(d,C)}yA(a.F.eventId);fp(function(){if(m){var F=Ag(d),G=F.body;g=F.params;h=G}return{targetId:a.target.destinationId,request:{url:d.baseUrl+"?"+g,parameterEncoding:2,postBody:h,endpoint:d.endpoint},ab:e,isBatched:!1}})};k.add=function(a){if(H(100)){var b=S(a,R.C.Sh);if(b){X(a,M.m.Ub,S(a,R.C.Tl));X(a,M.m.Wg,"1");this.Lb(a,b);return}}var c=ly(a);if(H(100)&&c){var d;var e=a.target.destinationId,f;var g=c,h=WM(e);if(h){var m=VM(g);f=(fo(h)||{})[m]}else f=void 0;var n=f;d=n?n.sentTo[e]:
void 0;if(d&&d+6E4>zb())c=void 0,X(a,M.m.Ub);else{var p=c,q=a.target.destinationId,r=WM(q);if(r){var t=VM(p),u=fo(r)||{},v=u[t];if(v)v.timestamp=zb(),v.sentTo=v.sentTo||{},v.sentTo[q]=zb(),v.pending=!0;else{var w={};u[t]={pending:!0,timestamp:zb(),sentTo:(w[q]=zb(),w)}}XM(u,t);eo(r,u)}}}!c||RL||H(125)&&!H(93)?this.Lb(a):this.rq(a)};k.flush=function(){if(this.D.events.length){var a=Cg(this.D,this.O++);$M(this.D.baseUrl,a.params,a.body,this.D.J,{destinationId:this.D.destinationId||"",endpoint:this.D.endpoint,
eventId:this.D.ia,priorityId:this.D.la});this.D=new xg;this.J>=0&&(y.clearTimeout(this.J),this.J=-1)}};k.hm=function(a,b){var c=Sv(a,M.m.Ub);X(a,M.m.Ub);b.then(function(d){var e={},f=(e[R.C.Sh]=d,e[R.C.Tl]=c,e),g=Qw(a.target.destinationId,M.m.Qd,a.F.D);Tw(g,a.F.eventId,{eventMetadata:f})})};k.rq=function(a){var b=this,c=ly(a);if(xj(c)){var d=mj(c,H(93));d?H(100)?(this.hm(a,d),this.Lb(a)):d.then(function(g){b.Lb(a,g)},function(){b.Lb(a)}):this.Lb(a)}else{var e=wj(c);if(H(93)){var f=hj(e);f?H(100)?
(this.hm(a,f),this.Lb(a)):f.then(function(g){b.Lb(a,g)},function(){b.Lb(a,e)}):this.Lb(a,e)}else this.Lb(a,e)}};var dN=sg('',500),eN=sg('',5E3),cN=!0;
var fN=function(a,b,c){c===void 0&&(c={});if(b==null)return c;if(typeof b==="object")for(var d=l(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;fN(a+"."+f,b[f],c)}else c[a]=b;return c},gN=function(a){for(var b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=!!sp(e)}return b},iN=function(a,b){var c=hN.filter(function(e){return!sp(e)});if(c.length){var d=gN(c);tp(c,function(){for(var e=gN(c),f=[],g=l(c),h=g.next();!h.done;h=g.next()){var m=h.value;!d[m]&&e[m]&&f.push(m);e[m]&&
(d[m]=!0)}if(f.length){V(b,R.C.Df,!0);var n=f.map(function(p){return Io[p]}).join(".");n&&jy(b,"gcut",n);a(b)}})}},jN=function(a){my(a)&&jy(a,"navt",Wc())},kN=function(a){my(a)&&jy(a,"lpc",Vt())},lN=function(a){if(H(152)&&my(a)){var b=P(a.F,M.m.Tb),c;b===!0&&(c="1");b===!1&&(c="0");c&&jy(a,"rdp",c)}},mN=function(a){H(147)&&my(a)&&P(a.F,M.m.Ke,!0)===!1&&X(a,M.m.Ke,0)},nN=function(a,b){if(my(b)){var c=S(b,R.C.Ef);(b.eventName==="page_view"||c)&&iN(a,b)}},oN=function(a){if(my(a)&&a.eventName===M.m.Qd&&
S(a,R.C.Df)){var b=Sv(a,M.m.Uh);b&&(jy(a,"gcut",b),jy(a,"syn",1))}},pN=function(a){my(a)&&V(a,R.C.Ja,!1)},qN=function(a){my(a)&&(S(a,R.C.Ja)&&jy(a,"sp",1),S(a,R.C.ko)&&jy(a,"syn",1),S(a,R.C.Ee)&&(jy(a,"em_event",1),jy(a,"sp",1)))},rN=function(a){if(my(a)){var b=ck;b&&jy(a,"tft",Number(b))}},sN=function(a){function b(e){var f=fN(M.m.Ya,e);sb(f,function(g,h){X(a,g,h)})}if(my(a)){var c=lw(a,"ccd_add_1p_data",!1)?1:0;jy(a,"ude",c);var d=P(a.F,M.m.Ya);d!==void 0?(b(d),X(a,M.m.Ub,"c")):b(S(a,R.C.Ta));V(a,
R.C.Ta)}},tN=function(a){if(my(a)){var b=Pv();b&&jy(a,"us_privacy",b);var c=xr();c&&jy(a,"gdpr",c);var d=wr();d&&jy(a,"gdpr_consent",d);var e=Cv.gppString;e&&jy(a,"gpp",e);var f=Cv.D;f&&jy(a,"gpp_sid",f)}},uN=function(a){my(a)&&jn()&&P(a.F,M.m.Aa)&&jy(a,"adr",1)},vN=function(a){if(my(a)){var b=Oz();b&&jy(a,"gcsub",b)}},wN=function(a){if(my(a)){P(a.F,M.m.Mb,void 0,4)===!1&&jy(a,"ngs",1);so()&&jy(a,"ga_rd",1);fK()||jy(a,"ngst",1);var b=wo();b&&jy(a,"etld",b)}},xN=function(a){},yN=function(a){my(a)&&jn()&&jy(a,"rnd",pv())},hN=[M.m.V,M.m.W];
var zN=function(a,b){var c;a:{var d=oM(a);if(d){if(mM(d,a)){c=d;break a}O(25);a.isAborted=!0}c=void 0}var e=c;return{clientId:dM(a,b),sb:e}},AN=function(a,b,c,d,e){var f=No(P(a.F,M.m.Ob));if(P(a.F,M.m.Kc)&&P(a.F,M.m.Jc))f?bM(a,f,1):(O(127),a.isAborted=!0);else{var g=f?1:8;V(a,R.C.jh,!1);f||(f=eM(a),g=3);f||(f=b,g=5);if(!f){var h=sp(M.m.ja),m=YL();f=!m.from_cookie||h?m.vid:void 0;g=6}f?f=""+f:(f=vs(),g=7,V(a,R.C.Ff,!0),V(a,R.C.jh,!0));bM(a,f,g)}var n=S(a,R.C.jb),p=Math.floor(n/1E3),q=void 0;S(a,R.C.jh)||
(q=nM(a)||c);var r=ub(P(a.F,M.m.lf,30));r=Math.min(475,r);r=Math.max(5,r);var t=ub(P(a.F,M.m.li,1E4)),u=iM(q);V(a,R.C.Ff,!1);V(a,R.C.ee,!1);V(a,R.C.Hf,0);u&&u.j&&V(a,R.C.Hf,Math.max(0,u.j-Math.max(0,p-u.t)));var v=!1;if(!u){V(a,R.C.Ff,!0);v=!0;var w={};u=(w.s=String(p),w.o=1,w.g=!1,w.t=p,w.l=!1,w.h=void 0,w)}p>u.t+r*60&&(v=!0,u.s=String(p),u.o++,u.g=!1,u.h=void 0);if(v)V(a,R.C.ee,!0),d.Cp(a);else if(d.rp()>t||a.eventName===M.m.Xc)u.g=!0;S(a,R.C.od)?P(a.F,M.m.Sa)?u.l=!0:(u.l&&!H(9)&&(u.h=void 0),u.l=
!1):u.l=!1;var x=u.h;if(S(a,R.C.od)||my(a)){var z=P(a.F,M.m.Mg),B=z?1:8;z||(z=x,B=4);z||(z=us(),B=7);var C=z.toString(),F=B,G=S(a,R.C.jk);if(G===void 0||F<=G)X(a,M.m.Mg,C),V(a,R.C.jk,F)}e?(a.copyToHitData(M.m.uc,u.s),a.copyToHitData(M.m.Zg,u.o),a.copyToHitData(M.m.Yg,u.g?1:0)):(X(a,M.m.uc,u.s),X(a,M.m.Zg,u.o),X(a,M.m.Yg,u.g?1:0));V(a,M.m.ei,u.l?1:0);if(jk()){var I=y.crypto||y.msCrypto,K;if(!(K=u.d))a:{if(I&&I.getRandomValues)try{var U=new Uint8Array(25);I.getRandomValues(U);K=btoa(String.fromCharCode.apply(String,
ua(U))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(Q){}K=void 0}V(a,R.C.Gf,K)}};var BN=window,CN=document,DN=function(a){var b=BN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||CN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&BN["ga-disable-"+a]===!0)return!0;try{var c=BN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(CN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return CN.getElementById("__gaOptOutExtension")?!0:!1};
var FN=function(a){return!a||EN.test(a)||Ao.hasOwnProperty(a)},GN=function(a){var b=M.m.Mc,c;c||(c=function(){});Sv(a,b)!==void 0&&X(a,b,c(Sv(a,b)))},HN=function(a){var b=a.indexOf("?"),c=b===-1?a:a.substring(0,b),d=Jk(c);d&&(c=d);return b===-1?c:""+c+a.substring(b)},IN=function(a){P(a.F,M.m.Cb)&&(sp(M.m.ja)||P(a.F,M.m.Ob)||X(a,M.m.vl,!0));var b;var c;c=c===void 0?3:c;var d=y.location.href;if(d){var e=Qk(d).search.replace("?",""),f=Hk(e,"_gl",!1,!0)||"";b=f?at(f,c)!==void 0:!1}else b=!1;b&&my(a)&&
jy(a,"glv",1);if(a.eventName!==M.m.ra)return{};P(a.F,M.m.Cb)&&Tu(["aw","dc"]);Vu(["aw","dc"]);var g=vM(a),h=tM(a);return Object.keys(g).length?g:h},JN=function(a){var b=void 0;H(167)&&(b=Lo(Oq.D[M.m.qa]));var c=Ib(a.F.getMergedValues(M.m.qa,1,b),".");c&&X(a,M.m.Sb,c);var d=Ib(a.F.getMergedValues(M.m.qa,2),".");d&&X(a,M.m.Rb,d)},KN={ep:""},LN={},MN=(LN[M.m.Le]=1,LN[M.m.Me]=1,LN[M.m.Ne]=1,LN[M.m.Oe]=1,LN[M.m.Qe]=1,LN[M.m.Re]=1,LN),EN=/^(_|ga_|google_|gtag\.|firebase_).*$/,
NN=[kw,hw,Uv,mw,JN,Kw],ON=function(a){this.O=a;this.D=this.sb=this.clientId=void 0;this.la=this.T=!1;this.Za=0;this.R=!1;this.Ca=!0;this.ia=new bN;this.J=new UL};k=ON.prototype;k.fq=function(a,b,c){var d=this,e=Op(this.O);if(e)if(c.eventMetadata[R.C.ud]&&a.charAt(0)==="_")c.onFailure();else{a!==M.m.ra&&a!==M.m.Ab&&FN(a)&&O(58);PN(c.D);var f=new JH(e,a,c);V(f,R.C.jb,b);var g=[M.m.ja],h=my(f);V(f,R.C.kh,h);if(lw(f,M.m.Zd,P(f.F,M.m.Zd))||h)g.push(M.m.V),g.push(M.m.W);Xy(function(){vp(function(){d.gq(f)},
g)});H(88)&&a===M.m.ra&&lw(f,"ga4_ads_linked",!1)&&vn(xn(Xm.Z.Ea),function(){d.bq(a,c,f)})}else c.onFailure()};k.bq=function(a,b,c){function d(){for(var h=l(NN),m=h.next();!m.done;m=h.next()){var n=m.value;n(f);if(f.isAborted)break}S(f,R.C.Ja)||f.isAborted||$z(f)}var e=Op(this.O),f=new JH(e,a,b);V(f,R.C.fa,N.K.Ia);V(f,R.C.Ja,!0);V(f,R.C.kh,S(c,R.C.kh));var g=[M.m.V,M.m.W];vp(function(){d();sp(g)||up(function(h){var m,n;m=h.consentEventId;n=h.consentPriorityId;V(f,R.C.ka,!0);V(f,R.C.Be,m);V(f,R.C.Ce,
n);d()},g)},g)};k.gq=function(a){var b=this;try{kw(a);if(a.isAborted){XL();return}H(165)||(this.D=a);QN(a);RN(a);SN(a);TN(a);H(138)&&(a.isAborted=!0);bw(a);var c={};AM(a,c);if(a.isAborted){a.F.onFailure();XL();return}H(165)&&(this.D=a);var d=c.Lo;c.To===0&&VL(25);d===0&&VL(26);mw(a);V(a,R.C.Nf,Xm.Z.Ec);UN(a);VN(a);this.no(a);this.J.Aq(a);WN(a);XN(a);YN(a);ZN(a);this.Cm(IN(a));var e=a.eventName===M.m.ra;e&&(this.R=!0);$N(a);e&&!a.isAborted&&this.Za++>0&&VL(17);aO(a);bO(a);AN(a,this.clientId,this.sb,
this.J,!this.la);cO(a);dO(a);eO(a);this.Ca=fO(a,this.Ca);gO(a);hO(a);iO(a);jO(a);kO(a);wM(a);CM(a);yN(a);xN(a);wN(a);vN(a);uN(a);tN(a);rN(a);qN(a);oN(a);mN(a);lN(a);kN(a);jN(a);xM(a);yM(a);lO(a);mO(a);nO(a);dw(a);cw(a);jw(a);oO(a);pO(a);Kw(a);qO(a);sN(a);pN(a);rO(a);!this.R&&S(a,R.C.Ee)&&VL(18);WL(a);if(S(a,R.C.Ja)||a.isAborted){a.F.onFailure();XL();return}this.Cm(zN(a,this.clientId));this.la=!0;this.xq(a);sO(a);nN(function(f){b.Vl(f)},a);this.J.Kj();tO(a);iw(a);if(a.isAborted){a.F.onFailure();XL();
return}this.Vl(a);a.F.onSuccess()}catch(f){a.F.onFailure()}XL()};k.Vl=function(a){this.ia.add(a)};k.Cm=function(a){var b=a.clientId,c=a.sb;b&&c&&(this.clientId=b,this.sb=c)};k.flush=function(){this.ia.flush()};k.xq=function(a){var b=this;if(!this.T){var c=sp(M.m.W),d=sp(M.m.ja);tp([M.m.W,M.m.ja],function(){var e=sp(M.m.W),f=sp(M.m.ja),g=!1,h={},m={};if(d!==f&&b.D&&b.sb&&b.clientId){var n=b.clientId,p;var q=iM(b.sb);p=q?q.h:void 0;if(f){var r=eM(b.D);if(r){b.clientId=r;var t=nM(b.D);t&&(b.sb=kM(t,
b.sb,b.D))}else cM(b.clientId,b.D),$L(b.clientId,!0);mM(b.sb,b.D);g=!0;h[M.m.hi]=n;H(69)&&p&&(h[M.m.Pn]=p)}else b.sb=void 0,b.clientId=void 0,y.gaGlobal={}}e&&!c&&(g=!0,m[R.C.Df]=!0,h[M.m.Uh]=Io[M.m.W]);if(g){var u=Qw(b.O,M.m.Qd,h);Tw(u,a.F.eventId,{eventMetadata:m})}d=f;c=e});this.T=!0}};k.no=function(a){a.eventName!==M.m.Ab&&this.J.mo(a)};var SN=function(a){var b=A.location.protocol;b!=="http:"&&b!=="https:"&&(O(29),a.isAborted=!0)},TN=function(a){pc&&pc.loadPurpose==="preview"&&(O(30),a.isAborted=
!0)},UN=function(a){var b={prefix:String(P(a.F,M.m.ib,"")),path:String(P(a.F,M.m.Qb,"/")),flags:String(P(a.F,M.m.wb,"")),domain:String(P(a.F,M.m.pb,"auto")),Ac:Number(P(a.F,M.m.qb,63072E3))};V(a,R.C.za,b)},WN=function(a){S(a,R.C.vd)?V(a,R.C.od,!1):lw(a,"ccd_add_ec_stitching",!1)&&V(a,R.C.od,!0)},XN=function(a){if(S(a,R.C.od)&&lw(a,"ccd_add_1p_data",!1)){var b=a.F.J[M.m.ah];if(Bk(b)){var c=P(a.F,M.m.Ya);if(c===null)V(a,R.C.me,null);else if(b.enable_code&&hd(c)&&V(a,R.C.me,c),hd(b.selectors)&&!S(a,
R.C.sh)){var d={};V(a,R.C.sh,zk(b.selectors,d));H(60)&&a.mergeHitDataForKey(M.m.rc,{ec_data_layer:wk(d)})}}}},YN=function(a){if(H(91)&&!H(88)&&lw(a,"ga4_ads_linked",!1)&&a.eventName===M.m.ra){var b=P(a.F,M.m.Qa)!==!1;if(b){var c=Qv(a);c.Ac&&(c.Ac=Math.min(c.Ac,7776E3));Rv({pe:b,xe:Lo(P(a.F,M.m.Ra)),Ae:!!P(a.F,M.m.Cb),Qc:c})}}},ZN=function(a){if(H(97)){var b=Er(a.F);P(a.F,M.m.Tb)===!0&&(b=!1);V(a,R.C.Lh,b)}},lO=function(a){if(!Ty(y))O(87);else if(Yy!==void 0){O(85);var b=Ry();b?P(a.F,M.m.Xg)&&!my(a)||
Wy(b,a):O(86)}},$N=function(a){a.eventName===M.m.ra&&(P(a.F,M.m.rb,!0)?(a.F.D[M.m.qa]&&(a.F.O[M.m.qa]=a.F.D[M.m.qa],a.F.D[M.m.qa]=void 0,X(a,M.m.qa)),a.eventName=M.m.Xc):a.isAborted=!0)},VN=function(a){function b(c,d){yo[c]||d===void 0||X(a,c,d)}sb(a.F.O,b);sb(a.F.D,b)},cO=function(a){var b=fq(a.F),c=function(d,e){MN[d]&&X(a,d,e)};hd(b[M.m.Pe])?sb(b[M.m.Pe],function(d,e){c((M.m.Pe+"_"+d).toLowerCase(),e)}):sb(b,c)},aO=JN,sO=function(a){if(H(132)&&my(a)&&!(uc("; wv")||uc("FBAN")||uc("FBAV")||wc())&&
sp(M.m.ja)){V(a,R.C.xl,!0);my(a)&&jy(a,"sw_exp",1);a:{if(!H(132)||!my(a))break a;var b=Vk(Yk(a.F),"/_/service_worker");Ky(b);}}},oO=function(a){if(a.eventName===M.m.Ab){var b=P(a.F,M.m.qc),c=P(a.F,M.m.Hc),d;d=Sv(a,b);c(d||P(a.F,b));a.isAborted=!0}},dO=function(a){if(!P(a.F,M.m.Jc)||!P(a.F,M.m.Kc)){var b=a.copyToHitData,c=M.m.Ba,d="",e=A.location;if(e){var f=e.pathname||"";f.charAt(0)!=="/"&&(f="/"+f);
var g=e.search||"";if(g&&g[0]==="?")for(var h=g.substring(1).split("&"),m=0;m<h.length;++m){var n=h[m].split("=");n&&n.length===2&&n[0]==="wbraid"&&(g=g.replace(/([?&])wbraid=[^&]+/,"$1wbraid="+Kb(n[1])))}d=e.protocol+"//"+e.hostname+f+g}b.call(a,c,d,HN);var p=a.copyToHitData,q=M.m.Wa,r;a:{var t=ds("_opt_expid",void 0,void 0,M.m.ja)[0];if(t){var u=Jk(t);if(u){var v=u.split("$");if(v.length===3){r=v[2];break a}}}var w=Cp.ga4_referrer_override;if(w!==void 0)r=w;else{var x=pk("gtm.gtagReferrer."+a.target.destinationId),
z=A.referrer;r=x?""+x:z}}p.call(a,q,r||void 0,HN);a.copyToHitData(M.m.Bb,A.title);a.copyToHitData(M.m.xb,(pc.language||"").toLowerCase());var B=ax();a.copyToHitData(M.m.Mc,B.width+"x"+B.height);H(145)&&a.copyToHitData(M.m.jf,void 0,HN);H(87)&&sv()&&a.copyToHitData(M.m.jd,"1")}},fO=function(a,b){var c=S(a,R.C.Hf);c=c||0;var d=sp(M.m.V),e=c===0||!b&&d||!!S(a,R.C.Df)||!!Sv(a,M.m.hi);V(a,R.C.Ii,e);e&&V(a,R.C.Hf,60);return d},gO=function(a){V(a,R.C.yg,!1);V(a,R.C.Ld,!1);if(!my(a)&&!S(a,R.C.vd)&&P(a.F,
M.m.Mb)!==!1&&fK()&&sp(M.m.V)&&(!H(143)||sp(M.m.ja))){var b=ny(a);(S(a,R.C.ee)||P(a.F,M.m.hi))&&V(a,R.C.yg,!!b);b&&S(a,R.C.Ii)&&S(a,R.C.Gi)&&V(a,R.C.Ld,!0)}},hO=function(a){V(a,R.C.zg,!1);V(a,R.C.Ag,!1);if(!(H(187)&&uo()||!jk()||my(a)||S(a,R.C.vd))&&S(a,R.C.Ii)){var b=S(a,R.C.Ld);S(a,R.C.Gf)&&(b?V(a,R.C.Ag,!0):H(176)&&V(a,R.C.zg,!0))}},kO=function(a){a.copyToHitData(M.m.oi);for(var b=P(a.F,M.m.ii)||[],c=0;c<b.length;c++){var d=b[c];if(d.rule_result){a.copyToHitData(M.m.oi,d.traffic_type);VL(3);break}}},
tO=function(a){a.copyToHitData(M.m.Kk);P(a.F,M.m.Xg)&&(X(a,M.m.Xg,!0),my(a)||GN(a))},pO=function(a){a.copyToHitData(M.m.Sa);a.copyToHitData(M.m.Vb)},eO=function(a){lw(a,"google_ng")&&!so()?a.copyToHitData(M.m.Yd,1):ew(a)},rO=function(a){var b=P(a.F,M.m.Kc);b&&VL(12);S(a,R.C.Ee)&&VL(14);var c=Gm(Hm());(b||Tm(c)||c&&c.parent&&c.context&&c.context.source===5)&&VL(19)},QN=function(a){if(DN(a.target.destinationId))O(28),a.isAborted=!0;else if(H(144)){var b=Fm();if(b&&Array.isArray(b.destinations))for(var c=
0;c<b.destinations.length;c++)if(DN(b.destinations[c])){O(125);a.isAborted=!0;break}}},mO=function(a){Il("attribution-reporting")&&X(a,M.m.bd,"1")},RN=function(a){if(KN.ep.replace(/\s+/g,"").split(",").indexOf(a.eventName)>=0)a.isAborted=!0;else{var b=ky(a);b&&b.blacklisted&&(a.isAborted=!0)}},iO=function(a){var b=function(c){return!!c&&c.conversion};V(a,R.C.Ef,b(ky(a)));S(a,R.C.Ff)&&V(a,R.C.tl,b(ky(a,"first_visit")));S(a,R.C.ee)&&V(a,R.C.wl,b(ky(a,"session_start")))},jO=function(a){Co.hasOwnProperty(a.eventName)&&
(V(a,R.C.sl,!0),a.copyToHitData(M.m.wa),a.copyToHitData(M.m.Va))},qO=function(a){if(!my(a)&&S(a,R.C.Ef)&&sp(M.m.V)&&lw(a,"ga4_ads_linked",!1)){var b=Qv(a),c=ku(b.prefix),d=Lv(c);X(a,M.m.Rd,d.zh);X(a,M.m.Td,d.Bh);X(a,M.m.Sd,d.Ah)}},nO=function(a){if(H(122)){var b=uo();b&&V(a,R.C.co,b)}},bO=function(a){V(a,R.C.Gi,ny(a)&&P(a.F,M.m.Mb)!==!1&&fK()&&!so())};
function PN(a){sb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[M.m.Vb]||{};sb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};var vO=function(a){if(!uO(a)){var b=!1,c=function(){!b&&uO(a)&&(b=!0,Jc(A,"visibilitychange",c),H(5)&&Jc(A,"prerenderingchange",c),O(55))};Ic(A,"visibilitychange",c);H(5)&&Ic(A,"prerenderingchange",c);O(54)}},uO=function(a){if(H(5)&&"prerendering"in A?A.prerendering:A.visibilityState==="prerender")return!1;a();return!0};function wO(a,b){vO(function(){var c=Op(a);if(c){var d=xO(c,b);Nq(a,d,Xm.Z.Ec)}});}function xO(a,b){var c=function(){};var d=new ON(a.id),e=a.prefix==="MC";c=function(f,g,h,m){e&&(m.eventMetadata[R.C.vd]=!0);d.fq(g,h,m)};tm||yO(a,d,b);return c}
function yO(a,b,c){var d=b.J,e={},f={eventId:c,eventMetadata:(e[R.C.Tj]=!0,e),deferrable:!0};d.hq(function(){RL=!0;Oq.flush();d.Ch()>=1E3&&pc.sendBeacon!==void 0&&Pq(M.m.Qd,{},a.id,f);b.flush();d.Dm(function(){RL=!1;d.Dm()})});};var zO=xO;function BO(a,b,c){var d=this;}BO.N="internal.gtagConfig";
function DO(a,b){}
DO.publicName="gtagSet";function EO(){var a={};return a};function FO(a){}FO.N="internal.initializeServiceWorker";function GO(a,b){}GO.publicName="injectHiddenIframe";var HO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function IO(a,b,c,d,e){}IO.N="internal.injectHtml";var MO={};
function OO(a,b,c,d){}var PO={dl:1,id:1},QO={};
function RO(a,b,c,d){}H(160)?RO.publicName="injectScript":OO.publicName="injectScript";RO.N="internal.injectScript";function SO(){return vo()}SO.N="internal.isAutoPiiEligible";function TO(a){var b=!0;return b}TO.publicName="isConsentGranted";function UO(a){var b=!1;return b}UO.N="internal.isDebugMode";function VO(){return to()}VO.N="internal.isDmaRegion";function WO(a){var b=!1;return b}WO.N="internal.isEntityInfrastructure";function XO(a){var b=!1;if(!oh(a))throw J(this.getName(),["number"],[a]);b=H(a);return b}XO.N="internal.isFeatureEnabled";function YO(){var a=!1;return a}YO.N="internal.isFpfe";function ZO(){var a=!1;return a}ZO.N="internal.isGcpConversion";function $O(){var a=!1;return a}$O.N="internal.isLandingPage";function aP(){var a;return a}aP.N="internal.isSafariPcmEligibleBrowser";function bP(){var a=Lh(function(b){qF(this).log("error",b)});a.publicName="JSON";return a};function cP(a){var b=void 0;return yd(b)}cP.N="internal.legacyParseUrl";function dP(){return!1}
var eP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function fP(){}fP.publicName="logToConsole";function gP(a,b){}gP.N="internal.mergeRemoteConfig";function hP(a,b,c){c=c===void 0?!0:c;var d=[];return yd(d)}hP.N="internal.parseCookieValuesFromString";function iP(a){var b=void 0;if(typeof a!=="string")return;a&&Eb(a,"//")&&(a=A.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=yd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Qk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Jk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=yd(n);
return b}iP.publicName="parseUrl";function jP(a){}jP.N="internal.processAsNewEvent";function kP(a,b,c){var d;return d}kP.N="internal.pushToDataLayer";function lP(a){var b=ya.apply(1,arguments),c=!1;if(!jh(a))throw J(this.getName(),["string"],arguments);for(var d=[this,a],e=l(b),f=e.next();!f.done;f=e.next())d.push(xd(f.value,this.M,1));try{L.apply(null,d),c=!0}catch(g){return!1}return c}lP.publicName="queryPermission";function mP(a){var b=this;}mP.N="internal.queueAdsTransmission";function nP(a,b){var c=void 0;return c}nP.publicName="readAnalyticsStorage";function oP(){var a="";return a}oP.publicName="readCharacterSet";function pP(){return Rj}pP.N="internal.readDataLayerName";function qP(){var a="";return a}qP.publicName="readTitle";function rP(a,b){var c=this;if(!jh(a)||!fh(b))throw J(this.getName(),["string","function"],arguments);Lw(a,function(d){b.invoke(c.M,yd(d,c.M,1))});}rP.N="internal.registerCcdCallback";function sP(a,b){return!0}sP.N="internal.registerDestination";var tP=["config","event","get","set"];function uP(a,b,c){}uP.N="internal.registerGtagCommandListener";function vP(a,b){var c=!1;return c}vP.N="internal.removeDataLayerEventListener";function wP(a,b){}
wP.N="internal.removeFormData";function xP(){}xP.publicName="resetDataLayer";function yP(a,b,c){var d=void 0;return d}yP.N="internal.scrubUrlParams";function zP(a){}zP.N="internal.sendAdsHit";function AP(a,b,c,d){if(arguments.length<2||!dh(d)||!dh(c))throw J(this.getName(),["any","any","Object|undefined","Object|undefined"],arguments);var e=c?xd(c):{},f=xd(a),g=Array.isArray(f)?f:[f];b=String(b);var h=d?xd(d):{},m=qF(this);h.originatingEntity=gG(m);for(var n=0;n<g.length;n++){var p=g[n];if(typeof p==="string"){var q=
{};id(e,q);var r={};id(h,r);var t=Qw(p,b,q);Tw(t,h.eventId||m.eventId,r)}}}AP.N="internal.sendGtagEvent";function BP(a,b,c){}BP.publicName="sendPixel";function CP(a,b){}CP.N="internal.setAnchorHref";function DP(a){}DP.N="internal.setContainerConsentDefaults";function EP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}EP.publicName="setCookie";function FP(a){}FP.N="internal.setCorePlatformServices";function GP(a,b){}GP.N="internal.setDataLayerValue";function HP(a){}HP.publicName="setDefaultConsentState";function IP(a,b){}IP.N="internal.setDelegatedConsentType";function JP(a,b){}JP.N="internal.setFormAction";function KP(a,b,c){c=c===void 0?!1:c;}KP.N="internal.setInCrossContainerData";function LP(a,b,c){return!1}LP.publicName="setInWindow";function MP(a,b,c){}MP.N="internal.setProductSettingsParameter";function NP(a,b,c){if(!jh(a)||!jh(b)||arguments.length!==3)throw J(this.getName(),["string","string","any"],arguments);for(var d=b.split("."),e=Rq(a),f=0;f<d.length-1;f++){if(e[d[f]]===void 0)e[d[f]]={};else if(!hd(e[d[f]]))throw Error("setRemoteConfigParameter failed, path contains a non-object type: "+d[f]);e=e[d[f]]}e[d[f]]=xd(c,this.M,1);}NP.N="internal.setRemoteConfigParameter";function OP(a,b){}OP.N="internal.setTransmissionMode";function PP(a,b,c,d){var e=this;}PP.publicName="sha256";function QP(a,b,c){}
QP.N="internal.sortRemoteConfigParameters";function RP(a){}RP.N="internal.storeAdsBraidLabels";function SP(a,b){var c=void 0;return c}SP.N="internal.subscribeToCrossContainerData";var TP={},UP={};TP.getItem=function(a){var b=null;L(this,"access_template_storage");var c=qF(this).Hb();UP[c]&&(b=UP[c].hasOwnProperty("gtm."+a)?UP[c]["gtm."+a]:null);return b};TP.setItem=function(a,b){L(this,"access_template_storage");var c=qF(this).Hb();UP[c]=UP[c]||{};UP[c]["gtm."+a]=b;};
TP.removeItem=function(a){L(this,"access_template_storage");var b=qF(this).Hb();if(!UP[b]||!UP[b].hasOwnProperty("gtm."+a))return;delete UP[b]["gtm."+a];};TP.clear=function(){L(this,"access_template_storage"),delete UP[qF(this).Hb()];};TP.publicName="templateStorage";function VP(a,b){var c=!1;return c}VP.N="internal.testRegex";function WP(a){var b;return b};function XP(a){var b;return b}XP.N="internal.unsiloId";function YP(a,b){var c;return c}YP.N="internal.unsubscribeFromCrossContainerData";function ZP(a){}ZP.publicName="updateConsentState";function $P(a){var b=!1;return b}$P.N="internal.userDataNeedsEncryption";var aQ;function bQ(a,b,c){aQ=aQ||new Wh;aQ.add(a,b,c)}function cQ(a,b){var c=aQ=aQ||new Wh;if(c.D.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.D[a]=kb(b)?rh(a,b):sh(a,b)}
function dQ(){return function(a){var b;var c=aQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.D.hasOwnProperty(a)){var e=this.M.D;if(e){var f=!1,g=e.Hb();if(g){yh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.D.hasOwnProperty(a)?c.D[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function eQ(){var a=function(c){return void cQ(c.N,c)},b=function(c){return void bQ(c.publicName,c)};b(kF);b(rF);b(GG);b(IG);b(JG);b(QG);b(SG);b(NH);b(bP());b(PH);b(jL);b(kL);b(GL);b(HL);b(IL);b(OL);b(DO);b(GO);b(TO);b(fP);b(iP);b(lP);b(oP);b(qP);b(BP);b(EP);b(HP);b(LP);b(PP);b(TP);b(ZP);bQ("Math",wh());bQ("Object",Uh);bQ("TestHelper",Yh());bQ("assertApi",th);bQ("assertThat",uh);bQ("decodeUri",zh);bQ("decodeUriComponent",Ah);bQ("encodeUri",Bh);bQ("encodeUriComponent",Ch);bQ("fail",Hh);bQ("generateRandom",
Ih);bQ("getTimestamp",Jh);bQ("getTimestampMillis",Jh);bQ("getType",Kh);bQ("makeInteger",Mh);bQ("makeNumber",Nh);bQ("makeString",Oh);bQ("makeTableMap",Ph);bQ("mock",Sh);bQ("mockObject",Th);bQ("fromBase64",cL,!("atob"in y));bQ("localStorage",eP,!dP());bQ("toBase64",WP,!("btoa"in y));a(jF);a(nF);a(IF);a(UF);a(aG);a(fG);a(vG);a(EG);a(HG);a(KG);a(LG);a(MG);a(NG);a(OG);a(PG);a(RG);a(TG);a(MH);a(OH);a(QH);a(RH);a(SH);a(TH);a(UH);a(VH);a($H);a(hI);a(iI);a(tI);a(yI);a(DI);a(MI);a(RI);a(dJ);a(fJ);a(tJ);a(uJ);
a(wJ);a(aL);a(bL);a(dL);a(eL);a(fL);a(gL);a(hL);a(mL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(wL);a(xL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(FL);a(JL);a(KL);a(LL);a(ML);a(NL);a(QL);a(BO);a(FO);a(IO);a(RO);a(SO);a(UO);a(VO);a(WO);a(XO);a(YO);a(ZO);a($O);a(aP);a(cP);a(tG);a(gP);a(hP);a(jP);a(kP);a(mP);a(pP);a(rP);a(sP);a(uP);a(vP);a(wP);a(yP);a(zP);a(AP);a(CP);a(DP);a(FP);a(GP);a(IP);a(JP);a(KP);a(MP);a(NP);a(OP);a(QP);a(RP);a(SP);a(VP);a(XP);a(YP);a($P);cQ("internal.IframingStateSchema",
EO());
H(104)&&a(lL);H(160)?b(RO):b(OO);H(177)&&b(nP);return dQ()};var hF;
function fQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;hF=new Se;gQ();Af=gF();var e=hF,f=eQ(),g=new qd("require",f);g.eb();e.D.D.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&Wf(n,d[m]);try{hF.execute(n),H(120)&&el&&n[0]===50&&h.push(n[1])}catch(r){}}H(120)&&(Nf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,"");fk[q]=
["sandboxedScripts"]}hQ(b)}function gQ(){hF.D.D.O=function(a,b,c){Cp.SANDBOXED_JS_SEMAPHORE=Cp.SANDBOXED_JS_SEMAPHORE||0;Cp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Cp.SANDBOXED_JS_SEMAPHORE--}}}function hQ(a){a&&sb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");fk[e]=fk[e]||[];fk[e].push(b)}})};function iQ(a){Tw(Nw("developer_id."+a,!0),0,{})};var jQ=Array.isArray;function kQ(a,b){return id(a,b||null)}function Y(a){return window.encodeURIComponent(a)}function lQ(a,b,c){Fc(a,b,c)}function mQ(a,b){if(!a)return!1;var c=Kk(Qk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}
function nQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}var wQ=y.clearTimeout,xQ=y.setTimeout;function yQ(a,b,c){if(Qr()){b&&D(b)}else return Bc(a,b,c,void 0)}function zQ(){return y.location.href}function AQ(a,b){return pk(a,b||2)}function BQ(a,b){y[a]=b}function CQ(a,b,c){b&&(y[a]===void 0||c&&!y[a])&&(y[a]=b);return y[a]}function DQ(a,b){if(Qr()){b&&D(b)}else Dc(a,b)}
var EQ={};var Z={securityGroups:{}};

Z.securityGroups.access_template_storage=["google"],Z.__access_template_storage=function(){return{assert:function(){},U:function(){return{}}}},Z.__access_template_storage.H="access_template_storage",Z.__access_template_storage.isVendorTemplate=!0,Z.__access_template_storage.priorityOverride=0,Z.__access_template_storage.isInfrastructure=!1,Z.__access_template_storage.runInSiloedMode=!1;
Z.securityGroups.v=["google"],Z.__v=function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=AQ(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return c!==void 0?c:a.vtp_defaultValue},Z.__v.H="v",Z.__v.isVendorTemplate=!0,Z.__v.priorityOverride=0,Z.__v.isInfrastructure=!0,Z.__v.runInSiloedMode=!1;

Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.H="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data.runInSiloedMode=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!lb(g))throw e(f,{key:g},"Key must be a string.");
if(c!=="any"){try{if(c==="specific"&&g!=null&&Hg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},U:a}})}();

Z.securityGroups.detect_youtube_activity_events=["google"],function(){function a(b,c){return{options:{fixMissingApi:!!c.fixMissingApi}}}(function(b){Z.__detect_youtube_activity_events=b;Z.__detect_youtube_activity_events.H="detect_youtube_activity_events";Z.__detect_youtube_activity_events.isVendorTemplate=!0;Z.__detect_youtube_activity_events.priorityOverride=0;Z.__detect_youtube_activity_events.isInfrastructure=!1;Z.__detect_youtube_activity_events.runInSiloedMode=!1})(function(b){var c=!!b.vtp_allowFixMissingJavaScriptApi,
d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.fixMissingApi)throw d(e,{},"Prohibited option: fixMissingApi.");},U:a}})}();


Z.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_history_change_events=b;Z.__detect_history_change_events.H="detect_history_change_events";Z.__detect_history_change_events.isVendorTemplate=!0;Z.__detect_history_change_events.priorityOverride=0;Z.__detect_history_change_events.isInfrastructure=!1;Z.__detect_history_change_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();



Z.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_link_click_events=b;Z.__detect_link_click_events.H="detect_link_click_events";Z.__detect_link_click_events.isVendorTemplate=!0;Z.__detect_link_click_events.priorityOverride=0;Z.__detect_link_click_events.isInfrastructure=!1;Z.__detect_link_click_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},U:a}})}();
Z.securityGroups.detect_form_submit_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Z.__detect_form_submit_events=b;Z.__detect_form_submit_events.H="detect_form_submit_events";Z.__detect_form_submit_events.isVendorTemplate=!0;Z.__detect_form_submit_events.priorityOverride=0;Z.__detect_form_submit_events.isInfrastructure=!1;Z.__detect_form_submit_events.runInSiloedMode=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&
f&&f.waitForTags)throw d(e,{},"Prohibited option waitForTags.");},U:a}})}();Z.securityGroups.read_container_data=["google"],Z.__read_container_data=function(){return{assert:function(){},U:function(){return{}}}},Z.__read_container_data.H="read_container_data",Z.__read_container_data.isVendorTemplate=!0,Z.__read_container_data.priorityOverride=0,Z.__read_container_data.isInfrastructure=!1,Z.__read_container_data.runInSiloedMode=!1;

Z.securityGroups.listen_data_layer=["google"],function(){function a(b,c){return{eventName:c}}(function(b){Z.__listen_data_layer=b;Z.__listen_data_layer.H="listen_data_layer";Z.__listen_data_layer.isVendorTemplate=!0;Z.__listen_data_layer.priorityOverride=0;Z.__listen_data_layer.isInfrastructure=!1;Z.__listen_data_layer.runInSiloedMode=!1})(function(b){var c=b.vtp_accessType,d=b.vtp_allowedEvents||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!lb(g))throw e(f,{eventName:g},"Event name must be a string.");
if(!(c==="any"||c==="specific"&&d.indexOf(g)>=0))throw e(f,{eventName:g},"Prohibited listen on data layer event.");},U:a}})}();
Z.securityGroups.detect_user_provided_data=["google"],function(){function a(b,c){return{dataSource:c}}(function(b){Z.__detect_user_provided_data=b;Z.__detect_user_provided_data.H="detect_user_provided_data";Z.__detect_user_provided_data.isVendorTemplate=!0;Z.__detect_user_provided_data.priorityOverride=0;Z.__detect_user_provided_data.isInfrastructure=!1;Z.__detect_user_provided_data.runInSiloedMode=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(e!=="auto"&&e!=="manual"&&
e!=="code")throw c(d,{},"Unknown user provided data source.");if(b.vtp_limitDataSources)if(e!=="auto"||b.vtp_allowAutoDataSources){if(e==="manual"&&!b.vtp_allowManualDataSources)throw c(d,{},"Detection of user provided data via manually specified CSS selectors is not allowed.");if(e==="code"&&!b.vtp_allowCodeDataSources)throw c(d,{},"Detection of user provided data from an in-page variable is not allowed.");}else throw c(d,{},"Automatic detection of user provided data is not allowed.");},U:a}})}();



Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.H="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url.runInSiloedMode=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),
b.vtp_fragment&&c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!lb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!lb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},U:a}})}();



Z.securityGroups.gct=["google"],function(){function a(b){for(var c=[],d=0;d<b.length;d++)try{c.push(new RegExp(b[d]))}catch(e){}return c}(function(b){Z.__gct=b;Z.__gct.H="gct";Z.__gct.isVendorTemplate=!0;Z.__gct.priorityOverride=0;Z.__gct.isInfrastructure=!1;Z.__gct.runInSiloedMode=!0})(function(b){var c={},d=b.vtp_sessionDuration;d>0&&(c[M.m.lf]=d);c[M.m.Pg]=b.vtp_eventSettings;c[M.m.uk]=b.vtp_dynamicEventSettings;c[M.m.Zd]=b.vtp_googleSignals===1;c[M.m.Lk]=b.vtp_foreignTld;c[M.m.Jk]=b.vtp_restrictDomain===
1;c[M.m.ii]=b.vtp_internalTrafficResults;var e=M.m.Ra,f=b.vtp_linker;f&&f[M.m.na]&&(f[M.m.na]=a(f[M.m.na]));c[e]=f;var g=M.m.ji,h=b.vtp_referralExclusionDefinition;h&&h.include_conditions&&(h.include_conditions=a(h.include_conditions));c[g]=h;var m=Im(b.vtp_trackingId);Tq(m,c);wO(m,b.vtp_gtmEventId);D(b.vtp_gtmOnSuccess)})}();



Z.securityGroups.get=["google"],Z.__get=function(a){var b=a.vtp_settings,c=b.eventParameters||{},d=String(a.vtp_eventName),e={};e.eventId=a.vtp_gtmEventId;e.priorityId=a.vtp_gtmPriorityId;a.vtp_deferrable&&(e.deferrable=!0);var f=Qw(String(b.streamId),d,c);Tw(f,e.eventId,e);a.vtp_gtmOnSuccess()},Z.__get.H="get",Z.__get.isVendorTemplate=!0,Z.__get.priorityOverride=0,Z.__get.isInfrastructure=!1,Z.__get.runInSiloedMode=!1;
Z.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_scroll_events=b;Z.__detect_scroll_events.H="detect_scroll_events";Z.__detect_scroll_events.isVendorTemplate=!0;Z.__detect_scroll_events.priorityOverride=0;Z.__detect_scroll_events.isInfrastructure=!1;Z.__detect_scroll_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();



Z.securityGroups.detect_form_interaction_events=["google"],function(){function a(){return{}}(function(b){Z.__detect_form_interaction_events=b;Z.__detect_form_interaction_events.H="detect_form_interaction_events";Z.__detect_form_interaction_events.isVendorTemplate=!0;Z.__detect_form_interaction_events.priorityOverride=0;Z.__detect_form_interaction_events.isInfrastructure=!1;Z.__detect_form_interaction_events.runInSiloedMode=!1})(function(){return{assert:function(){},U:a}})}();

var Gp={dataLayer:qk,callback:function(a){ek.hasOwnProperty(a)&&kb(ek[a])&&ek[a]();delete ek[a]},bootstrap:0};
function FQ(){Ep();Lm();WB();Cb(fk,Z.securityGroups);var a=Gm(Hm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;cp(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);Mf={No:bg}}var GQ=!1;
function no(){try{if(GQ||!Um()){Nj();Kj.T=Ni(18,"");
Kj.Eb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Kj.Za="ad_storage|analytics_storage|ad_user_data";Kj.Ca="5690";Kj.Ca="5690";Jm();if(H(109)){}Ia[8]=!0;var a=Dp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});jp(a);Bp();YE();rr();Ip();if(Mm()){qG();GB().removeExternalRestrictions(Em());}else{Zy();SB();Kf();Gf=Z;Hf=IE;dg=new kg;fQ();FQ();lo||(ko=po());
yp();UD();gD();AD=!1;A.readyState==="complete"?CD():Ic(y,"load",CD);aD();el&&(vq(Jq),y.setInterval(Iq,864E5),vq(ZE),vq(yC),vq(gA),vq(Mq),vq(dF),vq(JC),H(120)&&(vq(DC),vq(EC),vq(FC)),$E={},vq(aF),Qi());fl&&(Qn(),bq(),WD(),$D(),YD(),Gn("bt",String(Kj.D?2:Kj.O?1:0)),Gn("ct",String(Kj.D?0:Kj.O?1:Qr()?2:3)),XD());yE();$n(1);rG();dE();dk=zb();Gp.bootstrap=dk;Kj.R&&TD();H(109)&&AA();H(134)&&(typeof y.name==="string"&&Eb(y.name,"web-pixel-sandbox-CUSTOM")&&Yc()?iQ("dMDg0Yz"):y.Shopify&&(iQ("dN2ZkMj"),Yc()&&iQ("dNTU0Yz")))}}}catch(b){$n(4),Fq()}}
(function(a){function b(){n=A.documentElement.getAttribute("data-tag-assistant-present");Qo(n)&&(m=h.ml)}function c(){m&&sc?g(m):a()}if(!y[Ni(37,"__TAGGY_INSTALLED")]){var d=!1;if(A.referrer){var e=Qk(A.referrer);d=Mk(e,"host")===Ni(38,"cct.google")}if(!d){var f=ds(Ni(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(y[Ni(37,"__TAGGY_INSTALLED")]=!0,Bc(Ni(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";Xj&&(v="OGT",w="GTAG");
var x=Ni(23,"google.tagmanager.debugui2.queue"),z=y[x];z||(z=[],y[x]=z,Bc("https://"+Oj.Bg+"/debug/bootstrap?id="+hg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Ur()));var B={messageType:"CONTAINER_STARTING",data:{scriptSource:sc,containerProduct:v,debug:!1,id:hg.ctid,targetRef:{ctid:hg.ctid,isDestination:vm()},aliases:ym(),destinations:wm()}};B.data.resume=function(){a()};Oj.Wm&&(B.data.initialPublish=!0);z.push(B)},h={io:1,pl:2,El:3,hk:4,ml:5};h[h.io]="GTM_DEBUG_LEGACY_PARAM";h[h.pl]="GTM_DEBUG_PARAM";h[h.El]="REFERRER";
h[h.hk]="COOKIE";h[h.ml]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Kk(y.location,"query",!1,void 0,"gtm_debug");Qo(p)&&(m=h.pl);if(!m&&A.referrer){var q=Qk(A.referrer);Mk(q,"host")===Ni(24,"tagassistant.google.com")&&(m=h.El)}if(!m){var r=ds("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.hk)}m||b();if(!m&&Po(n)){var t=!1;Ic(A,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);y.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){H(83)&&GQ&&!po()["0"]?mo():no()});

})()

