#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para copiar o CPP processado para o diretório LexJus
"""

import shutil
import os
import json
from datetime import datetime

def copiar_cpp_para_lexjus():
    """
    Copia os arquivos CPP processados para o diretório LexJus
    """
    print("=" * 60)
    print("📁 COPIANDO CPP CORPUS927 PARA LEXJUS")
    print("=" * 60)
    
    # Arquivos de origem
    arquivo_origem_json = 'cpp_corpus927_processado.json'
    arquivo_origem_js = 'cpp_corpus927_processado.js'
    
    # Diretório de destino
    diretorio_destino = '../lexjus_VOID'
    
    # Arquivos de destino
    arquivo_destino_json = os.path.join(diretorio_destino, 'codigo_processo_penal.json')
    arquivo_destino_js = os.path.join(diretorio_destino, 'codigo_processo_penal.js')
    
    try:
        # Verificar se os arquivos de origem existem
        if not os.path.exists(arquivo_origem_json):
            print(f"❌ Arquivo {arquivo_origem_json} não encontrado!")
            return
        
        if not os.path.exists(arquivo_origem_js):
            print(f"❌ Arquivo {arquivo_origem_js} não encontrado!")
            return
        
        # Verificar se o diretório de destino existe
        if not os.path.exists(diretorio_destino):
            print(f"❌ Diretório {diretorio_destino} não encontrado!")
            return
        
        # Carregar e verificar dados
        with open(arquivo_origem_json, 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        
        print(f"📊 Dados carregados: {len(artigos)} artigos")
        
        # Copiar arquivo JSON
        shutil.copy2(arquivo_origem_json, arquivo_destino_json)
        print(f"✅ Arquivo JSON copiado: {arquivo_destino_json}")
        
        # Copiar arquivo JS
        shutil.copy2(arquivo_origem_js, arquivo_destino_js)
        print(f"✅ Arquivo JS copiado: {arquivo_destino_js}")
        
        # Verificar tamanhos dos arquivos
        tamanho_json = os.path.getsize(arquivo_destino_json)
        tamanho_js = os.path.getsize(arquivo_destino_js)
        
        print(f"\n📊 INFORMAÇÕES DOS ARQUIVOS:")
        print(f"   • JSON: {tamanho_json:,} bytes")
        print(f"   • JS: {tamanho_js:,} bytes")
        print(f"   • Total de artigos: {len(artigos)}")
        
        if artigos:
            print(f"   • Primeiro artigo: {artigos[0]['artigo']}")
            print(f"   • Último artigo: {artigos[-1]['artigo']}")
            
            # Estatísticas de conteúdo
            total_incisos = sum(len(art['incisos']) for art in artigos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos)
            total_paragrafos_unicos = sum(1 for art in artigos if art['paragrafo_unico'])
            
            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")
        
        print(f"\n✅ CÓPIA CONCLUÍDA COM SUCESSO!")
        print(f"   📄 Os arquivos do CPP estão prontos para uso no LexJus")
        print(f"   🌐 Fonte: https://corpus927.enfam.jus.br/legislacao/cpp-41")
        
        # Criar arquivo de informações
        info_content = f"""# Código de Processo Penal - Informações

## Fonte
- **URL**: https://corpus927.enfam.jus.br/legislacao/cpp-41
- **Extraído em**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Método**: Scraper Corpus927

## Estatísticas
- **Total de artigos**: {len(artigos)}
- **Total de incisos**: {total_incisos}
- **Total de parágrafos numerados**: {total_paragrafos}
- **Total de parágrafos únicos**: {total_paragrafos_unicos}

## Arquivos Gerados
- `codigo_processo_penal.json` - Dados em formato JSON
- `codigo_processo_penal.js` - Dados em formato JavaScript com funções de busca

## Qualidade dos Dados
- ✅ Artigos ordenados numericamente
- ✅ Duplicações removidas
- ✅ Estrutura hierárquica preservada (incisos, parágrafos, alíneas)
- ✅ Referências legais limpas
- ✅ Texto formatado e normalizado

## Uso no LexJus
Os arquivos estão prontos para integração no sistema LexJus e podem ser utilizados
para busca, estudo e consulta dos artigos do Código de Processo Penal.
"""
        
        info_file = os.path.join(diretorio_destino, 'cpp_info.md')
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content)
        print(f"   📄 Arquivo de informações criado: cpp_info.md")
        
    except Exception as e:
        print(f"❌ Erro ao copiar arquivos: {e}")

if __name__ == '__main__':
    copiar_cpp_para_lexjus()
