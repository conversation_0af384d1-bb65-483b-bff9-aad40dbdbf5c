<?php
session_start();
require_once __DIR__ . '/../conexao_POST.php';

// Verificar autenticação
if (!isset($_SESSION['idusuario'])) {
    die("❌ Usuário não autenticado. Faça login primeiro.");
}

$usuario_id = $_SESSION['idusuario'];

echo "<h1>🔧 Correção Automática - Sistema de Revisão</h1>";
echo "<p><strong>Usuário ID:</strong> $usuario_id</p>";
echo "<hr>";

// Função para executar correção
function executarCorrecao($titulo, $descricao, $callback) {
    echo "<div style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px;'>";
    echo "<h3>🔧 $titulo</h3>";
    echo "<p>$descricao</p>";
    
    try {
        $resultado = $callback();
        if ($resultado['sucesso']) {
            echo "<div style='color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px;'>";
            echo "✅ <strong>Sucesso:</strong> {$resultado['mensagem']}";
            echo "</div>";
        } else {
            echo "<div style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px;'>";
            echo "❌ <strong>Erro:</strong> {$resultado['mensagem']}";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='color: #721c24; background: #f8d7da; padding: 10px; border-radius: 4px;'>";
        echo "❌ <strong>Exceção:</strong> " . $e->getMessage();
        echo "</div>";
    }
    
    echo "</div>";
}

// 1. Verificar e criar tabelas necessárias
executarCorrecao(
    "Verificar Estrutura do Banco",
    "Verifica se todas as tabelas necessárias existem e estão corretas.",
    function() use ($conexao) {
        $tabelas_verificar = [
            'lexjus_revisoes',
            'lexjus_historico_revisoes', 
            'lexjus_config_revisao'
        ];
        
        $tabelas_faltando = [];
        
        foreach ($tabelas_verificar as $tabela) {
            $query = "SELECT COUNT(*) as existe FROM information_schema.tables 
                      WHERE table_schema = 'appestudo' AND table_name = '$tabela'";
            $result = pg_query($conexao, $query);
            $row = pg_fetch_assoc($result);
            
            if ($row['existe'] == 0) {
                $tabelas_faltando[] = $tabela;
            }
        }
        
        if (empty($tabelas_faltando)) {
            return ['sucesso' => true, 'mensagem' => 'Todas as tabelas necessárias existem.'];
        } else {
            return ['sucesso' => false, 'mensagem' => 'Tabelas faltando: ' . implode(', ', $tabelas_faltando) . '. Execute o script SQL de instalação.'];
        }
    }
);

// 2. Criar configuração padrão se não existir
executarCorrecao(
    "Configuração do Usuário",
    "Cria configuração padrão se não existir.",
    function() use ($conexao, $usuario_id) {
        $query_verificar = "SELECT COUNT(*) as existe FROM appestudo.lexjus_config_revisao WHERE usuario_id = $1";
        $result = pg_query_params($conexao, $query_verificar, [$usuario_id]);
        $row = pg_fetch_assoc($result);
        
        if ($row['existe'] > 0) {
            return ['sucesso' => true, 'mensagem' => 'Configuração já existe.'];
        }
        
        $query_criar = "INSERT INTO appestudo.lexjus_config_revisao (usuario_id) VALUES ($1)";
        $result = pg_query_params($conexao, $query_criar, [$usuario_id]);
        
        if ($result) {
            return ['sucesso' => true, 'mensagem' => 'Configuração padrão criada.'];
        } else {
            return ['sucesso' => false, 'mensagem' => 'Erro ao criar configuração: ' . pg_last_error($conexao)];
        }
    }
);

// 3. Limpar revisões duplicadas
executarCorrecao(
    "Limpar Revisões Duplicadas",
    "Remove revisões duplicadas que podem causar problemas.",
    function() use ($conexao, $usuario_id) {
        $query = "
            DELETE FROM appestudo.lexjus_revisoes 
            WHERE id NOT IN (
                SELECT MIN(id) 
                FROM appestudo.lexjus_revisoes 
                WHERE usuario_id = $1 
                GROUP BY usuario_id, artigo_numero
            ) AND usuario_id = $1";
        
        $result = pg_query_params($conexao, $query, [$usuario_id]);
        
        if ($result) {
            $linhas_removidas = pg_affected_rows($result);
            return ['sucesso' => true, 'mensagem' => "Removidas $linhas_removidas revisões duplicadas."];
        } else {
            return ['sucesso' => false, 'mensagem' => 'Erro ao limpar duplicadas: ' . pg_last_error($conexao)];
        }
    }
);

// 4. Corrigir datas de revisão inválidas
executarCorrecao(
    "Corrigir Datas Inválidas",
    "Corrige datas de próxima revisão que estão muito no passado.",
    function() use ($conexao, $usuario_id) {
        $query = "
            UPDATE appestudo.lexjus_revisoes 
            SET data_proxima_revisao = CURRENT_TIMESTAMP + INTERVAL '1 day'
            WHERE usuario_id = $1 
            AND data_proxima_revisao < CURRENT_TIMESTAMP - INTERVAL '30 days'";
        
        $result = pg_query_params($conexao, $query, [$usuario_id]);
        
        if ($result) {
            $linhas_atualizadas = pg_affected_rows($result);
            return ['sucesso' => true, 'mensagem' => "Corrigidas $linhas_atualizadas datas inválidas."];
        } else {
            return ['sucesso' => false, 'mensagem' => 'Erro ao corrigir datas: ' . pg_last_error($conexao)];
        }
    }
);

// 5. Normalizar números de artigos
executarCorrecao(
    "Normalizar Números de Artigos",
    "Padroniza o formato dos números de artigos.",
    function() use ($conexao, $usuario_id) {
        $query_select = "SELECT id, artigo_numero FROM appestudo.lexjus_revisoes WHERE usuario_id = $1";
        $result = pg_query_params($conexao, $query_select, [$usuario_id]);
        
        $atualizacoes = 0;
        
        while ($row = pg_fetch_assoc($result)) {
            $numero_original = $row['artigo_numero'];
            $numero_limpo = preg_replace('/^Art\.\s*/', '', $numero_original);
            $numero_limpo = preg_replace('/\.$/', '', $numero_limpo);
            $numero_limpo = trim($numero_limpo);
            
            if ($numero_limpo !== $numero_original) {
                $query_update = "UPDATE appestudo.lexjus_revisoes SET artigo_numero = $1 WHERE id = $2";
                $result_update = pg_query_params($conexao, $query_update, [$numero_limpo, $row['id']]);
                
                if ($result_update) {
                    $atualizacoes++;
                }
            }
        }
        
        return ['sucesso' => true, 'mensagem' => "Normalizados $atualizacoes números de artigos."];
    }
);

// 6. Verificar integridade dos dados
executarCorrecao(
    "Verificar Integridade dos Dados",
    "Verifica se os dados estão consistentes.",
    function() use ($conexao, $usuario_id) {
        $problemas = [];
        
        // Verificar facilidade fora do range
        $query1 = "SELECT COUNT(*) as count FROM appestudo.lexjus_revisoes 
                   WHERE usuario_id = $1 AND (facilidade < 1.30 OR facilidade > 3.00)";
        $result1 = pg_query_params($conexao, $query1, [$usuario_id]);
        $row1 = pg_fetch_assoc($result1);
        if ($row1['count'] > 0) {
            $problemas[] = "{$row1['count']} artigos com facilidade fora do range";
        }
        
        // Verificar qualidade inválida
        $query2 = "SELECT COUNT(*) as count FROM appestudo.lexjus_revisoes 
                   WHERE usuario_id = $1 AND (ultima_qualidade < 0 OR ultima_qualidade > 5)";
        $result2 = pg_query_params($conexao, $query2, [$usuario_id]);
        $row2 = pg_fetch_assoc($result2);
        if ($row2['count'] > 0) {
            $problemas[] = "{$row2['count']} artigos com qualidade inválida";
        }
        
        // Verificar status inválido
        $query3 = "SELECT COUNT(*) as count FROM appestudo.lexjus_revisoes 
                   WHERE usuario_id = $1 AND status NOT IN ('novo', 'aprendendo', 'revisando', 'dominado', 'dificil')";
        $result3 = pg_query_params($conexao, $query3, [$usuario_id]);
        $row3 = pg_fetch_assoc($result3);
        if ($row3['count'] > 0) {
            $problemas[] = "{$row3['count']} artigos com status inválido";
        }
        
        if (empty($problemas)) {
            return ['sucesso' => true, 'mensagem' => 'Todos os dados estão íntegros.'];
        } else {
            return ['sucesso' => false, 'mensagem' => 'Problemas encontrados: ' . implode(', ', $problemas)];
        }
    }
);

// 7. Otimizar performance
executarCorrecao(
    "Otimizar Performance",
    "Executa VACUUM e ANALYZE nas tabelas de revisão.",
    function() use ($conexao) {
        $tabelas = ['lexjus_revisoes', 'lexjus_historico_revisoes', 'lexjus_config_revisao'];
        
        foreach ($tabelas as $tabela) {
            $query = "VACUUM ANALYZE appestudo.$tabela";
            pg_query($conexao, $query);
        }
        
        return ['sucesso' => true, 'mensagem' => 'Otimização de performance executada.'];
    }
);

echo "<hr>";
echo "<h2>🎯 Correção Concluída</h2>";
echo "<p>Todas as correções automáticas foram executadas.</p>";
echo "<p><strong>Próximos passos:</strong></p>";
echo "<ul>";
echo "<li>🧪 <a href='teste_problemas_revisao.html'>Executar testes completos</a></li>";
echo "<li>📊 <a href='diagnostico_revisao.php'>Ver diagnóstico detalhado</a></li>";
echo "<li>🔄 <a href='index.php'>Voltar ao sistema principal</a></li>";
echo "</ul>";

echo "<div style='margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;'>";
echo "<h3>✅ Dicas para Evitar Problemas Futuros:</h3>";
echo "<ol>";
echo "<li><strong>Não adicione artigos manualmente</strong> - Use apenas o sistema automático</li>";
echo "<li><strong>Evite limpar o localStorage</strong> - Isso pode causar dessincronização</li>";
echo "<li><strong>Faça backup regular</strong> - Use o sistema de backup do admin</li>";
echo "<li><strong>Monitore os logs</strong> - Verifique erros no console do navegador</li>";
echo "<li><strong>Mantenha o sistema atualizado</strong> - Execute correções quando necessário</li>";
echo "</ol>";
echo "</div>";

echo "<p><small><strong>Data/Hora da correção:</strong> " . date('d/m/Y H:i:s') . "</small></p>";
?>
