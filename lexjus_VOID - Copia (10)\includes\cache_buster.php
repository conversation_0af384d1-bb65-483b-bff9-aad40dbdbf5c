<?php
/**
 * Sistema de Cache Busting para LexJus
 * 
 * Este arquivo resolve o problema de cache do navegador adicionando
 * automaticamente parâmetros de versão aos arquivos CSS e JS
 */

class CacheBuster {
    private static $version = null;
    private static $fileVersions = [];
    
    /**
     * Gera uma versão baseada no timestamp do arquivo
     * Se o arquivo não existir, usa um timestamp padrão
     */
    public static function getFileVersion($filePath) {
        // Cache das versões para evitar múltiplas chamadas filemtime
        if (isset(self::$fileVersions[$filePath])) {
            return self::$fileVersions[$filePath];
        }
        
        $fullPath = __DIR__ . '/../' . $filePath;
        
        if (file_exists($fullPath)) {
            $version = filemtime($fullPath);
        } else {
            // Se o arquivo não existir, usar timestamp atual
            $version = time();
            error_log("Cache Buster: Arquivo não encontrado: $filePath");
        }
        
        // Armazenar no cache
        self::$fileVersions[$filePath] = $version;
        
        return $version;
    }
    
    /**
     * Gera URL com cache busting para CSS
     */
    public static function css($filePath) {
        $version = self::getFileVersion($filePath);
        return $filePath . '?v=' . $version;
    }
    
    /**
     * Gera URL com cache busting para JS
     */
    public static function js($filePath) {
        $version = self::getFileVersion($filePath);
        return $filePath . '?v=' . $version;
    }
    
    /**
     * Gera URL com cache busting para qualquer arquivo
     */
    public static function asset($filePath) {
        $version = self::getFileVersion($filePath);
        return $filePath . '?v=' . $version;
    }
    
    /**
     * Gera uma versão global baseada na última modificação
     * de qualquer arquivo importante do sistema
     */
    public static function getGlobalVersion() {
        if (self::$version !== null) {
            return self::$version;
        }
        
        $importantFiles = [
            'style.css',
            'script.js',
            'css/sistema-revisao.css',
            'js/sistema-revisao.js',
            'index.php'
        ];
        
        $latestTime = 0;
        
        foreach ($importantFiles as $file) {
            $fullPath = __DIR__ . '/../' . $file;
            if (file_exists($fullPath)) {
                $fileTime = filemtime($fullPath);
                if ($fileTime > $latestTime) {
                    $latestTime = $fileTime;
                }
            }
        }
        
        self::$version = $latestTime ?: time();
        return self::$version;
    }
    
    /**
     * Limpa o cache interno (útil para desenvolvimento)
     */
    public static function clearCache() {
        self::$version = null;
        self::$fileVersions = [];
    }
}

/**
 * Funções auxiliares para facilitar o uso
 */
function cache_css($filePath) {
    return CacheBuster::css($filePath);
}

function cache_js($filePath) {
    return CacheBuster::js($filePath);
}

function cache_asset($filePath) {
    return CacheBuster::asset($filePath);
}

function cache_version() {
    return CacheBuster::getGlobalVersion();
}
