-- Sistema de Revisão Inteligente para LexJus VOID
-- Baseado em repetição espaçada e algoritmos de memorização

-- Tabela principal para controle de revisões
CREATE TABLE IF NOT EXISTS appestudo.lexjus_revisoes (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    artigo_numero VARCHAR(20) NOT NULL,
    
    -- Controle de dificuldade e performance
    facilidade DECIMAL(3,2) DEFAULT 2.50, -- Fator de facilidade (1.30 a 2.50+)
    intervalo_dias INTEGER DEFAULT 1,      -- Intervalo atual em dias
    repeticoes INTEGER DEFAULT 0,          -- Número de repetições corretas consecutivas
    
    -- Datas importantes
    data_primeira_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_ultima_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_proxima_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Status e qualidade da última revisão
    status VARCHAR(20) DEFAULT 'novo',     -- novo, aprendendo, revisando, dominado
    ultima_qualidade INTEGER DEFAULT 0,    -- 0-5 (0=blackout, 5=perfeito)
    
    -- Estatísticas
    total_revisoes INTEGER DEFAULT 0,
    acertos_consecutivos INTEGER DEFAULT 0,
    tempo_medio_resposta INTEGER DEFAULT 0, -- em segundos
    
    -- Metadados
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_revisoes_usuario 
        FOREIGN KEY (usuario_id) 
        REFERENCES appestudo.usuario(idusuario) 
        ON DELETE CASCADE,
    
    CONSTRAINT unique_usuario_artigo_revisao 
        UNIQUE (usuario_id, artigo_numero),
        
    CONSTRAINT check_facilidade 
        CHECK (facilidade >= 1.30 AND facilidade <= 3.00),
        
    CONSTRAINT check_qualidade 
        CHECK (ultima_qualidade >= 0 AND ultima_qualidade <= 5),
        
    CONSTRAINT check_status 
        CHECK (status IN ('novo', 'aprendendo', 'revisando', 'dominado', 'dificil'))
);

-- Tabela para histórico detalhado de revisões
CREATE TABLE IF NOT EXISTS appestudo.lexjus_historico_revisoes (
    id SERIAL PRIMARY KEY,
    revisao_id INTEGER NOT NULL,
    usuario_id INTEGER NOT NULL,
    artigo_numero VARCHAR(20) NOT NULL,
    
    -- Dados da sessão de revisão
    qualidade_resposta INTEGER NOT NULL,   -- 0-5
    tempo_resposta INTEGER,                -- em segundos
    tipo_revisao VARCHAR(20) NOT NULL,     -- inicial, revisao, reforco
    
    -- Estado antes da revisão
    facilidade_anterior DECIMAL(3,2),
    intervalo_anterior INTEGER,
    
    -- Estado após a revisão
    facilidade_nova DECIMAL(3,2),
    intervalo_novo INTEGER,
    
    -- Contexto da revisão
    dispositivo VARCHAR(50),               -- desktop, mobile, tablet
    hora_do_dia INTEGER,                   -- 0-23
    dia_da_semana INTEGER,                 -- 1-7
    
    -- Metadados
    data_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_historico_revisao 
        FOREIGN KEY (revisao_id) 
        REFERENCES appestudo.lexjus_revisoes(id) 
        ON DELETE CASCADE,
        
    CONSTRAINT fk_historico_usuario 
        FOREIGN KEY (usuario_id) 
        REFERENCES appestudo.usuario(idusuario) 
        ON DELETE CASCADE,
        
    CONSTRAINT check_qualidade_historico 
        CHECK (qualidade_resposta >= 0 AND qualidade_resposta <= 5),
        
    CONSTRAINT check_tipo_revisao 
        CHECK (tipo_revisao IN ('inicial', 'revisao', 'reforco', 'teste'))
);

-- Tabela para configurações personalizadas de revisão
CREATE TABLE IF NOT EXISTS appestudo.lexjus_config_revisao (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    
    -- Configurações do algoritmo
    facilidade_inicial DECIMAL(3,2) DEFAULT 2.50,
    intervalo_inicial INTEGER DEFAULT 1,
    multiplicador_facil DECIMAL(3,2) DEFAULT 1.30,
    multiplicador_dificil DECIMAL(3,2) DEFAULT 0.80,
    
    -- Configurações de estudo
    max_revisoes_dia INTEGER DEFAULT 20,
    max_novos_dia INTEGER DEFAULT 10,
    horario_preferido_inicio TIME DEFAULT '08:00:00',
    horario_preferido_fim TIME DEFAULT '22:00:00',
    
    -- Configurações de notificação
    notificar_revisoes BOOLEAN DEFAULT TRUE,
    notificar_antecedencia INTEGER DEFAULT 60, -- minutos
    
    -- Configurações de dificuldade
    auto_promover_faceis BOOLEAN DEFAULT TRUE,
    auto_rebaixar_dificeis BOOLEAN DEFAULT TRUE,
    limite_acertos_promocao INTEGER DEFAULT 3,
    limite_erros_rebaixamento INTEGER DEFAULT 2,
    
    -- Metadados
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_config_usuario 
        FOREIGN KEY (usuario_id) 
        REFERENCES appestudo.usuario(idusuario) 
        ON DELETE CASCADE,
        
    CONSTRAINT unique_config_usuario 
        UNIQUE (usuario_id)
);

-- Índices para otimização de performance
CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_usuario_id 
    ON appestudo.lexjus_revisoes(usuario_id);

CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_proxima_revisao 
    ON appestudo.lexjus_revisoes(data_proxima_revisao);

CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_status 
    ON appestudo.lexjus_revisoes(status);

CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_usuario_status 
    ON appestudo.lexjus_revisoes(usuario_id, status);

CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_usuario_proxima 
    ON appestudo.lexjus_revisoes(usuario_id, data_proxima_revisao);

CREATE INDEX IF NOT EXISTS idx_lexjus_historico_revisao_id 
    ON appestudo.lexjus_historico_revisoes(revisao_id);

CREATE INDEX IF NOT EXISTS idx_lexjus_historico_usuario_data 
    ON appestudo.lexjus_historico_revisoes(usuario_id, data_revisao);

-- Triggers para atualização automática de timestamps
CREATE OR REPLACE FUNCTION update_revisao_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.data_atualizacao = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_revisao_timestamp
    BEFORE UPDATE ON appestudo.lexjus_revisoes
    FOR EACH ROW
    EXECUTE FUNCTION update_revisao_timestamp();

CREATE TRIGGER trigger_update_config_revisao_timestamp
    BEFORE UPDATE ON appestudo.lexjus_config_revisao
    FOR EACH ROW
    EXECUTE FUNCTION update_revisao_timestamp();

-- Função para calcular próxima data de revisão (Algoritmo SM-2 modificado)
CREATE OR REPLACE FUNCTION calcular_proxima_revisao(
    p_facilidade DECIMAL(3,2),
    p_intervalo INTEGER,
    p_qualidade INTEGER,
    p_repeticoes INTEGER
) RETURNS TABLE (
    nova_facilidade DECIMAL(3,2),
    novo_intervalo INTEGER,
    novas_repeticoes INTEGER,
    nova_data_revisao TIMESTAMP
) AS $$
DECLARE
    v_facilidade DECIMAL(3,2);
    v_intervalo INTEGER;
    v_repeticoes INTEGER;
BEGIN
    -- Calcular nova facilidade baseada na qualidade da resposta
    v_facilidade := p_facilidade + (0.1 - (5 - p_qualidade) * (0.08 + (5 - p_qualidade) * 0.02));
    
    -- Garantir que facilidade está dentro dos limites
    IF v_facilidade < 1.30 THEN
        v_facilidade := 1.30;
    END IF;
    
    -- Calcular novo intervalo e repetições
    IF p_qualidade < 3 THEN
        -- Resposta ruim - reiniciar
        v_repeticoes := 0;
        v_intervalo := 1;
    ELSE
        -- Resposta boa - avançar
        v_repeticoes := p_repeticoes + 1;
        
        IF v_repeticoes = 1 THEN
            v_intervalo := 1;
        ELSIF v_repeticoes = 2 THEN
            v_intervalo := 6;
        ELSE
            v_intervalo := ROUND(p_intervalo * v_facilidade);
        END IF;
    END IF;
    
    -- Retornar resultados
    RETURN QUERY SELECT 
        v_facilidade,
        v_intervalo,
        v_repeticoes,
        (CURRENT_TIMESTAMP + INTERVAL '1 day' * v_intervalo)::TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Comentários para documentação
COMMENT ON TABLE appestudo.lexjus_revisoes IS 'Controle principal do sistema de revisão com repetição espaçada';
COMMENT ON TABLE appestudo.lexjus_historico_revisoes IS 'Histórico detalhado de todas as sessões de revisão';
COMMENT ON TABLE appestudo.lexjus_config_revisao IS 'Configurações personalizadas do sistema de revisão por usuário';

COMMENT ON COLUMN appestudo.lexjus_revisoes.facilidade IS 'Fator de facilidade do algoritmo SM-2 (1.30-3.00)';
COMMENT ON COLUMN appestudo.lexjus_revisoes.intervalo_dias IS 'Intervalo atual entre revisões em dias';
COMMENT ON COLUMN appestudo.lexjus_revisoes.ultima_qualidade IS 'Qualidade da última resposta (0=péssima, 5=perfeita)';
COMMENT ON COLUMN appestudo.lexjus_revisoes.status IS 'Status atual do artigo no sistema de revisão';

-- Inserir configuração padrão para usuários existentes
INSERT INTO appestudo.lexjus_config_revisao (usuario_id)
SELECT DISTINCT idusuario 
FROM appestudo.usuario 
WHERE idusuario NOT IN (
    SELECT usuario_id 
    FROM appestudo.lexjus_config_revisao
);
