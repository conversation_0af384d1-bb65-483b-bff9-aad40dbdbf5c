@echo off
chcp 65001 > nul
echo ========================================
echo    EXTRAÇÃO CORRIGIDA - CÓDIGO CIVIL
echo ========================================
echo.
echo Este script extrai todos os artigos do Código Civil
echo e gera o JSON corrigido.
echo.
echo FONTES DISPONÍVEIS:
echo 1. Web (padrão): https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm
echo 2. Local: L10406.html
echo.
echo Arquivo de saída: codigo_civil_lexjus_corrigido.json
echo.
echo Escolha a fonte:
echo [1] Baixar da web (recomendado - versão mais atualizada)
echo [2] Usar arquivo local
echo [3] Cancelar
echo.
set /p opcao="Digite sua opção (1-3): "

if "%opcao%"=="1" (
    echo.
    echo Executando extração da web...
    echo Isso pode levar alguns minutos...
    echo.
    python executar_extracao_corrigido.py
) else if "%opcao%"=="2" (
    echo.
    echo Verificando arquivo local...
    if not exist "L10406.html" (
        echo ERRO: Arquivo L10406.html não encontrado!
        echo Baixe o arquivo de: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm
        pause
        exit /b 1
    )
    echo Executando extração do arquivo local...
    echo Isso pode levar alguns minutos...
    echo.
    python executar_extracao_corrigido.py --local
) else if "%opcao%"=="3" (
    echo.
    echo Operação cancelada.
    goto fim
) else (
    echo.
    echo Opção inválida. Executando modo padrão (web)...
    echo Isso pode levar alguns minutos...
    echo.
    python executar_extracao_corrigido.py
)

echo.
if exist "codigo_civil_lexjus_corrigido.json" (
    echo ✓ Extração concluída!
    echo Arquivo gerado: codigo_civil_lexjus_corrigido.json
    echo.
    echo Verificando tamanho do arquivo...
    for %%A in ("codigo_civil_lexjus_corrigido.json") do echo Tamanho: %%~zA bytes
) else (
    echo ❌ Erro na extração! Arquivo não foi gerado.
)

echo.
echo ========================================
echo EXTRAÇÃO CONCLUÍDA!
echo ========================================

:fim
pause
