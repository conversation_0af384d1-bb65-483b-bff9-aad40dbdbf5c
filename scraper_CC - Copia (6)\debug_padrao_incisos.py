#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# Carregar HTML
with open('L10406_web_backup.html', 'r', encoding='utf-8') as f:
    conteudo = f.read()

# Testar o padrão atual usado no script
padrao_incisos = r'name="(art\d+(?:[a-z](?:\.\d+)?)?(?:p?[ivx]+)\.?)"'
nomes_incisos = re.findall(padrao_incisos, conteudo, re.IGNORECASE)

print("=== PADRÃO ATUAL ===")
print(f"Padrão: {padrao_incisos}")
print(f"Total encontrado: {len(nomes_incisos)}")

# Filtrar apenas incisos do artigo 4
incisos_art4 = [name for name in nomes_incisos if name.startswith('art4') and re.match(r'art4[ivx]', name, re.IGNORECASE)]
print(f"\nIncisos do artigo 4 encontrados pelo padrão atual:")
for inciso in incisos_art4:
    print(f"- {inciso}")

# Testar padrão mais específico para incisos
print("\n=== TESTANDO PADRÕES ALTERNATIVOS ===")

# Padrão 1: Incluir incisos com números romanos maiúsculos
padrao1 = r'name="(art\d+(?:[a-z](?:\.\d+)?)?[ivx]+\.?)"'
incisos1 = re.findall(padrao1, conteudo, re.IGNORECASE)
incisos_art4_1 = [name for name in incisos1 if name.startswith('art4') and len(name) <= 10]
print(f"Padrão 1: {padrao1}")
print(f"Incisos art4: {incisos_art4_1}")

# Padrão 2: Buscar especificamente art4 + romano
padrao2 = r'name="(art4[ivx]+\.?)"'
incisos2 = re.findall(padrao2, conteudo, re.IGNORECASE)
print(f"Padrão 2: {padrao2}")
print(f"Incisos art4: {incisos2}")

# Verificar manualmente os incisos que deveriam ser capturados
incisos_esperados = ['art4i', 'art4ii.', 'art4iii.', 'art4iv']
print(f"\n=== VERIFICAÇÃO MANUAL ===")
for inciso in incisos_esperados:
    encontrado = re.search(rf'name="{re.escape(inciso)}"', conteudo, re.IGNORECASE)
    print(f"{inciso}: {'ENCONTRADO' if encontrado else 'NÃO ENCONTRADO'}")
