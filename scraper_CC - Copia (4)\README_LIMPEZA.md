# 🧹 Scripts de Limpeza do Código Civil

Este diretório contém scripts para processar e limpar os dados do Código Civil para uso no sistema LexJus.

## 📁 Arquivos Principais

### Scripts de Processamento
- **`executar_extracao_corrigido.py`** - Extrai dados do HTML para JSON
- **`limpar_caput_duplicado.py`** - Remove duplicações no campo caput
- **`verificar_limpeza_caput.py`** - Verifica qualidade da limpeza
- **`processar_codigo_civil_completo.py`** - Executa todo o pipeline

### Scripts Batch (Windows)
- **`EXECUTAR_EXTRACAO_CORRIGIDA.bat`** - Executa extração
- **`EXECUTAR_LIMPEZA_CAPUT.bat`** - Executa limpeza do caput

### Arquivos de Dados
- **`L10406.html`** - Arquivo HTML fonte local (backup)
- **`L10406_web_backup.html`** - Backup da versão baixada da web
- **`codigo_civil_lexjus_corrigido.json`** - Resultado da extração
- **`codigo_civil_formato_lexjus.json`** - Formato específico LexJus
- **`codigo_civil_formato_lexjus_limpo.json`** - Versão final limpa

## 🌐 Fontes de Dados

### Fonte Principal (Web)
- **URL**: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm
- **Vantagens**: Sempre atualizada, inclui últimas alterações legais
- **Requisitos**: Conexão com internet

### Fonte Alternativa (Local)
- **Arquivo**: `L10406.html`
- **Vantagens**: Funciona offline, controle total sobre a versão
- **Requisitos**: Arquivo deve estar no diretório

### Estratégia Automática
1. 🌐 Tenta baixar da web primeiro (versão mais atualizada)
2. 📁 Se falhar, usa arquivo local como fallback
3. 💾 Salva backup da versão web para uso futuro

## 🚀 Como Usar

### Opção 1: Processamento Completo (Recomendado)
```bash
python processar_codigo_civil_completo.py
```
Este script executa todo o pipeline automaticamente.

### Opção 2: Etapas Individuais

#### 1. Extração dos Dados
```bash
# Baixar da web (padrão - recomendado)
python executar_extracao_corrigido.py

# Usar arquivo local
python executar_extracao_corrigido.py --local

# Ver ajuda
python executar_extracao_corrigido.py --help
```
- **Input**: Web (`https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm`) ou `L10406.html`
- **Output**: `codigo_civil_lexjus_corrigido.json`

#### 2. Limpeza do Caput
```bash
python limpar_caput_duplicado.py
```
- **Input**: `codigo_civil_formato_lexjus.json`
- **Output**: `codigo_civil_formato_lexjus_limpo.json`

#### 3. Verificação da Qualidade
```bash
python verificar_limpeza_caput.py
```
- **Input**: `codigo_civil_formato_lexjus_limpo.json`

### Opção 3: Scripts Batch (Windows)
```cmd
EXECUTAR_EXTRACAO_CORRIGIDA.bat
EXECUTAR_LIMPEZA_CAPUT.bat
```

## 🔧 Problema Resolvido

### Antes da Limpeza
```json
{
  "artigo": "Art. 4º",
  "caput": "Art. 4º São incapazes, relativamente a certos atos...",
  "incisos": [...]
}
```

### Depois da Limpeza
```json
{
  "artigo": "Art. 4º",
  "caput": "São incapazes, relativamente a certos atos...",
  "incisos": [...]
}
```

## 📊 Estrutura do JSON Final

```json
{
  "artigo": "Art. Xº",
  "caput": "texto do artigo sem duplicação",
  "incisos": ["I - ...", "II - ..."],
  "paragrafos_numerados": [
    {
      "numero": "§ 1º",
      "texto": "texto do parágrafo",
      "alineas": []
    }
  ],
  "paragrafo_unico": "texto ou null"
}
```

## 🔍 Verificações Realizadas

### Script de Limpeza
- ✅ Remove "Art. X." do início do caput
- ✅ Trata números com pontos (ex: Art. 1.071.)
- ✅ Lida com ordinais (º) e graus (°)
- ✅ Preserva conteúdo original se não houver duplicação

### Script de Verificação
- 📊 Analisa estrutura geral do arquivo
- 🔍 Detecta duplicações restantes
- ⚠️ Identifica outros problemas (caput vazio, etc.)
- 📋 Mostra exemplos de artigos limpos

## 🎯 Resultados Esperados

- **Total de artigos**: 2.046
- **Artigos com incisos**: ~179 (8.7%)
- **Artigos com parágrafos numerados**: ~177 (8.6%)
- **Artigos com parágrafo único**: ~389 (19.0%)
- **Duplicações no caput**: 0 (após limpeza)

## 🔄 Quando Executar

Execute estes scripts sempre que:
- 📥 Baixar nova versão do HTML do Código Civil
- 🔄 Atualizar o scraper de extração
- 🧹 Precisar limpar duplicações em novos dados
- ✅ Verificar qualidade dos dados existentes

## ⚠️ Requisitos

- Python 3.6+
- Módulos: `json`, `re`, `os`, `subprocess` (padrão do Python)
- Arquivo `L10406.html` no mesmo diretório

## 📝 Logs e Debug

Todos os scripts fornecem output detalhado:
- 📊 Estatísticas de processamento
- 📋 Exemplos de modificações
- ✅ Confirmação de sucesso
- ❌ Detalhes de erros (se houver)

## 🆘 Solução de Problemas

### Erro: "Arquivo não encontrado"
- Verifique se `L10406.html` está no diretório
- Certifique-se de estar no diretório correto

### Erro: "Nenhum artigo extraído"
- Verifique se o arquivo HTML não está corrompido
- Execute `verificar_limpeza_caput.py` para diagnóstico

### Duplicações não removidas
- Execute `limpar_caput_duplicado.py` novamente
- Verifique se o arquivo de entrada está correto

---

**📞 Suporte**: Execute `python verificar_limpeza_caput.py` para diagnóstico completo.
