#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper para o arquivo HTML local do Código de Processo Penal
Baseado na estrutura real do HTML salvo
"""

import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def limpar_texto(texto):
    """
    Limpa o texto mantendo as referências legais
    """
    if not texto:
        return ""
    
    # Remover quebras de linha e espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)
    
    # Manter todas as referências legais - não remover nada entre parênteses
    
    return texto.strip()

def extrair_numero_artigo(elemento_artigo):
    """
    Extrai o número do artigo do elemento
    """
    if not elemento_artigo:
        return None
    
    texto_artigo = elemento_artigo.get_text(strip=True)
    
    # Padrões para extrair número do artigo
    patterns = [
        r'Art\.\s*(\d+)(?:º|°)?(?:-([A-Z]))?',
        r'Artigo\s+(\d+)(?:º|°)?(?:-([A-Z]))?'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, texto_artigo, re.IGNORECASE)
        if match:
            numero = int(match.group(1))
            sufixo = match.group(2) if match.group(2) else ""
            return f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")
    
    return None

def processar_artigo_html_local(elemento_artigo, soup):
    """
    Processa um artigo específico baseado na estrutura real do HTML
    """
    artigo_id = extrair_numero_artigo(elemento_artigo)
    if not artigo_id:
        return None
    
    # Encontrar o parágrafo que contém o artigo
    paragrafo_artigo = elemento_artigo.find_parent('p')
    if not paragrafo_artigo:
        return None
    
    # Extrair caput - texto após o número do artigo no mesmo parágrafo
    texto_paragrafo = paragrafo_artigo.get_text(separator=' ', strip=True)
    caput_match = re.search(r'Art\.\s*\d+(?:º|°)?(?:-[A-Z])?\s*\.?\s*(.+)', texto_paragrafo, re.IGNORECASE)
    caput = limpar_texto(caput_match.group(1)) if caput_match else ""
    
    # Inicializar estrutura do artigo
    artigo = {
        "artigo": artigo_id,
        "caput": caput,
        "incisos": [],
        "paragrafos_numerados": [],
        "paragrafo_unico": None
    }
    
    # Buscar próximos parágrafos até encontrar outro artigo
    elemento_atual = paragrafo_artigo.find_next_sibling('p')
    
    while elemento_atual:
        # Parar se encontrar outro artigo
        if elemento_atual.find('a', class_='numero_artigo') or elemento_atual.find('span', id=re.compile(r'art-\d+')):
            break
            
        # Parar se encontrar título/seção
        texto_elemento = elemento_atual.get_text(separator=' ', strip=True)
        if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO)', texto_elemento, re.IGNORECASE):
            break
            
        # Pular elementos muito pequenos
        if len(texto_elemento.strip()) < 3:
            elemento_atual = elemento_atual.find_next_sibling('p')
            continue
        
        # Processar diferentes tipos de elementos
        processar_elemento_html(elemento_atual, artigo)
        
        elemento_atual = elemento_atual.find_next_sibling('p')
    
    return artigo

def processar_elemento_html(elemento, artigo):
    """
    Processa um elemento específico baseado na estrutura HTML real
    """
    texto = elemento.get_text(separator=' ', strip=True)
    
    # Verificar se é parágrafo único
    if re.search(r'Parágrafo\s+único', texto, re.IGNORECASE):
        paragrafo_match = re.search(r'Parágrafo\s+único\.?\s*(.+)', texto, re.IGNORECASE | re.DOTALL)
        if paragrafo_match and not artigo["paragrafo_unico"]:
            texto_paragrafo_unico = paragrafo_match.group(1).strip()
            artigo["paragrafo_unico"] = limpar_texto(texto_paragrafo_unico)
    
    # Verificar se é parágrafo numerado
    elif re.search(r'§\s*\d+(?:º|°)?', texto):
        paragrafo_match = re.search(r'(§\s*\d+(?:º|°)?)\s*(.+)', texto, re.DOTALL)
        if paragrafo_match:
            numero_paragrafo = paragrafo_match.group(1).strip()
            texto_paragrafo = limpar_texto(paragrafo_match.group(2))
            
            # Verificar se já existe este parágrafo
            paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo["paragrafos_numerados"])
            if not paragrafo_existe and texto_paragrafo and texto_paragrafo not in [".", "(VETADO)."]:
                artigo["paragrafos_numerados"].append({
                    "numero": numero_paragrafo,
                    "texto": texto_paragrafo,
                    "incisos": [],
                    "alineas": []
                })
    
    # Verificar se é inciso (romano)
    elif re.search(r'^[IVX]+\s*[-–]', texto):
        inciso_texto = limpar_texto(texto)
        if inciso_texto and inciso_texto not in artigo["incisos"]:
            artigo["incisos"].append(inciso_texto)
    
    # Verificar se é alínea
    elif re.search(r'^[a-z]\)', texto):
        alinea_texto = limpar_texto(texto)
        if alinea_texto:
            # Adicionar à última estrutura disponível (parágrafo)
            if artigo["paragrafos_numerados"]:
                ultimo_paragrafo = artigo["paragrafos_numerados"][-1]
                if alinea_texto not in ultimo_paragrafo["alineas"]:
                    ultimo_paragrafo["alineas"].append(alinea_texto)

def scraper_html_local():
    """
    Função principal do scraper para arquivo HTML local
    """
    print("=" * 60)
    print("🔍 SCRAPER CPP HTML LOCAL")
    print("=" * 60)
    
    arquivo_html = "CÓDIGO DE PROCESSO PENAL - DEL 3689_1941.html"
    
    try:
        print(f"📄 Lendo arquivo: {arquivo_html}")
        
        with open(arquivo_html, 'r', encoding='utf-8') as f:
            conteudo_html = f.read()
        
        print(f"✅ Arquivo lido com sucesso. Tamanho: {len(conteudo_html):,} caracteres")
        
        # Parse do HTML
        soup = BeautifulSoup(conteudo_html, 'html.parser')
        
        # Encontrar todos os artigos - buscar por class="numero_artigo" e id que começam com "art-"
        links_artigos = soup.find_all('a', class_='numero_artigo')
        spans_artigos = soup.find_all('span', id=re.compile(r'art-\d+'))
        
        print(f"🔍 Encontrados {len(links_artigos)} links de artigos")
        print(f"🔍 Encontrados {len(spans_artigos)} spans de artigos")
        
        artigos_extraidos = []
        artigos_processados = set()  # Para evitar duplicações
        
        # Processar links de artigos
        for i, link_artigo in enumerate(links_artigos):
            try:
                artigo = processar_artigo_html_local(link_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])
                    
                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")
                        
            except Exception as e:
                print(f"⚠️ Erro ao processar link artigo {i+1}: {e}")
                continue
        
        # Processar spans de artigos (para casos como Art. 3°-C, 3°-D, 3°-E)
        for i, span_artigo in enumerate(spans_artigos):
            try:
                artigo = processar_artigo_html_local(span_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])
                    
                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")
                        
            except Exception as e:
                print(f"⚠️ Erro ao processar span artigo {i+1}: {e}")
                continue
        
        # Ordenar por número do artigo
        def extrair_numero_para_ordenacao(artigo_id):
            match = re.search(r'Art\.\s*(\d+)', artigo_id)
            return int(match.group(1)) if match else 0
        
        artigos_extraidos.sort(key=lambda x: extrair_numero_para_ordenacao(x["artigo"]))
        
        print(f"✅ Extração concluída. Total: {len(artigos_extraidos)} artigos únicos")
        
        # Salvar arquivo
        nome_arquivo_json = 'cpp_html_local.json'
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_extraidos, f, ensure_ascii=False, indent=2)
        print(f"💾 Arquivo JSON salvo: {nome_arquivo_json}")
        
        # Estatísticas detalhadas
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   • Total de artigos: {len(artigos_extraidos)}")
        if artigos_extraidos:
            print(f"   • Primeiro artigo: {artigos_extraidos[0]['artigo']}")
            print(f"   • Último artigo: {artigos_extraidos[-1]['artigo']}")
            
            # Contar estruturas
            total_incisos = sum(len(art['incisos']) for art in artigos_extraidos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos_extraidos)
            total_paragrafos_unicos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'])
            
            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")
            
            # Verificar primeiros artigos para debug
            print(f"\n🔍 VERIFICAÇÃO DOS PRIMEIROS ARTIGOS:")
            for i, art in enumerate(artigos_extraidos[:10]):
                print(f"   {art['artigo']}: {len(art['incisos'])} incisos, {len(art['paragrafos_numerados'])} parágrafos")
                if i < 3:  # Mostrar caput dos 3 primeiros
                    print(f"      Caput: {art['caput'][:100]}...")
        
        return artigos_extraidos
        
    except FileNotFoundError:
        print(f"❌ Arquivo não encontrado: {arquivo_html}")
        return []
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return []

if __name__ == '__main__':
    artigos = scraper_html_local()
    if artigos:
        print(f"\n🎉 Scraping HTML LOCAL concluído com sucesso!")
        print(f"📁 Arquivo gerado: cpp_html_local.json")
        print(f"🔧 Baseado na estrutura real do HTML!")
    else:
        print(f"\n❌ Falha no scraping HTML LOCAL!")
