#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT DE LIMPEZA DO CAPUT - CÓDIGO CIVIL
==========================================

Este script remove duplicações do número do artigo no campo 'caput' do arquivo JSON.

PROBLEMA:
- Campo 'artigo': "Art. 4º"
- Campo 'caput': "Art. 4º São incapazes, relativamente a certos atos..."

SOLUÇÃO:
- Campo 'artigo': "Art. 4º" (mantém)
- Campo 'caput': "São incapazes, relativamente a certos atos..." (remove duplicação)

USO:
    python limpar_caput_duplicado.py

ARQUIVOS:
    Input:  codigo_civil_formato_lexjus.json
    Output: codigo_civil_formato_lexjus_limpo.json
"""

import json
import re
import os

def extrair_numero_artigo(artigo_str):
    """Extrai o número do artigo (ex: 'Art. 1071º' -> '1071')"""
    return artigo_str.replace('Art. ', '').replace('º', '').replace('°', '')

def criar_padroes_remocao(numero):
    """Cria padrões regex para remover duplicações do caput"""
    padroes = []
    
    # Padrões básicos
    padroes.extend([
        rf'^Art\.\s*{re.escape(numero)}[ºo°]?\.\s*',  # Art. 4º.
        rf'^Art\s+{re.escape(numero)}[ºo°]?\.\s*',    # Art 4º.
        rf'^Art\.\s*{re.escape(numero)}[ºo°]?\s+',    # Art. 4º
        rf'^Art\s+{re.escape(numero)}[ºo°]?\s+',      # Art 4º
        rf'^{re.escape(numero)}[ºo°]?\.\s*',          # 4º.
        rf'^{re.escape(numero)}[ºo°]?\s+',            # 4º
    ])
    
    # Para números com 4+ dígitos, adicionar versão com ponto (ex: 1071 -> 1.071)
    if len(numero) >= 4:
        numero_com_ponto = numero[:-3] + '.' + numero[-3:]
        padroes.extend([
            rf'^Art\.\s*{re.escape(numero_com_ponto)}\.\s*',  # Art. 1.071.
            rf'^Art\s+{re.escape(numero_com_ponto)}\.\s*',    # Art 1.071.
            rf'^{re.escape(numero_com_ponto)}\.\s*',          # 1.071.
        ])
    
    return padroes

def limpar_caput(caput, numero_artigo):
    """Remove duplicação do número do artigo no caput"""
    caput_original = caput
    padroes = criar_padroes_remocao(numero_artigo)
    
    # Tentar cada padrão
    for padrao in padroes:
        match = re.match(padrao, caput, re.IGNORECASE)
        if match:
            caput = caput[match.end():].strip()
            break
    
    # Limpeza adicional: remover pontos órfãos no início
    caput = re.sub(r'^\.+\s*', '', caput)
    
    return caput if caput.strip() else caput_original

def verificar_duplicacoes_restantes(data):
    """Verifica se ainda há duplicações após a limpeza"""
    problemas = []
    
    for artigo in data:
        caput = artigo['caput']
        numero = extrair_numero_artigo(artigo['artigo'])
        
        # Verificar padrões problemáticos
        padroes_verificar = [
            rf'^Art\.?\s*{re.escape(numero)}[ºo°\.\s]',
            rf'^{re.escape(numero)}[ºo°]\.',
        ]
        
        # Para números com 4+ dígitos, verificar também versão com ponto
        if len(numero) >= 4:
            numero_com_ponto = numero[:-3] + '.' + numero[-3:]
            padroes_verificar.extend([
                rf'^Art\.?\s*{re.escape(numero_com_ponto)}\.',
                rf'^{re.escape(numero_com_ponto)}\.',
            ])
        
        for padrao in padroes_verificar:
            if re.match(padrao, caput, re.IGNORECASE):
                problemas.append({
                    'artigo': artigo['artigo'],
                    'caput': caput[:80] + '...'
                })
                break
    
    return problemas

def main():
    print("=== LIMPEZA DE CAPUT DUPLICADO - CÓDIGO CIVIL ===")
    
    # Verificar se arquivo existe
    arquivo_entrada = 'codigo_civil_formato_lexjus.json'
    if not os.path.exists(arquivo_entrada):
        print(f"ERRO: Arquivo {arquivo_entrada} não encontrado!")
        return
    
    try:
        # Carregar arquivo
        print(f"Carregando {arquivo_entrada}...")
        with open(arquivo_entrada, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Total de artigos carregados: {len(data)}")
        
        # Processar cada artigo
        artigos_modificados = 0
        exemplos_mostrados = 0
        
        for i, artigo in enumerate(data):
            if i % 500 == 0:
                print(f"Processando artigo {i+1}/{len(data)}...")
            
            caput_original = artigo['caput']
            numero_artigo = extrair_numero_artigo(artigo['artigo'])
            
            # Limpar caput
            caput_limpo = limpar_caput(caput_original, numero_artigo)
            
            # Se houve mudança, atualizar
            if caput_limpo != caput_original:
                artigo['caput'] = caput_limpo
                artigos_modificados += 1
                
                # Mostrar alguns exemplos
                if exemplos_mostrados < 5:
                    print(f"\nExemplo {exemplos_mostrados + 1}:")
                    print(f"  {artigo['artigo']}")
                    print(f"  Antes: {caput_original[:80]}...")
                    print(f"  Depois: {caput_limpo[:80]}...")
                    exemplos_mostrados += 1
        
        print(f"\n=== PROCESSAMENTO CONCLUÍDO ===")
        print(f"Artigos modificados: {artigos_modificados}")
        print(f"Artigos não modificados: {len(data) - artigos_modificados}")
        
        # Verificação final
        print(f"\nVerificação final...")
        problemas = verificar_duplicacoes_restantes(data)
        
        if problemas:
            print(f"  ⚠️  {len(problemas)} artigos ainda com possíveis problemas:")
            for i, problema in enumerate(problemas[:3]):
                print(f"    {i+1}. {problema['artigo']}: {problema['caput']}")
            if len(problemas) > 3:
                print(f"    ... e mais {len(problemas) - 3} artigos")
        else:
            print("  ✅ Nenhuma duplicação restante!")
        
        # Salvar arquivo limpo
        arquivo_saida = 'codigo_civil_formato_lexjus_limpo.json'
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"\nArquivo limpo salvo: {arquivo_saida}")
        
        # Mostrar exemplos finais
        print(f"\nExemplos do resultado final:")
        exemplos = [0, 3, 7, 24, 40, 1070]  # Artigos 1, 4, 8, 25, 41, 1071
        for i in exemplos:
            if i < len(data):
                artigo = data[i]
                print(f"\n{artigo['artigo']}:")
                print(f"  Caput: {artigo['caput'][:100]}...")
        
        print(f"\n✅ Limpeza concluída com sucesso!")
        
    except Exception as e:
        print(f"ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
