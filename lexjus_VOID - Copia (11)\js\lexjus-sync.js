/**
 * Classe para gerenciar a sincronização dos dados do LexJus com o servidor
 */
class LexJusSync {
    constructor() {
        this.init();
    }

    /**
     * Inicializa o sistema de sincronização
     */
    async init() {
        // Carrega os dados do servidor
        await this.carregarDados();

        // Configura os listeners para sincronização
        this.configurarListeners();
    }

    /**
     * Carrega todos os dados do servidor
     */
    async carregarDados() {
        try {
            // Carrega favoritos
            const favoritos = await this.carregarFavoritos();
            this.salvarFavoritosLocal(favoritos);

            // Carrega progresso
            const progresso = await this.carregarProgresso();
            this.salvarProgressoLocal(progresso);

            // Carrega listas
            const listas = await this.carregarListas();
            this.salvarListasLocal(listas);

            // Atualiza a interface
            this.atualizarInterface();

        } catch (error) {
            console.error('Erro ao carregar dados do servidor:', error);
        }
    }

    /**
     * Carrega favoritos do servidor
     */
    async carregarFavoritos() {
        const response = await fetch('./api/favoritos.php');
        if (!response.ok) {
            throw new Error('Erro ao carregar favoritos');
        }
        const data = await response.json();
        return data.favoritos || [];
    }

    /**
     * Carrega progresso de leitura do servidor
     */
    async carregarProgresso() {
        const response = await fetch('./api/progresso.php');
        if (!response.ok) {
            throw new Error('Erro ao carregar progresso');
        }
        const data = await response.json();
        return data.progresso || {};
    }

    /**
     * Carrega listas do servidor
     */
    async carregarListas() {
        const response = await fetch('./api/listas.php');
        if (!response.ok) {
            throw new Error('Erro ao carregar listas');
        }
        const data = await response.json();
        return data.listas || [];
    }

    /**
     * Salva favoritos no localStorage
     */
    salvarFavoritosLocal(favoritos) {
        const favoritosSimples = favoritos.map(f => f.artigo_numero);
        localStorage.setItem('favoritos', JSON.stringify(favoritosSimples));
    }

    /**
     * Salva progresso no localStorage
     */
    salvarProgressoLocal(progresso) {
        const artigosLidos = Object.keys(progresso).filter(artigo => progresso[artigo].lido);
        localStorage.setItem('artigosLidos', JSON.stringify(artigosLidos));
    }

    /**
     * Salva listas no localStorage
     */
    salvarListasLocal(listas) {
        localStorage.setItem('listas', JSON.stringify(listas));
    }

    /**
     * Configura listeners para eventos de UI
     */
    configurarListeners() {
        // Listener para favoritar/desfavoritar artigos
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.favoritar-artigo, .favoritar-artigo *')) {
                const artigoNumero = e.target.closest('.artigo').dataset.artigo;
                await this.toggleFavorito(artigoNumero);
            }
        });

        // Listener para marcar artigo como lido
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.marcar-lido, .marcar-lido *')) {
                const artigoNumero = e.target.closest('.artigo').dataset.artigo;
                await this.marcarComoLido(artigoNumero);
            }
        });

        // Listener para adicionar artigo a uma lista
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.adicionar-lista, .adicionar-lista *')) {
                const artigoNumero = e.target.closest('.artigo').dataset.artigo;
                const listaId = e.target.dataset.listaId;
                await this.adicionarArtigoLista(artigoNumero, listaId);
            }
        });

        // Listener para criar nova lista
        document.getElementById('btnCriarLista')?.addEventListener('click', async () => {
            const nomeLista = prompt('Digite o nome da nova lista:');
            if (nomeLista) {
                await this.criarNovaLista(nomeLista);
            }
        });
    }

    /**
     * Atualiza a interface com os dados carregados
     */
    atualizarInterface() {
        // Atualiza contador de favoritos
        this.atualizarContadorFavoritos();

        // Atualiza contador de listas
        this.atualizarContadorListas();

        // Atualiza progresso de leitura
        this.atualizarProgressoLeitura();

        // Atualiza estado visual dos cards
        this.atualizarEstadoVisualCards();
    }

    /**
     * Atualiza o contador de favoritos na interface
     */
    atualizarContadorFavoritos() {
        const favoritos = JSON.parse(localStorage.getItem('favoritos') || '[]');
        const contador = document.getElementById('favoritosCount');
        if (contador) {
            contador.textContent = favoritos.length;
        }
    }

    /**
     * Atualiza o contador de listas na interface
     */
    atualizarContadorListas() {
        const listas = JSON.parse(localStorage.getItem('listas') || '{}');
        const contador = document.getElementById('listasCount');
        if (contador) {
            contador.textContent = Object.keys(listas).length;
        }
    }

    /**
     * Atualiza o progresso de leitura na interface
     */
    atualizarProgressoLeitura() {
        const artigosLidos = JSON.parse(localStorage.getItem('artigosLidos') || '[]');
        const totalArtigos = document.querySelectorAll('.card').length;

        // Atualiza texto de progresso
        const progressoTexto = document.getElementById('progressText');
        if (progressoTexto) {
            const percentual = totalArtigos > 0 ? Math.round((artigosLidos.length / totalArtigos) * 100) : 0;
            progressoTexto.textContent = `${artigosLidos.length} de ${totalArtigos} artigos lidos (${percentual}%)`;
        }

        // Atualiza barra de progresso
        const progressoFill = document.getElementById('progressFill');
        if (progressoFill && totalArtigos > 0) {
            const percentual = (artigosLidos.length / totalArtigos) * 100;
            progressoFill.style.width = `${percentual}%`;
        }

        // Atualiza estatísticas
        const totalArtigosElement = document.getElementById('totalArtigos');
        if (totalArtigosElement) {
            totalArtigosElement.textContent = totalArtigos;
        }

        const artigosLidosElement = document.getElementById('artigosLidos');
        if (artigosLidosElement) {
            artigosLidosElement.textContent = artigosLidos.length;
        }
    }

    /**
     * Atualiza o estado visual dos cards (lido, favorito, etc)
     */
    atualizarEstadoVisualCards() {
        const favoritos = JSON.parse(localStorage.getItem('favoritos') || '[]');
        const artigosLidos = JSON.parse(localStorage.getItem('artigosLidos') || '[]');

        document.querySelectorAll('.card').forEach(card => {
            const artigoNumero = card.dataset.artigo;

            // Marca como favorito se estiver na lista
            if (favoritos.includes(artigoNumero)) {
                card.classList.add('favorito');
            } else {
                card.classList.remove('favorito');
            }

            // Marca como lido se estiver na lista
            if (artigosLidos.includes(artigoNumero)) {
                card.classList.add('lido');
            } else {
                card.classList.remove('lido');
            }
        });
    }

    /**
     * Adiciona ou remove um artigo dos favoritos
     */
    async toggleFavorito(artigoNumero) {
        try {
            const favoritos = JSON.parse(localStorage.getItem('lexjus_favoritos') || '[]');
            const isFavorito = favoritos.includes(artigoNumero);

            if (isFavorito) {
                // Remove do favorito
                await fetch('./api/favoritos.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ artigo_numero: artigoNumero })
                });

                // Atualiza localStorage
                const novosFavoritos = favoritos.filter(a => a !== artigoNumero);
                localStorage.setItem('lexjus_favoritos', JSON.stringify(novosFavoritos));
            } else {
                // Adiciona aos favoritos
                await fetch('./api/favoritos.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ artigo_numero: artigoNumero })
                });

                // Atualiza localStorage
                favoritos.push(artigoNumero);
                localStorage.setItem('lexjus_favoritos', JSON.stringify(favoritos));
            }

            // Atualiza interface
            this.atualizarContadorFavoritos();
            this.atualizarEstadoVisualCards();

        } catch (error) {
            console.error('Erro ao atualizar favorito:', error);
        }
    }

    /**
     * Marca um artigo como lido ou não lido
     */
    async marcarComoLido(artigoNumero, lido = true) {
        try {
            const artigosLidos = JSON.parse(localStorage.getItem('lexjus_artigos_lidos') || '[]');
            const jaLido = artigosLidos.includes(artigoNumero);

            // Se o estado atual é diferente do desejado
            if (jaLido !== lido) {
                // Atualiza no servidor
                await fetch('./api/progresso.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        artigo_numero: artigoNumero,
                        lido: lido
                    })
                });

                // Atualiza localStorage
                if (lido) {
                    artigosLidos.push(artigoNumero);
                } else {
                    const index = artigosLidos.indexOf(artigoNumero);
                    if (index !== -1) {
                        artigosLidos.splice(index, 1);
                    }
                }
                localStorage.setItem('lexjus_artigos_lidos', JSON.stringify(artigosLidos));

                // Atualiza interface
                this.atualizarProgressoLeitura();
                this.atualizarEstadoVisualCards();
            }

        } catch (error) {
            console.error('Erro ao atualizar progresso:', error);
        }
    }

    /**
     * Cria uma nova lista
     */
    async criarNovaLista(nome, descricao = '') {
        try {
            const response = await fetch('./api/listas.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    nome: nome,
                    descricao: descricao
                })
            });

            if (!response.ok) {
                throw new Error('Erro ao criar lista');
            }

            const data = await response.json();

            // Recarrega as listas do servidor
            const listas = await this.carregarListas();
            this.salvarListasLocal(listas);

            // Atualiza interface
            this.atualizarContadorListas();

            return data.lista_id;

        } catch (error) {
            console.error('Erro ao criar lista:', error);
            return null;
        }
    }

    /**
     * Adiciona um artigo a uma lista
     */
    async adicionarArtigoLista(artigoNumero, listaId) {
        try {
            const response = await fetch('./api/lista_artigos.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    lista_id: listaId,
                    artigo_numero: artigoNumero
                })
            });

            if (!response.ok) {
                throw new Error('Erro ao adicionar artigo à lista');
            }

            // Recarrega as listas do servidor
            const listas = await this.carregarListas();
            this.salvarListasLocal(listas);

            // Exibe mensagem de sucesso
            alert('Artigo adicionado à lista com sucesso!');

        } catch (error) {
            console.error('Erro ao adicionar artigo à lista:', error);
            alert('Erro ao adicionar artigo à lista. Tente novamente.');
        }
    }
}

// Inicializa a sincronização quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.lexjusSync = new LexJusSync();
});