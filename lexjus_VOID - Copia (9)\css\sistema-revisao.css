/* Sistema de Revisão - Estilos CSS */

/* Modal de Revisão */
.modal-revisao {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-revisao .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border);
}

.revisao-stats-mini {
    display: flex;
    gap: 1rem;
}

.stat-mini {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.stat-mini i {
    color: var(--primary);
}

/* Dashboard de Revisão */
.revisao-dashboard {
    text-align: center;
}

.revisao-dashboard h3 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.revisao-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.revisao-card {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.revisao-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.revisao-card.pendentes {
    border-color: #f39c12;
}

.revisao-card.aprendendo {
    border-color: #3498db;
}

.revisao-card.dominados {
    border-color: #27ae60;
}

.card-icon {
    font-size: 2rem;
    color: var(--primary);
}

.revisao-card.pendentes .card-icon {
    color: #f39c12;
}

.revisao-card.aprendendo .card-icon {
    color: #3498db;
}

.revisao-card.dominados .card-icon {
    color: #27ae60;
}

.card-content {
    text-align: left;
}

.card-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.card-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Ações de Revisão */
.revisao-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-iniciar-revisao,
.btn-estatisticas {
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-light);
}

.btn-iniciar-revisao {
    background: var(--primary-gradient);
    color: white;
}

.btn-iniciar-revisao:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-iniciar-revisao:disabled {
    background: var(--border);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.btn-estatisticas {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 2px solid var(--border);
}

.btn-estatisticas:hover {
    background: var(--hover);
    border-color: var(--primary);
}

/* Sessão de Revisão */
.revisao-sessao {
    max-width: 800px;
    margin: 0 auto;
}

.sessao-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.artigo-info h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary);
    font-size: 1.8rem;
}

.artigo-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.meta-item i {
    color: var(--primary);
}

.sessao-progresso {
    background: var(--card-bg);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    border: 2px solid var(--border);
    font-weight: 600;
    color: var(--primary);
}

/* Conteúdo do Artigo na Revisão */
.artigo-conteudo {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.6;
}

.artigo-caput,
.artigo-incisos,
.artigo-paragrafo-unico,
.artigo-paragrafos {
    margin-bottom: 1.5rem;
}

.artigo-caput strong,
.artigo-incisos strong,
.artigo-paragrafo-unico strong,
.artigo-paragrafos strong {
    color: var(--primary);
    display: block;
    margin-bottom: 0.5rem;
}

.artigo-incisos ul,
.alineas-lista {
    margin-left: 1rem;
    margin-top: 0.5rem;
}

.artigo-incisos li,
.alineas-lista li {
    margin-bottom: 0.5rem;
}

.paragrafo-numerado {
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--hover);
    border-radius: 8px;
    border-left: 4px solid var(--primary);
}

.paragrafo-numero {
    font-weight: 600;
    color: var(--primary);
    display: block;
    margin-bottom: 0.5rem;
}

/* Controles de Qualidade */
.revisao-controles {
    text-align: center;
}

.revisao-controles h4 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.qualidade-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.btn-qualidade {
    padding: 1rem;
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-primary);
    box-shadow: var(--shadow-light);
}

.btn-qualidade:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary);
}

.btn-qualidade.selected {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-qualidade i {
    font-size: 1.5rem;
}

/* Cores específicas para cada qualidade */
.btn-qualidade[data-qualidade="0"]:hover { border-color: #e74c3c; }
.btn-qualidade[data-qualidade="1"]:hover { border-color: #f39c12; }
.btn-qualidade[data-qualidade="2"]:hover { border-color: #f1c40f; }
.btn-qualidade[data-qualidade="3"]:hover { border-color: #3498db; }
.btn-qualidade[data-qualidade="4"]:hover { border-color: #2ecc71; }
.btn-qualidade[data-qualidade="5"]:hover { border-color: #9b59b6; }

.btn-qualidade[data-qualidade="0"].selected { background: #e74c3c; border-color: #e74c3c; }
.btn-qualidade[data-qualidade="1"].selected { background: #f39c12; border-color: #f39c12; }
.btn-qualidade[data-qualidade="2"].selected { background: #f1c40f; border-color: #f1c40f; }
.btn-qualidade[data-qualidade="3"].selected { background: #3498db; border-color: #3498db; }
.btn-qualidade[data-qualidade="4"].selected { background: #2ecc71; border-color: #2ecc71; }
.btn-qualidade[data-qualidade="5"].selected { background: #9b59b6; border-color: #9b59b6; }

/* Feedback de Resposta */
.feedback-resposta {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--card-bg);
    border: 2px solid var(--primary);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-heavy);
    z-index: 10001;
    animation: feedbackAppear 0.3s ease-out;
    text-align: center;
    min-width: 300px;
}

.feedback-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.feedback-content i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.feedback-content span {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.feedback-content small {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.feedback-resposta.qualidade-0,
.feedback-resposta.qualidade-1,
.feedback-resposta.qualidade-2 {
    border-color: #e74c3c;
}

.feedback-resposta.qualidade-0 i,
.feedback-resposta.qualidade-1 i,
.feedback-resposta.qualidade-2 i {
    color: #e74c3c;
}

.feedback-resposta.qualidade-3,
.feedback-resposta.qualidade-4,
.feedback-resposta.qualidade-5 {
    border-color: #27ae60;
}

.feedback-resposta.qualidade-3 i,
.feedback-resposta.qualidade-4 i,
.feedback-resposta.qualidade-5 i {
    color: #27ae60;
}

@keyframes feedbackAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Resultado da Sessão */
.revisao-resultado {
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.resultado-header {
    margin-bottom: 2rem;
}

.resultado-icon {
    font-size: 4rem;
    color: #27ae60;
    margin-bottom: 1rem;
}

.resultado-header h3 {
    font-size: 2rem;
    color: var(--text-primary);
    margin: 0;
}

.resultado-stats {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
}

.stat-resultado {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border);
}

.stat-resultado:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    color: var(--primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.resultado-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-nova-revisao,
.btn-fechar-revisao {
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-light);
}

.btn-nova-revisao {
    background: var(--primary-gradient);
    color: white;
}

.btn-nova-revisao:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-fechar-revisao {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 2px solid var(--border);
}

.btn-fechar-revisao:hover {
    background: var(--hover);
    border-color: var(--primary);
}

/* Modal de Estatísticas */
.modal-estatisticas {
    max-width: 800px;
    width: 95%;
}

.estatisticas-content {
    padding: 1rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary);
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--primary);
}

.stat-info {
    text-align: left;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

/* Seção de Progresso */
.progress-section h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    text-align: center;
}

.progress-bars {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-label {
    min-width: 100px;
    font-weight: 500;
    color: var(--text-primary);
}

.progress-bar {
    flex: 1;
    height: 20px;
    background: var(--border);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.progress-fill.novos { background: #f39c12; }
.progress-fill.aprendendo { background: #3498db; }
.progress-fill.revisando { background: #e74c3c; }
.progress-fill.dominados { background: #27ae60; }

.progress-value {
    min-width: 40px;
    text-align: right;
    font-weight: 600;
    color: var(--text-primary);
}

/* Botão Adicionar à Revisão - REMOVIDO (Sistema agora é automático baseado em status de leitura) */
/*
.btn-adicionar-revisao {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 8px;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn-adicionar-revisao::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-adicionar-revisao:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
    background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
}

.btn-adicionar-revisao:hover::before {
    left: 100%;
}

.btn-adicionar-revisao:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.btn-adicionar-revisao:disabled {
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn-adicionar-revisao i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.btn-adicionar-revisao:hover i {
    transform: scale(1.1) rotate(5deg);
}

.btn-adicionar-revisao span {
    font-weight: 600;
    position: relative;
}

/* Estados especiais do botão */
.btn-adicionar-revisao.loading {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-adicionar-revisao.loading i {
    animation: spin 1s linear infinite;
}

.btn-adicionar-revisao.success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-adicionar-revisao.success i {
    animation: bounce 0.6s ease;
}

.btn-adicionar-revisao.error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-adicionar-revisao.error i {
    animation: shake 0.5s ease;
}

/* Animações */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Efeito de pulso para chamar atenção */
.btn-adicionar-revisao.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    }
    50% {
        box-shadow: 0 4px 25px rgba(155, 89, 182, 0.6);
    }
    100% {
        box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    }
}
*/

/* Conteúdo de Fallback */
.artigo-fallback {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    margin: 20px 0;
}

.fallback-header {
    margin-bottom: 30px;
}

.fallback-header i {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 15px;
}

.fallback-header h3 {
    font-size: 2em;
    color: #495057;
    margin: 0;
    font-weight: 600;
}

.fallback-content {
    max-width: 500px;
    margin: 0 auto;
    text-align: left;
}

.fallback-content p {
    font-size: 1.1em;
    color: #6c757d;
    margin-bottom: 20px;
}

.fallback-content ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.fallback-content li {
    padding: 10px 0;
    font-size: 1em;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
}

.fallback-content li:last-child {
    border-bottom: none;
}

.fallback-tip {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.fallback-tip i {
    color: #856404;
    font-size: 18px;
}

.fallback-tip span {
    color: #856404;
    font-weight: 500;
}

/* Responsividade */
@media (max-width: 768px) {
    .btn-adicionar-revisao {
        padding: 10px 16px;
        font-size: 13px;
    }

    .btn-adicionar-revisao span {
        display: none;
    }

    .btn-adicionar-revisao i {
        margin: 0;
    }

    .artigo-fallback {
        padding: 30px 15px;
    }

    .fallback-header h3 {
        font-size: 1.5em;
    }

    .fallback-header i {
        font-size: 36px;
    }
}

/* Responsividade para Modal de Revisão */
@media (max-width: 768px) {
    .modal-revisao {
        width: 98%;
        margin: 1% auto;
    }

    .revisao-cards {
        grid-template-columns: 1fr;
    }

    .qualidade-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .sessao-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .artigo-meta {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .progress-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .progress-label {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .qualidade-buttons {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .revisao-actions,
    .resultado-actions {
        flex-direction: column;
    }
}

/* Interface de Estudo (sem dependência de API externa) */
.artigo-estudo {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.6;
}

.estudo-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border);
}

.estudo-header i {
    font-size: 2rem;
    color: var(--primary);
}

.estudo-header h3 {
    margin: 0;
    color: var(--primary);
    font-size: 1.8rem;
    flex: 1;
}

.badge-constituicao {
    background: var(--primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.estudo-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.estudo-instrucoes,
.estudo-dicas {
    background: var(--hover);
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary);
}

.estudo-instrucoes h4,
.estudo-dicas h4 {
    margin: 0 0 1rem 0;
    color: var(--primary);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.estudo-instrucoes ol {
    margin: 0;
    padding-left: 1.5rem;
}

.estudo-instrucoes li,
.estudo-dicas li {
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.estudo-dicas ul {
    margin: 0;
    padding-left: 0;
    list-style: none;
}

.estudo-dicas li {
    padding-left: 1.5rem;
    position: relative;
}

.estudo-revisao {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.revisao-info {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.revisao-info i {
    font-size: 2rem;
    margin-top: 0.25rem;
    opacity: 0.9;
}

.revisao-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
}

.revisao-info p {
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
}
