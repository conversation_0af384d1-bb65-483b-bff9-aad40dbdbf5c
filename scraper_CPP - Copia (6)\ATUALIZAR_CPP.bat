@echo off

:inicio
cls
echo ============================================================
echo ATUALIZACAO DO CPP - LEXJUS v2.0
echo ============================================================
echo.
echo Sistema otimizado para extrair 845+ artigos do CPP
echo Fonte: https://corpus927.enfam.jus.br/legislacao/cpp-41
echo.
echo Escolha o metodo de atualizacao:
echo.
echo [1] UNIFICADO (RECOMENDADO)
echo     - Busca dados atualizados do site oficial
echo     - Aplica 33+ correcoes automaticamente
echo     - Processo completo em uma execucao
echo     - Dados sempre atualizados
echo     - Tempo: aproximadamente 10 segundos
echo.
echo [2] RAPIDO
echo     - Usa arquivo base ja corrigido
echo     - Apenas gera arquivos finais
echo     - Mais rapido (poucos segundos)
echo     - Requer base atualizada
echo.
echo [3] MANUAL (3 ETAPAS)
echo     - Controle total do processo
echo     - Execucao passo a passo
echo     - Para debugging/desenvolvimento
echo.
echo [4] CANCELAR
echo.
set /p opcao="Digite sua opcao (1, 2, 3 ou 4): "

if "%opcao%"=="1" goto unificado
if "%opcao%"=="2" goto rapido
if "%opcao%"=="3" goto manual
if "%opcao%"=="4" goto cancelar
echo.
echo Opcao invalida! Tente novamente.
echo.
pause
goto inicio

:unificado
echo.
echo ============================================================
echo EXECUTANDO ATUALIZACAO UNIFICADA
echo ============================================================
echo.
echo Este processo ira:
echo    1. Extrair 845+ artigos do site oficial
echo    2. Aplicar 33+ correcoes de caputs automaticamente
echo    3. Gerar arquivos finais prontos para LexJus
echo.
echo Tempo estimado: 10-15 segundos
echo Metodo: RECOMENDADO (dados atualizados)
echo.
echo Iniciando processo unificado...
echo.
python atualizar_cpp_unificado.py
if %errorlevel% neq 0 (
    echo.
    echo Erro durante a execucao do script unificado!
    echo Tente o metodo manual para diagnosticar o problema.
    echo.
    pause
    goto inicio
)
goto fim

:rapido
echo.
echo ============================================================
echo EXECUTANDO ATUALIZACAO RAPIDA
echo ============================================================
echo.
echo Gerando arquivos usando base ja corrigida...
echo ATENCAO: Certifique-se de que a base esta atualizada!
echo.
python atualizar_cpp_rapido.py
if %errorlevel% neq 0 (
    echo.
    echo Erro durante a execucao rapida!
    echo Execute primeiro o metodo unificado para atualizar a base.
    echo.
    pause
    goto inicio
)
goto fim

:manual
echo.
echo ============================================================
echo EXECUTANDO PROCESSO MANUAL (3 ETAPAS)
echo ============================================================
echo.
echo Executando passo a passo:
echo.
echo [ETAPA 1/3] Extraindo artigos do site oficial...
python scraper_final_perfeito.py
if %errorlevel% neq 0 (
    echo Erro na etapa 1! Verifique conexao com internet.
    pause
    goto inicio
)
echo Etapa 1 concluida!
echo.

echo [ETAPA 2/3] Aplicando correcoes de caputs...
python corrigir_caputs_simples.py
if %errorlevel% neq 0 (
    echo Erro na etapa 2! Verifique arquivo base.
    pause
    goto inicio
)
echo Etapa 2 concluida!
echo.

echo [ETAPA 3/3] Gerando arquivos finais...
python atualizar_cpp_rapido.py
if %errorlevel% neq 0 (
    echo Erro na etapa 3! Verifique arquivos intermediarios.
    pause
    goto inicio
)
echo Etapa 3 concluida!
echo.
echo Processo manual concluido com sucesso!
goto fim

:cancelar
echo.
echo Operacao cancelada pelo usuario.
echo.
pause
exit

:fim
echo.
echo ============================================================
echo ATUALIZACAO CONCLUIDA COM SUCESSO!
echo ============================================================
echo.
echo ESTATISTICAS:
echo    - 845+ artigos extraidos
echo    - 33+ correcoes aplicadas
echo    - Estrutura legal preservada
echo    - Pronto para uso no LexJus
echo.
echo ARQUIVO MAIS RECENTE GERADO:
for /f %%i in ('dir cpp_lexjus_*.js /b /o-d 2^>nul') do (
    echo    %%i
    goto :mostrou_arquivo
)
:mostrou_arquivo
echo.
echo PROXIMOS PASSOS:
echo    1. Copie o arquivo .js mais recente para o sistema LexJus
echo    2. Atualize as referencias no codigo
echo    3. Teste o funcionamento
echo    4. Deploy em producao
echo.
echo Para mais informacoes, consulte o README.md
echo Fonte oficial: https://corpus927.enfam.jus.br/legislacao/cpp-41
echo.
set /p continuar="Pressione ENTER para voltar ao menu ou digite 'S' para sair: "
if /i "%continuar%"=="S" exit
if /i "%continuar%"=="s" exit
goto inicio
