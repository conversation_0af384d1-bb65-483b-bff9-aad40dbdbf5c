#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def verificar_correcao():
    """Verifica se os problemas foram corrigidos no arquivo"""
    
    print("=== VERIFICAÇÃO DA CORREÇÃO ===")
    
    # Carregar o arquivo corrigido
    with open('codigo_civil_formato_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Total de artigos no arquivo corrigido: {len(data)}")
    
    # 1. Verificar artigos 48 específicos
    print("\n1. ARTIGOS 48 NO ARQUIVO CORRIGIDO:")
    artigos_48 = []
    for i, art in enumerate(data):
        artigo = art['artigo']
        if artigo in ['Art. 48º', 'Art. 48-Aº', 'Art. 48-Pº']:
            artigos_48.append((i, art))
    
    for i, art in artigos_48:
        print(f"\nPosição {i}: {art['artigo']}")
        print(f"  Caput: {art['caput'][:100]}...")
        print(f"  Parágrafo único: {art['paragrafo_unico']}")
        print(f"  Incisos: {len(art['incisos'])}")
        print(f"  Parágrafos numerados: {len(art['paragrafos_numerados'])}")
    
    # 2. Verificar se ainda existem duplicatas
    print("\n2. VERIFICANDO DUPLICATAS:")
    artigos_vistos = {}
    duplicatas = []
    
    for i, art in enumerate(data):
        artigo = art['artigo']
        if artigo in artigos_vistos:
            duplicatas.append(f"Posição {i}: {artigo} (já existe na posição {artigos_vistos[artigo]})")
        else:
            artigos_vistos[artigo] = i
    
    if duplicatas:
        print("Duplicatas encontradas:")
        for dup in duplicatas:
            print(f"  {dup}")
    else:
        print("✅ Nenhuma duplicata encontrada!")
    
    # 3. Verificar artigos com caput vazio
    print("\n3. VERIFICANDO ARTIGOS COM CAPUT VAZIO:")
    caput_vazio = []
    
    for i, art in enumerate(data):
        if art['caput'].strip() == '':
            caput_vazio.append(f"Posição {i}: {art['artigo']}")
    
    if caput_vazio:
        print("Artigos com caput vazio:")
        for vazio in caput_vazio[:10]:
            print(f"  {vazio}")
        if len(caput_vazio) > 10:
            print(f"  ... e mais {len(caput_vazio) - 10} artigos")
    else:
        print("✅ Nenhum artigo com caput vazio encontrado!")
    
    # 4. Verificar artigos suspeitos (46-V, etc.)
    print("\n4. VERIFICANDO ARTIGOS SUSPEITOS:")
    suspeitos = []
    
    for i, art in enumerate(data):
        artigo = art['artigo']
        if artigo in ['Art. 46-Vº', 'Art. 48-Pº']:
            suspeitos.append(f"Posição {i}: {artigo}")
    
    if suspeitos:
        print("Artigos suspeitos encontrados:")
        for susp in suspeitos:
            print(f"  {susp}")
    else:
        print("✅ Nenhum artigo suspeito encontrado!")
    
    # 5. Verificar ordenação dos artigos 48
    print("\n5. VERIFICANDO ORDENAÇÃO DOS ARTIGOS 48:")
    artigos_48_todos = []
    
    for i, art in enumerate(data):
        artigo = art['artigo']
        if '48' in artigo and len(artigo) <= 12:  # Filtrar apenas artigos 48, não 148, 248, etc.
            if not any(x in artigo for x in ['148', '248', '348', '448', '548', '648', '748', '848', '948']):
                artigos_48_todos.append((i, artigo))
    
    print("Sequência dos artigos 48:")
    for i, artigo in artigos_48_todos:
        print(f"  Posição {i}: {artigo}")
    
    return len(data), len(duplicatas), len(caput_vazio), len(suspeitos)

if __name__ == "__main__":
    total, duplicatas, vazios, suspeitos = verificar_correcao()
    print(f"\n=== RESULTADO DA VERIFICAÇÃO ===")
    print(f"Total de artigos: {total}")
    print(f"Duplicatas: {duplicatas}")
    print(f"Artigos com caput vazio: {vazios}")
    print(f"Artigos suspeitos: {suspeitos}")
    
    if duplicatas == 0 and vazios == 0 and suspeitos == 0:
        print("✅ ARQUIVO CORRIGIDO COM SUCESSO!")
    else:
        print("⚠️ Ainda existem problemas no arquivo.")
