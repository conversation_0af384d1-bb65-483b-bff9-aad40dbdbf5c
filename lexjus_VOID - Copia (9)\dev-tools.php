<?php
/**
 * Ferramentas de Desenvolvimento - LexJus
 * 
 * Script para facilitar tarefas comuns de desenvolvimento
 */

// Verificar se é linha de comando ou web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    header('Content-Type: text/html; charset=UTF-8');
    echo "<!DOCTYPE html><html><head><title>Dev Tools</title></head><body>";
    echo "<h1>🛠️ Ferramentas de Desenvolvimento</h1>";
}

// Incluir sistema de cache busting
require_once 'includes/cache_buster.php';

function output($message, $isError = false) {
    global $isCLI;
    
    if ($isCLI) {
        echo ($isError ? "❌ " : "✅ ") . $message . "\n";
    } else {
        $color = $isError ? "#dc3545" : "#28a745";
        echo "<p style='color: $color; font-family: monospace;'>" . 
             ($isError ? "❌ " : "✅ ") . htmlspecialchars($message) . "</p>";
    }
}

function showUsage() {
    global $isCLI;
    
    $usage = "
Uso: php dev-tools.php [comando]

Comandos disponíveis:
  clear-cache     Limpa cache interno
  force-update    Força atualização de todos os arquivos
  check-status    Mostra status atual
  watch          Monitora mudanças em arquivos (experimental)
  help           Mostra esta ajuda

Exemplos:
  php dev-tools.php clear-cache
  php dev-tools.php check-status
";
    
    if ($isCLI) {
        echo $usage;
    } else {
        echo "<pre>" . htmlspecialchars($usage) . "</pre>";
    }
}

function clearCache() {
    CacheBuster::clearCache();
    output("Cache interno limpo");
}

function forceUpdate() {
    $files = ['style.css', 'script.js', 'css/sistema-revisao.css', 'js/sistema-revisao.js'];
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            touch($file);
            output("Atualizado: $file");
        } else {
            output("Arquivo não encontrado: $file", true);
        }
    }
}

function checkStatus() {
    global $isCLI;
    
    output("=== STATUS DO SISTEMA ===");
    output("Versão Global: " . cache_version());
    output("Data/Hora: " . date('d/m/Y H:i:s', cache_version()));
    output("");
    
    $files = [
        'style.css',
        'script.js', 
        'css/sistema-revisao.css',
        'js/sistema-revisao.js',
        'index.php',
        'artigos.json'
    ];
    
    output("=== ARQUIVOS ===");
    foreach ($files as $file) {
        if (file_exists($file)) {
            $version = filemtime($file);
            $size = number_format(filesize($file)/1024, 1);
            output("$file: v$version ({$size} KB)");
        } else {
            output("$file: NÃO ENCONTRADO", true);
        }
    }
    
    // Verificar API
    output("");
    output("=== VERIFICAÇÃO DA API ===");
    
    $apiFile = 'api/version.php';
    if (file_exists($apiFile)) {
        output("API disponível: $apiFile");
        
        // Testar API se não for CLI
        if (!$isCLI) {
            $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/version.php';
            echo "<p><a href='$url' target='_blank'>🔗 Testar API</a></p>";
        }
    } else {
        output("API não encontrada: $apiFile", true);
    }
}

function watchFiles() {
    output("🔍 Monitorando arquivos... (Pressione Ctrl+C para parar)");
    
    $files = ['style.css', 'script.js', 'css/sistema-revisao.css', 'js/sistema-revisao.js'];
    $lastModified = [];
    
    // Obter timestamps iniciais
    foreach ($files as $file) {
        if (file_exists($file)) {
            $lastModified[$file] = filemtime($file);
        }
    }
    
    while (true) {
        foreach ($files as $file) {
            if (file_exists($file)) {
                $currentModified = filemtime($file);
                
                if (!isset($lastModified[$file]) || $currentModified > $lastModified[$file]) {
                    output("📝 Arquivo modificado: $file");
                    $lastModified[$file] = $currentModified;
                    
                    // Limpar cache quando arquivo for modificado
                    CacheBuster::clearCache();
                    output("🔄 Cache limpo automaticamente");
                }
            }
        }
        
        sleep(1); // Verificar a cada segundo
    }
}

// Processar comando
if ($isCLI) {
    $command = $argv[1] ?? 'help';
} else {
    $command = $_GET['cmd'] ?? 'help';
}

switch ($command) {
    case 'clear-cache':
        clearCache();
        break;
        
    case 'force-update':
        forceUpdate();
        break;
        
    case 'check-status':
        checkStatus();
        break;
        
    case 'watch':
        if (!$isCLI) {
            output("Comando 'watch' só funciona via linha de comando", true);
            break;
        }
        watchFiles();
        break;
        
    case 'help':
    default:
        showUsage();
        break;
}

if (!$isCLI) {
    echo "<hr>";
    echo "<h2>🔗 Links Rápidos</h2>";
    echo "<p><a href='?cmd=clear-cache'>🗑️ Limpar Cache</a></p>";
    echo "<p><a href='?cmd=force-update'>🔄 Forçar Atualização</a></p>";
    echo "<p><a href='?cmd=check-status'>📊 Ver Status</a></p>";
    echo "<p><a href='clear-cache.php'>🛠️ Página de Admin Completa</a></p>";
    echo "<p><a href='api/version.php'>📡 API de Versão</a></p>";
    echo "</body></html>";
}
?>
