#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORREÇÃO ESPECÍFICA PARA ART. 1403º - PROBLEMA DO CAPUT
========================================================

Este script corrige o problema específico do Art. 1403º onde o caput
está começando com "403 Incumbem ao usufrutuário:" em vez de apenas
"Incumbem ao usufrutuário:".

Problema identificado:
- Art. 1403º (Começa com número/ponto): 403 Incumbem ao usufrutuário:...

Solução:
- Remover o número "403" do início do caput
- Garantir que o caput fique correto: "Incumbem ao usufrutuário:"
"""

import json
import re
import os
import sys

def corrigir_art_1403_caput():
    """Corrige especificamente o caput do Art. 1403º"""
    
    print("=== CORREÇÃO DO CAPUT DO ART. 1403º ===")
    
    # Verificar se o arquivo JSON existe
    arquivo_json = "codigo_civil_formato_lexjus_final.json"
    if not os.path.exists(arquivo_json):
        print(f"❌ Arquivo {arquivo_json} não encontrado!")
        print("Execute primeiro o pipeline de extração completo.")
        return False
    
    # Carregar o JSON
    print(f"Carregando arquivo: {arquivo_json}")
    with open(arquivo_json, 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    print(f"Total de artigos carregados: {len(artigos)}")
    
    # Buscar o Art. 1403º
    art_1403_encontrado = False
    artigos_corrigidos = 0
    
    for i, artigo in enumerate(artigos):
        artigo_nome = artigo.get('artigo', '')
        caput = artigo.get('caput', '')
        
        # Verificar se é o Art. 1403º
        if '1403' in artigo_nome:
            print(f"\n✅ Art. 1403º encontrado no índice {i}")
            print(f"Artigo: {artigo_nome}")
            print(f"Caput antes: {caput}")
            
            # Verificar se o caput tem o problema (começa com número)
            if re.match(r'^[0-9]+\s+', caput):
                # Remover o número do início
                caput_corrigido = re.sub(r'^[0-9]+\s+', '', caput)
                artigo['caput'] = caput_corrigido
                artigos_corrigidos += 1
                
                print(f"Caput depois: {caput_corrigido}")
                print("✅ Caput corrigido com sucesso!")
                art_1403_encontrado = True
            else:
                print("ℹ️  Caput já está correto (não começa com número)")
                art_1403_encontrado = True
            
            break
    
    if not art_1403_encontrado:
        print("❌ Art. 1403º não encontrado no arquivo!")
        return False
    
    if artigos_corrigidos == 0:
        print("ℹ️  Nenhuma correção necessária - Art. 1403º já está correto")
        return True
    
    # Salvar o arquivo corrigido
    arquivo_corrigido = "codigo_civil_formato_lexjus_final.json"
    print(f"\nSalvando arquivo corrigido: {arquivo_corrigido}")
    
    with open(arquivo_corrigido, 'w', encoding='utf-8') as f:
        json.dump(artigos, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Arquivo salvo com sucesso!")
    print(f"📊 Artigos corrigidos: {artigos_corrigidos}")
    
    return True

def verificar_correcao():
    """Verifica se a correção foi aplicada corretamente"""
    
    print("\n=== VERIFICAÇÃO DA CORREÇÃO ===")
    
    arquivo_json = "codigo_civil_formato_lexjus_final.json"
    if not os.path.exists(arquivo_json):
        print("❌ Arquivo não encontrado!")
        return False
    
    with open(arquivo_json, 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    # Buscar o Art. 1403º
    for artigo in artigos:
        if '1403' in artigo.get('artigo', ''):
            artigo_nome = artigo.get('artigo')
            caput = artigo.get('caput', '')
            
            print(f"Artigo encontrado: {artigo_nome}")
            print(f"Caput: {caput}")
            
            # Verificar se ainda tem problema
            if re.match(r'^[0-9]+\s+', caput):
                print("❌ Problema ainda existe - caput ainda começa com número!")
                return False
            else:
                print("✅ Caput está correto - não começa com número!")
                
                # Verificar se tem o conteúdo esperado
                if 'Incumbem ao usufrutuário' in caput:
                    print("✅ Conteúdo do caput está correto!")
                    return True
                else:
                    print("⚠️  Conteúdo do caput pode estar incorreto")
                    print(f"Esperado: 'Incumbem ao usufrutuário:'")
                    print(f"Encontrado: '{caput}'")
                    return False
    
    print("❌ Art. 1403º não encontrado!")
    return False

def verificar_outros_problemas():
    """Verifica se existem outros artigos com problemas similares"""
    
    print("\n=== VERIFICAÇÃO DE OUTROS PROBLEMAS SIMILARES ===")
    
    arquivo_json = "codigo_civil_formato_lexjus_final.json"
    if not os.path.exists(arquivo_json):
        print("❌ Arquivo não encontrado!")
        return
    
    with open(arquivo_json, 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    problemas_encontrados = []
    
    for i, artigo in enumerate(artigos):
        caput = artigo.get('caput', '')
        artigo_nome = artigo.get('artigo', '')
        
        # Verificar se o caput começa com número
        if re.match(r'^[0-9]+\s+', caput):
            problemas_encontrados.append({
                'indice': i,
                'artigo': artigo_nome,
                'caput': caput[:80] + '...' if len(caput) > 80 else caput
            })
    
    if problemas_encontrados:
        print(f"⚠️  Encontrados {len(problemas_encontrados)} artigos com problemas similares:")
        for problema in problemas_encontrados[:5]:  # Mostrar apenas os primeiros 5
            print(f"  - {problema['artigo']}: {problema['caput']}")
        
        if len(problemas_encontrados) > 5:
            print(f"  ... e mais {len(problemas_encontrados) - 5} artigos")
    else:
        print("✅ Nenhum outro artigo com problema similar encontrado!")

def main():
    """Função principal"""
    print("🔧 CORREÇÃO ESPECÍFICA DO ART. 1403º")
    print("=" * 50)
    
    # Executar correção
    if corrigir_art_1403_caput():
        # Verificar se a correção funcionou
        if verificar_correcao():
            print("\n🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!")
        else:
            print("\n❌ CORREÇÃO FALHOU - Verificar manualmente")
    else:
        print("\n❌ ERRO NA CORREÇÃO")
    
    # Verificar outros problemas similares
    verificar_outros_problemas()
    
    print("\n" + "=" * 50)
    print("Correção finalizada. Execute o script de verificação geral para confirmar.")

if __name__ == "__main__":
    main()
