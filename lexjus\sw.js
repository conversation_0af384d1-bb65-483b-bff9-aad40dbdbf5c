/**
 * Service Worker Simples para LexJus
 *
 * Fornece cache básico para melhorar performance
 */

const CACHE_NAME = 'lexjus-v2-fixed'; // Versão atualizada para forçar renovação

// Detectar se estamos em subdiretório
const isSubdirectory = self.location.pathname.includes('/lexjus_VOID/');
const basePath = isSubdirectory ? '/lexjus_VOID' : '';

const urlsToCache = [
    `${basePath}/`,
    `${basePath}/style.css`,
    `${basePath}/script.js`,
    `${basePath}/css/sistema-revisao.css`,
    `${basePath}/js/sistema-revisao.js`,
    `${basePath}/js/cache-manager.js`
];

// Instalação do Service Worker
self.addEventListener('install', event => {
    console.log('🔧 Service Worker: Instalando versão corrigida...');

    // Forçar ativação imediata da nova versão
    self.skipWaiting();

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 Service Worker: <PERSON><PERSON> aberto (versão corrigida)');
                return cache.addAll(urlsToCache);
            })
            .catch(error => {
                console.log('❌ Service Worker: Erro ao cachear:', error);
            })
    );
});

// Ativação do Service Worker
self.addEventListener('activate', event => {
    console.log('✅ Service Worker: Ativando versão corrigida...');

    event.waitUntil(
        Promise.all([
            // Limpar caches antigos
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('🗑️ Service Worker: Removendo cache antigo:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            // Tomar controle imediato de todas as abas
            self.clients.claim()
        ])
    );

    console.log('🎉 Service Worker: Versão corrigida ativa para todos os usuários!');
});

// Interceptação de requisições
self.addEventListener('fetch', event => {
    // Apenas cachear requisições GET
    if (event.request.method !== 'GET') {
        return;
    }

    // Não cachear APIs dinâmicas
    if (event.request.url.includes('/api/')) {
        return;
    }

    // Não cachear extensões do Chrome ou outros protocolos não suportados
    if (event.request.url.startsWith('chrome-extension://') ||
        event.request.url.startsWith('moz-extension://') ||
        event.request.url.startsWith('safari-extension://') ||
        event.request.url.startsWith('edge-extension://')) {
        return;
    }

    // Apenas cachear requisições do mesmo domínio
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Retornar do cache se disponível
                if (response) {
                    return response;
                }

                // Buscar na rede
                return fetch(event.request)
                    .then(response => {
                        // Verificar se é uma resposta válida
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // Verificar se a URL é válida para cache ANTES de tentar cachear
                        const isValidUrl = event.request.url.startsWith('http') &&
                                         !event.request.url.startsWith('chrome-extension://') &&
                                         !event.request.url.startsWith('moz-extension://') &&
                                         !event.request.url.startsWith('safari-extension://') &&
                                         !event.request.url.startsWith('edge-extension://');

                        if (isValidUrl) {
                            // Clonar resposta para cache apenas se URL for válida
                            const responseToCache = response.clone();

                            caches.open(CACHE_NAME)
                                .then(cache => {
                                    return cache.put(event.request, responseToCache);
                                })
                                .catch(error => {
                                    console.log('❌ Service Worker: Erro ao cachear:', error);
                                });
                        }

                        return response;
                    });
            })
            .catch(error => {
                console.log('❌ Service Worker: Erro na requisição:', error);

                // Retornar página offline se disponível
                if (event.request.destination === 'document') {
                    return caches.match('/');
                }
            })
    );
});

// Mensagens do cliente
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }

    if (event.data && event.data.type === 'CLEAR_CACHE') {
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
        }).then(() => {
            console.log('🧹 Service Worker: Cache limpo');
            event.ports[0].postMessage({ success: true });
        });
    }
});
