<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Teste API de Artigos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 { color: #333; text-align: center; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste API de Artigos</h1>

    <div class="container">
        <h2>🔍 Testar Artigo Específico</h2>
        <input type="text" id="artigoInput" value="14" placeholder="Número do artigo">
        <button onclick="testarArtigo()">Buscar Artigo</button>
        <button onclick="testarTodos()">Testar Vários</button>
        
        <div id="resultado" class="result"></div>
    </div>

    <script>
        async function testarArtigo() {
            const numero = document.getElementById('artigoInput').value;
            const resultado = document.getElementById('resultado');
            
            try {
                resultado.textContent = 'Buscando artigo ' + numero + '...';
                
                const response = await fetch(`./api/artigos.php?artigo_numero=${encodeURIComponent(numero)}`);
                
                console.log('Status:', response.status);
                console.log('Headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('Resposta completa:', responseText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseText}`);
                }
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (jsonError) {
                    throw new Error(`JSON inválido: ${jsonError.message}\n\nResposta: ${responseText}`);
                }
                
                resultado.className = 'result success';
                resultado.textContent = `✅ SUCESSO!\n\n${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                console.error('Erro:', error);
                resultado.className = 'result error';
                resultado.textContent = `❌ ERRO: ${error.message}`;
            }
        }

        async function testarTodos() {
            const artigos = ['1', '2', '3', '5', '13', '14', '37'];
            const resultado = document.getElementById('resultado');
            
            resultado.textContent = 'Testando múltiplos artigos...\n\n';
            
            for (const artigo of artigos) {
                try {
                    const response = await fetch(`./api/artigos.php?artigo_numero=${artigo}`);
                    const responseText = await response.text();
                    
                    if (response.ok) {
                        const data = JSON.parse(responseText);
                        resultado.textContent += `✅ Artigo ${artigo}: ${data.encontrado ? 'ENCONTRADO' : 'NÃO ENCONTRADO'}\n`;
                    } else {
                        resultado.textContent += `❌ Artigo ${artigo}: ERRO HTTP ${response.status}\n`;
                    }
                } catch (error) {
                    resultado.textContent += `❌ Artigo ${artigo}: ${error.message}\n`;
                }
                
                // Pequeno delay entre requisições
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            resultado.textContent += '\n✅ Teste concluído!';
        }

        // Testar automaticamente ao carregar
        window.onload = function() {
            console.log('Página carregada, testando artigo 14...');
            testarArtigo();
        };
    </script>
</body>
</html>
