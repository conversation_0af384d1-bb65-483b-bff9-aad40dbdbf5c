<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

switch ($metodo) {
    case 'POST':
        // Adicionar artigo a uma lista
        if (!isset($dados['lista_id']) || !isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }
        
        $lista_id = $dados['lista_id'];
        $artigo_numero = $dados['artigo_numero'];
        
        // Verificar se a lista pertence ao usuário
        $query_verificar = "SELECT id FROM appestudo.lexjus_listas 
                           WHERE id = $1 AND usuario_id = $2";
        
        $result_verificar = pg_query_params($conexao, $query_verificar, [$lista_id, $usuario_id]);
        
        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Lista não encontrada ou sem permissão']);
            exit;
        }
        
        // Adicionar artigo à lista
        $query = "INSERT INTO appestudo.lexjus_lista_artigos (lista_id, artigo_numero) 
                 VALUES ($1, $2) 
                 ON CONFLICT (lista_id, artigo_numero) DO NOTHING";
        
        $result = pg_query_params($conexao, $query, [$lista_id, $artigo_numero]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao adicionar artigo à lista']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo adicionado à lista']);
        break;
        
    case 'DELETE':
        // Remover artigo de uma lista
        if (!isset($dados['lista_id']) || !isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }
        
        $lista_id = $dados['lista_id'];
        $artigo_numero = $dados['artigo_numero'];
        
        // Verificar se a lista pertence ao usuário
        $query_verificar = "SELECT id FROM appestudo.lexjus_listas 
                           WHERE id = $1 AND usuario_id = $2";
        
        $result_verificar = pg_query_params($conexao, $query_verificar, [$lista_id, $usuario_id]);
        
        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Lista não encontrada ou sem permissão']);
            exit;
        }
        
        // Remover artigo da lista
        $query = "DELETE FROM appestudo.lexjus_lista_artigos 
                 WHERE lista_id = $1 AND artigo_numero = $2";
        
        $result = pg_query_params($conexao, $query, [$lista_id, $artigo_numero]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao remover artigo da lista']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo removido da lista']);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>