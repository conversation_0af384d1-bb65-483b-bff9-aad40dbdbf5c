#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import json
import re
from datetime import datetime

# URL do Código de Processo Penal no site do Planalto
URL_CPP = 'https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689.htm'
ARQUIVO_SAIDA_JS = 'codigo_processo_penal.js'
ARQUIVO_SAIDA_JSON = 'codigo_processo_penal.json'

def buscar_conteudo_pagina(url):
    """
    Busca o conteúdo HTML de uma página web com tratamento robusto de encoding
    """
    try:
        print(f"Buscando conteúdo da URL: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        print(f"Encoding detectado: {response.encoding}")

        # Tentar diferentes estratégias de encoding
        content = None

        # Estratégia 1: Usar o encoding detectado se for confiável
        if response.encoding and response.encoding.lower() not in ['iso-8859-1', 'latin-1']:
            try:
                content = response.text
                print(f"✅ Usando encoding detectado: {response.encoding}")
            except UnicodeDecodeError:
                content = None

        # Estratégia 2: Forçar ISO-8859-1 e converter para UTF-8
        if not content:
            try:
                response.encoding = 'iso-8859-1'
                content = response.text
                print(f"✅ Usando ISO-8859-1 convertido para UTF-8")
            except UnicodeDecodeError:
                content = None

        # Estratégia 3: Usar bytes brutos e tentar decodificar
        if not content:
            try:
                # Tentar UTF-8 primeiro
                content = response.content.decode('utf-8')
                print(f"✅ Decodificado como UTF-8 a partir de bytes")
            except UnicodeDecodeError:
                try:
                    # Tentar ISO-8859-1
                    content = response.content.decode('iso-8859-1')
                    print(f"✅ Decodificado como ISO-8859-1 a partir de bytes")
                except UnicodeDecodeError:
                    # Último recurso: usar errors='replace'
                    content = response.content.decode('utf-8', errors='replace')
                    print(f"⚠️ Decodificado com substituição de caracteres inválidos")

        if content:
            print(f"✅ Página carregada com sucesso! Tamanho: {len(content)} caracteres")
            return content
        else:
            print(f"❌ Falha ao decodificar o conteúdo da página")
            return None

    except requests.RequestException as e:
        print(f"❌ Erro ao buscar a página: {e}")
        return None

def limpar_texto(texto):
    """
    Limpa e normaliza o texto extraído preservando caracteres especiais portugueses
    """
    if not texto:
        return ""

    # Garantir que o texto está em UTF-8
    if isinstance(texto, bytes):
        texto = texto.decode('utf-8', errors='replace')

    # Remove quebras de linha e espaços extras
    texto = re.sub(r'\s+', ' ', texto)
    texto = texto.strip()

    # Remove apenas caracteres de controle, preservando acentos portugueses
    texto = re.sub(r'[\u00a0\u2000-\u200f\u2028-\u202f\ufeff]', ' ', texto)

    # Remove espaços múltiplos novamente após limpeza
    texto = re.sub(r'\s+', ' ', texto)

    return texto

def extrair_numero_artigo(texto):
    """
    Extrai o número do artigo do texto, incluindo sufixos como -A, -B
    """
    # Padrões para artigos do CPP
    patterns = [
        r'Art\.?\s*(\d+)[ºo°]?[-\s]*([AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz])?',
        r'Artigo\s+(\d+)[ºo°]?[-\s]*([AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz])?'
    ]

    for pattern in patterns:
        match = re.search(pattern, texto, re.IGNORECASE)
        if match:
            try:
                numero = int(match.group(1))
                sufixo = match.group(2) if len(match.groups()) > 1 and match.group(2) else ""
                return numero, sufixo.upper() if sufixo else ""
            except (ValueError, IndexError):
                continue

    return None, None

def analisar_estrutura_cpp_online(html_content):
    """
    Função para analisar a estrutura do CPP online com melhor detecção de artigos
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    paragrafo_atual = None
    artigos_processados = set()  # Para evitar duplicações

    print("Analisando estrutura do CPP online...")

    # Buscar todos os elementos <p>
    elementos_p = soup.find_all('p')
    print(f"Total de elementos <p> encontrados: {len(elementos_p)}")

    for i, p_tag in enumerate(elementos_p):
        texto_paragrafo_raw = p_tag.get_text(separator=' ', strip=False)
        texto_limpo = limpar_texto(texto_paragrafo_raw)

        if not texto_limpo or len(texto_limpo) < 3:
            continue

        # Pular títulos e cabeçalhos
        if re.search(r'^(LIVRO|TÍTULO|CAPÍTULO|SEÇÃO|DO\s|DA\s|DOS\s|DAS\s)', texto_limpo, re.IGNORECASE):
            continue

        # DEBUG: Mostrar progresso
        if i % 100 == 0:
            print(f"Processando parágrafo {i}/{len(elementos_p)}: {texto_limpo[:50]}...")

        # Detectar início de artigo (incluindo sufixos como -A, -B)
        if re.search(r'^Art\.?\s*\d+[ºo°]?[-\s]*[A-Z]?\.?\s', texto_limpo, re.IGNORECASE):
            # Salvar artigo anterior se existir
            if artigo_atual and artigo_atual["artigo"] not in artigos_processados:
                artigos_extraidos.append(artigo_atual)
                artigos_processados.add(artigo_atual["artigo"])

            # Extrair número e sufixo do artigo
            match = re.search(r'Art\.?\s*(\d+)[ºo°]?[-\s]*([A-Z])?\.?\s*(.+)', texto_limpo, re.IGNORECASE)
            if match:
                numero = int(match.group(1))
                sufixo = match.group(2).upper() if match.group(2) else ""
                caput_texto = match.group(3).strip()

                # Limpar caput - remover caracteres estranhos no início
                caput_texto = re.sub(r'^[ºo°\.\s]*', '', caput_texto)

                # Criar identificador único do artigo
                artigo_id = f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")

                # Verificar se já processamos este artigo
                if artigo_id in artigos_processados:
                    continue

                # Criar novo artigo
                artigo_atual = {
                    "artigo": artigo_id,
                    "caput": caput_texto,
                    "incisos": [],
                    "paragrafos_numerados": [],
                    "paragrafo_unico": None
                }
                paragrafo_atual = None

                if len(artigos_extraidos) % 10 == 0:
                    print(f"📋 {artigo_id} extraído ({len(artigos_extraidos)} artigos processados)")

        elif artigo_atual:
            # Detectar parágrafo único
            if re.search(r'Parágrafo\s+único', texto_limpo, re.IGNORECASE):
                match = re.search(r'Parágrafo\s+único\.?\s*[-–]?\s*(.+)', texto_limpo, re.IGNORECASE)
                if match and not artigo_atual["paragrafo_unico"]:
                    artigo_atual["paragrafo_unico"] = "- " + match.group(1).strip()
                paragrafo_atual = None

            # Detectar parágrafos numerados
            elif re.search(r'§\s*\d+[ºo°]?', texto_limpo):
                match = re.search(r'(§\s*\d+[ºo°]?)\s*[-–]?\s*(.+)', texto_limpo)
                if match:
                    numero_paragrafo = match.group(1).strip()
                    texto_paragrafo = match.group(2).strip()

                    # Verificar se já existe este parágrafo
                    paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo_atual["paragrafos_numerados"])
                    if not paragrafo_existe:
                        paragrafo_atual = {
                            "numero": numero_paragrafo,
                            "texto": texto_paragrafo,
                            "incisos": [],
                            "alineas": []
                        }
                        artigo_atual["paragrafos_numerados"].append(paragrafo_atual)

            # Detectar incisos (romanos)
            elif re.search(r'^[IVX]+\s*[-–]', texto_limpo):
                inciso_texto = texto_limpo.strip()
                if paragrafo_atual:
                    if inciso_texto not in paragrafo_atual["incisos"]:
                        paragrafo_atual["incisos"].append(inciso_texto)
                else:
                    if inciso_texto not in artigo_atual["incisos"]:
                        artigo_atual["incisos"].append(inciso_texto)

            # Detectar alíneas
            elif re.search(r'^[a-z]\)', texto_limpo):
                alinea_texto = texto_limpo.strip()
                if paragrafo_atual:
                    if alinea_texto not in paragrafo_atual["alineas"]:
                        paragrafo_atual["alineas"].append(alinea_texto)

            # Continuação do caput (apenas se não há estrutura interna ainda)
            elif (not artigo_atual["incisos"] and
                  not artigo_atual["paragrafos_numerados"] and
                  not artigo_atual["paragrafo_unico"] and
                  len(artigo_atual["caput"]) < 500):  # Limitar tamanho do caput
                artigo_atual["caput"] += " " + texto_limpo

    # Adicionar último artigo se existir e não foi processado
    if artigo_atual and artigo_atual["artigo"] not in artigos_processados:
        artigos_extraidos.append(artigo_atual)

    print(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def gerar_javascript(artigos, nome_arquivo):
    """
    Gera arquivo JavaScript com os artigos e funções de busca seguindo padrão do Código Penal
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Os dados já estão em UTF-8 correto após o processamento
    artigos_utf8 = artigos

    js_content = f"""// Código de Processo Penal Brasileiro - Dados extraídos automaticamente
// Gerado em: {timestamp}
// Total de artigos: {len(artigos_utf8)}
// URL fonte: {URL_CPP}

const codigoProcessoPenalArtigos = {json.dumps(artigos_utf8, ensure_ascii=False, indent=2)};


// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""

    try:
        with open(nome_arquivo, 'w', encoding='utf-8', newline='\n') as f:
            f.write(js_content)
        print(f"✅ Arquivo JavaScript salvo: {nome_arquivo}")
        return True
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo JavaScript: {e}")
        return False

def salvar_em_json(artigos, nome_arquivo):
    """
    Salva os artigos em formato JSON com encoding UTF-8 correto
    """
    try:
        with open(nome_arquivo, 'w', encoding='utf-8', newline='\n') as f:
            json.dump(artigos, f, ensure_ascii=False, indent=2)
        print(f"✅ Arquivo JSON salvo: {nome_arquivo} ({len(artigos)} artigos)")
        return True
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo JSON: {e}")
        return False

def main():
    print("=" * 60)
    print("🏛️  SCRAPER DO CÓDIGO DE PROCESSO PENAL - PLANALTO")
    print("=" * 60)
    print(f"📋 Extraindo artigos de: {URL_CPP}")
    print("=" * 60)

    # Buscar conteúdo online
    html_content = buscar_conteudo_pagina(URL_CPP)

    if not html_content:
        print("❌ Não foi possível obter o conteúdo da página")
        return

    # Extrair artigos
    artigos = analisar_estrutura_cpp_online(html_content)

    if not artigos:
        print("❌ Nenhum artigo foi extraído")
        return

    print(f"\n📊 RESULTADOS:")
    print(f"   • Total de artigos extraídos: {len(artigos)}")

    if artigos:
        print(f"   • Primeiro artigo: {artigos[0]['artigo']}")
        print(f"   • Último artigo: {artigos[-1]['artigo']}")

    # Salvar arquivos
    print(f"\n💾 Salvando arquivos...")

    sucesso_json = salvar_em_json(artigos, ARQUIVO_SAIDA_JSON)
    sucesso_js = gerar_javascript(artigos, ARQUIVO_SAIDA_JS)

    if sucesso_json and sucesso_js:
        print(f"\n✅ PROCESSO CONCLUÍDO COM SUCESSO!")
        print(f"   📄 Arquivo JSON: {ARQUIVO_SAIDA_JSON}")
        print(f"   📄 Arquivo JS: {ARQUIVO_SAIDA_JS}")
        print(f"\n🔄 Execute 'python limpar_referencias_finais_cpp.py' para limpeza final")
    else:
        print(f"\n⚠️  Processo concluído com erros na gravação dos arquivos")

if __name__ == '__main__':
    main()
