#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json
import os
import requests
import sys
from urllib.parse import urljoin

# Mudar para o diretório do script
os.chdir(os.path.dirname(os.path.abspath(__file__)))

def baixar_html_web(url):
    """Baixa o HTML diretamente da web"""
    print(f"Baixando HTML da web: {url}")

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        # Detectar encoding
        if response.encoding:
            conteudo = response.text
        else:
            # Tentar diferentes encodings
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    conteudo = response.content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                conteudo = response.content.decode('utf-8', errors='ignore')

        print(f"HTML baixado com sucesso: {len(conteudo):,} caracteres")

        # Salvar uma cópia local para backup
        with open('L10406_web_backup.html', 'w', encoding='utf-8') as f:
            f.write(conteudo)
        print("Backup salvo: L10406_web_backup.html")

        return conteudo

    except requests.RequestException as e:
        print(f"ERRO ao baixar da web: {e}")
        return None
    except Exception as e:
        print(f"ERRO inesperado ao baixar: {e}")
        return None

def carregar_html(forcar_local=False):
    """Carrega HTML da web ou arquivo local"""
    url_web = "https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm"
    arquivo_local = "L10406.html"

    print("=== CARREGANDO CÓDIGO CIVIL ===")

    if forcar_local:
        print("🔒 Modo forçado: Usando apenas arquivo local")

        if os.path.exists(arquivo_local):
            print(f"Carregando arquivo local: {arquivo_local}")
            try:
                with open(arquivo_local, 'r', encoding='utf-8', errors='ignore') as f:
                    conteudo = f.read()
                print(f"✅ Arquivo local carregado: {len(conteudo):,} caracteres")
                return conteudo
            except Exception as e:
                print(f"ERRO ao ler arquivo local: {e}")
        else:
            print(f"❌ Arquivo local {arquivo_local} não encontrado")

        print("\n❌ ERRO: Não foi possível carregar o arquivo local!")
        return None

    # Modo normal: tentar web primeiro, depois local
    print("🌐 Tentativa 1: Baixar da web (versão mais atualizada)")

    conteudo = baixar_html_web(url_web)

    if conteudo:
        print("✅ Usando versão da web")
        return conteudo

    # Se falhar, usar arquivo local
    print("\n📁 Tentativa 2: Usar arquivo local")

    if os.path.exists(arquivo_local):
        print(f"Carregando arquivo local: {arquivo_local}")
        try:
            with open(arquivo_local, 'r', encoding='utf-8', errors='ignore') as f:
                conteudo = f.read()
            print(f"✅ Arquivo local carregado: {len(conteudo):,} caracteres")
            return conteudo
        except Exception as e:
            print(f"ERRO ao ler arquivo local: {e}")
    else:
        print(f"❌ Arquivo local {arquivo_local} não encontrado")

    # Se ambos falharem
    print("\n❌ ERRO: Não foi possível carregar o HTML!")
    print("Soluções:")
    print("1. Verifique sua conexão com a internet")
    print("2. Coloque o arquivo L10406.html no diretório atual")
    print("3. Baixe manualmente de: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm")
    print("4. Use o parâmetro --local para forçar uso do arquivo local")
    return None

print("=== EXTRATOR DE CÓDIGO CIVIL CORRIGIDO ===")

# Verificar parâmetros de linha de comando
forcar_local = '--local' in sys.argv or '-l' in sys.argv

if forcar_local:
    print("Parâmetro detectado: Forçando uso do arquivo local")
elif len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
    print("USO:")
    print("  python executar_extracao_corrigido.py           # Baixa da web (padrão)")
    print("  python executar_extracao_corrigido.py --local   # Usa arquivo local")
    print("  python executar_extracao_corrigido.py -l        # Usa arquivo local")
    print("")
    print("FONTES:")
    print("  Web:   https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm")
    print("  Local: L10406.html")
    print("")
    print("O script tenta baixar da web primeiro (versão mais atualizada),")
    print("e usa o arquivo local como fallback se a web falhar.")
    exit(0)

print("Iniciando extração...")

try:
    # Carregar HTML (web ou local)
    conteudo = carregar_html(forcar_local)

    if not conteudo:
        exit(1)

    # Extrair artigos
    artigos = {}

    # Padrão para artigos - buscar todas as ocorrências
    padrao_artigos = r'name="(art\d+\.?)"'
    nomes_artigos = re.findall(padrao_artigos, conteudo, re.IGNORECASE)

    print(f"Encontrados {len(nomes_artigos)} referências de artigos")

    # Processar artigos
    for i, name in enumerate(nomes_artigos):
        if i % 100 == 0:
            print(f"Processando artigo {i+1}/{len(nomes_artigos)}: {name}")

        # Extrair número
        match = re.match(r'^art(\d+)\.?$', name)
        if not match:
            continue

        numero = match.group(1)
        tem_ponto = name.endswith('.')

        # Priorizar versão com ponto (atual)
        if numero in artigos and not tem_ponto:
            continue

        if numero in artigos and artigos[numero].get('versao_atual', False) and not tem_ponto:
            continue

        # Buscar o contexto do artigo de forma mais simples
        # Encontrar a posição do <a name>
        padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
        match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

        if not match_pos:
            continue

        inicio = match_pos.end()

        # Buscar o texto do artigo a partir dessa posição
        texto_restante = conteudo[inicio:inicio+2000]  # Limitar busca a 2000 chars

        # Padrão que funciona para ambas as estruturas HTML
        # Busca qualquer texto após </a> até o próximo elemento
        padrao_art = r'</a>(.*?)(?=<a name="|</p>|<p align="CENTER"|$)'
        match_art = re.search(padrao_art, texto_restante, re.DOTALL | re.IGNORECASE)

        if match_art:
            texto_html = match_art.group(1)

            # Limpar HTML
            texto = re.sub(r'<[^>]+>', '', texto_html)
            texto = texto.replace('&nbsp;', ' ')
            texto = texto.replace('&amp;', '&')
            texto = texto.replace('&lt;', '<')
            texto = texto.replace('&gt;', '>')
            texto = texto.replace('&quot;', '"')
            texto = re.sub(r'\s+', ' ', texto)
            texto = texto.strip()

            # Verificar se o texto contém o número do artigo e extrair apenas o conteúdo após "Art. X."
            padrao_numero = rf'Art\.\s*{re.escape(numero)}[ºo]?\.\s*(.*)'
            match_numero = re.search(padrao_numero, texto, re.IGNORECASE)
            if match_numero:
                texto = match_numero.group(1).strip()

            # Filtrar títulos de capítulos e seções
            if (texto and len(texto) > 15 and
                not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo|Subseo)', texto, re.IGNORECASE) and
                not re.match(r'^[IVX]+\s*-', texto) and
                'CAPTULO' not in texto and 'TTULO' not in texto):

                artigos[numero] = {
                    'numero': int(numero),
                    'caput': texto,
                    'paragrafos': {},
                    'incisos': {},
                    'versao_atual': tem_ponto
                }

    print(f"Artigos únicos processados: {len(artigos)}")

    # Processar incisos de forma mais simples
    print("Processando incisos...")
    padrao_incisos = r'name="(art\d+(?:p?[ivx]+)\.?)"'
    nomes_incisos = re.findall(padrao_incisos, conteudo, re.IGNORECASE)

    print(f"Encontrados {len(nomes_incisos)} incisos")

    for name in nomes_incisos:
        match = re.match(r'^art(\d+)(p?[ivx]+)\.?$', name)
        if not match:
            continue

        numero_artigo = match.group(1)
        inciso_completo = match.group(2)
        tem_ponto = name.endswith('.')

        # Determinar se é inciso de parágrafo único ou normal
        if inciso_completo.startswith('p'):
            inciso_romano = inciso_completo[1:]
            eh_inciso_paragrafo = True
        else:
            inciso_romano = inciso_completo
            eh_inciso_paragrafo = False

        # Garantir que o artigo existe
        if numero_artigo not in artigos:
            artigos[numero_artigo] = {
                'numero': int(numero_artigo),
                'caput': '',
                'paragrafos': {},
                'incisos': {},
                'versao_atual': False
            }

        chave_inciso = inciso_completo if eh_inciso_paragrafo else inciso_romano

        # Priorizar versão com ponto
        if chave_inciso in artigos[numero_artigo]['incisos'] and not tem_ponto:
            continue

        # Buscar texto do inciso de forma mais simples
        padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
        match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

        if match_pos:
            inicio = match_pos.end()
            texto_restante = conteudo[inicio:inicio+1000]

            padrao_inciso = rf'{inciso_romano.upper()}\s*-\s*(.*?)(?=<a name="|</p>|[IVX]+\s*-|$)'
            match_inciso = re.search(padrao_inciso, texto_restante, re.DOTALL | re.IGNORECASE)

            if match_inciso:
                texto_html = match_inciso.group(1)
                texto = re.sub(r'<[^>]+>', '', texto_html)
                texto = texto.replace('&nbsp;', ' ')
                texto = texto.replace('&amp;', '&')
                texto = re.sub(r'\s+', ' ', texto)
                texto = texto.strip()

                if (texto and len(texto) > 5 and
                    not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo)', texto, re.IGNORECASE)):

                    artigos[numero_artigo]['incisos'][chave_inciso] = {
                        'texto': texto,
                        'alineas': {},
                        'versao_atual': tem_ponto,
                        'eh_inciso_paragrafo': eh_inciso_paragrafo
                    }

    # Processar parágrafos de forma mais simples
    print("Processando parágrafos...")
    padrao_paragrafos = r'name="(art\d+(?:p|[§�]\d+)\.?)"'
    nomes_paragrafos = re.findall(padrao_paragrafos, conteudo, re.IGNORECASE)

    print(f"Encontrados {len(nomes_paragrafos)} parágrafos")

    for name in nomes_paragrafos:
        match = re.match(r'^art(\d+)(p|[§�]\d+)\.?$', name)
        if not match:
            continue

        numero_artigo = match.group(1)
        identificador_paragrafo = match.group(2)
        tem_ponto = name.endswith('.')

        # Normalizar identificador do parágrafo
        if identificador_paragrafo == 'p':
            numero_paragrafo = 'unico'
        elif identificador_paragrafo.startswith('§') or identificador_paragrafo.startswith('�'):
            num_match = re.search(r'[§�](\d+)', identificador_paragrafo)
            if num_match:
                numero_paragrafo = num_match.group(1)
            else:
                numero_paragrafo = '1'
        else:
            numero_paragrafo = identificador_paragrafo

        # Garantir que o artigo existe
        if numero_artigo not in artigos:
            artigos[numero_artigo] = {
                'numero': int(numero_artigo),
                'caput': '',
                'paragrafos': {},
                'incisos': {},
                'versao_atual': False
            }

        # Priorizar versão com ponto
        if numero_paragrafo in artigos[numero_artigo]['paragrafos'] and not tem_ponto:
            continue

        # Buscar texto do parágrafo
        padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
        match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

        if match_pos:
            inicio = match_pos.end()
            texto_restante = conteudo[inicio:inicio+1500]

            # Extrair texto após o elemento <a name>
            padrao_paragrafo = r'(.*?)(?=<a name="|</p>|<p align="CENTER"|$)'
            match_paragrafo = re.search(padrao_paragrafo, texto_restante, re.DOTALL | re.IGNORECASE)

            if match_paragrafo:
                texto_html = match_paragrafo.group(1)
                texto = re.sub(r'<[^>]+>', '', texto_html)
                texto = texto.replace('&nbsp;', ' ')
                texto = texto.replace('&amp;', '&')
                texto = texto.replace('&lt;', '<')
                texto = texto.replace('&gt;', '>')
                texto = texto.replace('&quot;', '"')
                texto = re.sub(r'\s+', ' ', texto)
                texto = texto.strip()

                if (texto and len(texto) > 10 and
                    not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo)', texto, re.IGNORECASE)):

                    artigos[numero_artigo]['paragrafos'][numero_paragrafo] = {
                        'texto': texto,
                        'versao_atual': tem_ponto
                    }

    # Converter para lista ordenada
    lista_artigos = []
    artigos_vistos = set()

    for num_art in sorted(artigos.keys(), key=lambda x: int(x)):
        artigo = artigos[num_art]
        numero = artigo['numero']

        # Filtrar artigos com números muito altos
        if numero > 2100:
            continue

        # Evitar duplicatas
        if numero in artigos_vistos:
            continue

        artigos_vistos.add(numero)
        lista_artigos.append(artigo)

    # Salvar JSON
    dados = {
        'documento': 'Código Civil - Lei 10.406/2002',
        'total_artigos': len(lista_artigos),
        'artigos': lista_artigos
    }

    with open('codigo_civil_lexjus_corrigido.json', 'w', encoding='utf-8') as f:
        json.dump(dados, f, ensure_ascii=False, indent=2)

    print(f"\n=== EXTRAÇÃO CONCLUÍDA ===")
    print(f"Total de artigos extraídos: {len(lista_artigos)}")
    print(f"Arquivo salvo: codigo_civil_lexjus_corrigido.json")

    # Verificar artigos próximos ao 1070
    print(f"\nVerificando artigos 1070-1075:")
    for artigo in lista_artigos:
        if 1070 <= artigo['numero'] <= 1075:
            print(f"Art. {artigo['numero']}: {artigo['caput'][:100]}...")

except Exception as e:
    print(f"ERRO: {e}")
    import traceback
    traceback.print_exc()
