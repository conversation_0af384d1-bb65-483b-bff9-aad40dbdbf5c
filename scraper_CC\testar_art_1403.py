#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TESTE ESPECÍFICO PARA ART. 1403º
================================

Este script testa especificamente a extração do Art. 1403º para identificar o problema.
"""

import re
import os

def testar_art_1403():
    """Testa a extração específica do Art. 1403º"""
    
    # Carregar o arquivo HTML
    arquivo_html = "L10406_web_backup.html"
    
    if not os.path.exists(arquivo_html):
        print(f"❌ Arquivo {arquivo_html} não encontrado!")
        return
    
    print("=== TESTE DO ART. 1403º ===")
    
    with open(arquivo_html, 'r', encoding='utf-8', errors='ignore') as f:
        conteudo = f.read()
    
    print(f"Arquivo carregado: {len(conteudo):,} caracteres")
    
    # 1. <PERSON><PERSON> todas as ocorrências de "1403" no HTML
    print("\n1. <PERSON><PERSON><PERSON> todas as ocorrências de '1403':")
    ocorrencias_1403 = []
    for match in re.finditer(r'1403', conteudo, re.IGNORECASE):
        inicio = max(0, match.start() - 50)
        fim = min(len(conteudo), match.end() + 50)
        contexto = conteudo[inicio:fim]
        ocorrencias_1403.append((match.start(), contexto))
    
    print(f"Encontradas {len(ocorrencias_1403)} ocorrências de '1403'")
    for i, (pos, contexto) in enumerate(ocorrencias_1403):
        print(f"  {i+1}. Posição {pos}: {contexto}")
    
    # 2. Buscar especificamente por name="art1403"
    print("\n2. Buscando por name=\"art1403\":")
    padrao_name = r'name="(art1403[^"]*)"'
    matches_name = re.findall(padrao_name, conteudo, re.IGNORECASE)
    print(f"Encontrados {len(matches_name)} matches: {matches_name}")
    
    # 3. Buscar por <a name="art1403">
    print("\n3. Buscando por <a name=\"art1403\">:")
    padrao_a_name = r'<a name="art1403[^"]*"[^>]*>'
    matches_a_name = re.findall(padrao_a_name, conteudo, re.IGNORECASE)
    print(f"Encontrados {len(matches_a_name)} matches:")
    for match in matches_a_name:
        print(f"  {match}")
    
    # 4. Buscar o contexto completo do Art. 1403
    print("\n4. Contexto completo do Art. 1403:")
    padrao_contexto = r'<a name="art1403"[^>]*>.*?Art\.\s*1\.403[^<]*'
    match_contexto = re.search(padrao_contexto, conteudo, re.IGNORECASE | re.DOTALL)
    
    if match_contexto:
        print(f"Contexto encontrado:")
        print(f"  {match_contexto.group(0)}")
        
        # Extrair o texto após o <a name>
        inicio_texto = match_contexto.end()
        texto_seguinte = conteudo[inicio_texto:inicio_texto+500]
        print(f"\nTexto seguinte (500 chars):")
        print(f"  {texto_seguinte}")
    else:
        print("❌ Contexto não encontrado!")
    
    # 5. Testar o padrão usado pelo scraper
    print("\n5. Testando padrão do scraper:")
    padrao_scraper = r'name="(art\d+(?:[a-z](?:\.\d+)?)?\.?)"'
    matches_scraper = re.findall(padrao_scraper, conteudo, re.IGNORECASE)
    
    # Filtrar apenas os que contêm 1403
    matches_1403 = [m for m in matches_scraper if '1403' in m]
    print(f"Matches do scraper contendo '1403': {matches_1403}")
    
    # 6. Verificar se está dentro de <strike>
    print("\n6. Verificando se está dentro de <strike>:")
    for match in re.finditer(r'<a name="art1403"[^>]*>', conteudo, re.IGNORECASE):
        inicio_contexto = max(0, match.start() - 200)
        contexto_anterior = conteudo[inicio_contexto:match.start()]
        
        strike_aberta = contexto_anterior.rfind('<strike>')
        strike_fechada = contexto_anterior.rfind('</strike>')
        
        print(f"  Strike aberta: {strike_aberta}, Strike fechada: {strike_fechada}")
        if strike_aberta > strike_fechada:
            print("  ❌ Artigo está dentro de <strike> (texto revogado)")
        else:
            print("  ✅ Artigo NÃO está dentro de <strike>")
    
    # 7. Simular extração completa
    print("\n7. Simulando extração completa:")
    
    # Buscar o <a name="art1403">
    match_pos = re.search(r'<a name="art1403"[^>]*>', conteudo, re.IGNORECASE)
    if match_pos:
        inicio = match_pos.end()
        texto_restante = conteudo[inicio:inicio+2000]
        
        # Aplicar o padrão de extração
        padrao_art = r'</a>(.*?)(?=<a name="|</p>|<p align="CENTER"|$)'
        match_art = re.search(padrao_art, texto_restante, re.DOTALL | re.IGNORECASE)
        
        if match_art:
            texto_html = match_art.group(1)
            print(f"Texto HTML extraído: {texto_html[:200]}...")
            
            # Limpar HTML
            texto = re.sub(r'<[^>]+>', '', texto_html)
            texto = texto.replace('&nbsp;', ' ')
            texto = re.sub(r'\s+', ' ', texto)
            texto = texto.strip()
            
            print(f"Texto limpo: {texto[:200]}...")
            
            # Verificar se contém o número do artigo
            padrao_numero = r'Art\.\s*1403[ºo]?\.\s*(.*)'
            match_numero = re.search(padrao_numero, texto, re.IGNORECASE)
            if match_numero:
                texto_final = match_numero.group(1).strip()
                print(f"Texto final (após remoção do número): {texto_final}")
            else:
                print(f"❌ Padrão do número não encontrado no texto")
                # Tentar padrão alternativo
                padrao_alt = r'Art\.\s*1\.403[ºo]?\s*(.*)'
                match_alt = re.search(padrao_alt, texto, re.IGNORECASE)
                if match_alt:
                    texto_final = match_alt.group(1).strip()
                    print(f"Texto final (padrão alternativo): {texto_final}")
                else:
                    print(f"❌ Padrão alternativo também não funcionou")
        else:
            print("❌ Padrão de extração do artigo não funcionou")
    else:
        print("❌ <a name=\"art1403\"> não encontrado")

if __name__ == "__main__":
    testar_art_1403()
