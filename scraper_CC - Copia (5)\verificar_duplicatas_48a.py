#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("=== VERIFICANDO DUPLICATAS DO ART. 48-A ===")

# Encontrar todas as versões do Art. 48-A
art_48a_versoes = []
for artigo in data['artigos']:
    numero = str(artigo['numero'])
    if numero == '48-A':
        art_48a_versoes.append(artigo)

print(f"Encontradas {len(art_48a_versoes)} versões do Art. 48-A:")
print()

for i, versao in enumerate(art_48a_versoes, 1):
    print(f"VERSÃO {i}:")
    print(f"  Número: {versao['numero']}")
    print(f"  Sufixo letra: '{versao['sufixo_letra']}'")
    print(f"  Versão numérica: '{versao['versao_numerica']}'")
    print(f"  Número ordenação: {versao['numero_ordenacao']}")
    print(f"  Versão atual: {versao['versao_atual']}")
    print(f"  Caput: {versao['caput'][:100]}...")
    print()

print("=== ANÁLISE ===")
print("Segundo a lógica explicada:")
print("- art48a (sem número) = versão base")
print("- art48a.0 = primeira alteração")
print("- art48a.1 = segunda alteração (seria a atual)")
print()
print("Como não temos art48a.1, a versão atual deveria ser art48a.0")
print("Mas ambas estão marcadas como versao_atual: False")
