#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper v2 para o Código de Processo Penal da fonte Corpus927
Versão corrigida com melhor extração de conteúdo
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def limpar_texto(texto):
    """
    Limpa o texto removendo caracteres desnecessários
    """
    if not texto:
        return ""

    # Remover quebras de linha e espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)

    # Remover referências legais entre parênteses
    texto = re.sub(r'\s*\([^)]*(?:Lei|Decreto|Incluído|Redação|Vide|Vigência|VETADO)[^)]*\)\s*', '', texto, flags=re.IGNORECASE)

    return texto.strip()

def extrair_numero_artigo(link_artigo):
    """
    Extrai o número do artigo do link
    """
    if not link_artigo:
        return None

    texto_artigo = link_artigo.get_text(strip=True)

    # Padrões para extrair número do artigo
    patterns = [
        r'Art\.\s*(\d+)(?:º|°)?(?:-([A-Z]))?',
        r'Artigo\s+(\d+)(?:º|°)?(?:-([A-Z]))?'
    ]

    for pattern in patterns:
        match = re.search(pattern, texto_artigo, re.IGNORECASE)
        if match:
            numero = int(match.group(1))
            sufixo = match.group(2) if match.group(2) else ""
            return f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")

    return None

def extrair_caput_completo(paragrafo_artigo):
    """
    Extrai o caput completo do parágrafo que contém o artigo
    """
    if not paragrafo_artigo:
        return ""

    # Pegar todo o texto do parágrafo, preservando estrutura
    texto_completo = ""
    for content in paragrafo_artigo.contents:
        if hasattr(content, 'get_text'):
            texto_completo += content.get_text(separator=' ')
        else:
            texto_completo += str(content)

    # Normalizar espaços
    texto_completo = re.sub(r'\s+', ' ', texto_completo).strip()

    # Remover o número do artigo do início para pegar só o caput
    caput_match = re.search(r'Art\.\s*\d+(?:º|°)?(?:-[A-Z])?\s*\.?\s*(.+)', texto_completo, re.IGNORECASE)

    if caput_match:
        caput = caput_match.group(1).strip()
        # Limpar referências legais mas preservar texto principal
        caput = re.sub(r'\s*\([^)]*(?:Lei|Decreto|Incluído|Redação|Vide|Vigência|VETADO)[^)]*\)\s*', '', caput, flags=re.IGNORECASE)
        caput = re.sub(r'\s+', ' ', caput).strip()
        return caput

    return ""

def processar_artigo_v2(link_artigo, soup):
    """
    Processa um artigo específico com algoritmo melhorado
    """
    artigo_id = extrair_numero_artigo(link_artigo)
    if not artigo_id:
        return None

    # Encontrar o parágrafo que contém o artigo
    paragrafo_artigo = link_artigo.find_parent('p')
    if not paragrafo_artigo:
        return None

    # Extrair caput completo
    caput = extrair_caput_completo(paragrafo_artigo)

    # Inicializar estrutura do artigo
    artigo = {
        "artigo": artigo_id,
        "caput": caput,
        "incisos": [],
        "paragrafos_numerados": [],
        "paragrafo_unico": None
    }

    # Buscar elementos relacionados ao artigo (próximos parágrafos)
    elemento_atual = paragrafo_artigo.find_next_sibling('p')

    while elemento_atual:
        # Parar se encontrar outro artigo
        if elemento_atual.find('a', class_='numero_artigo'):
            break

        # Parar se encontrar título/seção
        texto_elemento = elemento_atual.get_text(separator=' ', strip=True)
        if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO)', texto_elemento, re.IGNORECASE):
            break

        # Pular elementos vazios
        if len(texto_elemento) < 3:
            elemento_atual = elemento_atual.find_next_sibling('p')
            continue

        # Verificar se é parágrafo único
        if re.search(r'Parágrafo\s+único', texto_elemento, re.IGNORECASE):
            paragrafo_match = re.search(r'Parágrafo\s+único\.?\s*(.+)', texto_elemento, re.IGNORECASE)
            if paragrafo_match and not artigo["paragrafo_unico"]:
                artigo["paragrafo_unico"] = limpar_texto(paragrafo_match.group(1))

        # Verificar se é parágrafo numerado
        elif re.search(r'§\s*\d+(?:º|°)?', texto_elemento):
            paragrafo_match = re.search(r'(§\s*\d+(?:º|°)?)\s*(.+)', texto_elemento)
            if paragrafo_match:
                numero_paragrafo = paragrafo_match.group(1).strip()
                texto_paragrafo = limpar_texto(paragrafo_match.group(2))

                # Verificar se já existe este parágrafo
                paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo["paragrafos_numerados"])
                if not paragrafo_existe:
                    artigo["paragrafos_numerados"].append({
                        "numero": numero_paragrafo,
                        "texto": texto_paragrafo,
                        "incisos": [],
                        "alineas": []
                    })

        # Verificar se é inciso (romano)
        elif re.search(r'^[IVX]+\s*[-–]', texto_elemento):
            inciso_texto = limpar_texto(texto_elemento)
            if inciso_texto and inciso_texto not in artigo["incisos"]:
                artigo["incisos"].append(inciso_texto)

        # Verificar se é alínea
        elif re.search(r'^[a-z]\)', texto_elemento):
            alinea_texto = limpar_texto(texto_elemento)
            if alinea_texto:
                # Adicionar à última estrutura disponível (parágrafo ou artigo)
                if artigo["paragrafos_numerados"]:
                    ultimo_paragrafo = artigo["paragrafos_numerados"][-1]
                    if alinea_texto not in ultimo_paragrafo["alineas"]:
                        ultimo_paragrafo["alineas"].append(alinea_texto)

        elemento_atual = elemento_atual.find_next_sibling('p')

    return artigo

def scraper_corpus927_v2():
    """
    Função principal do scraper v2
    """
    print("=" * 60)
    print("🔍 SCRAPER CPP V2 - FONTE CORPUS927")
    print("=" * 60)

    url = "https://corpus927.enfam.jus.br/legislacao/cpp-41"

    try:
        print(f"📡 Fazendo requisição para: {url}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        print(f"✅ Requisição bem-sucedida. Status: {response.status_code}")
        print(f"📄 Tamanho do conteúdo: {len(response.content):,} bytes")

        # Parse do HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Encontrar todos os artigos
        links_artigos = soup.find_all('a', class_='numero_artigo')
        print(f"🔍 Encontrados {len(links_artigos)} links de artigos")

        artigos_extraidos = []
        artigos_processados = set()  # Para evitar duplicações

        for i, link_artigo in enumerate(links_artigos):
            try:
                artigo = processar_artigo_v2(link_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])

                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")

            except Exception as e:
                print(f"⚠️ Erro ao processar artigo {i+1}: {e}")
                continue

        # Ordenar por número do artigo
        def extrair_numero_para_ordenacao(artigo_id):
            match = re.search(r'Art\.\s*(\d+)', artigo_id)
            return int(match.group(1)) if match else 0

        artigos_extraidos.sort(key=lambda x: extrair_numero_para_ordenacao(x["artigo"]))

        print(f"✅ Extração concluída. Total: {len(artigos_extraidos)} artigos únicos")

        # Salvar arquivos
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Arquivo JSON
        nome_arquivo_json = 'cpp_corpus927_v2.json'
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_extraidos, f, ensure_ascii=False, indent=2)
        print(f"💾 Arquivo JSON salvo: {nome_arquivo_json}")

        # Arquivo JavaScript
        nome_arquivo_js = 'cpp_corpus927_v2.js'
        js_content = f"""// Código de Processo Penal Brasileiro - Fonte Corpus927 V2
// Extraído de: {url}
// Gerado em: {timestamp}
// Total de artigos: {len(artigos_extraidos)}

const codigoProcessoPenalArtigos = {json.dumps(artigos_extraidos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""

        with open(nome_arquivo_js, 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"💾 Arquivo JavaScript salvo: {nome_arquivo_js}")

        # Estatísticas
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   • Total de artigos: {len(artigos_extraidos)}")
        if artigos_extraidos:
            print(f"   • Primeiro artigo: {artigos_extraidos[0]['artigo']}")
            print(f"   • Último artigo: {artigos_extraidos[-1]['artigo']}")

            # Contar estruturas
            total_incisos = sum(len(art['incisos']) for art in artigos_extraidos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos_extraidos)
            total_paragrafos_unicos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'])

            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")

        return artigos_extraidos

    except requests.RequestException as e:
        print(f"❌ Erro na requisição: {e}")
        return []
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return []

if __name__ == '__main__':
    artigos = scraper_corpus927_v2()
    if artigos:
        print(f"\n🎉 Scraping V2 concluído com sucesso!")
        print(f"📁 Arquivos gerados: cpp_corpus927_v2.json e cpp_corpus927_v2.js")
    else:
        print(f"\n❌ Falha no scraping V2!")
