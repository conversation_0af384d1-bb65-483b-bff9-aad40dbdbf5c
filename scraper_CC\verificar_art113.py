#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON gerado
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Procurar artigo 113
art113 = [a for a in data['artigos'] if a['numero'] == '113']

if art113:
    print("=== ARTIGO 113 ENCONTRADO ===")
    artigo = art113[0]
    print(f"Número: {artigo['numero']}")
    print(f"Caput: {artigo['caput']}")
    
    # Mostrar detalhes dos incisos
    if artigo['incisos']:
        print(f"\n=== INCISOS ({len(artigo['incisos'])}) ===")
        for inciso, dados in artigo['incisos'].items():
            print(f"{inciso}: {dados['texto']}")
    else:
        print("\n=== INCISOS ===")
        print("Nenhum inciso encontrado")
    
    # Mostrar detalhes dos parágrafos
    if artigo['paragrafos']:
        print(f"\n=== PARÁGRAFOS ({len(artigo['paragrafos'])}) ===")
        for paragrafo, dados in artigo['paragrafos'].items():
            print(f"{paragrafo}: {dados['texto'][:200]}...")
    else:
        print("\n=== PARÁGRAFOS ===")
        print("Nenhum parágrafo encontrado")
            
else:
    print("=== ARTIGO 113 NÃO ENCONTRADO ===")
