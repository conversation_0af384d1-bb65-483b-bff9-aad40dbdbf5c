<?php
// Incluir sistema de cache busting
require_once 'includes/cache_buster.php';
include_once("../session_config.php");
require_once '../conexao_POST.php';

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

// Buscar nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0) 
    ? pg_fetch_assoc($resultado_nome)['nome'] 
    : "Usuário";
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexJus - Constituição Federal</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <meta name="app-version" content="<?php echo cache_version(); ?>">
    <link rel="stylesheet" href="<?php echo cache_css('style.css'); ?>">
    <link rel="stylesheet" href="<?php echo cache_css('css/sistema-revisao.css'); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <div class="header-barra">
            <div class="header-left">
                <div class="logo">
                    <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario ?? 'Usuário'); // Adicionado fallback para $nome_usuario ?></span>
                </div>
                <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn" title="Alternar tema">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                </div>
            </div>
    </div>


</head>
<body>
    <div class="container">
        <!-- Barra de Navegação Superior -->
        <nav class="top-nav">
            <div class="nav-brand">
                <i class="fas fa-balance-scale"></i>
                <span>LexJus</span>
            </div>
            <div class="nav-controls">
                <button id="btnFavoritos" class="nav-btn" title="Ver Favoritos">
                    <i class="fas fa-heart"></i>
                    <span class="btn-text">Favoritos</span>
                    <span id="favoritosCount" class="count-badge">0</span>
                </button>
                <button id="btnListas" class="nav-btn" title="Ver Minhas Listas">
                    <i class="fas fa-bookmark"></i>
                    <span class="btn-text">Minhas Listas</span>
                    <span id="listasCount" class="count-badge">0</span>
                </button>

            </div>
        </nav>

        <div class="header">
            <h1>Constituição Federal</h1>
            <p class="subtitle">Explore os artigos da Constituição Federal do Brasil de forma moderna e interativa</p>

            <!-- Barra de busca -->
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-box" placeholder="Buscar artigos..." id="searchInput">
            </div>

            <!-- Barra de Progresso -->
            <div class="progress-container">
                <div class="progress-header">
                    <span class="progress-label">Progresso de Leitura</span>
                    <span class="progress-text" id="progressText">0 de 0 artigos lidos (0%)</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>

            <!-- Estatísticas -->
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number" id="totalArtigos">0</div>
                    <div class="stat-label">Artigos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="artigosLidos">0</div>
                    <div class="stat-label">Lidos</div>
                </div>
            </div>
        </div>

        <div class="grid-container">
            <?php
            $json_data = file_get_contents('artigos.json');
            $artigos = json_decode($json_data, true);

            if ($artigos) {
                foreach ($artigos as $index => $artigo_data) {
                    // Prepara os dados para os atributos data-*
                    $numero_artigo = htmlspecialchars($artigo_data['artigo'] ?? 'Artigo não encontrado');
                    $caput_artigo = htmlspecialchars($artigo_data['caput'] ?? '');

                    $incisos_json = '[]';
                    if (!empty($artigo_data['incisos'])) {
                        $incisos_formatados = [];
                        foreach ($artigo_data['incisos'] as $inciso) {
                            $incisos_formatados[] = htmlspecialchars($inciso);
                        }
                        $incisos_json = htmlspecialchars(json_encode($incisos_formatados), ENT_QUOTES, 'UTF-8');
                    }

                    $paragrafo_unico_artigo = '';
                    if (isset($artigo_data['paragrafo_unico']) && $artigo_data['paragrafo_unico']) {
                        $paragrafo_unico_artigo = htmlspecialchars($artigo_data['paragrafo_unico']);
                    }

                    // Adiciona parágrafos numerados se existirem
                    $paragrafos_numerados_json = '[]';
                    if (!empty($artigo_data['paragrafos_numerados'])) {
                        $paragrafos_formatados = [];
                        foreach($artigo_data['paragrafos_numerados'] as $pn) {
                            $paragrafos_formatados[] = [
                                'numero' => htmlspecialchars($pn['numero'] ?? ''),
                                'texto' => htmlspecialchars($pn['texto'] ?? ''),
                                'alineas' => array_map('htmlspecialchars', $pn['alineas'] ?? [])
                            ];
                        }
                        $paragrafos_numerados_json = htmlspecialchars(json_encode($paragrafos_formatados), ENT_QUOTES, 'UTF-8');
                    }

                    // Adiciona alíneas do artigo se existirem (aquelas não aninhadas em parágrafos)
                    $alineas_do_artigo_json = '[]';
                     if (!empty($artigo_data['alineas_do_artigo'])) {
                        $alineas_artigo_formatadas = [];
                        foreach ($artigo_data['alineas_do_artigo'] as $alinea_art) {
                            $alineas_artigo_formatadas[] = htmlspecialchars($alinea_art);
                        }
                        $alineas_do_artigo_json = htmlspecialchars(json_encode($alineas_artigo_formatadas), ENT_QUOTES, 'UTF-8');
                    }


                    // Prepara a string com todos os atributos data-*
                    $card_data_attributes = sprintf(
                        'data-artigo="%s" data-caput="%s" data-incisos="%s" data-paragrafo-unico="%s" data-paragrafos-numerados="%s" data-alineas-artigo="%s"',
                        $numero_artigo,             // Já escapado com htmlspecialchars
                        $caput_artigo,              // Já escapado com htmlspecialchars
                        $incisos_json,              // Resultado de htmlspecialchars(json_encode(...), ENT_QUOTES)
                        $paragrafo_unico_artigo,    // Já escapado com htmlspecialchars
                        $paragrafos_numerados_json, // Resultado de htmlspecialchars(json_encode(...), ENT_QUOTES)
                        $alineas_do_artigo_json     // Resultado de htmlspecialchars(json_encode(...), ENT_QUOTES)
                    );

                    // Imprime a tag do card
                    echo '<div class="card" ' . $card_data_attributes . ' style="animation-delay: ' . ($index * 0.05) . 's;">';
                    echo '  <h2>' . $numero_artigo . '</h2>';
                    echo '</div>';
                }
            } else {
                echo '<p>Não foi possível carregar os artigos.</p>';
            }
            ?>
        </div>
    </div>

    <!-- Loading Global -->
    <div id="globalLoading" class="global-loading">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">
                <h3>Carregando LexJus</h3>
                <p id="loadingStatus">Inicializando sistema...</p>
                <div class="loading-progress">
                    <div class="loading-progress-bar" id="loadingProgressBar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estrutura do Modal -->
    <div id="artigoModal" class="modal">
        <div class="modal-content">
            <span class="modal-close-btn">&times;</span>

            <!-- Header limpo -->
            <div class="modal-header">
                <h2 id="modalArtigoNumero"></h2>
            </div>
            <div id="modalArtigoConteudo">
                <div id="modalArtigoCaputContainer">
                    <strong>Caput:</strong>
                    <div id="modalArtigoCaput"></div>
                </div>
                <div id="modalArtigoIncisosContainer">
                    <p><strong>Incisos:</strong></p>
                    <ul id="modalArtigoIncisosLista"></ul>
                </div>
                <div id="modalArtigoParagrafoUnicoContainer">
                    <p><strong>Parágrafo Único:</strong> <span id="modalArtigoParagrafoUnico"></span></p>
                </div>
                <div id="modalArtigoParagrafosNumeradosContainer">
                     <p><strong>Parágrafos:</strong></p>
                     <div id="modalArtigoParagrafosNumeradosLista"></div>
                </div>
                <div id="modalArtigoAlineasArtigoContainer" style="display: none;">
                    <ul id="modalArtigoAlineasArtigoLista"></ul>
                </div>
            </div>

            <!-- Controles do Modal -->
            <div class="modal-controls">
                <div class="modal-actions">
                    <button id="btnFavoritar" class="btn-favoritar" title="Adicionar aos favoritos">
                        <i class="far fa-heart"></i>
                        <span>Favoritar</span>
                    </button>
                    <button id="btnAdicionarLista" class="btn-adicionar-lista" title="Adicionar a uma lista">
                        <i class="fas fa-bookmark"></i>
                        <span>Adicionar à Lista</span>
                    </button>
                    <button id="btnGerenciarListas" class="btn-gerenciar-listas" title="Gerenciar listas deste artigo" style="display: none;">
                        <i class="fas fa-list"></i>
                        <span>Gerenciar Listas</span>
                    </button>
                    <button id="btnMarcarLido" class="btn-marcar-lido" title="Marcar como lido">
                        <i class="far fa-check-circle"></i>
                        <span>Marcar como Lido</span>
                    </button>
                </div>
            </div>

            <!-- Seção de Notas -->
            <div class="notas-section">
                <div class="notas-header">
                    <h3><i class="fas fa-sticky-note"></i> Minhas Notas</h3>
                    <button id="btnAdicionarNota" class="btn-adicionar-nota">
                        <i class="fas fa-plus"></i>
                        Adicionar Nota
                    </button>
                </div>

                <div id="notasContainer" class="notas-container">
                    <div id="nenhumaNota" class="nenhuma-nota">
                        <i class="fas fa-sticky-note"></i>
                        <p>Nenhuma nota adicionada ainda.</p>
                        <small>Clique em "Adicionar Nota" para começar a anotar seus estudos.</small>
                    </div>
                </div>

                <!-- Formulário para nova nota -->
                <div id="formNovaNota" class="form-nova-nota" style="display: none;">
                    <!-- Barra de ferramentas de formatação -->
                    <div class="editor-toolbar">
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" data-command="bold" title="Negrito (Ctrl+B)">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-command="italic" title="Itálico (Ctrl+I)">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button type="button" class="toolbar-btn" data-command="underline" title="Sublinhado (Ctrl+U)">
                                <i class="fas fa-underline"></i>
                            </button>
                        </div>

                        <div class="toolbar-group">
                            <select class="toolbar-select" data-command="fontSize" title="Tamanho da fonte">
                                <option value="12px">Pequeno</option>
                                <option value="14px" selected>Normal</option>
                                <option value="16px">Médio</option>
                                <option value="18px">Grande</option>
                            </select>
                        </div>

                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn color-btn" data-color="#000000" title="Preto" style="background: #000000;"></button>
                            <button type="button" class="toolbar-btn color-btn" data-color="#e74c3c" title="Vermelho" style="background: #e74c3c;"></button>
                            <button type="button" class="toolbar-btn color-btn" data-color="#3498db" title="Azul" style="background: #3498db;"></button>
                            <button type="button" class="toolbar-btn color-btn" data-color="#27ae60" title="Verde" style="background: #27ae60;"></button>
                            <button type="button" class="toolbar-btn color-btn" data-color="#f39c12" title="Laranja" style="background: #f39c12;"></button>
                            <button type="button" class="toolbar-btn color-btn" data-color="#9b59b6" title="Roxo" style="background: #9b59b6;"></button>
                        </div>

                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" data-command="removeFormat" title="Remover formatação">
                                <i class="fas fa-remove-format"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Editor de texto rico -->
                    <div id="editorNovaNota" class="editor-content" contenteditable="true" placeholder="Digite sua nota aqui..."></div>

                    <div class="form-actions">
                        <button id="btnSalvarNota" class="btn-salvar-nota">
                            <i class="fas fa-save"></i>
                            Salvar
                        </button>
                        <button id="btnCancelarNota" class="btn-cancelar-nota">
                            <i class="fas fa-times"></i>
                            Cancelar
                        </button>
                    </div>
                </div>
            </div>

            <!-- Navegação do Modal (Rodapé) -->
            <div class="modal-navigation">
                <button id="btnAnterior" class="modal-nav-btn modal-nav-prev" title="Artigo anterior (←)">
                    <i class="fas fa-chevron-left"></i>
                    <span>Anterior</span>
                </button>

                <div class="modal-nav-spacer"></div>

                <button id="btnProximo" class="modal-nav-btn modal-nav-next" title="Próximo artigo (→)">
                    <span>Próximo</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Modal de Favoritos -->
    <div id="favoritosModal" class="modal">
        <div class="modal-content modal-favoritos">
            <span class="modal-close-btn">&times;</span>
            <h2><i class="fas fa-heart"></i> Meus Favoritos</h2>

            <div id="favoritosContainer" class="favoritos-container">
                <div id="nenhumFavorito" class="nenhum-favorito">
                    <i class="fas fa-heart"></i>
                    <p>Nenhum artigo favoritado ainda.</p>
                    <small>Clique no coração nos artigos para adicioná-los aos favoritos.</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Listas -->
    <div id="listasModal" class="modal">
        <div class="modal-content modal-listas">
            <span class="modal-close-btn">&times;</span>
            <h2><i class="fas fa-bookmark"></i> Minhas Listas</h2>

            <div class="listas-header">
                <button id="btnNovaLista" class="btn-nova-lista">
                    <i class="fas fa-plus"></i>
                    Nova Lista
                </button>
            </div>

            <div id="listasContainer" class="listas-container">
                <div id="nenhumaLista" class="nenhuma-lista">
                    <i class="fas fa-bookmark"></i>
                    <p>Nenhuma lista criada ainda.</p>
                    <small>Crie listas personalizadas para organizar seus estudos.</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Nova Lista -->
    <div id="novaListaModal" class="modal">
        <div class="modal-content modal-nova-lista">
            <span class="modal-close-btn">&times;</span>
            <h2><i class="fas fa-bookmark"></i> <span id="tituloModalLista">Nova Lista</span></h2>

            <form id="formNovaLista" class="form-nova-lista">
                <div class="form-group">
                    <label for="nomeListaInput">Nome da Lista:</label>
                    <input type="text" id="nomeListaInput" class="input-nome-lista" placeholder="Ex: Direitos Fundamentais" required>
                </div>

                <div class="form-group">
                    <label for="corListaInput">Cor da Lista:</label>
                    <div class="cores-lista">
                        <input type="radio" id="cor1" name="corLista" value="#e74c3c" checked>
                        <label for="cor1" class="cor-option" style="background: #e74c3c;"></label>

                        <input type="radio" id="cor2" name="corLista" value="#3498db">
                        <label for="cor2" class="cor-option" style="background: #3498db;"></label>

                        <input type="radio" id="cor3" name="corLista" value="#2ecc71">
                        <label for="cor3" class="cor-option" style="background: #2ecc71;"></label>

                        <input type="radio" id="cor4" name="corLista" value="#f39c12">
                        <label for="cor4" class="cor-option" style="background: #f39c12;"></label>

                        <input type="radio" id="cor5" name="corLista" value="#9b59b6">
                        <label for="cor5" class="cor-option" style="background: #9b59b6;"></label>

                        <input type="radio" id="cor6" name="corLista" value="#34495e">
                        <label for="cor6" class="cor-option" style="background: #34495e;"></label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-salvar-lista">
                        <i class="fas fa-save"></i>
                        Salvar Lista
                    </button>
                    <button type="button" id="btnCancelarLista" class="btn-cancelar-lista">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Adicionar à Lista -->
    <div id="adicionarListaModal" class="modal">
        <div class="modal-content modal-adicionar-lista">
            <span class="modal-close-btn">&times;</span>
            <h2><i class="fas fa-bookmark"></i> Adicionar à Lista</h2>
            <p class="artigo-selecionado">Artigo: <span id="artigoParaLista"></span></p>

            <div id="listasDisponiveis" class="listas-disponiveis">
                <div class="nenhuma-lista-disponivel">
                    <i class="fas fa-bookmark"></i>
                    <p>Nenhuma lista criada ainda.</p>
                    <button id="btnCriarPrimeiraLista" class="btn-criar-primeira-lista">
                        <i class="fas fa-plus"></i>
                        Criar Primeira Lista
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Gerenciar Listas -->
    <div id="gerenciarListasModal" class="modal">
        <div class="modal-content modal-gerenciar-listas">
            <span class="modal-close-btn">&times;</span>
            <h2><i class="fas fa-list"></i> Gerenciar Listas</h2>
            <p class="artigo-selecionado">Artigo: <span id="artigoParaGerenciar"></span></p>

            <div id="listasDoArtigo" class="listas-do-artigo">
                <div class="nenhuma-lista-artigo">
                    <i class="fas fa-bookmark"></i>
                    <p>Este artigo não está em nenhuma lista.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Manager (carregar primeiro) -->
    <script src="<?php echo cache_js('js/cache-manager.js'); ?>"></script>

    <!-- Scripts principais -->
    <script src="<?php echo cache_js('script.js'); ?>"></script>
    <script src="<?php echo cache_js('js/sistema-revisao.js'); ?>"></script>
</body>
</html>
