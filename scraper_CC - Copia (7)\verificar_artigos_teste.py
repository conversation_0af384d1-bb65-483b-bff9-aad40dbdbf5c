#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON gerado
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Verificar alguns artigos específicos
artigos_teste = ['1', '2', '3', '4', '5']

for num in artigos_teste:
    artigo = [a for a in data['artigos'] if a['numero'] == num]
    
    if artigo:
        art = artigo[0]
        print(f"=== ARTIGO {num} ===")
        print(f"Caput: {art['caput'][:100]}...")
        print(f"Incisos: {len(art['incisos'])} - {list(art['incisos'].keys())}")
        print(f"Parágrafos: {len(art['paragrafos'])} - {list(art['paragrafos'].keys())}")
        print()
    else:
        print(f"=== ARTIGO {num} NÃO ENCONTRADO ===")
        print()

print(f"Total de artigos extraídos: {len(data['artigos'])}")
