#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

# Carregar o JSON gerado
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Procurar artigo 113
art113 = [a for a in data['artigos'] if a['numero'] == '113']

if art113:
    artigo = art113[0]
    print("=== ARTIGO 113 - ANÁLISE DO PARÁGRAFO ===")
    
    if artigo['paragrafos']:
        for paragrafo, dados in artigo['paragrafos'].items():
            texto = dados['texto']
            print(f"\nParágrafo {paragrafo}:")
            print(f"Texto completo: {texto}")
            print(f"Tamanho: {len(texto)} caracteres")
            
            # Testar diferentes padrões regex para incisos
            print(f"\n--- TESTANDO PADRÕES REGEX ---")
            
            # Padrão 1: Original
            padrao1 = r'([IVX]+)\s*-\s*([^;]+(?:;|\.(?:\s*\([^)]*\))*\s*$))'
            matches1 = re.findall(padrao1, texto, re.IGNORECASE)
            print(f"Padrão 1: {len(matches1)} matches - {matches1}")
            
            # Padrão 2: Mais flexível
            padrao2 = r'([IVX]+)\s*-\s*([^;]+;?)'
            matches2 = re.findall(padrao2, texto, re.IGNORECASE)
            print(f"Padrão 2: {len(matches2)} matches - {matches2}")
            
            # Padrão 3: Ainda mais flexível
            padrao3 = r'([IVX]+)\s*-\s*([^IVX]+?)(?=\s*[IVX]+\s*-|$)'
            matches3 = re.findall(padrao3, texto, re.IGNORECASE | re.DOTALL)
            print(f"Padrão 3: {len(matches3)} matches - {matches3}")
            
            # Padrão 4: Buscar por quebras de linha ou pontos
            padrao4 = r'([IVX]+)\s*-\s*([^;]+?)(?=\s*[IVX]+\s*-|;|\.(?:\s*\([^)]*\))*\s*$)'
            matches4 = re.findall(padrao4, texto, re.IGNORECASE | re.DOTALL)
            print(f"Padrão 4: {len(matches4)} matches - {matches4}")
            
            # Mostrar onde estão os números romanos no texto
            print(f"\n--- POSIÇÕES DOS NÚMEROS ROMANOS ---")
            for match in re.finditer(r'\b([IVX]+)\s*-', texto, re.IGNORECASE):
                start, end = match.span()
                print(f"'{match.group(1)}' na posição {start}-{end}: ...{texto[max(0,start-20):end+50]}...")
else:
    print("Artigo 113 não encontrado")
