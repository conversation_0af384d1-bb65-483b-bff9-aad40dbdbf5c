#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para corrigir a ordem dos artigos no arquivo CPP
Especificamente para corrigir a ordem dos artigos 3º-A até 3º-F
"""

import json
import re
from datetime import datetime

def extrair_numero_artigo(artigo_id):
    """
    Extrai o número do artigo para ordenação correta
    """
    # Padrão para artigos normais: Art. 123º
    match_normal = re.search(r'Art\.\s*(\d+)[ºo°]?$', artigo_id)
    if match_normal:
        return (int(match_normal.group(1)), '')
    
    # Padrão para artigos com sufixo: Art. 123º-A
    match_sufixo = re.search(r'Art\.\s*(\d+)[ºo°]?-([A-Z])', artigo_id)
    if match_sufixo:
        return (int(match_sufixo.group(1)), match_sufixo.group(2))
    
    # Padrão para artigos com sufixo sem º: Art. 123-A
    match_sufixo_sem_grau = re.search(r'Art\.\s*(\d+)-([A-Z])', artigo_id)
    if match_sufixo_sem_grau:
        return (int(match_sufixo_sem_grau.group(1)), match_sufixo_sem_grau.group(2))
    
    # Se não conseguir extrair, retorna um valor alto para ficar no final
    return (9999, artigo_id)

def ordenar_artigos(artigos):
    """
    Ordena os artigos na sequência correta
    """
    def chave_ordenacao(artigo):
        numero, sufixo = extrair_numero_artigo(artigo['artigo'])
        return (numero, sufixo)
    
    return sorted(artigos, key=chave_ordenacao)

def corrigir_ordem_artigos():
    """
    Corrige a ordem dos artigos no arquivo CPP
    """
    print("=" * 60)
    print("🔧 CORREÇÃO DA ORDEM DOS ARTIGOS - CPP")
    print("=" * 60)
    
    # Carregar arquivo atual
    arquivo_entrada = 'cpp_lexjus_20250620_002948.json'
    
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Arquivo carregado: {len(artigos)} artigos")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return
    
    # Mostrar ordem atual dos primeiros artigos
    print("\n🔍 ORDEM ATUAL (primeiros 10 artigos):")
    for i, artigo in enumerate(artigos[:10]):
        print(f"   {i+1:2d}. {artigo['artigo']}")
    
    # Verificar especificamente os artigos 3º
    artigos_3 = [art for art in artigos if art['artigo'].startswith('Art. 3º')]
    print(f"\n🔍 ARTIGOS 3º ATUAIS ({len(artigos_3)} encontrados):")
    for i, artigo in enumerate(artigos_3):
        print(f"   {i+1}. {artigo['artigo']}")
    
    # Ordenar artigos
    print("\n🔄 Ordenando artigos...")
    artigos_ordenados = ordenar_artigos(artigos)
    
    # Verificar ordem corrigida dos artigos 3º
    artigos_3_ordenados = [art for art in artigos_ordenados if art['artigo'].startswith('Art. 3º')]
    print(f"\n✅ ARTIGOS 3º CORRIGIDOS ({len(artigos_3_ordenados)} encontrados):")
    for i, artigo in enumerate(artigos_3_ordenados):
        print(f"   {i+1}. {artigo['artigo']}")
    
    # Mostrar ordem corrigida dos primeiros artigos
    print("\n✅ ORDEM CORRIGIDA (primeiros 10 artigos):")
    for i, artigo in enumerate(artigos_ordenados[:10]):
        print(f"   {i+1:2d}. {artigo['artigo']}")
    
    # Salvar arquivo corrigido
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    arquivo_saida_json = f'cpp_lexjus_ordenado_{timestamp}.json'
    arquivo_saida_js = f'cpp_lexjus_ordenado_{timestamp}.js'
    
    try:
        # Salvar JSON
        with open(arquivo_saida_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_ordenados, f, ensure_ascii=False, indent=2)
        print(f"\n💾 Arquivo JSON salvo: {arquivo_saida_json}")
        
        # Salvar JavaScript
        js_content = f"""// Código de Processo Penal Brasileiro - LexJus (Ordem Corrigida)
// Gerado automaticamente em: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// Total de artigos: {len(artigos_ordenados)}
// Status: ORDEM CORRIGIDA - Artigos em sequência legal correta

const codigoProcessoPenalArtigos = {json.dumps(artigos_ordenados, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Função para obter estatísticas do CPP
function obterEstatisticasCPP() {{
    const stats = {{
        totalArtigos: codigoProcessoPenalArtigos.length,
        artigosComIncisos: codigoProcessoPenalArtigos.filter(art => art.incisos.length > 0).length,
        artigosComParagrafos: codigoProcessoPenalArtigos.filter(art => art.paragrafos_numerados.length > 0).length,
        artigosComParagrafoUnico: codigoProcessoPenalArtigos.filter(art => art.paragrafo_unico).length,
        primeiroArtigo: codigoProcessoPenalArtigos[0]?.artigo || 'N/A',
        ultimoArtigo: codigoProcessoPenalArtigos[codigoProcessoPenalArtigos.length - 1]?.artigo || 'N/A'
    }};
    return stats;
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto,
        obterEstatisticasCPP
    }};
}}

// Log de inicialização
console.log('📚 CPP carregado (ordem corrigida):', obterEstatisticasCPP());
"""
        
        with open(arquivo_saida_js, 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"💾 Arquivo JavaScript salvo: {arquivo_saida_js}")
        
    except Exception as e:
        print(f"❌ Erro ao salvar arquivos: {e}")
        return
    
    print("\n" + "=" * 60)
    print("🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!")
    print("=" * 60)
    print(f"📄 Arquivo JSON: {arquivo_saida_json}")
    print(f"📄 Arquivo JS: {arquivo_saida_js}")
    print("=" * 60)
    print("✅ CORREÇÕES APLICADAS:")
    print("   • Artigos ordenados em sequência legal correta")
    print("   • Art. 3º-A, 3º-B, 3º-C, 3º-D, 3º-E, 3º-F em ordem")
    print("   • Todos os demais artigos mantidos em ordem")
    print("=" * 60)
    print("💡 PRÓXIMO PASSO:")
    print(f"   Use o arquivo: {arquivo_saida_js}")
    print("=" * 60)

if __name__ == '__main__':
    corrigir_ordem_artigos()
