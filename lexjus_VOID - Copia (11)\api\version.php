<?php
/**
 * API de Versão do LexJus
 * 
 * Retorna informações sobre a versão atual do sistema
 * para verificação de atualizações
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Incluir sistema de cache busting
require_once '../includes/cache_buster.php';

try {
    // Arquivos importantes para verificar mudanças
    $importantFiles = [
        '../style.css',
        '../script.js',
        '../css/sistema-revisao.css',
        '../js/sistema-revisao.js',
        '../index.php',
        '../artigos.json'
    ];
    
    $latestTime = 0;
    $fileVersions = [];
    
    // Verificar timestamp de cada arquivo
    foreach ($importantFiles as $file) {
        $fullPath = __DIR__ . '/' . $file;
        if (file_exists($fullPath)) {
            $fileTime = filemtime($fullPath);
            $fileName = basename($file);
            $fileVersions[$fileName] = [
                'timestamp' => $fileTime,
                'date' => date('Y-m-d H:i:s', $fileTime),
                'size' => filesize($fullPath)
            ];
            
            if ($fileTime > $latestTime) {
                $latestTime = $fileTime;
            }
        }
    }
    
    // Gerar hash único baseado nos arquivos
    $versionHash = md5(serialize($fileVersions));
    
    // Informações do sistema
    $systemInfo = [
        'version' => $latestTime,
        'version_hash' => $versionHash,
        'last_update' => date('Y-m-d H:i:s', $latestTime),
        'global_version' => CacheBuster::getGlobalVersion(),
        'files' => $fileVersions,
        'server_time' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'system' => [
            'name' => 'LexJus',
            'description' => 'Sistema de Estudo da Constituição Federal',
            'environment' => $_SERVER['SERVER_NAME'] ?? 'localhost'
        ]
    ];
    
    // Log da requisição (opcional, para debug)
    if (isset($_GET['debug']) && $_GET['debug'] === '1') {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'version_requested' => $latestTime
        ];
        
        $logFile = __DIR__ . '/../logs/version_checks.log';
        if (!file_exists(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    echo json_encode($systemInfo, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => 'Erro ao obter informações de versão',
        'details' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
