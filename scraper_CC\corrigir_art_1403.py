#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORREÇÃO ESPECÍFICA PARA ART. 1403º
===================================

Este script corrige o problema específico do Art. 1403º onde os incisos
estão sendo nomeados incorretamente como art1401i e art1401ii.
"""

import re
import json
import os

def corrigir_art_1403():
    """Corrige especificamente o Art. 1403º"""
    
    print("=== CORREÇÃO DO ART. 1403º ===")
    
    # Verificar se o arquivo JSON existe
    arquivo_json = "codigo_civil_formato_lexjus_final.json"
    if not os.path.exists(arquivo_json):
        print(f"❌ Arquivo {arquivo_json} não encontrado!")
        print("Execute primeiro o pipeline de extração completo.")
        return False
    
    # Carregar o JSON
    with open(arquivo_json, 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    print(f"Arquivo carregado: {len(artigos)} artigos")
    
    # Buscar o Art. 1403º
    art_1403 = None
    indice_1403 = -1
    
    for i, artigo in enumerate(artigos):
        if artigo.get('artigo') == 'Art. 1403º' or '1403' in artigo.get('artigo', ''):
            art_1403 = artigo
            indice_1403 = i
            break
    
    if art_1403:
        print(f"✅ Art. 1403º encontrado no índice {indice_1403}")
        print(f"Artigo atual: {art_1403.get('artigo')}")
        print(f"Caput atual: {art_1403.get('caput', '')[:100]}...")
        print(f"Incisos atuais: {len(art_1403.get('incisos', []))}")
        
        # Verificar se já tem o conteúdo correto
        if 'Incumbem ao usufrutuário' in art_1403.get('caput', ''):
            print("✅ Art. 1403º já tem o conteúdo correto!")
            return True
    else:
        print("❌ Art. 1403º não encontrado no JSON!")
        
        # Criar o artigo do zero
        print("Criando Art. 1403º do zero...")
        art_1403 = {
            "artigo": "Art. 1403º",
            "caput": "Incumbem ao usufrutuário:",
            "incisos": [
                "I - as despesas ordinárias de conservação dos bens no estado em que os recebeu;",
                "II - as prestações e os tributos devidos pela posse ou rendimento da coisa usufruída."
            ],
            "paragrafos_numerados": [],
            "paragrafo_unico": None
        }
        
        # Encontrar a posição correta para inserir (após Art. 1402º)
        posicao_insercao = -1
        for i, artigo in enumerate(artigos):
            if '1402' in artigo.get('artigo', ''):
                posicao_insercao = i + 1
                break
        
        if posicao_insercao > 0:
            artigos.insert(posicao_insercao, art_1403)
            print(f"✅ Art. 1403º inserido na posição {posicao_insercao}")
        else:
            # Se não encontrar 1402, inserir no final
            artigos.append(art_1403)
            print("✅ Art. 1403º inserido no final")
    
    # Verificar se o Art. 1403º está correto agora
    art_1403_final = None
    for artigo in artigos:
        if '1403' in artigo.get('artigo', ''):
            art_1403_final = artigo
            break
    
    if art_1403_final:
        print(f"\n=== VERIFICAÇÃO FINAL ===")
        print(f"Artigo: {art_1403_final.get('artigo')}")
        print(f"Caput: {art_1403_final.get('caput')}")
        print(f"Incisos: {art_1403_final.get('incisos', [])}")
        
        # Garantir que tem o formato correto
        if not art_1403_final.get('caput'):
            art_1403_final['caput'] = "Incumbem ao usufrutuário:"
        
        if not art_1403_final.get('incisos'):
            art_1403_final['incisos'] = [
                "I - as despesas ordinárias de conservação dos bens no estado em que os recebeu;",
                "II - as prestações e os tributos devidos pela posse ou rendimento da coisa usufruída."
            ]
        
        # Garantir que tem o artigo correto
        if art_1403_final.get('artigo') != 'Art. 1403º':
            art_1403_final['artigo'] = 'Art. 1403º'
        
        # Garantir estrutura completa
        if 'paragrafos_numerados' not in art_1403_final:
            art_1403_final['paragrafos_numerados'] = []
        
        if 'paragrafo_unico' not in art_1403_final:
            art_1403_final['paragrafo_unico'] = None
    
    # Salvar o arquivo corrigido
    arquivo_corrigido = "codigo_civil_formato_lexjus_final_corrigido.json"
    with open(arquivo_corrigido, 'w', encoding='utf-8') as f:
        json.dump(artigos, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Arquivo corrigido salvo: {arquivo_corrigido}")
    print(f"Total de artigos: {len(artigos)}")
    
    # Verificar artigos próximos ao 1403
    print(f"\n=== ARTIGOS PRÓXIMOS AO 1403 ===")
    for artigo in artigos:
        artigo_num = artigo.get('artigo', '')
        if any(num in artigo_num for num in ['1401', '1402', '1403', '1404', '1405']):
            print(f"{artigo_num}: {artigo.get('caput', '')[:50]}...")
    
    return True

def verificar_correcao():
    """Verifica se a correção foi aplicada corretamente"""
    
    arquivo_corrigido = "codigo_civil_formato_lexjus_final_corrigido.json"
    if not os.path.exists(arquivo_corrigido):
        print("❌ Arquivo corrigido não encontrado!")
        return False
    
    with open(arquivo_corrigido, 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    # Buscar o Art. 1403º
    for artigo in artigos:
        if '1403' in artigo.get('artigo', ''):
            print(f"\n=== VERIFICAÇÃO DA CORREÇÃO ===")
            print(f"Artigo: {artigo.get('artigo')}")
            print(f"Caput: {artigo.get('caput')}")
            print(f"Incisos: {len(artigo.get('incisos', []))}")
            for i, inciso in enumerate(artigo.get('incisos', [])):
                print(f"  {i+1}. {inciso}")
            
            if 'Incumbem ao usufrutuário' in artigo.get('caput', ''):
                print("✅ Art. 1403º corrigido com sucesso!")
                return True
            else:
                print("❌ Art. 1403º ainda não está correto!")
                return False
    
    print("❌ Art. 1403º não encontrado!")
    return False

if __name__ == "__main__":
    if corrigir_art_1403():
        verificar_correcao()
