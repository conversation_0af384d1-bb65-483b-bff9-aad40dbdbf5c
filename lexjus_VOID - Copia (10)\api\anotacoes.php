<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

switch ($metodo) {
    case 'GET':
        // Obter todas as anotações do usuário
        if (isset($_GET['artigo_numero'])) {
            // Buscar anotações de um artigo específico
            $artigo_numero = $_GET['artigo_numero'];
            $query = "SELECT id, artigo_numero, texto, data_criacao, data_atualizacao 
                     FROM appestudo.lexjus_anotacoes 
                     WHERE usuario_id = $1 AND artigo_numero = $2 
                     ORDER BY data_criacao DESC";
            
            $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero]);
        } else {
            // Buscar todas as anotações do usuário
            $query = "SELECT id, artigo_numero, texto, data_criacao, data_atualizacao 
                     FROM appestudo.lexjus_anotacoes 
                     WHERE usuario_id = $1 
                     ORDER BY data_atualizacao DESC";
            
            $result = pg_query_params($conexao, $query, [$usuario_id]);
        }
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao consultar anotações']);
            exit;
        }
        
        $anotacoes = [];
        while ($row = pg_fetch_assoc($result)) {
            $anotacoes[] = [
                'id' => (int)$row['id'],
                'artigo_numero' => $row['artigo_numero'],
                'texto' => $row['texto'],
                'data_criacao' => $row['data_criacao'],
                'data_atualizacao' => $row['data_atualizacao']
            ];
        }
        
        echo json_encode(['anotacoes' => $anotacoes]);
        break;
        
    case 'POST':
        // Criar nova anotação
        if (!isset($dados['artigo_numero']) || !isset($dados['texto'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }
        
        $artigo_numero = $dados['artigo_numero'];
        $texto = $dados['texto'];
        
        $query = "INSERT INTO appestudo.lexjus_anotacoes (usuario_id, artigo_numero, texto) 
                 VALUES ($1, $2, $3) 
                 RETURNING id";
        
        $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero, $texto]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao criar anotação']);
            exit;
        }
        
        $row = pg_fetch_assoc($result);
        echo json_encode([
            'sucesso' => true, 
            'mensagem' => 'Anotação criada com sucesso',
            'id' => (int)$row['id']
        ]);
        break;
        
    case 'PUT':
        // Atualizar anotação existente
        if (!isset($dados['id']) || !isset($dados['texto'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }
        
        $id = $dados['id'];
        $texto = $dados['texto'];
        
        // Verificar se a anotação pertence ao usuário
        $query_verificar = "SELECT id FROM appestudo.lexjus_anotacoes 
                           WHERE id = $1 AND usuario_id = $2";
        
        $result_verificar = pg_query_params($conexao, $query_verificar, [$id, $usuario_id]);
        
        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Anotação não encontrada ou sem permissão']);
            exit;
        }
        
        $query = "UPDATE appestudo.lexjus_anotacoes 
                 SET texto = $1, data_atualizacao = CURRENT_TIMESTAMP 
                 WHERE id = $2 AND usuario_id = $3";
        
        $result = pg_query_params($conexao, $query, [$texto, $id, $usuario_id]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao atualizar anotação']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Anotação atualizada com sucesso']);
        break;
        
    case 'DELETE':
        // Deletar anotação
        if (!isset($dados['id'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'ID da anotação não informado']);
            exit;
        }
        
        $id = $dados['id'];
        
        // Verificar se a anotação pertence ao usuário
        $query_verificar = "SELECT id FROM appestudo.lexjus_anotacoes 
                           WHERE id = $1 AND usuario_id = $2";
        
        $result_verificar = pg_query_params($conexao, $query_verificar, [$id, $usuario_id]);
        
        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Anotação não encontrada ou sem permissão']);
            exit;
        }
        
        $query = "DELETE FROM appestudo.lexjus_anotacoes 
                 WHERE id = $1 AND usuario_id = $2";
        
        $result = pg_query_params($conexao, $query, [$id, $usuario_id]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao deletar anotação']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Anotação deletada com sucesso']);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>
