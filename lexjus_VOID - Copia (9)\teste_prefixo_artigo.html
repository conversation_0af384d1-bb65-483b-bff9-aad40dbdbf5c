<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Prefixo "Art." no Mo<PERSON> de <PERSON>ão</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .preview-modal {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: white;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        .artigo-info h3 {
            margin: 0;
            color: #007bff;
            font-size: 1.8rem;
        }
        .artigo-meta {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            color: #666;
        }
        .sessao-progresso {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
            font-weight: 600;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste - Prefixo "Art." no Modal de Revisão</h1>
        <p>Esta página testa se o prefixo "Art." está sendo exibido corretamente no modal de revisão.</p>

        <!-- Teste Visual -->
        <div class="test-section">
            <h2>👁️ Preview Visual</h2>
            <p>Assim deve aparecer no modal de revisão:</p>
            
            <div class="preview-modal">
                <div class="modal-header">
                    <div class="artigo-info">
                        <h3 id="previewArtigoNumero">Art. 5º</h3>
                        <div class="artigo-meta">
                            <span class="meta-item">
                                <i class="fas fa-repeat"></i>
                                <span>3</span> repetições
                            </span>
                            <span class="meta-item">
                                <i class="fas fa-star"></i>
                                Facilidade: <span>2.8</span>
                            </span>
                        </div>
                    </div>
                    <div class="sessao-progresso">
                        <span>1</span> / <span>5</span>
                    </div>
                </div>
                <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <p><strong>✅ Correto:</strong> O título mostra "Art. 5º" em vez de apenas "5º"</p>
                </div>
            </div>
        </div>

        <!-- Teste de Função JavaScript -->
        <div class="test-section">
            <h2>🔧 Teste da Função JavaScript</h2>
            <p>Teste da função que atualiza o número do artigo:</p>
            
            <div>
                <label>Número do artigo:</label>
                <input type="text" id="numeroTeste" value="5º" placeholder="Ex: 5º, 37, 1">
                <button class="test-button" onclick="testarFuncaoAtualizacao()">🧪 Testar Função</button>
            </div>
            
            <div id="resultadoTeste" class="result-box">
                <p>Clique no botão para testar...</p>
            </div>
        </div>

        <!-- Teste de Diferentes Formatos -->
        <div class="test-section">
            <h2>📝 Teste de Diferentes Formatos</h2>
            <p>Teste com diferentes formatos de números de artigos:</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <button class="test-button" onclick="testarFormato('1')">Artigo 1</button>
                <button class="test-button" onclick="testarFormato('5º')">Artigo 5º</button>
                <button class="test-button" onclick="testarFormato('37')">Artigo 37</button>
                <button class="test-button" onclick="testarFormato('142')">Artigo 142</button>
                <button class="test-button" onclick="testarFormato('1º')">Artigo 1º</button>
                <button class="test-button" onclick="testarFormato('2º')">Artigo 2º</button>
            </div>
            
            <div id="resultadoFormatos" class="result-box">
                <p>Clique nos botões acima para testar diferentes formatos...</p>
            </div>
        </div>

        <!-- Verificação do Sistema Real -->
        <div class="test-section">
            <h2>🔗 Verificação do Sistema Real</h2>
            <p>Links para testar no sistema real:</p>
            
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="index.php" target="_blank" class="test-button" style="text-decoration: none;">📖 Abrir LexJus</a>
                <button class="test-button" onclick="abrirSistemaRevisao()">🧠 Abrir Sistema de Revisão</button>
                <button class="test-button" onclick="verificarSistemaCarregado()">🔍 Verificar Sistema</button>
            </div>
            
            <div id="resultadoSistema" class="result-box">
                <p>Use os botões acima para verificar o sistema real...</p>
            </div>
        </div>

        <!-- Instruções -->
        <div class="test-section">
            <h2>📋 Instruções de Teste</h2>
            <ol>
                <li><strong>Abra o LexJus:</strong> Clique no link "Abrir LexJus" acima</li>
                <li><strong>Acesse o Sistema de Revisão:</strong> Clique no botão "Sistema de Revisão"</li>
                <li><strong>Inicie uma Revisão:</strong> Clique em "Iniciar Revisão"</li>
                <li><strong>Verifique o Título:</strong> O título deve mostrar "Art. X" em vez de apenas "X"</li>
                <li><strong>Teste com Diferentes Artigos:</strong> Navegue entre diferentes artigos</li>
            </ol>
            
            <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #155724;">✅ Resultado Esperado:</h4>
                <ul style="margin: 0;">
                    <li>Título do modal: <strong>"Art. 5º"</strong> (não apenas "5º")</li>
                    <li>Interface de estudo: <strong>"Art. 5º"</strong></li>
                    <li>Consistência em toda a interface</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Simular a função de atualização do sistema real
        function atualizarInterfaceRevisao(artigo) {
            const numeroElement = document.getElementById('previewArtigoNumero');
            if (numeroElement) {
                // Esta é a correção implementada
                numeroElement.textContent = `Art. ${artigo.artigo_numero}`;
            }
            return `Art. ${artigo.artigo_numero}`;
        }

        function testarFuncaoAtualizacao() {
            const numero = document.getElementById('numeroTeste').value;
            const resultado = document.getElementById('resultadoTeste');
            
            try {
                // Simular objeto artigo
                const artigo = { artigo_numero: numero };
                
                // Testar função
                const textoResultado = atualizarInterfaceRevisao(artigo);
                
                resultado.innerHTML = `
                    <div class="success">
                        <h4>✅ Teste Bem-sucedido!</h4>
                        <p><strong>Entrada:</strong> "${numero}"</p>
                        <p><strong>Resultado:</strong> "${textoResultado}"</p>
                        <p><strong>Função:</strong> numeroElement.textContent = \`Art. \${artigo.artigo_numero}\`</p>
                    </div>
                `;
            } catch (error) {
                resultado.innerHTML = `
                    <div class="error">
                        <h4>❌ Erro no Teste!</h4>
                        <p><strong>Erro:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        function testarFormato(numero) {
            const resultado = document.getElementById('resultadoFormatos');
            const artigo = { artigo_numero: numero };
            const textoResultado = atualizarInterfaceRevisao(artigo);
            
            resultado.innerHTML = `
                <div class="info">
                    <h4>🧪 Teste de Formato</h4>
                    <p><strong>Número testado:</strong> "${numero}"</p>
                    <p><strong>Resultado:</strong> "${textoResultado}"</p>
                    <p><strong>Status:</strong> ${textoResultado.startsWith('Art. ') ? '✅ Correto' : '❌ Incorreto'}</p>
                </div>
            `;
        }

        function abrirSistemaRevisao() {
            const resultado = document.getElementById('resultadoSistema');
            
            // Verificar se o sistema está disponível
            if (window.sistemaRevisao) {
                resultado.innerHTML = `
                    <div class="success">
                        <h4>✅ Sistema de Revisão Encontrado!</h4>
                        <p>O sistema está carregado e disponível.</p>
                        <p><strong>Próximo passo:</strong> Abra o modal de revisão para testar.</p>
                    </div>
                `;
                
                // Tentar abrir o modal
                try {
                    window.sistemaRevisao.abrirModal();
                } catch (error) {
                    resultado.innerHTML += `
                        <div class="error">
                            <p><strong>Erro ao abrir modal:</strong> ${error.message}</p>
                        </div>
                    `;
                }
            } else {
                resultado.innerHTML = `
                    <div class="error">
                        <h4>❌ Sistema de Revisão Não Encontrado</h4>
                        <p>O sistema não está carregado nesta página.</p>
                        <p><strong>Solução:</strong> Abra a página principal do LexJus primeiro.</p>
                    </div>
                `;
            }
        }

        function verificarSistemaCarregado() {
            const resultado = document.getElementById('resultadoSistema');
            
            const verificacoes = {
                'Sistema de Revisão': !!window.sistemaRevisao,
                'jQuery': !!window.jQuery,
                'Página LexJus': window.location.pathname.includes('lexjus'),
                'DOM Carregado': document.readyState === 'complete'
            };
            
            let html = '<h4>🔍 Verificação do Sistema:</h4><ul>';
            
            Object.entries(verificacoes).forEach(([nome, status]) => {
                const icon = status ? '✅' : '❌';
                const classe = status ? 'success' : 'error';
                html += `<li class="${classe}">${icon} <strong>${nome}:</strong> ${status ? 'OK' : 'Não encontrado'}</li>`;
            });
            
            html += '</ul>';
            
            if (verificacoes['Sistema de Revisão']) {
                html += '<p class="success"><strong>✅ Sistema pronto para teste!</strong></p>';
            } else {
                html += '<p class="error"><strong>❌ Abra a página principal do LexJus primeiro.</strong></p>';
            }
            
            resultado.innerHTML = html;
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Página de teste carregada');
            
            // Teste automático inicial
            setTimeout(() => {
                testarFuncaoAtualizacao();
            }, 500);
        });
    </script>
</body>
</html>
