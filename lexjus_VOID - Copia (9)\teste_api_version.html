<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste API Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #0c5460;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
        }
        button:hover {
            background: #005a87;
        }
        pre {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste da API Version</h1>
        
        <div class="info">
            <strong>📍 URL Atual:</strong> <span id="currentUrl"></span><br>
            <strong>📁 Caminho:</strong> <span id="currentPath"></span><br>
            <strong>🌐 Host:</strong> <span id="currentHost"></span>
        </div>
        
        <h2>🔍 Testes de Caminho</h2>
        
        <button onclick="testarCaminhoRelativo()">Testar ./api/version.php</button>
        <button onclick="testarCaminhoAbsoluto()">Testar /lexjus_VOID/api/version.php</button>
        <button onclick="testarCaminhoCompleto()">Testar URL Completa</button>
        <button onclick="verificarArquivos()">Verificar Arquivos</button>
        
        <div id="resultados"></div>
        
        <h2>📋 Checklist de Arquivos</h2>
        <div id="checklist">
            <p>Verificando arquivos necessários...</p>
        </div>
    </div>

    <script>
        // Mostrar informações da URL atual
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentPath').textContent = window.location.pathname;
        document.getElementById('currentHost').textContent = window.location.host;
        
        function adicionarResultado(titulo, conteudo, tipo = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${tipo}`;
            div.innerHTML = `<strong>${titulo}</strong><br><pre>${conteudo}</pre>`;
            document.getElementById('resultados').appendChild(div);
        }
        
        async function testarCaminhoRelativo() {
            try {
                const response = await fetch('./api/version.php');
                if (response.ok) {
                    const data = await response.json();
                    adicionarResultado('✅ Caminho Relativo - SUCESSO', JSON.stringify(data, null, 2), 'success');
                } else {
                    adicionarResultado('❌ Caminho Relativo - ERRO', `Status: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                adicionarResultado('❌ Caminho Relativo - ERRO', error.message, 'error');
            }
        }
        
        async function testarCaminhoAbsoluto() {
            try {
                const response = await fetch('/lexjus_VOID/api/version.php');
                if (response.ok) {
                    const data = await response.json();
                    adicionarResultado('✅ Caminho Absoluto - SUCESSO', JSON.stringify(data, null, 2), 'success');
                } else {
                    adicionarResultado('❌ Caminho Absoluto - ERRO', `Status: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                adicionarResultado('❌ Caminho Absoluto - ERRO', error.message, 'error');
            }
        }
        
        async function testarCaminhoCompleto() {
            try {
                const url = `${window.location.protocol}//${window.location.host}/lexjus_VOID/api/version.php`;
                const response = await fetch(url);
                if (response.ok) {
                    const data = await response.json();
                    adicionarResultado('✅ URL Completa - SUCESSO', JSON.stringify(data, null, 2), 'success');
                } else {
                    adicionarResultado('❌ URL Completa - ERRO', `Status: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                adicionarResultado('❌ URL Completa - ERRO', error.message, 'error');
            }
        }
        
        async function verificarArquivos() {
            const arquivos = [
                './api/version.php',
                './sw.js',
                './api/progresso.php',
                './api/favoritos.php',
                './includes/cache_buster.php'
            ];
            
            let resultados = 'Verificação de Arquivos:\n\n';
            
            for (const arquivo of arquivos) {
                try {
                    const response = await fetch(arquivo, { method: 'HEAD' });
                    if (response.ok) {
                        resultados += `✅ ${arquivo} - Existe\n`;
                    } else {
                        resultados += `❌ ${arquivo} - Não encontrado (${response.status})\n`;
                    }
                } catch (error) {
                    resultados += `❌ ${arquivo} - Erro: ${error.message}\n`;
                }
            }
            
            adicionarResultado('📋 Verificação de Arquivos', resultados, 'info');
        }
        
        // Executar verificação automática ao carregar
        window.onload = function() {
            setTimeout(verificarArquivos, 1000);
        };
    </script>
</body>
</html>
