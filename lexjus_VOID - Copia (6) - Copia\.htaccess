# Cache Busting Simples para LexJus
# Versão simplificada para evitar conflitos

# Habilitar compressão GZIP (se disponível)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configurações básicas de cache
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache curto para arquivos dinâmicos
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/x-httpd-php "access plus 1 hour"
    
    # Cache médio para CSS e JS (temos cache busting)
    ExpiresByType text/css "access plus 1 day"
    ExpiresByType application/javascript "access plus 1 day"
    
    # Cache longo para imagens
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Headers básicos de cache
<IfModule mod_headers.c>
    # Para arquivos CSS e JS
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=86400"
    </FilesMatch>
    
    # Para arquivos PHP
    <FilesMatch "\.php$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </FilesMatch>
</IfModule>

# Configurações de charset
AddDefaultCharset UTF-8

# Prevenir acesso a arquivos sensíveis
<FilesMatch "\.(htaccess|htpasswd|ini|log|sql)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Configurações de MIME types básicas
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType application/json .json
</IfModule>
