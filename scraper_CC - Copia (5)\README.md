# 🏛️ Scraper do Código Civil - Sistema LexJus

Este diretório contém os scripts para extrair e processar o Código Civil brasileiro para uso no sistema LexJus.

## 📋 Ordem de Execução

### ⚠️ **IMPORTANTE: Execute os scripts na ordem correta!**

```
1️⃣ executar_extracao_corrigido.py    (Extração dos dados)
         ⬇️
2️⃣ processar_codigo_civil_completo.py (Processamento completo)
```

## 🚀 Guia Passo a Passo

### **Passo 1: Extração dos Dados**
```bash
python executar_extracao_corrigido.py
```

**O que faz:**
- 🌐 Baixa o Código Civil do site oficial do Planalto
- 📄 Extrai todos os 2046 artigos do HTML
- 💾 Gera o arquivo `codigo_civil_lexjus_corrigido.json`
- 🔄 Salva backup local para uso futuro

**Arquivo gerado:**
- `codigo_civil_lexjus_corrigido.json` (formato bruto)

---

### **Passo 2: Processamento Completo**
```bash
python processar_codigo_civil_completo.py
```

**O que faz:**
- 🔄 Converte para formato específico do LexJus
- 🧹 Remove duplicações no campo caput
- 🔍 Verifica qualidade dos dados
- ✅ Gera arquivo final pronto para uso

**Arquivos gerados:**
- `codigo_civil_formato_lexjus.json` (formato LexJus)
- `codigo_civil_formato_lexjus_limpo.json` (caput limpo)
- `codigo_civil_formato_lexjus_final.json` (versão final)

---

## 🎯 Resultado Final

Após executar ambos os scripts, você terá:

### 📁 **Arquivo Principal para o LexJus:**
- **`codigo_civil_formato_lexjus_final.json`** ← **Use este no sistema LexJus**

### 📊 **Estrutura do JSON Final:**
```json
{
  "artigo": "Art. 4º",
  "caput": "São incapazes, relativamente a certos atos...",
  "incisos": ["I - ...", "II - ..."],
  "paragrafos_numerados": [
    {
      "numero": "§ 1º",
      "texto": "texto do parágrafo",
      "alineas": []
    }
  ],
  "paragrafo_unico": "texto ou null"
}
```

## 🔄 Execução Alternativa

### **🥇 Opção 1: Menu Interativo (Mais Fácil)**
```bash
python menu_sistema_completo.py
```
**ou no Windows:**
```cmd
MENU_SISTEMA.bat
```
- 🎯 **Menu completo** com todas as opções
- ✅ **Pipeline automático** ou etapas individuais
- 🔍 **Diagnósticos** e relatórios
- 🧹 **Limpeza** de arquivos temporários
- 📊 **Status** em tempo real

### **🥈 Opção 2: Pipeline Completo Automático**
```cmd
EXECUTAR_PIPELINE_COMPLETO.bat
```
- 🎯 **Duplo clique** e pronto!
- ✅ Executa ambos os passos automaticamente
- 🔍 Verifica cada etapa
- 📊 Mostra resultados detalhados

### **🥉 Opção 3: Scripts Individuais**
1. **Duplo clique** em `EXECUTAR_EXTRACAO_CORRIGIDA.bat`
2. **Aguarde** a conclusão
3. **Duplo clique** em `EXECUTAR_LIMPEZA_CAPUT.bat`

### **Opção 4: Processamento Unificado**
⚠️ **ATENÇÃO**: Só funciona se você já tiver executado o Passo 1 antes!

```bash
python processar_codigo_civil_completo.py
```

## 🌐 Fontes de Dados

### **Fonte Principal (Recomendada)**
- **URL**: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm
- **Vantagem**: Sempre atualizada com as últimas alterações legais

### **Fonte Alternativa**
- **Arquivo**: `L10406.html` (local)
- **Vantagem**: Funciona offline

### **Estratégia Automática**
1. 🌐 Tenta baixar da web primeiro
2. 📁 Se falhar, usa arquivo local
3. 💾 Salva backup da versão web

## 📊 Dados Extraídos

- ✅ **2.046 artigos** completos
- ✅ **766 incisos** estruturados
- ✅ **830 parágrafos** organizados
- ✅ **Hierarquia completa** (caputs, incisos, parágrafos, alíneas)
- ✅ **Caput limpo** sem duplicações

## 🔧 Parâmetros Disponíveis

### **Script de Extração**
```bash
# Baixar da web (padrão)
python executar_extracao_corrigido.py

# Usar arquivo local
python executar_extracao_corrigido.py --local

# Ver ajuda
python executar_extracao_corrigido.py --help
```

## 🆘 Solução de Problemas

### **Erro: "Arquivo não encontrado"**
- Execute primeiro o **Passo 1** (extração)
- Verifique se `codigo_civil_lexjus_corrigido.json` foi gerado

### **Erro de conexão**
- Use o parâmetro `--local` no Passo 1
- Certifique-se de ter o arquivo `L10406.html`

### **Dados incompletos**
- Execute `python verificar_limpeza_caput.py` para diagnóstico
- Reexecute os scripts na ordem correta

## 📁 Arquivos Importantes

### **Scripts Principais**
- `executar_extracao_corrigido.py` - Extração dos dados
- `processar_codigo_civil_completo.py` - Processamento completo
- `limpar_caput_duplicado.py` - Limpeza específica
- `verificar_limpeza_caput.py` - Verificação de qualidade

### **Scripts Batch (Windows)**
- `EXECUTAR_EXTRACAO_CORRIGIDA.bat` - Extração
- `EXECUTAR_LIMPEZA_CAPUT.bat` - Limpeza

### **Arquivos de Dados**
- `L10406.html` - HTML fonte (local)
- `L10406_web_backup.html` - Backup da web
- `codigo_civil_lexjus_corrigido.json` - Extração bruta
- `codigo_civil_formato_lexjus.json` - Formato LexJus
- `codigo_civil_formato_lexjus_limpo.json` - Caput limpo
- `codigo_civil_formato_lexjus_final.json` - **Arquivo final**

## 🎯 Resumo Rápido

### **🥇 Opção Mais Fácil (Recomendada):**
```bash
python menu_sistema_completo.py
```
**ou no Windows:**
```cmd
MENU_SISTEMA.bat
```
- 🎯 **Menu interativo** completo
- ✅ **Todas as funcionalidades** em um lugar
- 📊 **Status em tempo real**

### **🥈 Opção Automática (Windows):**
```cmd
EXECUTAR_PIPELINE_COMPLETO.bat
```
- 🎯 **Duplo clique** e aguarde
- ✅ Faz tudo automaticamente na ordem correta

### **🥉 Opção Manual (Qualquer SO):**
```bash
# 1. Extrair dados
python executar_extracao_corrigido.py

# 2. Processar e limpar
python processar_codigo_civil_completo.py
```

### **Para Atualizações Futuras:**
```bash
# Opção 1: Menu interativo (Recomendada)
python menu_sistema_completo.py

# Opção 2: Automática (Windows)
EXECUTAR_PIPELINE_COMPLETO.bat

# Opção 3: Manual
python executar_extracao_corrigido.py
python processar_codigo_civil_completo.py
```

### **Arquivo Final:**
- 📄 `codigo_civil_formato_lexjus_final.json` ← **Use este no LexJus**

---

**🔄 Sempre execute na ordem: Extração → Processamento**

**📞 Para diagnósticos**: `python verificar_limpeza_caput.py`
