<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Simples - Sistema de Revisão</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .step {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .quality-btn {
            margin: 5px;
            padding: 8px 15px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        .quality-btn:hover { border-color: #007bff; background: #e7f3ff; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number { font-size: 24px; font-weight: bold; }
        .stat-label { font-size: 12px; opacity: 0.9; }
    </style>
</head>
<body>
    <h1>🧪 Teste Simples - Sistema de Revisão</h1>

    <div class="container">
        <h2>1. 🔐 Verificar Autenticação</h2>
        <div class="step">
            <p>Primeiro, vamos verificar se você está logado no sistema:</p>
            <button onclick="verificarAuth()">Verificar Autenticação</button>
            <div id="auth-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>2. 📝 Adicionar Artigos</h2>
        <div class="step">
            <p>Vamos adicionar alguns artigos ao sistema de revisão:</p>
            <button onclick="adicionarArtigo('1')">Adicionar 1º</button>
            <button onclick="adicionarArtigo('5')">Adicionar 5º</button>
            <button onclick="adicionarArtigo('37')">Adicionar 37</button>
            <button onclick="adicionarTodos()">Adicionar Todos</button>
            <div id="adicionar-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>3. 📊 Ver Estatísticas</h2>
        <div class="step">
            <p>Vamos ver as estatísticas atuais do sistema:</p>
            <button onclick="verEstatisticas()">Carregar Estatísticas</button>
            <div id="stats-display" class="stats-grid"></div>
            <div id="stats-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>4. 📖 Responder Revisões</h2>
        <div class="step">
            <p>Agora vamos simular respostas de revisão:</p>
            <div>
                <label>Artigo:</label>
                <select id="artigoSelect">
                    <option value="1">1º</option>
                    <option value="5">5º</option>
                    <option value="37">37</option>
                </select>
            </div>
            <div style="margin: 10px 0;">
                <p>Qualidade da resposta:</p>
                <button class="quality-btn" onclick="responder(0)">0 - Não lembro</button>
                <button class="quality-btn" onclick="responder(1)">1 - Muito difícil</button>
                <button class="quality-btn" onclick="responder(2)">2 - Difícil</button>
                <button class="quality-btn" onclick="responder(3)">3 - Normal</button>
                <button class="quality-btn" onclick="responder(4)">4 - Fácil</button>
                <button class="quality-btn" onclick="responder(5)">5 - Muito fácil</button>
            </div>
            <div id="resposta-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>5. 📋 Listar Revisões</h2>
        <div class="step">
            <p>Vamos ver todas as revisões criadas:</p>
            <button onclick="listarRevisoes()">Listar Todas as Revisões</button>
            <div id="lista-result" class="result"></div>
        </div>
    </div>

    <script>
        async function fazerRequisicao(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                const data = await response.json();
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        function mostrarResultado(elementId, resultado) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (resultado.ok ? 'success' : 'error');
            element.textContent = JSON.stringify(resultado, null, 2);
        }

        async function verificarAuth() {
            const resultado = await fazerRequisicao('./api/revisao_simples.php?acao=estatisticas');
            mostrarResultado('auth-result', resultado);

            if (resultado.ok) {
                document.getElementById('auth-result').innerHTML =
                    '<div class="success">✅ Autenticado com sucesso!</div>' +
                    '<pre>' + JSON.stringify(resultado, null, 2) + '</pre>';
            }
        }

        async function adicionarArtigo(numero) {
            const resultado = await fazerRequisicao('./api/revisao_simples.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'iniciar',
                    artigo_numero: numero
                })
            });

            const element = document.getElementById('adicionar-result');
            const classe = resultado.ok ? 'success' : 'error';
            const emoji = resultado.ok ? '✅' : '❌';

            element.innerHTML += `<div class="${classe}">${emoji} Artigo ${numero}: ${resultado.data?.mensagem || resultado.error}</div>`;
        }

        async function adicionarTodos() {
            document.getElementById('adicionar-result').innerHTML = '<div>Adicionando artigos...</div>';

            const artigos = ['1', '5', '37'];
            for (const artigo of artigos) {
                await adicionarArtigo(artigo);
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Atualizar estatísticas após adicionar
            setTimeout(verEstatisticas, 500);
        }

        async function verEstatisticas() {
            const resultado = await fazerRequisicao('./api/revisao_simples.php?acao=estatisticas');
            mostrarResultado('stats-result', resultado);

            if (resultado.ok && resultado.data.estatisticas) {
                mostrarEstatisticasVisuais(resultado.data.estatisticas);
            }
        }

        function mostrarEstatisticasVisuais(stats) {
            const container = document.getElementById('stats-display');
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_artigos || 0}</div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.novos || 0}</div>
                    <div class="stat-label">Novos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.aprendendo || 0}</div>
                    <div class="stat-label">Aprendendo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.revisando || 0}</div>
                    <div class="stat-label">Revisando</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.dominados || 0}</div>
                    <div class="stat-label">Dominados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${(stats.facilidade_media || 0).toFixed(1)}</div>
                    <div class="stat-label">Facilidade</div>
                </div>
            `;
        }

        async function responder(qualidade) {
            const artigo = document.getElementById('artigoSelect').value;

            const resultado = await fazerRequisicao('./api/revisao_simples.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'responder',
                    artigo_numero: artigo,
                    qualidade: qualidade,
                    tempo_resposta: Math.floor(Math.random() * 60) + 10
                })
            });

            mostrarResultado('resposta-result', resultado);

            if (resultado.ok) {
                const data = resultado.data;
                document.getElementById('resposta-result').innerHTML = `
                    <div class="success">
                        ✅ Resposta processada com sucesso!<br><br>
                        📊 <strong>Resultados:</strong><br>
                        • Nova facilidade: ${data.nova_facilidade}<br>
                        • Novo intervalo: ${data.novo_intervalo} dias<br>
                        • Repetições: ${data.novas_repeticoes}<br>
                        • Status: ${data.novo_status}<br>
                        • Próxima revisão: ${data.data_proxima_revisao}<br>
                    </div>
                `;

                // Atualizar estatísticas
                setTimeout(verEstatisticas, 500);
            }
        }

        async function listarRevisoes() {
            const resultado = await fazerRequisicao('./api/revisao_simples.php');
            mostrarResultado('lista-result', resultado);
        }

        // Verificar autenticação ao carregar
        window.onload = function() {
            console.log('🧪 Teste simples carregado!');
            verificarAuth();
        };
    </script>
</body>
</html>
