#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MENU SISTEMA COMPLETO - CÓDIGO CIVIL LEXJUS
===========================================

Menu interativo para gerenciar todo o pipeline de processamento
do Código Civil para o sistema LexJus.

Funcionalidades:
- Pipeline completo automatizado
- Execução de etapas individuais
- Verificação de arquivos e qualidade
- Limpeza de arquivos temporários
- Diagnósticos e relatórios

Autor: Sistema LexJus
Data: 2025
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime

class MenuSistemaCompleto:
    def __init__(self):
        self.diretorio_trabalho = os.path.dirname(os.path.abspath(__file__))
        os.chdir(self.diretorio_trabalho)
        
        self.arquivos_esperados = {
            'entrada': ['L10406.html', 'L10406_web_backup.html'],
            'scripts': [
                'executar_extracao_corrigido.py',
                'converter_cc_para_lexjus.py',
                'limpar_caput_duplicado.py',
                'limpar_paragrafos_duplicados.py',
                'verificar_limpeza_caput.py'
            ],
            'saida': [
                'codigo_civil_lexjus_corrigido.json',
                'codigo_civil_formato_lexjus.json',
                'codigo_civil_formato_lexjus_limpo.json',
                'codigo_civil_formato_lexjus_final.json'
            ]
        }

    def limpar_tela(self):
        """Limpa a tela do terminal"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def exibir_cabecalho(self):
        """Exibe o cabeçalho do sistema"""
        print("=" * 70)
        print("🏛️  SISTEMA COMPLETO - CÓDIGO CIVIL LEXJUS")
        print("=" * 70)
        print(f"📂 Diretório: {self.diretorio_trabalho}")
        print(f"🕒 Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        print("=" * 70)

    def verificar_arquivos(self):
        """Verifica o status dos arquivos do sistema"""
        status = {
            'scripts_ok': True,
            'entrada_disponivel': False,
            'saida_completa': False,
            'arquivos_faltando': []
        }

        # Verificar scripts
        for script in self.arquivos_esperados['scripts']:
            if not os.path.exists(script):
                status['scripts_ok'] = False
                status['arquivos_faltando'].append(script)

        # Verificar arquivos de entrada
        for arquivo in self.arquivos_esperados['entrada']:
            if os.path.exists(arquivo):
                status['entrada_disponivel'] = True
                break

        # Verificar arquivos de saída
        arquivos_saida_existentes = 0
        for arquivo in self.arquivos_esperados['saida']:
            if os.path.exists(arquivo):
                arquivos_saida_existentes += 1

        status['saida_completa'] = arquivos_saida_existentes == len(self.arquivos_esperados['saida'])

        return status

    def exibir_status_sistema(self):
        """Exibe o status atual do sistema"""
        status = self.verificar_arquivos()
        
        print("\n📊 STATUS DO SISTEMA:")
        print("-" * 50)
        
        # Scripts
        if status['scripts_ok']:
            print("✅ Scripts: Todos disponíveis")
        else:
            print("❌ Scripts: Arquivos faltando")
            for arquivo in status['arquivos_faltando']:
                print(f"   - {arquivo}")

        # Entrada
        if status['entrada_disponivel']:
            print("✅ Dados de entrada: Disponíveis")
        else:
            print("❌ Dados de entrada: Nenhum arquivo HTML encontrado")

        # Saída
        if status['saida_completa']:
            print("✅ Pipeline: Completo (todos os arquivos gerados)")
        else:
            print("⚠️  Pipeline: Incompleto")
            
        # Listar arquivos existentes
        print("\n📁 ARQUIVOS EXISTENTES:")
        for categoria, arquivos in self.arquivos_esperados.items():
            print(f"\n{categoria.upper()}:")
            for arquivo in arquivos:
                if os.path.exists(arquivo):
                    tamanho = os.path.getsize(arquivo) / 1024 / 1024  # MB
                    print(f"  ✅ {arquivo} ({tamanho:.1f} MB)")
                else:
                    print(f"  ❌ {arquivo}")

    def executar_comando(self, comando, descricao):
        """Executa um comando Python e exibe o progresso"""
        print(f"\n🔄 {descricao}")
        print("-" * 50)
        
        try:
            inicio = time.time()
            result = subprocess.run([sys.executable, comando], 
                                  capture_output=False, 
                                  text=True)
            fim = time.time()
            
            if result.returncode == 0:
                print(f"\n✅ {descricao} - CONCLUÍDO!")
                print(f"⏱️  Tempo: {fim - inicio:.1f} segundos")
                return True
            else:
                print(f"\n❌ {descricao} - FALHOU!")
                print(f"Código de erro: {result.returncode}")
                return False
                
        except Exception as e:
            print(f"\n❌ Erro ao executar {comando}: {e}")
            return False

    def pipeline_completo(self):
        """Executa o pipeline completo"""
        self.limpar_tela()
        self.exibir_cabecalho()
        print("\n🚀 EXECUTANDO PIPELINE COMPLETO")
        print("=" * 50)
        
        etapas = [
            ('executar_extracao_corrigido.py', 'Extração dos dados'),
            ('converter_cc_para_lexjus.py', 'Conversão para formato LexJus'),
            ('limpar_caput_duplicado.py', 'Limpeza do caput'),
            ('limpar_paragrafos_duplicados.py', 'Limpeza dos parágrafos'),
            ('verificar_limpeza_caput.py codigo_civil_formato_lexjus_final.json', 'Verificação final')
        ]
        
        sucesso_total = True
        
        for i, (comando, descricao) in enumerate(etapas, 1):
            print(f"\n📋 ETAPA {i}/5: {descricao}")
            
            if not self.executar_comando(comando.split()[0], descricao):
                sucesso_total = False
                resposta = input(f"\n⚠️  Etapa {i} falhou. Continuar? (s/N): ").strip().lower()
                if resposta not in ['s', 'sim', 'y', 'yes']:
                    break
        
        print(f"\n{'='*50}")
        if sucesso_total:
            print("🎉 PIPELINE COMPLETO - SUCESSO!")
            print("📁 Arquivo final: codigo_civil_formato_lexjus_final.json")
        else:
            print("⚠️  PIPELINE COMPLETO - COM AVISOS")
            print("Verifique os erros acima")
        
        input("\nPressione Enter para continuar...")

    def executar_etapa_individual(self):
        """Menu para executar etapas individuais"""
        while True:
            self.limpar_tela()
            self.exibir_cabecalho()
            print("\n🔧 EXECUÇÃO DE ETAPAS INDIVIDUAIS")
            print("=" * 50)
            
            opcoes = [
                ("1", "Extração dos dados (web/local)", "executar_extracao_corrigido.py"),
                ("2", "Conversão para formato LexJus", "converter_cc_para_lexjus.py"),
                ("3", "Limpeza do caput", "limpar_caput_duplicado.py"),
                ("4", "Limpeza dos parágrafos", "limpar_paragrafos_duplicados.py"),
                ("5", "Verificação de qualidade", "verificar_limpeza_caput.py"),
                ("0", "Voltar ao menu principal", None)
            ]
            
            for num, desc, _ in opcoes:
                print(f"[{num}] {desc}")
            
            escolha = input("\nEscolha uma opção: ").strip()
            
            if escolha == "0":
                break
            
            opcao_encontrada = None
            for num, desc, comando in opcoes:
                if escolha == num and comando:
                    opcao_encontrada = (desc, comando)
                    break
            
            if opcao_encontrada:
                desc, comando = opcao_encontrada
                
                # Opções especiais para alguns comandos
                if comando == "executar_extracao_corrigido.py":
                    fonte = input("\nFonte (1=Web, 2=Local): ").strip()
                    if fonte == "2":
                        comando += " --local"
                elif comando == "verificar_limpeza_caput.py":
                    arquivo = input("\nArquivo para verificar (Enter=final): ").strip()
                    if not arquivo:
                        arquivo = "codigo_civil_formato_lexjus_final.json"
                    comando += f" {arquivo}"
                
                self.executar_comando(comando.split()[0], desc)
                input("\nPressione Enter para continuar...")
            else:
                print("❌ Opção inválida!")
                time.sleep(1)

    def diagnosticos_relatorios(self):
        """Menu de diagnósticos e relatórios"""
        while True:
            self.limpar_tela()
            self.exibir_cabecalho()
            print("\n🔍 DIAGNÓSTICOS E RELATÓRIOS")
            print("=" * 50)
            
            opcoes = [
                ("1", "Status completo do sistema"),
                ("2", "Verificar qualidade dos dados"),
                ("3", "Relatório de arquivos gerados"),
                ("4", "Teste de conectividade web"),
                ("0", "Voltar ao menu principal")
            ]
            
            for num, desc in opcoes:
                print(f"[{num}] {desc}")
            
            escolha = input("\nEscolha uma opção: ").strip()
            
            if escolha == "0":
                break
            elif escolha == "1":
                self.exibir_status_sistema()
            elif escolha == "2":
                self.verificar_qualidade_dados()
            elif escolha == "3":
                self.relatorio_arquivos()
            elif escolha == "4":
                self.teste_conectividade()
            else:
                print("❌ Opção inválida!")
                time.sleep(1)
            
            if escolha != "0":
                input("\nPressione Enter para continuar...")

    def verificar_qualidade_dados(self):
        """Verifica a qualidade dos dados gerados"""
        print("\n🔍 VERIFICAÇÃO DE QUALIDADE DOS DADOS")
        print("-" * 50)
        
        arquivo_final = 'codigo_civil_formato_lexjus_final.json'
        
        if not os.path.exists(arquivo_final):
            print("❌ Arquivo final não encontrado!")
            print("Execute o pipeline completo primeiro.")
            return
        
        try:
            with open(arquivo_final, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            total_artigos = len(data)
            com_incisos = len([art for art in data if art.get('incisos') and len(art['incisos']) > 0])
            com_paragrafos = len([art for art in data if art.get('paragrafos_numerados') and len(art['paragrafos_numerados']) > 0])
            com_paragrafo_unico = len([art for art in data if art.get('paragrafo_unico')])
            
            print(f"📊 ESTATÍSTICAS:")
            print(f"  Total de artigos: {total_artigos}")
            print(f"  Com incisos: {com_incisos} ({com_incisos/total_artigos*100:.1f}%)")
            print(f"  Com parágrafos numerados: {com_paragrafos} ({com_paragrafos/total_artigos*100:.1f}%)")
            print(f"  Com parágrafo único: {com_paragrafo_unico} ({com_paragrafo_unico/total_artigos*100:.1f}%)")
            
            # Verificar problemas
            problemas = []
            for artigo in data:
                if not artigo.get('caput', '').strip():
                    problemas.append(f"Art. {artigo.get('artigo', 'N/A')}: Caput vazio")
            
            if problemas:
                print(f"\n⚠️  PROBLEMAS ENCONTRADOS ({len(problemas)}):")
                for problema in problemas[:5]:
                    print(f"  - {problema}")
                if len(problemas) > 5:
                    print(f"  ... e mais {len(problemas) - 5} problemas")
            else:
                print("\n✅ NENHUM PROBLEMA ENCONTRADO!")
                
        except Exception as e:
            print(f"❌ Erro ao verificar dados: {e}")

    def relatorio_arquivos(self):
        """Gera relatório dos arquivos gerados"""
        print("\n📋 RELATÓRIO DE ARQUIVOS GERADOS")
        print("-" * 50)
        
        for arquivo in self.arquivos_esperados['saida']:
            if os.path.exists(arquivo):
                stat = os.stat(arquivo)
                tamanho_mb = stat.st_size / 1024 / 1024
                modificado = datetime.fromtimestamp(stat.st_mtime)
                print(f"✅ {arquivo}")
                print(f"   Tamanho: {tamanho_mb:.1f} MB")
                print(f"   Modificado: {modificado.strftime('%d/%m/%Y %H:%M:%S')}")
                print()
            else:
                print(f"❌ {arquivo} - Não encontrado")

    def teste_conectividade(self):
        """Testa conectividade com a fonte web"""
        print("\n🌐 TESTE DE CONECTIVIDADE WEB")
        print("-" * 50)
        
        try:
            import requests
            url = "https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm"
            print(f"Testando: {url}")
            
            response = requests.head(url, timeout=10)
            
            if response.status_code == 200:
                print("✅ Conectividade OK!")
                print(f"Status: {response.status_code}")
                if 'content-length' in response.headers:
                    tamanho = int(response.headers['content-length']) / 1024 / 1024
                    print(f"Tamanho: {tamanho:.1f} MB")
            else:
                print(f"⚠️  Status: {response.status_code}")
                
        except ImportError:
            print("❌ Módulo 'requests' não encontrado!")
            print("Execute: pip install requests")
        except Exception as e:
            print(f"❌ Erro de conectividade: {e}")

    def limpeza_arquivos(self):
        """Menu de limpeza de arquivos"""
        self.limpar_tela()
        self.exibir_cabecalho()
        print("\n🧹 LIMPEZA DE ARQUIVOS")
        print("=" * 50)
        
        arquivos_temporarios = [
            'codigo_civil_lexjus_corrigido.json',
            'codigo_civil_formato_lexjus.json',
            'codigo_civil_formato_lexjus_limpo.json'
        ]
        
        print("Arquivos que serão removidos:")
        arquivos_existentes = []
        for arquivo in arquivos_temporarios:
            if os.path.exists(arquivo):
                tamanho = os.path.getsize(arquivo) / 1024 / 1024
                print(f"  📄 {arquivo} ({tamanho:.1f} MB)")
                arquivos_existentes.append(arquivo)
            else:
                print(f"  ❌ {arquivo} (não encontrado)")
        
        if not arquivos_existentes:
            print("\n✅ Nenhum arquivo temporário encontrado!")
            input("\nPressione Enter para continuar...")
            return
        
        print(f"\nArquivo final será mantido:")
        if os.path.exists('codigo_civil_formato_lexjus_final.json'):
            tamanho = os.path.getsize('codigo_civil_formato_lexjus_final.json') / 1024 / 1024
            print(f"  ✅ codigo_civil_formato_lexjus_final.json ({tamanho:.1f} MB)")
        else:
            print("  ❌ codigo_civil_formato_lexjus_final.json (não encontrado)")
        
        confirmacao = input(f"\nRemover {len(arquivos_existentes)} arquivos temporários? (s/N): ").strip().lower()
        
        if confirmacao in ['s', 'sim', 'y', 'yes']:
            removidos = 0
            for arquivo in arquivos_existentes:
                try:
                    os.remove(arquivo)
                    print(f"✅ Removido: {arquivo}")
                    removidos += 1
                except Exception as e:
                    print(f"❌ Erro ao remover {arquivo}: {e}")
            
            print(f"\n🎉 Limpeza concluída! {removidos} arquivos removidos.")
        else:
            print("\n❌ Limpeza cancelada.")
        
        input("\nPressione Enter para continuar...")

    def menu_principal(self):
        """Menu principal do sistema"""
        while True:
            self.limpar_tela()
            self.exibir_cabecalho()
            self.exibir_status_sistema()
            
            print("\n🎯 MENU PRINCIPAL")
            print("=" * 50)
            
            opcoes = [
                ("1", "🚀 Pipeline Completo (Recomendado)"),
                ("2", "🔧 Execução de Etapas Individuais"),
                ("3", "🔍 Diagnósticos e Relatórios"),
                ("4", "🧹 Limpeza de Arquivos Temporários"),
                ("5", "📖 Ajuda e Documentação"),
                ("0", "🚪 Sair")
            ]
            
            for num, desc in opcoes:
                print(f"[{num}] {desc}")
            
            escolha = input("\nEscolha uma opção: ").strip()
            
            if escolha == "0":
                print("\n👋 Obrigado por usar o Sistema LexJus!")
                break
            elif escolha == "1":
                self.pipeline_completo()
            elif escolha == "2":
                self.executar_etapa_individual()
            elif escolha == "3":
                self.diagnosticos_relatorios()
            elif escolha == "4":
                self.limpeza_arquivos()
            elif escolha == "5":
                self.exibir_ajuda()
            else:
                print("❌ Opção inválida!")
                time.sleep(1)

    def exibir_ajuda(self):
        """Exibe ajuda e documentação"""
        self.limpar_tela()
        self.exibir_cabecalho()
        print("\n📖 AJUDA E DOCUMENTAÇÃO")
        print("=" * 50)
        
        print("""
🎯 SOBRE O SISTEMA:
Este sistema automatiza a extração e processamento do Código Civil
brasileiro para uso no sistema LexJus.

🔄 PIPELINE COMPLETO:
1. Extração dos dados (web ou local)
2. Conversão para formato LexJus
3. Limpeza do caput (remove duplicações)
4. Limpeza dos parágrafos (remove duplicações)
5. Verificação final da qualidade

📁 ARQUIVOS PRINCIPAIS:
- codigo_civil_formato_lexjus_final.json ← Use este no LexJus

🌐 FONTE DOS DADOS:
- Web: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm
- Local: L10406.html (backup)

🆘 SOLUÇÃO DE PROBLEMAS:
- Use "Diagnósticos e Relatórios" para verificar problemas
- Execute etapas individuais se o pipeline falhar
- Verifique conectividade web se extração falhar

📞 SUPORTE:
- Consulte README.md para documentação completa
- Use verificação de qualidade para diagnósticos
        """)
        
        input("\nPressione Enter para continuar...")

def main():
    """Função principal"""
    try:
        menu = MenuSistemaCompleto()
        menu.menu_principal()
    except KeyboardInterrupt:
        print("\n\n👋 Sistema interrompido pelo usuário. Até logo!")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        print("Consulte a documentação ou execute etapas individuais.")

if __name__ == "__main__":
    main()
