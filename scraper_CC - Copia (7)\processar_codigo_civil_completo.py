#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PROCESSAMENTO COMPLETO DO CÓDIGO CIVIL
======================================

Este script executa todo o pipeline de processamento do Código Civil:
1. Extração dos dados do HTML
2. Limpeza das duplicações no caput
3. Verificação final da qualidade

USO:
    python processar_codigo_civil_completo.py

ARQUIVOS GERADOS:
    - codigo_civil_lexjus_corrigido.json (extração bruta)
    - codigo_civil_formato_lexjus.json (formato LexJus)
    - codigo_civil_formato_lexjus_limpo.json (versão final limpa)
"""

import json
import re
import os
import subprocess
import sys

def executar_script(script_name, descricao, argumentos=None):
    """Executa um script Python e retorna se foi bem-sucedido"""
    print(f"\n{'='*50}")
    print(f"EXECUTANDO: {descricao}")
    print(f"Script: {script_name}")
    if argumentos:
        print(f"Argumentos: {' '.join(argumentos)}")
    print(f"{'='*50}")

    try:
        # Preparar comando
        comando = [sys.executable, script_name]
        if argumentos:
            comando.extend(argumentos)

        # Executar sem capturar output para evitar problemas de encoding
        result = subprocess.run(comando, encoding='utf-8', errors='ignore')

        if result.returncode == 0:
            print(f"✅ {descricao} - SUCESSO!")
            return True
        else:
            print(f"❌ ERRO em {descricao} (código: {result.returncode})")
            return False

    except Exception as e:
        print(f"❌ ERRO ao executar {script_name}: {e}")
        return False

def verificar_arquivos_necessarios():
    """Verifica se todos os arquivos necessários estão presentes"""
    arquivos_necessarios = [
        'L10406_web_backup.html',
        'executar_extracao_corrigido.py',
        'converter_cc_para_lexjus.py',
        'limpar_caput_duplicado.py',
        'limpar_paragrafos_duplicados.py',
        'verificar_limpeza_caput.py'
    ]

    arquivos_faltando = []
    for arquivo in arquivos_necessarios:
        if not os.path.exists(arquivo):
            arquivos_faltando.append(arquivo)

    return arquivos_faltando

def main():
    print("🏛️  PROCESSAMENTO COMPLETO DO CÓDIGO CIVIL")
    print("=" * 60)
    print("Este script executa todo o pipeline de processamento:")
    print("1. ✅ Extração dos dados do HTML")
    print("2. 🔄 Conversão para formato LexJus")
    print("3. 🧹 Limpeza das duplicações no caput")
    print("4. 🧹 Limpeza das duplicações nos parágrafos")
    print("5. 🔍 Verificação final da qualidade")
    print("=" * 60)

    # Verificar arquivos necessários
    print("\n🔍 Verificando arquivos necessários...")
    arquivos_faltando = verificar_arquivos_necessarios()

    if arquivos_faltando:
        print("❌ ERRO: Arquivos necessários não encontrados:")
        for arquivo in arquivos_faltando:
            print(f"  - {arquivo}")
        print("\nCertifique-se de que todos os arquivos estão no diretório atual.")
        return

    print("✅ Todos os arquivos necessários encontrados!")

    # Confirmar execução
    resposta = input("\nDeseja prosseguir com o processamento completo? (s/N): ").strip().lower()
    if resposta not in ['s', 'sim', 'y', 'yes']:
        print("Processamento cancelado.")
        return

    sucesso_total = True

    # Etapa 1: Extração
    if executar_script('executar_extracao_corrigido.py', 'EXTRAÇÃO DOS DADOS'):
        print("📄 Arquivo gerado: codigo_civil_lexjus_corrigido.json")
    else:
        print("❌ Falha na extração. Interrompendo processamento.")
        return

    # Verificar se o arquivo de extração foi gerado
    if not os.path.exists('codigo_civil_lexjus_corrigido.json'):
        print("❌ Arquivo de extração não foi gerado. Interrompendo.")
        return

    # Etapa 2: Conversão para formato LexJus
    if executar_script('converter_cc_para_lexjus.py', 'CONVERSÃO PARA FORMATO LEXJUS'):
        print("📄 Arquivo gerado: codigo_civil_formato_lexjus.json")
    else:
        print("⚠️  Falha na conversão, mas continuando...")
        sucesso_total = False

    # Etapa 3: Limpeza do caput
    if os.path.exists('codigo_civil_formato_lexjus.json'):
        if executar_script('limpar_caput_duplicado.py', 'LIMPEZA DO CAPUT'):
            print("📄 Arquivo gerado: codigo_civil_formato_lexjus_limpo.json")
        else:
            print("⚠️  Falha na limpeza do caput, mas continuando...")
            sucesso_total = False
    else:
        print("⚠️  Arquivo codigo_civil_formato_lexjus.json não encontrado.")
        print("    Pulando etapa de limpeza do caput.")
        sucesso_total = False

    # Etapa 4: Limpeza dos parágrafos
    if os.path.exists('codigo_civil_formato_lexjus_limpo.json'):
        if executar_script('limpar_paragrafos_duplicados.py', 'LIMPEZA DOS PARÁGRAFOS'):
            print("📄 Arquivo gerado: codigo_civil_formato_lexjus_final.json")
        else:
            print("⚠️  Falha na limpeza dos parágrafos, mas continuando...")
            sucesso_total = False
    else:
        print("⚠️  Arquivo codigo_civil_formato_lexjus_limpo.json não encontrado.")
        print("    Pulando etapa de limpeza dos parágrafos.")
        sucesso_total = False

    # Etapa 5: Verificação final
    arquivo_verificar = 'codigo_civil_formato_lexjus_final.json'
    if not os.path.exists(arquivo_verificar):
        arquivo_verificar = 'codigo_civil_formato_lexjus_limpo.json'
    if not os.path.exists(arquivo_verificar):
        arquivo_verificar = 'codigo_civil_formato_lexjus.json'
    if not os.path.exists(arquivo_verificar):
        arquivo_verificar = 'codigo_civil_lexjus_corrigido.json'

    if os.path.exists(arquivo_verificar):
        if executar_script('verificar_limpeza_caput.py', 'VERIFICAÇÃO FINAL', [arquivo_verificar]):
            print("✅ Verificação concluída!")
        else:
            print("⚠️  Falha na verificação, mas arquivos foram gerados.")
            sucesso_total = False

    # Resumo final
    print(f"\n{'='*60}")
    print("📋 RESUMO DO PROCESSAMENTO")
    print(f"{'='*60}")

    arquivos_gerados = []
    if os.path.exists('codigo_civil_lexjus_corrigido.json'):
        arquivos_gerados.append('codigo_civil_lexjus_corrigido.json')
    if os.path.exists('codigo_civil_formato_lexjus.json'):
        arquivos_gerados.append('codigo_civil_formato_lexjus.json')
    if os.path.exists('codigo_civil_formato_lexjus_limpo.json'):
        arquivos_gerados.append('codigo_civil_formato_lexjus_limpo.json')
    if os.path.exists('codigo_civil_formato_lexjus_final.json'):
        arquivos_gerados.append('codigo_civil_formato_lexjus_final.json')

    if arquivos_gerados:
        print("📁 Arquivos gerados:")
        for arquivo in arquivos_gerados:
            tamanho = os.path.getsize(arquivo) / 1024 / 1024  # MB
            print(f"  ✅ {arquivo} ({tamanho:.1f} MB)")

    if sucesso_total:
        print("\n🎉 PROCESSAMENTO COMPLETO COM SUCESSO!")
        print("   Todos os arquivos foram gerados e verificados.")
    else:
        print("\n⚠️  PROCESSAMENTO CONCLUÍDO COM AVISOS")
        print("   Alguns arquivos foram gerados, mas houve problemas.")

    print(f"\n📂 Diretório de trabalho: {os.getcwd()}")
    print("🔄 Para reprocessar, execute este script novamente.")

if __name__ == "__main__":
    main()
