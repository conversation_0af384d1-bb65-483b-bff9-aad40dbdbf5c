<?php
session_start();
require_once __DIR__ . '/../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    header('Location: login.php');
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$mensagem = '';
$tipo_mensagem = '';

// Processar ação de limpeza
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['acao'])) {
    try {
        switch ($_POST['acao']) {
            case 'limpar_tudo':
                // Limpar todas as revisões do usuário
                $query = "DELETE FROM appestudo.lexjus_revisoes WHERE usuario_id = $1";
                $result = pg_query_params($conexao, $query, [$usuario_id]);

                if ($result) {
                    $count = pg_affected_rows($result);
                    $mensagem = "✅ Todas as revisões foram removidas! ($count artigos)";
                    $tipo_mensagem = 'success';
                } else {
                    throw new Exception('Erro ao limpar revisões: ' . pg_last_error($conexao));
                }
                break;

            case 'limpar_teste':
                // Limpar apenas artigos de teste específicos
                $artigos_teste = ['1', '5', '37', '196'];
                $placeholders = implode(',', array_map(function($i) { return '$' . ($i + 2); }, array_keys($artigos_teste)));

                $query = "DELETE FROM appestudo.lexjus_revisoes WHERE usuario_id = $1 AND artigo_numero IN ($placeholders)";
                $params = array_merge([$usuario_id], $artigos_teste);
                $result = pg_query_params($conexao, $query, $params);

                if ($result) {
                    $count = pg_affected_rows($result);
                    $mensagem = "✅ Artigos de teste removidos! ($count artigos)";
                    $tipo_mensagem = 'success';
                } else {
                    throw new Exception('Erro ao limpar artigos de teste: ' . pg_last_error($conexao));
                }
                break;

            case 'limpar_historico':
                // Limpar histórico de revisões
                $query = "DELETE FROM appestudo.lexjus_historico_revisoes WHERE usuario_id = $1";
                $result = pg_query_params($conexao, $query, [$usuario_id]);

                if ($result) {
                    $count = pg_affected_rows($result);
                    $mensagem = "✅ Histórico de revisões limpo! ($count registros)";
                    $tipo_mensagem = 'success';
                } else {
                    // Não falhar se a tabela não existir
                    $mensagem = "ℹ️ Histórico não encontrado ou já limpo";
                    $tipo_mensagem = 'info';
                }
                break;

            case 'resetar_sistema':
                // Resetar completamente o sistema de revisão
                pg_query($conexao, "BEGIN");

                // Limpar histórico
                $query1 = "DELETE FROM appestudo.lexjus_historico_revisoes WHERE usuario_id = $1";
                pg_query_params($conexao, $query1, [$usuario_id]);

                // Limpar revisões
                $query2 = "DELETE FROM appestudo.lexjus_revisoes WHERE usuario_id = $1";
                $result2 = pg_query_params($conexao, $query2, [$usuario_id]);

                if ($result2) {
                    pg_query($conexao, "COMMIT");
                    $count = pg_affected_rows($result2);
                    $mensagem = "✅ Sistema de revisão completamente resetado! ($count artigos removidos)";
                    $tipo_mensagem = 'success';
                } else {
                    pg_query($conexao, "ROLLBACK");
                    throw new Exception('Erro ao resetar sistema: ' . pg_last_error($conexao));
                }
                break;
        }
    } catch (Exception $e) {
        $mensagem = "❌ Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Buscar estatísticas atuais
$stats = [];
try {
    $query_stats = "
        SELECT
            COUNT(*) as total_artigos,
            COUNT(CASE WHEN status = 'novo' THEN 1 END) as novos,
            COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
            COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
            COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
            COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1";

    $result_stats = pg_query_params($conexao, $query_stats, [$usuario_id]);
    if ($result_stats) {
        $stats = pg_fetch_assoc($result_stats);
    }
} catch (Exception $e) {
    // Ignorar erro de estatísticas
}

// Buscar artigos específicos
$artigos_especificos = [];
try {
    $query_artigos = "
        SELECT artigo_numero, status, facilidade, repeticoes, total_revisoes, data_proxima_revisao
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1
        ORDER BY artigo_numero::integer";

    $result_artigos = pg_query_params($conexao, $query_artigos, [$usuario_id]);
    if ($result_artigos) {
        while ($row = pg_fetch_assoc($result_artigos)) {
            $artigos_especificos[] = $row;
        }
    }
} catch (Exception $e) {
    // Ignorar erro
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 Limpar Sistema de Revisão</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #666;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
        }
        .alert.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .alert.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .action-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #e74c3c;
        }
        .action-card h3 {
            margin-top: 0;
            color: #333;
        }
        .action-card p {
            color: #666;
            margin-bottom: 15px;
        }
        .btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
        }
        .btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .artigos-lista {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .artigo-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        .artigo-item:last-child {
            border-bottom: none;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }
        .status-novo { background: #ffc107; color: #212529; }
        .status-aprendendo { background: #007bff; color: white; }
        .status-revisando { background: #dc3545; color: white; }
        .status-dominado { background: #28a745; color: white; }
        .status-dificil { background: #6f42c1; color: white; }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Voltar ao LexJus
        </a>

        <h1>🧹 Limpar Sistema de Revisão</h1>

        <?php if ($mensagem): ?>
            <div class="alert <?php echo $tipo_mensagem; ?>">
                <?php echo $mensagem; ?>
            </div>
        <?php endif; ?>

        <div class="alert warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Atenção:</strong> Estas ações são irreversíveis! Certifique-se antes de prosseguir.
        </div>
    </div>

    <div class="container">
        <h2>📊 Estatísticas Atuais</h2>

        <?php if (!empty($stats) && $stats['total_artigos'] > 0): ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_artigos']; ?></div>
                    <div class="stat-label">Total</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['novos']; ?></div>
                    <div class="stat-label">Novos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['aprendendo']; ?></div>
                    <div class="stat-label">Aprendendo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['revisando']; ?></div>
                    <div class="stat-label">Revisando</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['dominados']; ?></div>
                    <div class="stat-label">Dominados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['dificeis']; ?></div>
                    <div class="stat-label">Difíceis</div>
                </div>
            </div>

            <?php if (!empty($artigos_especificos)): ?>
                <h3>📋 Artigos na Revisão</h3>
                <div class="artigos-lista">
                    <?php foreach ($artigos_especificos as $artigo): ?>
                        <div class="artigo-item">
                            <div>
                                <strong>Artigo <?php echo $artigo['artigo_numero']; ?></strong>
                                <span class="status-badge status-<?php echo $artigo['status']; ?>">
                                    <?php echo ucfirst($artigo['status']); ?>
                                </span>
                            </div>
                            <div>
                                Facilidade: <?php echo $artigo['facilidade']; ?> |
                                Repetições: <?php echo $artigo['repeticoes']; ?> |
                                Total: <?php echo $artigo['total_revisoes']; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="alert info">
                <i class="fas fa-info-circle"></i>
                Nenhum artigo encontrado no sistema de revisão.
            </div>
        <?php endif; ?>
    </div>

    <div class="container">
        <h2>🗑️ Ações de Limpeza</h2>

        <div class="action-grid">
            <div class="action-card">
                <h3><i class="fas fa-broom"></i> Limpar Artigos de Teste</h3>
                <p>Remove apenas os artigos 1º, 5º, 37 e 196 que foram usados para teste.</p>
                <form method="POST" onsubmit="return confirm('Tem certeza que deseja remover os artigos de teste?')">
                    <input type="hidden" name="acao" value="limpar_teste">
                    <button type="submit" class="btn">Limpar Artigos de Teste</button>
                </form>
            </div>

            <div class="action-card">
                <h3><i class="fas fa-history"></i> Limpar Histórico</h3>
                <p>Remove o histórico de revisões, mas mantém os artigos no sistema.</p>
                <form method="POST" onsubmit="return confirm('Tem certeza que deseja limpar o histórico?')">
                    <input type="hidden" name="acao" value="limpar_historico">
                    <button type="submit" class="btn secondary">Limpar Histórico</button>
                </form>
            </div>

            <div class="action-card">
                <h3><i class="fas fa-trash"></i> Limpar Tudo</h3>
                <p>Remove TODOS os artigos do seu sistema de revisão.</p>
                <form method="POST" onsubmit="return confirm('ATENÇÃO: Isso removerá TODOS os seus artigos de revisão. Tem certeza?')">
                    <input type="hidden" name="acao" value="limpar_tudo">
                    <button type="submit" class="btn danger">Limpar Todos os Artigos</button>
                </form>
            </div>

            <div class="action-card">
                <h3><i class="fas fa-redo"></i> Reset Completo</h3>
                <p>Reseta completamente o sistema: remove artigos, histórico e estatísticas.</p>
                <form method="POST" onsubmit="return confirm('ATENÇÃO: Isso resetará COMPLETAMENTE seu sistema de revisão. Esta ação é irreversível! Tem certeza?')">
                    <input type="hidden" name="acao" value="resetar_sistema">
                    <button type="submit" class="btn danger">Reset Completo</button>
                </form>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>💡 Recomendações</h2>
        <div class="alert info">
            <h4>🎯 Para começar do zero:</h4>
            <ol>
                <li><strong>Limpar Artigos de Teste</strong> - Se você só quer remover os artigos que criamos para teste</li>
                <li><strong>Reset Completo</strong> - Se você quer começar completamente do zero</li>
                <li>Depois, adicione apenas os artigos que você realmente quer estudar</li>
            </ol>
        </div>
    </div>
</body>
</html>
