<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

switch ($metodo) {
    case 'GET':
        // Obter progresso de leitura do usuário
        $query = "SELECT artigo_numero, lido, data_leitura
                 FROM appestudo.lexjus_progresso
                 WHERE usuario_id = $1";

        $result = pg_query_params($conexao, $query, [$usuario_id]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao consultar progresso']);
            exit;
        }

        $progresso = [];
        while ($row = pg_fetch_assoc($result)) {
            $progresso[$row['artigo_numero']] = [
                'lido' => (bool)$row['lido'],
                'data_leitura' => $row['data_leitura']
            ];
        }

        echo json_encode(['progresso' => $progresso]);
        break;

    case 'POST':
        // Marcar artigo como lido/não lido
        if (!isset($dados['artigo_numero']) || !isset($dados['lido'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }

        $artigo_numero = $dados['artigo_numero'];
        $lido = $dados['lido'] ? true : false;

        if ($lido) {
            // Se está marcando como lido, insere ou atualiza
            $query = "INSERT INTO appestudo.lexjus_progresso (usuario_id, artigo_numero, lido)
                     VALUES ($1, $2, $3)
                     ON CONFLICT (usuario_id, artigo_numero)
                     DO UPDATE SET lido = $3, data_leitura = CURRENT_TIMESTAMP";

            $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero, $lido]);
        } else {
            // Se está desmarcando como lido, remove o registro
            $query = "DELETE FROM appestudo.lexjus_progresso
                     WHERE usuario_id = $1 AND artigo_numero = $2";

            $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero]);
        }

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao atualizar progresso']);
            exit;
        }

        echo json_encode(['sucesso' => true]);
        break;

    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>