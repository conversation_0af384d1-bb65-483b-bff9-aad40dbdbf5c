#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SISTEMA DE ATUALIZAÇÃO UNIFICADA DO CPP
=======================================

Este script automatiza todo o processo de 3 etapas em uma única execução:
1. scraper_final_perfeito.py
2. corrigir_caputs_simples.py  
3. atualizar_cpp_rapido.py

USO:
    python atualizar_cpp_unificado.py

ARQUIVOS GERADOS:
    - cpp_lexjus_YYYYMMDD_HHMMSS.json
    - cpp_lexjus_YYYYMMDD_HHMMSS.js
"""

import subprocess
import sys
import os
import glob
from datetime import datetime

def log_info(mensagem):
    """Exibe mensagem com timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {mensagem}")

def executar_comando(comando, descricao):
    """Executa um comando e retorna True se bem-sucedido"""
    try:
        log_info(f"Executando: {descricao}...")
        
        # Configurar encoding para Windows
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        result = subprocess.run(
            ['python', comando], 
            capture_output=True, 
            text=True, 
            cwd='.', 
            env=env,
            encoding='utf-8',
            errors='replace'
        )
        
        if result.returncode == 0:
            log_info(f"✅ {descricao} concluído com sucesso")
            return True
        else:
            log_info(f"❌ Erro em {descricao}:")
            log_info(f"   {result.stderr}")
            return False
            
    except Exception as e:
        log_info(f"❌ Erro ao executar {descricao}: {e}")
        return False

def main():
    """Função principal - executa todo o processo unificado"""
    print("=" * 70)
    print("🔄 SISTEMA DE ATUALIZAÇÃO UNIFICADA DO CPP")
    print("=" * 70)
    print("📋 Automatizando processo de 3 etapas:")
    print("   1. Extração dos artigos (scraper_final_perfeito.py)")
    print("   2. Correção de caputs (corrigir_caputs_simples.py)")
    print("   3. Geração final (atualizar_cpp_rapido.py)")
    print("=" * 70)
    
    # Verificar se os scripts existem
    scripts_necessarios = [
        'scraper_final_perfeito.py',
        'corrigir_caputs_simples.py', 
        'atualizar_cpp_rapido.py'
    ]
    
    for script in scripts_necessarios:
        if not os.path.exists(script):
            log_info(f"❌ Script não encontrado: {script}")
            return
    
    # Executar os 3 passos
    passos = [
        ('scraper_final_perfeito.py', 'Extração dos artigos'),
        ('corrigir_caputs_simples.py', 'Correção de caputs'),
        ('atualizar_cpp_rapido.py', 'Geração dos arquivos finais')
    ]
    
    for script, descricao in passos:
        if not executar_comando(script, descricao):
            log_info(f"❌ Processo interrompido no passo: {descricao}")
            return
    
    # Buscar arquivos gerados
    try:
        arquivos_js = glob.glob('cpp_lexjus_*.js')
        arquivos_json = glob.glob('cpp_lexjus_*.json')
        
        if arquivos_js and arquivos_json:
            arquivo_js_mais_recente = max(arquivos_js, key=os.path.getctime)
            arquivo_json_mais_recente = max(arquivos_json, key=os.path.getctime)
            
            print("\n" + "=" * 70)
            print("🎉 ATUALIZAÇÃO UNIFICADA CONCLUÍDA COM SUCESSO!")
            print("=" * 70)
            print(f"📄 Arquivo JSON: {arquivo_json_mais_recente}")
            print(f"📄 Arquivo JS: {arquivo_js_mais_recente}")
            print("=" * 70)
            print("✅ QUALIDADE GARANTIDA:")
            print("   • Caputs completos e corrigidos")
            print("   • Estrutura legal preservada")
            print("   • Ordem dos artigos correta")
            print("   • Pronto para uso no LexJus")
            print("=" * 70)
            print("💡 PRÓXIMOS PASSOS:")
            print("   1. Copie o arquivo .js para o sistema LexJus")
            print("   2. Atualize as referências no código")
            print("   3. Teste o funcionamento")
            print("=" * 70)
        else:
            log_info("❌ Arquivos finais não encontrados")
            
    except Exception as e:
        log_info(f"❌ Erro ao buscar arquivos gerados: {e}")

if __name__ == '__main__':
    main()
