#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ATUALIZAÇÃO RÁPIDA DO CPP - LEXJUS
==================================

Este script usa o arquivo já corrigido como base e apenas atualiza
o timestamp e gera novos arquivos para uso no LexJus.

USO:
    python atualizar_cpp_rapido.py

ARQUIVOS GERADOS:
    - cpp_lexjus_YYYYMMDD_HHMMSS.json
    - cpp_lexjus_YYYYMMDD_HHMMSS.js
"""

import json
import re
from datetime import datetime
import os

def extrair_numero_artigo(artigo_id):
    """Extrai o número do artigo para ordenação correta"""
    # Padrão para artigos normais: Art. 123º
    match_normal = re.search(r'Art\.\s*(\d+)[ºo°]?$', artigo_id)
    if match_normal:
        return (int(match_normal.group(1)), '')

    # Padrão para artigos com sufixo: Art. 123º-A
    match_sufixo = re.search(r'Art\.\s*(\d+)[ºo°]?-([A-Z])', artigo_id)
    if match_sufixo:
        return (int(match_sufixo.group(1)), match_sufixo.group(2))

    # Se não conseguir extrair, retorna um valor alto para ficar no final
    return (9999, artigo_id)

def ordenar_artigos(artigos):
    """Ordena os artigos na sequência correta"""
    def chave_ordenacao(artigo):
        numero, sufixo = extrair_numero_artigo(artigo['artigo'])
        return (numero, sufixo)

    return sorted(artigos, key=chave_ordenacao)

def log_info(mensagem):
    """Exibe mensagem com timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {mensagem}")

def gerar_arquivos_lexjus():
    """Gera arquivos atualizados para o LexJus baseado no arquivo corrigido"""
    print("=" * 70)
    print("🚀 ATUALIZAÇÃO RÁPIDA DO CPP - LEXJUS")
    print("=" * 70)
    print("📋 Usando arquivo base já corrigido e perfeito")
    print("=" * 70)

    # Verificar se existe o arquivo base com ordem corrigida
    arquivo_base_ordenado = 'cpp_lexjus_ordenado_20250620_004215.json'
    arquivo_base_antigo = 'cpp_final_caputs_corrigidos.json'

    # Priorizar arquivo com ordem corrigida
    if os.path.exists(arquivo_base_ordenado):
        arquivo_base = arquivo_base_ordenado
        log_info("✅ Usando arquivo base com ordem corrigida")
    elif os.path.exists(arquivo_base_antigo):
        arquivo_base = arquivo_base_antigo
        log_info("⚠️  Usando arquivo base antigo - será aplicada correção de ordem")
    else:
        log_info(f"❌ Nenhum arquivo base encontrado")
        log_info("💡 Execute primeiro: python corrigir_caputs_simples.py")
        return

    # Carregar arquivo base
    try:
        with open(arquivo_base, 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        log_info(f"✅ Arquivo base carregado: {len(artigos)} artigos")
    except Exception as e:
        log_info(f"❌ Erro ao carregar arquivo base: {e}")
        return

    # Se usando arquivo antigo, aplicar correção de ordem
    if arquivo_base == arquivo_base_antigo:
        log_info("🔧 Aplicando correção de ordem dos artigos...")
        artigos = ordenar_artigos(artigos)
        log_info("✅ Ordem dos artigos corrigida")

    # Gerar timestamp para os novos arquivos
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Arquivo JSON para LexJus
    arquivo_json = f'cpp_lexjus_{timestamp}.json'
    try:
        with open(arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos, f, ensure_ascii=False, indent=2)
        log_info(f"✅ Arquivo JSON salvo: {arquivo_json}")
    except Exception as e:
        log_info(f"❌ Erro ao salvar JSON: {e}")
        return

    # Arquivo JavaScript para LexJus
    arquivo_js = f'cpp_lexjus_{timestamp}.js'
    js_content = f"""// Código de Processo Penal Brasileiro - LexJus
// Gerado automaticamente em: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// Total de artigos: {len(artigos)}
// Fonte: Site do Planalto + Correções Automáticas
// Status: PERFEITO - Caputs completos, estrutura corrigida e ordem legal correta

const codigoProcessoPenalArtigos = {json.dumps(artigos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Função para obter estatísticas do CPP
function obterEstatisticasCPP() {{
    const stats = {{
        totalArtigos: codigoProcessoPenalArtigos.length,
        artigosComIncisos: codigoProcessoPenalArtigos.filter(art => art.incisos.length > 0).length,
        artigosComParagrafos: codigoProcessoPenalArtigos.filter(art => art.paragrafos_numerados.length > 0).length,
        artigosComParagrafoUnico: codigoProcessoPenalArtigos.filter(art => art.paragrafo_unico).length,
        primeiroArtigo: codigoProcessoPenalArtigos[0]?.artigo || 'N/A',
        ultimoArtigo: codigoProcessoPenalArtigos[codigoProcessoPenalArtigos.length - 1]?.artigo || 'N/A'
    }};
    return stats;
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto,
        obterEstatisticasCPP
    }};
}}

// Log de inicialização
console.log('📚 CPP carregado:', obterEstatisticasCPP());
"""

    try:
        with open(arquivo_js, 'w', encoding='utf-8') as f:
            f.write(js_content)
        log_info(f"✅ Arquivo JavaScript salvo: {arquivo_js}")
    except Exception as e:
        log_info(f"❌ Erro ao salvar JavaScript: {e}")
        return

    # Estatísticas do arquivo
    stats = {
        'total_artigos': len(artigos),
        'artigos_com_incisos': len([art for art in artigos if art.get('incisos', [])]),
        'artigos_com_paragrafos': len([art for art in artigos if art.get('paragrafos_numerados', [])]),
        'artigos_com_paragrafo_unico': len([art for art in artigos if art.get('paragrafo_unico')])
    }

    print("\n" + "=" * 70)
    print("🎉 ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!")
    print("=" * 70)
    print(f"📄 Arquivo JSON: {arquivo_json}")
    print(f"📄 Arquivo JS: {arquivo_js}")
    print("=" * 70)
    print("📊 ESTATÍSTICAS:")
    print(f"   • Total de artigos: {stats['total_artigos']}")
    print(f"   • Artigos com incisos: {stats['artigos_com_incisos']}")
    print(f"   • Artigos com parágrafos: {stats['artigos_com_paragrafos']}")
    print(f"   • Artigos com parágrafo único: {stats['artigos_com_paragrafo_unico']}")
    print("=" * 70)
    print("✅ QUALIDADE:")
    print("   • Caputs completos e corrigidos")
    print("   • Estrutura legal preservada")
    print("   • Referências mantidas")
    print("   • Ordem legal correta (Art. 3º-A, 3º-B, 3º-C, 3º-D, 3º-E, 3º-F)")
    print("   • Pronto para uso no LexJus")
    print("=" * 70)
    print("💡 PRÓXIMOS PASSOS:")
    print(f"   1. Copie o arquivo {arquivo_js} para o LexJus")
    print("   2. Atualize as referências no código")
    print("   3. Teste o funcionamento")
    print("=" * 70)

def main():
    """Função principal"""
    gerar_arquivos_lexjus()

if __name__ == '__main__':
    main()
