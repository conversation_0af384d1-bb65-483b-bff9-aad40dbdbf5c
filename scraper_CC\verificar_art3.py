#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON gerado
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Procurar artigo 3
art3 = [a for a in data['artigos'] if a['numero'] == '3']

if art3:
    print("=== ARTIGO 3 ENCONTRADO ===")
    artigo = art3[0]
    print(f"Número: {artigo['numero']}")
    print(f"Caput: {artigo['caput']}")
    print(f"Incisos: {list(artigo['incisos'].keys())}")
    print(f"Parágrafos: {list(artigo['paragrafos'].keys())}")
    
    # Mostrar detalhes dos incisos
    if artigo['incisos']:
        print("\n=== INCISOS ===")
        for inciso, dados in artigo['incisos'].items():
            print(f"{inciso}: {dados['texto']}")
    
    # Mostrar detalhes dos parágrafos
    if artigo['paragrafos']:
        print("\n=== PARÁGRAFOS ===")
        for paragrafo, dados in artigo['paragrafos'].items():
            print(f"{paragrafo}: {dados['texto']}")
            
else:
    print("=== ARTIGO 3 NÃO ENCONTRADO ===")
    
    # Verificar artigos próximos
    print("\nArtigos próximos encontrados:")
    for artigo in data['artigos'][:10]:
        print(f"Art. {artigo['numero']}: {artigo['caput'][:50]}...")
