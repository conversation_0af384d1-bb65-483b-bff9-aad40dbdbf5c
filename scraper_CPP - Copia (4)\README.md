# 🔄 Sistema de Atualização do CPP - LexJus

Este diretório contém o sistema completo para atualizar automaticamente o Código de Processo Penal Brasileiro no sistema LexJus.

## 🚀 ATUALIZAÇÃO RÁPIDA (RECOMENDADO)

### Método 1: <PERSON><PERSON><PERSON>lique (Windows)
1. **Duplo clique** no arquivo `ATUALIZAR_CPP.bat`
2. Aguarde o processo terminar
3. Copie o arquivo `.js` mais recente para o LexJus

### Método 2: Linha de Comando
```bash
python atualizar_cpp_completo.py
```

## 📁 Arquivos do Sistema

### 🔧 Scripts de Atualização:
- **`atualizar_cpp_completo.py`** - ⭐ Script principal de atualização completa
- **`ATUALIZAR_CPP.bat`** - ⭐ Executável Windows para atualização rápida
- **`corrigir_caputs_simples.py`** - Correção de caputs cortados

### 📊 Scripts Legados (para referência):
- **`scraper_planalto_cpp.py`** - Scraper original do Planalto
- **`scraper_corpus927_cpp.py`** - Scraper alternativo do Corpus927
- **`limpar_referencias_finais_cpp.py`** - Limpeza de referências

### 📄 Arquivos Gerados:
- **`cpp_atualizado_YYYYMMDD_HHMMSS.json`** - Arquivo JSON atualizado
- **`cpp_atualizado_YYYYMMDD_HHMMSS.js`** - ⭐ Arquivo JavaScript para LexJus
- **`cpp_final_caputs_corrigidos.json`** - Versão atual corrigida

## 🎯 Como Atualizar o CPP no LexJus

### Passo a Passo:

1. **Execute a atualização:**
   ```bash
   # Opção 1: Duplo clique
   ATUALIZAR_CPP.bat

   # Opção 2: Linha de comando
   python atualizar_cpp_completo.py
   ```

2. **Identifique o arquivo mais recente:**
   - Procure por: `cpp_atualizado_YYYYMMDD_HHMMSS.js`
   - Exemplo: `cpp_atualizado_20241220_143052.js`

3. **Integre no LexJus:**
   - Copie o arquivo `.js` para a pasta do LexJus
   - Atualize as referências no código (se necessário)
   - Teste o funcionamento

## 📊 O Que o Sistema Faz

### ✅ Extração Completa:
- **845 artigos** do CPP completo
- **Todos os incisos, parágrafos e alíneas**
- **Referências legais preservadas**
- **Estrutura JSON perfeita**

### ✅ Correções Automáticas:
- **Caputs cortados** são corrigidos automaticamente
- **Texto completo** de todos os artigos
- **Formatação consistente**

### ✅ Arquivos Prontos:
- **JSON** para backup e análise
- **JavaScript** pronto para uso no LexJus
- **Funções de busca** incluídas

## 🔍 Estrutura dos Dados

Cada artigo segue esta estrutura:
```json
{
  "artigo": "Art. 1º",
  "caput": "O processo penal reger-se-á, em todo o território nacional...",
  "incisos": [
    "I - os tratados, as convenções e regras de direito internacional;",
    "II - as prerrogativas constitucionais..."
  ],
  "paragrafos_numerados": [
    {
      "numero": "§ 1°",
      "texto": "Texto do parágrafo...",
      "incisos": [],
      "alineas": []
    }
  ],
  "paragrafo_unico": "Texto do parágrafo único..."
}
```

## 🛠️ Solução de Problemas

### ❌ Erro de Conexão:
- Verifique sua conexão com a internet
- Tente novamente em alguns minutos

### ❌ Erro de Permissão:
- Execute como administrador (Windows)
- Verifique permissões da pasta

### ❌ Python não encontrado:
- Instale Python 3.7+
- Adicione Python ao PATH do sistema

## 📈 Histórico de Versões

### Estatísticas:
- **Código de Processo Penal completo** (Livro I ao Livro III)
- **Texto limpo** sem referências legislativas
- **Estrutura organizada** por livros, títulos e capítulos

### Exemplo de Estrutura:
```json
{
  "numero": 1,
  "titulo": "Art. 1º",
  "conteudo": "O processo penal reger-se-á, em todo o território nacional, por este Código...",
  "livro": "Livro I",
  "titulo_livro": "Do Processo em Geral",
  "capitulo": "Título I - Disposições Preliminares"
}
```

## 🔧 Dependências

### Python Packages:
```bash
pip install requests beautifulsoup4
```

### Imports Necessários:
- `requests` - Para buscar páginas web
- `beautifulsoup4` - Para parsing HTML
- `json` - Para manipulação JSON
- `re` - Para expressões regulares
- `datetime` - Para timestamps

## 📝 Funcionalidades do JavaScript Gerado

### Buscar Artigo por Número:
```javascript
const art1 = buscarArtigoCPP(1); // Retorna artigo sobre processo penal
```

### Buscar Artigos por Texto:
```javascript
const artigos = buscarArtigosCPPPorTexto("inquérito"); // Busca em todo o conteúdo
```

### Usar no HTML:
```html
<script src="codigo_processo_penal_limpo.js"></script>
<script>
  // Agora você pode usar as funções
  console.log(buscarArtigoCPP(1));
</script>
```

## 🎯 URL Fonte

**URL Principal:** `https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689.htm`

O scraper foi configurado para extrair da versão oficial do Código de Processo Penal Brasileiro, garantindo que todos os artigos e atualizações estejam incluídos.

## ⚠️ Observações

1. **Conexão Internet:** O scraper precisa de conexão para buscar a página online
2. **Estrutura HTML:** Se o Planalto alterar a estrutura da página, pode ser necessário ajustar as regex
3. **Encoding:** Todos os arquivos são salvos em UTF-8 para suportar caracteres especiais
4. **Limpeza:** As referências legislativas são removidas para melhor legibilidade no sistema de estudos

## 📞 Suporte

Para dúvidas ou problemas, verifique:
1. Se as dependências estão instaladas
2. Se há conexão com a internet
3. Se a estrutura da página do Planalto não foi alterada

## 🔄 Integração com LexJus

Os arquivos gerados são compatíveis com o sistema LexJus e podem ser integrados diretamente ao banco de dados do sistema de estudos jurídicos.
