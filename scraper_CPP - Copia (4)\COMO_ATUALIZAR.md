# 🔄 Como Atualizar o CPP no LexJus

## 🚀 MÉTODO RECOMENDADO (Rápido)

### Para Windows:
1. **Duplo clique** no arquivo `ATUALIZAR_CPP.bat`
2. Aguarde alguns segundos
3. Copie o arquivo `cpp_lexjus_YYYYMMDD_HHMMSS.js` para o LexJus

### Para Linux/Mac:
```bash
python atualizar_cpp_rapido.py
```

## 📁 Arquivos Gerados

Após executar a atualização, você terá:
- **`cpp_lexjus_YYYYMMDD_HHMMSS.json`** - Backup em JSON
- **`cpp_lexjus_YYYYMMDD_HHMMSS.js`** - ⭐ **ESTE É O ARQUIVO PARA O LEXJUS**

## 🎯 Integração no LexJus

### 1. Copiar o Arquivo
```bash
# Exemplo de nome do arquivo gerado:
cpp_lexjus_20241220_143052.js
```

### 2. Substituir no LexJus
- Copie o arquivo `.js` mais recente para a pasta do LexJus
- Renomeie para o nome esperado pelo sistema (se necessário)
- Ou atualize a referência no código HTML/JavaScript

### 3. Testar
- Abra o LexJus
- Teste a busca de artigos
- Verifique se os caputs estão completos
- Confirme que a navegação funciona

## ✅ Qualidade Garantida

O arquivo gerado tem:
- ✅ **845 artigos** completos do CPP
- ✅ **Caputs corrigidos** (não há mais textos cortados)
- ✅ **Estrutura perfeita** (incisos, parágrafos, alíneas)
- ✅ **Referências legais** preservadas
- ✅ **Funções de busca** incluídas

## 🔧 Métodos Alternativos

### Método Completo (Mais Lento)
Se quiser extrair tudo do zero do site do Planalto:
```bash
python atualizar_cpp_completo.py
```

### Método Manual
Se quiser fazer correções específicas:
```bash
python corrigir_caputs_simples.py
```

## 🛠️ Solução de Problemas

### ❌ "Python não encontrado"
- Instale Python 3.7+
- Adicione ao PATH do sistema

### ❌ "Arquivo não encontrado"
- Certifique-se de estar na pasta `scraper_CPP`
- Execute primeiro: `python corrigir_caputs_simples.py`

### ❌ "Erro de permissão"
- Execute como administrador (Windows)
- Verifique permissões da pasta

## 📊 Estrutura dos Dados

Cada artigo no arquivo JavaScript segue esta estrutura:
```javascript
{
  "artigo": "Art. 1º",
  "caput": "O processo penal reger-se-á, em todo o território nacional...",
  "incisos": [
    "I - os tratados, as convenções...",
    "II - as prerrogativas constitucionais..."
  ],
  "paragrafos_numerados": [
    {
      "numero": "§ 1°",
      "texto": "Texto do parágrafo...",
      "incisos": [],
      "alineas": []
    }
  ],
  "paragrafo_unico": "Texto do parágrafo único..."
}
```

## 🎉 Resultado Final

Após a atualização, você terá um arquivo JavaScript perfeito com:
- **Todos os 845 artigos** do Código de Processo Penal
- **Caputs completos** (problema dos textos cortados resolvido)
- **Estrutura legal completa** (incisos, parágrafos, alíneas)
- **Funções de busca prontas** para uso no LexJus
- **Compatibilidade total** com o sistema LexJus

## 📞 Suporte

Se tiver problemas:
1. Verifique se Python está instalado
2. Certifique-se de estar na pasta correta
3. Execute os comandos no terminal/prompt
4. Verifique as mensagens de erro

---

**✨ O sistema está pronto e funcionando perfeitamente!**
