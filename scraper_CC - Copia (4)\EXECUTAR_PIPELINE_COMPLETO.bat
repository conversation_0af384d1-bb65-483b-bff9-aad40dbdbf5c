@echo off
echo ========================================
echo   PIPELINE COMPLETO - CODIGO CIVIL
echo ========================================
echo.
echo Este script executa todo o pipeline na ordem correta:
echo.
echo 1. EXTRACAO DOS DADOS
echo    - Baixa/carrega o Codigo Civil
echo    - Extrai todos os 2046 artigos
echo    - Gera: codigo_civil_lexjus_corrigido.json
echo.
echo 2. PROCESSAMENTO COMPLETO
echo    - Converte para formato LexJus
echo    - Remove duplicacoes no caput
echo    - Remove duplicacoes nos paragrafos
echo    - Verifica qualidade dos dados
echo    - Gera: codigo_civil_formato_lexjus_final.json
echo.
echo Tempo estimado: 3-5 minutos
echo.
echo Escolha a fonte para extracao:
echo [1] Baixar da web (recomendado - versao mais atualizada)
echo [2] Usar arquivo local (L10406.html)
echo [3] Cancelar
echo.
set /p opcao="Digite sua opcao (1-3): "

if "%opcao%"=="3" (
    echo.
    echo Operacao cancelada.
    goto fim
)

echo.
echo ========================================
echo   PASSO 1: EXTRACAO DOS DADOS
echo ========================================
echo.

if "%opcao%"=="1" goto web
if "%opcao%"=="2" goto local
echo Opcao invalida. Usando modo padrao (web)...
goto web

:web
echo Executando extracao da web...
echo Baixando do site oficial do Planalto...
echo.
python executar_extracao_corrigido.py
goto verificar

:local
echo Verificando arquivo local...
if not exist "L10406.html" (
    echo ERRO: Arquivo L10406.html nao encontrado!
    echo.
    echo Solucoes:
    echo 1. Baixe o arquivo de: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm
    echo 2. Execute novamente escolhendo a opcao 1 (web)
    echo.
    pause
    goto fim
)
echo Executando extracao do arquivo local...
echo.
python executar_extracao_corrigido.py --local
goto verificar

:verificar

echo.
echo ========================================
echo   VERIFICANDO PASSO 1
echo ========================================

if not exist "codigo_civil_lexjus_corrigido.json" (
    echo ERRO: Extracao falhou!
    echo O arquivo codigo_civil_lexjus_corrigido.json nao foi gerado.
    echo.
    echo Verifique:
    echo - Conexao com internet (se usando web)
    echo - Arquivo L10406.html existe (se usando local)
    echo - Mensagens de erro acima
    echo.
    pause
    goto fim
)

echo Passo 1 concluido com sucesso!
echo Arquivo gerado: codigo_civil_lexjus_corrigido.json

for %%A in ("codigo_civil_lexjus_corrigido.json") do (
    echo Tamanho: %%~zA bytes
)

echo.
echo ========================================
echo   PASSO 2: PROCESSAMENTO COMPLETO
echo ========================================
echo.
echo Executando processamento completo...
echo Isso inclui:
echo - Conversao para formato LexJus
echo - Limpeza de duplicacoes no caput
echo - Limpeza de duplicacoes nos paragrafos
echo - Verificacao de qualidade
echo.

python processar_codigo_civil_completo.py

echo.
echo ========================================
echo   VERIFICANDO RESULTADO FINAL
echo ========================================

if exist "codigo_civil_formato_lexjus_final.json" (
    echo PIPELINE CONCLUIDO COM SUCESSO!
    echo.
    echo ARQUIVOS GERADOS:

    if exist "codigo_civil_lexjus_corrigido.json" (
        for %%A in ("codigo_civil_lexjus_corrigido.json") do (
            echo   codigo_civil_lexjus_corrigido.json - %%~zA bytes
        )
    )

    if exist "codigo_civil_formato_lexjus.json" (
        for %%A in ("codigo_civil_formato_lexjus.json") do (
            echo   codigo_civil_formato_lexjus.json - %%~zA bytes
        )
    )

    if exist "codigo_civil_formato_lexjus_limpo.json" (
        for %%A in ("codigo_civil_formato_lexjus_limpo.json") do (
            echo   codigo_civil_formato_lexjus_limpo.json - %%~zA bytes
        )
    )

    for %%A in ("codigo_civil_formato_lexjus_final.json") do (
        echo   codigo_civil_formato_lexjus_final.json - %%~zA bytes [ARQUIVO FINAL]
    )

    echo.
    echo PRONTO PARA USO NO SISTEMA LEXJUS!
    echo Use o arquivo: codigo_civil_formato_lexjus_final.json

) else (
    echo ERRO: Pipeline nao foi concluido corretamente!
    echo.
    echo O arquivo final nao foi gerado. Verifique:
    echo - Mensagens de erro acima
    echo - Se o Passo 1 foi executado corretamente
    echo - Se ha espaco em disco suficiente
    echo.
    echo Para diagnostico, execute:
    echo python verificar_limpeza_caput.py
)

echo.
echo ========================================
echo   PIPELINE FINALIZADO
echo ========================================

:fim
pause
