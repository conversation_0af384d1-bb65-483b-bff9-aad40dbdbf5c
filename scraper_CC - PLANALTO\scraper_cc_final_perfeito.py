#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRAPER CÓDIGO CIVIL - VERSÃO FINAL PERFEITA
Versão que entende perfeitamente a estrutura HTML do Planalto
Baseado na análise detalhada do arquivo CC.html
"""

import json
import re
import os
from datetime import datetime

def extrair_numero_para_ordenacao_corrigida(artigo_id):
    """Extrai número para ordenação correta dos artigos do Código Civil - VERSÃO CORRIGIDA"""
    # Extrair número principal do artigo
    match = re.search(r'Art\.\s*(\d+)(?:\.(\d+))?(?:º|°)?(?:-([A-Z]))?', artigo_id)
    if match:
        num_principal = int(match.group(1))
        num_secundario = int(match.group(2)) if match.group(2) else 0
        sufixo = match.group(3) if match.group(3) else ""

        # Converter sufixo para número (A=1, B=2, etc.)
        sufixo_num = ord(sufixo) - ord('A') + 1 if sufixo else 0

        # CORREÇÃO CRÍTICA: Tratar artigos com ponto corretamente
        if num_secundario > 0:
            # Para artigos como 1.400, 1.500, etc., criar número único
            # Exemplo: Art. 1.400 = 1400, Art. 1.500 = 1500
            numero_completo = num_principal * 1000 + num_secundario
            return (numero_completo, sufixo_num)
        else:
            # Para artigos normais como 1, 2, 3, etc., usar número principal
            # Mas verificar se tem ponto no texto original para diferenciar
            if '.000' in artigo_id or '.0' in artigo_id:
                # Art. 1.000 = 1000, Art. 2.000 = 2000
                return (num_principal * 1000, sufixo_num)
            else:
                # Art. 1º = 1, Art. 2º = 2
                return (num_principal, sufixo_num)

    return (0, 0)

def limpar_texto(texto):
    """Remove tags HTML e limpa o texto de forma mais robusta"""
    if not texto:
        return ""

    # Remover tags HTML
    texto = re.sub(r'<[^>]+>', ' ', texto)

    # Decodificar entidades HTML
    texto = texto.replace('&nbsp;', ' ')
    texto = texto.replace('&amp;', '&')
    texto = texto.replace('&lt;', '<')
    texto = texto.replace('&gt;', '>')
    texto = texto.replace('&quot;', '"')
    texto = texto.replace('&#39;', "'")

    # Remover caracteres especiais de formatação
    texto = re.sub(r'[\r\n\t]', ' ', texto)

    # Remover quebras de linha e espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)

    # Remover pontos no início (artefatos de extração)
    texto = re.sub(r'^\.\s*', '', texto)

    return texto.strip()

def extrair_artigos_html_planalto(html_content):
    """Extrai artigos seguindo a estrutura específica do HTML do Planalto"""
    print("Extraindo artigos com análise específica da estrutura HTML do Planalto...")

    # Dividir o HTML em linhas para análise linha por linha
    linhas = html_content.split('\n')

    artigos_encontrados = {}
    i = 0

    while i < len(linhas):
        linha = linhas[i].strip()

        # Buscar por padrão de artigo
        match_artigo = re.search(r'Art\.\s*(\d+(?:\.\d+)?(?:º|°)?(?:-[A-Z])?)', linha)

        if match_artigo:
            numero_artigo = match_artigo.group(1)

            # Formatar ID do artigo
            if numero_artigo.endswith(('º', '°')):
                artigo_id = f"Art. {numero_artigo}"
            elif '.' in numero_artigo and not numero_artigo.endswith(('º', '°')):
                artigo_id = f"Art. {numero_artigo}"
            elif '-' in numero_artigo:
                artigo_id = f"Art. {numero_artigo}"
            else:
                artigo_id = f"Art. {numero_artigo}º"

            # Coletar conteúdo das próximas linhas até encontrar outro artigo
            conteudo_adicional = []
            j = i + 1

            while j < len(linhas):
                linha_seguinte = linhas[j].strip()

                # Parar se encontrar outro artigo
                if re.search(r'Art\.\s*\d+(?:\.\d+)?(?:º|°)?(?:-[A-Z])?', linha_seguinte):
                    break

                # Parar se encontrar título/seção
                if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO|PARTE)', linha_seguinte, re.IGNORECASE):
                    break

                # Parar se encontrar div de fechamento
                if '</div>' in linha_seguinte:
                    break

                if linha_seguinte:
                    conteudo_adicional.append(linha_seguinte)

                j += 1

            # Processar conteúdo completo do artigo
            conteudo_completo = ' '.join([linha] + conteudo_adicional)

            # Extrair caput completo usando a nova função
            caput = extrair_caput_completo(conteudo_completo, numero_artigo)

            # Extrair incisos
            incisos = extrair_incisos_html(conteudo_completo)

            # Extrair parágrafos numerados
            paragrafos_numerados = extrair_paragrafos_numerados_html(conteudo_completo)

            # Extrair parágrafo único
            paragrafo_unico = extrair_paragrafo_unico_html(conteudo_completo)

            artigo = {
                "artigo": artigo_id,
                "caput": caput,
                "incisos": incisos,
                "paragrafos_numerados": paragrafos_numerados,
                "paragrafo_unico": paragrafo_unico
            }

            artigos_encontrados[artigo_id] = artigo

            # Log de progresso
            if len(artigos_encontrados) % 500 == 0:
                print(f"Processados {len(artigos_encontrados)} artigos...")

            i = j - 1  # Ajustar índice para continuar da próxima linha

        i += 1

    print(f"Extração concluída: {len(artigos_encontrados)} artigos encontrados")
    return list(artigos_encontrados.values())

def extrair_caput_completo(conteudo_completo, numero_artigo):
    """Extrai o caput completo do artigo, incluindo texto que pode estar em múltiplas linhas"""
    # Limpar o conteúdo primeiro
    conteudo_limpo = limpar_texto(conteudo_completo)

    # Padrões para extrair o caput completo
    patterns = [
        # Padrão 1: Art. X até encontrar inciso, parágrafo ou próximo artigo
        rf'Art\.\s*{re.escape(numero_artigo)}(?:º|°)?\s*(.+?)(?=\s*[IVX]+\s*[-–]|\s*§\s*\d+|\s*Parágrafo\s+único|\s*Art\.\s*\d+|$)',
        # Padrão 2: Art. X até encontrar estruturas específicas
        rf'Art\.\s*{re.escape(numero_artigo)}(?:º|°)?\s+(.+?)(?=\s*[IVX]+\s*[-–]|\s*§\s*\d+|\s*Parágrafo\s+único|\s*Art\.\s*\d+|$)',
        # Padrão 3: Mais flexível
        rf'Art\.\s*{re.escape(numero_artigo)}[.\s]*(.+?)(?=\s*[IVX]+\s*[-–]|\s*§\s*\d+|\s*Parágrafo\s+único|\s*Art\.\s*\d+|$)'
    ]

    for pattern in patterns:
        match = re.search(pattern, conteudo_limpo, re.IGNORECASE | re.DOTALL)
        if match:
            caput = match.group(1).strip()

            # Remover referências legais do final
            caput = re.sub(r'\s*\([^)]*(?:Lei|Redação|Vigência|Vide|Incluído|Revogado)[^)]*\)\s*$', '', caput)

            # Remover quebras de linha e espaços múltiplos
            caput = re.sub(r'\s+', ' ', caput)

            # Verificar se o caput não está vazio e tem conteúdo significativo
            if caput and len(caput.strip()) > 5:
                return caput.strip()

    return ""

def extrair_incisos_html(conteudo):
    """Extrai os incisos do conteúdo HTML"""
    incisos = []

    # Buscar incisos (I, II, III, IV, V, etc.)
    inciso_matches = re.finditer(r'([IVX]+\s*[-–]\s*[^<]+?)(?=[IVX]+\s*[-–]|§\s*\d+|Parágrafo\s+único|Art\.|$)', conteudo, re.DOTALL)

    for match in inciso_matches:
        inciso_texto = limpar_texto(match.group(1))
        if inciso_texto and len(inciso_texto) > 5:
            incisos.append(inciso_texto)

    return incisos

def extrair_paragrafos_numerados_html(conteudo):
    """Extrai os parágrafos numerados do conteúdo HTML"""
    paragrafos = []

    # Buscar parágrafos numerados (§ 1º, § 2º, etc.)
    paragrafo_matches = re.finditer(r'(§\s*\d+(?:º|°)?)\s*([^<]+?)(?=§\s*\d+|Parágrafo\s+único|Art\.|$)', conteudo, re.DOTALL)

    for match in paragrafo_matches:
        numero = limpar_texto(match.group(1))
        texto = limpar_texto(match.group(2))

        if texto and len(texto) > 5:
            paragrafos.append({
                "numero": numero,
                "texto": texto,
                "incisos": [],
                "alineas": []
            })

    return paragrafos

def extrair_paragrafo_unico_html(conteudo):
    """Extrai o parágrafo único do conteúdo HTML"""
    # Buscar parágrafo único
    match = re.search(r'Parágrafo\s+único\.?\s*([^<]+?)(?=Art\.|$)', conteudo, re.IGNORECASE | re.DOTALL)

    if match:
        texto = limpar_texto(match.group(1))
        if texto and len(texto) > 5:
            return texto

    return None

def scraper_cc_final_perfeito():
    """Função principal do scraper FINAL PERFEITO"""
    print("=" * 80)
    print("SCRAPER CC - VERSÃO FINAL PERFEITA")
    print("=" * 80)

    arquivo_local = "CC.html"

    try:
        # Carregar arquivo local
        if os.path.exists(arquivo_local):
            print(f"Carregando arquivo: {arquivo_local}")
            # Tentar diferentes encodings
            encodings = ['utf-8', 'latin-1', 'windows-1252', 'iso-8859-1']
            conteudo_html = None

            for encoding in encodings:
                try:
                    with open(arquivo_local, 'r', encoding=encoding) as f:
                        conteudo_html = f.read()
                    print(f"Arquivo carregado com encoding: {encoding}")
                    break
                except UnicodeDecodeError:
                    continue

            if conteudo_html is None:
                print(f"ERRO: Não foi possível carregar o arquivo!")
                return []
        else:
            print(f"ERRO: Arquivo {arquivo_local} não encontrado!")
            return []

        # Extrair artigos com análise específica do HTML
        artigos_extraidos = extrair_artigos_html_planalto(conteudo_html)

        # Ordenar por número do artigo - USANDO FUNÇÃO CORRIGIDA
        print("Ordenando artigos com função corrigida...")
        artigos_extraidos.sort(key=lambda x: extrair_numero_para_ordenacao_corrigida(x["artigo"]))

        print(f"Extração concluída. Total: {len(artigos_extraidos)} artigos únicos")

        # Salvar arquivo
        nome_arquivo_json = 'cc_final_perfeito.json'
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_extraidos, f, ensure_ascii=False, indent=2)
        print(f"Arquivo JSON salvo: {nome_arquivo_json}")

        # Estatísticas detalhadas
        print(f"\n" + "="*70)
        print(f"ESTATÍSTICAS FINAIS - VERSÃO FINAL PERFEITA:")
        print(f"="*70)
        print(f"   Total de artigos: {len(artigos_extraidos)}")

        if artigos_extraidos:
            print(f"   Primeiro artigo: {artigos_extraidos[0]['artigo']}")
            print(f"   Último artigo: {artigos_extraidos[-1]['artigo']}")

            # Contar estruturas
            total_incisos = sum(len(art['incisos']) for art in artigos_extraidos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos_extraidos)
            total_paragrafos_unicos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'])
            artigos_com_caput = sum(1 for art in artigos_extraidos if art['caput'])

            print(f"   Artigos com caput: {artigos_com_caput} ({artigos_com_caput/len(artigos_extraidos)*100:.1f}%)")
            print(f"   Total de incisos: {total_incisos}")
            print(f"   Total de parágrafos numerados: {total_paragrafos}")
            print(f"   Total de parágrafos únicos: {total_paragrafos_unicos}")

            # Verificar primeiros e últimos artigos
            print(f"\n📋 PRIMEIROS 10 ARTIGOS:")
            for art in artigos_extraidos[:10]:
                caput_preview = art['caput'][:50] + "..." if len(art['caput']) > 50 else art['caput']
                print(f"   {art['artigo']}: '{caput_preview}'")

            print(f"\n📋 ÚLTIMOS 10 ARTIGOS:")
            for art in artigos_extraidos[-10:]:
                caput_preview = art['caput'][:50] + "..." if len(art['caput']) > 50 else art['caput']
                print(f"   {art['artigo']}: '{caput_preview}'")

        return artigos_extraidos

    except Exception as e:
        print(f"ERRO CRÍTICO: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == '__main__':
    print("INICIANDO SCRAPER CC - VERSÃO FINAL PERFEITA")
    print("Objetivo: EXTRAIR TODOS OS ARTIGOS COM CAPUTS CORRETOS")
    print("-" * 80)

    artigos = scraper_cc_final_perfeito()

    if artigos:
        print(f"\n🎉 EXTRAÇÃO FINAL PERFEITA CONCLUÍDA! {len(artigos)} artigos extraídos!")
        print(f"✅ Caputs extraídos corretamente da estrutura HTML do Planalto")
    else:
        print(f"\n❌ FALHA na extração!")
