<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste do Sistema de Revisão - LexJus VOID</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-novo { background: #ffc107; color: #000; }
        .status-aprendendo { background: #17a2b8; color: #fff; }
        .status-revisando { background: #dc3545; color: #fff; }
        .status-dominado { background: #28a745; color: #fff; }
        .status-dificil { background: #6f42c1; color: #fff; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .quality-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        .btn-quality {
            padding: 8px 12px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-quality:hover {
            border-color: #007bff;
            background: #e7f3ff;
        }
        .btn-quality.q0 { border-color: #dc3545; }
        .btn-quality.q1 { border-color: #fd7e14; }
        .btn-quality.q2 { border-color: #ffc107; }
        .btn-quality.q3 { border-color: #20c997; }
        .btn-quality.q4 { border-color: #28a745; }
        .btn-quality.q5 { border-color: #6f42c1; }
    </style>
</head>
<body>
    <h1>🧪 Teste do Sistema de Revisão - LexJus VOID</h1>

    <div class="container">
        <h2>📊 Status da Autenticação</h2>
        <button onclick="verificarAutenticacao()">Verificar Autenticação</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="container">
        <h2>🎯 Gerenciamento de Revisões</h2>
        <div class="test-section">
            <h3>Adicionar Artigos à Revisão</h3>
            <button onclick="adicionarArtigo('1')">Adicionar 1º</button>
            <button onclick="adicionarArtigo('5')">Adicionar 5º</button>
            <button onclick="adicionarArtigo('37')">Adicionar 37</button>
            <button onclick="adicionarArtigo('196')">Adicionar 196</button>
            <div id="adicionar-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Listar Revisões</h3>
            <button onclick="listarRevisoes()">Todas as Revisões</button>
            <button onclick="listarPendentes()">Apenas Pendentes</button>
            <div id="listar-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📈 Estatísticas e Métricas</h2>
        <button onclick="obterEstatisticas()">Carregar Estatísticas</button>
        <div id="stats-display" class="stats-grid"></div>
        <div id="estatisticas-result" class="result"></div>
    </div>

    <div class="container">
        <h2>🎓 Simulação de Sessão de Revisão</h2>
        <div class="test-section">
            <h3>Preparar Artigos para Revisão</h3>
            <p>Primeiro, adicione alguns artigos ao sistema de revisão:</p>
            <button onclick="adicionarTodosArtigos()">➕ Adicionar Todos os Artigos de Teste</button>
            <div id="preparar-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Responder Revisões</h3>
            <p><strong>⚠️ Importante:</strong> Primeiro adicione os artigos acima, depois selecione um artigo e uma qualidade para simular uma resposta:</p>

            <div>
                <label>Artigo:</label>
                <select id="artigoSelect">
                    <option value="1">1º</option>
                    <option value="5">5º</option>
                    <option value="37">37</option>
                    <option value="196">196</option>
                </select>
                <button onclick="verificarArtigoExiste()" style="margin-left: 10px;">🔍 Verificar se Artigo Existe</button>
            </div>

            <div>
                <label>Qualidade da Resposta:</label>
                <div class="quality-buttons">
                    <button class="btn-quality q0" onclick="responderRevisaoSegura(0)">0 - Não lembro</button>
                    <button class="btn-quality q1" onclick="responderRevisaoSegura(1)">1 - Muito difícil</button>
                    <button class="btn-quality q2" onclick="responderRevisaoSegura(2)">2 - Difícil</button>
                    <button class="btn-quality q3" onclick="responderRevisaoSegura(3)">3 - Normal</button>
                    <button class="btn-quality q4" onclick="responderRevisaoSegura(4)">4 - Fácil</button>
                    <button class="btn-quality q5" onclick="responderRevisaoSegura(5)">5 - Muito fácil</button>
                </div>
            </div>

            <div id="verificar-artigo-result" class="result"></div>
            <div id="resposta-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>⚙️ Configurações</h2>
        <div class="test-section">
            <h3>Configuração Atual</h3>
            <button onclick="obterConfiguracao()">Carregar Configuração</button>
            <div id="config-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Atualizar Configuração</h3>
            <button onclick="atualizarConfiguracao()">Atualizar Limites</button>
            <div id="update-config-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📚 Histórico de Revisões</h2>
        <button onclick="obterHistorico()">Carregar Histórico (últimas 20)</button>
        <button onclick="obterHistoricoCompleto()">Histórico Completo (últimas 100)</button>
        <div id="historico-result" class="result"></div>
    </div>

    <div class="container">
        <h2>🧮 Teste do Algoritmo</h2>
        <div class="test-section">
            <h3>Calcular Próxima Revisão</h3>
            <p>Teste o algoritmo SM-2 com diferentes parâmetros:</p>
            <button onclick="testarAlgoritmo(2.5, 1, 0, 0)">Primeira vez - Erro (Q=0)</button>
            <button onclick="testarAlgoritmo(2.5, 1, 3, 0)">Primeira vez - Normal (Q=3)</button>
            <button onclick="testarAlgoritmo(2.5, 6, 4, 2)">Segunda revisão - Fácil (Q=4)</button>
            <button onclick="testarAlgoritmo(2.6, 15, 5, 3)">Artigo dominado - Perfeito (Q=5)</button>
            <div id="algoritmo-result" class="result"></div>
        </div>
    </div>

    <script>
        // Variáveis globais
        let ultimaRevisaoId = null;

        async function fazerRequisicao(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                const data = await response.json();
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        function mostrarResultado(elementId, resultado) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (resultado.ok ? 'success' : 'error');
            element.textContent = JSON.stringify(resultado, null, 2);
        }

        async function verificarAutenticacao() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=estatisticas');
            mostrarResultado('auth-result', resultado);
        }

        async function adicionarArtigo(artigoNumero) {
            const resultado = await fazerRequisicao('./api/revisao.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'iniciar',
                    artigo_numero: artigoNumero
                })
            });
            mostrarResultado('adicionar-result', resultado);
        }

        async function listarRevisoes() {
            const resultado = await fazerRequisicao('./api/revisao.php');
            mostrarResultado('listar-result', resultado);
        }

        async function listarPendentes() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=pendentes');
            mostrarResultado('listar-result', resultado);
        }

        async function obterEstatisticas() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=estatisticas');
            mostrarResultado('estatisticas-result', resultado);

            if (resultado.ok && resultado.data.estatisticas) {
                mostrarEstatisticasVisuais(resultado.data.estatisticas);
            }
        }

        function mostrarEstatisticasVisuais(stats) {
            const container = document.getElementById('stats-display');
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_artigos || 0}</div>
                    <div class="stat-label">Total de Artigos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.pendentes || 0}</div>
                    <div class="stat-label">Pendentes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.aprendendo || 0}</div>
                    <div class="stat-label">Aprendendo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.revisando || 0}</div>
                    <div class="stat-label">Revisando</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.dominados || 0}</div>
                    <div class="stat-label">Dominados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.facilidade_media || 0}</div>
                    <div class="stat-label">Facilidade Média</div>
                </div>
            `;
        }

        async function adicionarTodosArtigos() {
            const artigos = ['1', '5', '37', '196'];
            const resultados = [];

            for (const artigo of artigos) {
                const resultado = await fazerRequisicao('./api/revisao.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        acao: 'iniciar',
                        artigo_numero: artigo
                    })
                });
                resultados.push(`Artigo ${artigo}: ${resultado.ok ? 'Adicionado' : 'Erro'}`);
            }

            const resultadoFinal = {
                ok: true,
                data: {
                    mensagem: 'Processo concluído',
                    detalhes: resultados
                }
            };

            mostrarResultado('preparar-result', resultadoFinal);

            // Atualizar estatísticas após adicionar
            setTimeout(() => {
                obterEstatisticas();
            }, 1000);
        }

        async function verificarArtigoExiste() {
            const artigoSelect = document.getElementById('artigoSelect');
            const artigoNumero = artigoSelect.value;

            const resultado = await fazerRequisicao('./api/revisao.php');

            if (resultado.ok && resultado.data.revisoes) {
                const artigoExiste = resultado.data.revisoes.find(r => r.artigo_numero === artigoNumero);

                const verificacao = {
                    ok: true,
                    data: {
                        artigo_numero: artigoNumero,
                        existe: !!artigoExiste,
                        detalhes: artigoExiste || 'Artigo não encontrado no sistema de revisão',
                        instrucao: artigoExiste ?
                            'Artigo encontrado! Pode responder a revisão.' :
                            'Artigo não encontrado. Adicione-o primeiro usando o botão "Adicionar Todos os Artigos".'
                    }
                };

                mostrarResultado('verificar-artigo-result', verificacao);
            } else {
                mostrarResultado('verificar-artigo-result', resultado);
            }
        }

        async function responderRevisaoSegura(qualidade) {
            const artigoSelect = document.getElementById('artigoSelect');
            const artigoNumero = artigoSelect.value;

            // Primeiro verificar se o artigo existe
            const verificacao = await fazerRequisicao('./api/revisao.php');

            if (verificacao.ok && verificacao.data.revisoes) {
                const artigoExiste = verificacao.data.revisoes.find(r => r.artigo_numero === artigoNumero);

                if (!artigoExiste) {
                    // Artigo não existe, tentar adicionar primeiro
                    const adicionar = await fazerRequisicao('./api/revisao.php', {
                        method: 'POST',
                        body: JSON.stringify({
                            acao: 'iniciar',
                            artigo_numero: artigoNumero
                        })
                    });

                    if (!adicionar.ok) {
                        mostrarResultado('resposta-result', {
                            ok: false,
                            error: `Erro: Artigo ${artigoNumero} não existe no sistema. Tentativa de adicionar falhou: ${adicionar.data?.erro || 'Erro desconhecido'}`
                        });
                        return;
                    }

                    // Aguardar um pouco para o banco processar
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            // Agora tentar responder
            const resultado = await fazerRequisicao('./api/revisao.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'responder',
                    artigo_numero: artigoNumero,
                    qualidade: qualidade,
                    tempo_resposta: Math.floor(Math.random() * 60) + 10 // Simular tempo de 10-70s
                })
            });

            mostrarResultado('resposta-result', resultado);

            // Atualizar estatísticas após responder
            if (resultado.ok) {
                setTimeout(() => {
                    obterEstatisticas();
                }, 1000);
            }
        }

        async function responderRevisao(qualidade) {
            // Função antiga mantida para compatibilidade
            return responderRevisaoSegura(qualidade);
        }

        async function obterConfiguracao() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=configuracao');
            mostrarResultado('config-result', resultado);
        }

        async function atualizarConfiguracao() {
            const resultado = await fazerRequisicao('./api/revisao.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'configurar',
                    max_revisoes_dia: 25,
                    max_novos_dia: 15,
                    notificar_revisoes: true
                })
            });
            mostrarResultado('update-config-result', resultado);
        }

        async function obterHistorico() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=historico&limite=20');
            mostrarResultado('historico-result', resultado);
        }

        async function obterHistoricoCompleto() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=historico&limite=100');
            mostrarResultado('historico-result', resultado);
        }

        async function testarAlgoritmo(facilidade, intervalo, qualidade, repeticoes) {
            // Simular chamada do algoritmo
            const resultado = {
                ok: true,
                data: {
                    entrada: { facilidade, intervalo, qualidade, repeticoes },
                    simulacao: calcularProximaRevisaoSimulada(facilidade, intervalo, qualidade, repeticoes)
                }
            };
            mostrarResultado('algoritmo-result', resultado);
        }

        function calcularProximaRevisaoSimulada(facilidade, intervalo, qualidade, repeticoes) {
            // Implementação simplificada do algoritmo SM-2
            let novaFacilidade = facilidade + (0.1 - (5 - qualidade) * (0.08 + (5 - qualidade) * 0.02));
            novaFacilidade = Math.max(1.30, Math.min(3.00, novaFacilidade));

            let novoIntervalo, novasRepeticoes;

            if (qualidade < 3) {
                novasRepeticoes = 0;
                novoIntervalo = 1;
            } else {
                novasRepeticoes = repeticoes + 1;
                if (novasRepeticoes === 1) {
                    novoIntervalo = 1;
                } else if (novasRepeticoes === 2) {
                    novoIntervalo = 6;
                } else {
                    novoIntervalo = Math.round(intervalo * novaFacilidade);
                }
            }

            const proximaData = new Date();
            proximaData.setDate(proximaData.getDate() + novoIntervalo);

            return {
                nova_facilidade: novaFacilidade.toFixed(2),
                novo_intervalo: novoIntervalo,
                novas_repeticoes: novasRepeticoes,
                proxima_revisao: proximaData.toISOString().split('T')[0],
                status_sugerido: determinarStatus(qualidade, novasRepeticoes)
            };
        }

        function determinarStatus(qualidade, repeticoes) {
            if (qualidade < 3) return 'dificil';
            if (repeticoes >= 5) return 'dominado';
            if (repeticoes >= 2) return 'revisando';
            return 'aprendendo';
        }

        // Verificar autenticação ao carregar a página
        window.onload = function() {
            verificarAutenticacao();
        };
    </script>
</body>
</html>
