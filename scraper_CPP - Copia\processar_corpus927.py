#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para processar e limpar os dados do CPP extraídos da fonte Corpus927
"""

import json
import re
from datetime import datetime

def limpar_texto_avancado(texto):
    """
    Limpeza avançada de texto
    """
    if not texto:
        return ""
    
    # Remover referências legais
    texto = re.sub(r'\s*\([^)]*(?:Lei|Decreto|Incluído|Redação|Vide|Vigência|VETADO)[^)]*\)\s*', '', texto, flags=re.IGNORECASE)
    
    # Remover espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)
    
    return texto.strip()

def extrair_numero_para_ordenacao(artigo_id):
    """
    Extrai número do artigo para ordenação
    """
    match = re.search(r'Art\.\s*(\d+)', artigo_id)
    return int(match.group(1)) if match else 0

def consolidar_artigos_duplicados(artigos):
    """
    Remove duplicações e consolida artigos
    """
    artigos_unicos = {}
    
    for artigo in artigos:
        artigo_id = artigo["artigo"]
        
        if artigo_id in artigos_unicos:
            # Mesclar com artigo existente
            existente = artigos_unicos[artigo_id]
            
            # Mesclar caput (pegar o mais completo)
            if len(artigo["caput"]) > len(existente["caput"]):
                existente["caput"] = artigo["caput"]
            
            # Mesclar incisos
            for inciso in artigo["incisos"]:
                if inciso not in existente["incisos"]:
                    existente["incisos"].append(inciso)
            
            # Mesclar parágrafos
            for paragrafo in artigo["paragrafos_numerados"]:
                existe = any(p["numero"] == paragrafo["numero"] for p in existente["paragrafos_numerados"])
                if not existe:
                    existente["paragrafos_numerados"].append(paragrafo)
            
            # Mesclar parágrafo único
            if not existente["paragrafo_unico"] and artigo["paragrafo_unico"]:
                existente["paragrafo_unico"] = artigo["paragrafo_unico"]
        else:
            # Adicionar novo artigo
            artigos_unicos[artigo_id] = {
                "artigo": artigo_id,
                "caput": limpar_texto_avancado(artigo["caput"]),
                "incisos": [limpar_texto_avancado(i) for i in artigo["incisos"]],
                "paragrafos_numerados": [
                    {
                        "numero": p["numero"],
                        "texto": limpar_texto_avancado(p["texto"]),
                        "incisos": [limpar_texto_avancado(i) for i in p["incisos"]],
                        "alineas": [limpar_texto_avancado(a) for a in p["alineas"]]
                    }
                    for p in artigo["paragrafos_numerados"]
                ],
                "paragrafo_unico": limpar_texto_avancado(artigo["paragrafo_unico"]) if artigo["paragrafo_unico"] else None
            }
    
    return list(artigos_unicos.values())

def filtrar_artigos_validos(artigos):
    """
    Filtra artigos válidos (com conteúdo significativo)
    """
    artigos_validos = []
    
    for artigo in artigos:
        # Verificar se tem conteúdo válido
        tem_caput = artigo["caput"] and len(artigo["caput"]) > 10
        tem_incisos = len(artigo["incisos"]) > 0
        tem_paragrafos = len(artigo["paragrafos_numerados"]) > 0
        tem_paragrafo_unico = artigo["paragrafo_unico"] and len(artigo["paragrafo_unico"]) > 10
        
        if tem_caput or tem_incisos or tem_paragrafos or tem_paragrafo_unico:
            artigos_validos.append(artigo)
    
    return artigos_validos

def processar_corpus927():
    """
    Função principal de processamento
    """
    print("=" * 60)
    print("🔧 PROCESSAMENTO CPP CORPUS927")
    print("=" * 60)
    
    # Carregar dados
    try:
        with open('cpp_corpus927.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Carregados {len(artigos)} artigos")
    except FileNotFoundError:
        print("❌ Arquivo cpp_corpus927.json não encontrado!")
        return
    
    # 1. Consolidar duplicações
    print("🔄 Consolidando artigos duplicados...")
    artigos = consolidar_artigos_duplicados(artigos)
    print(f"   Artigos após consolidação: {len(artigos)}")
    
    # 2. Filtrar artigos válidos
    print("🔄 Filtrando artigos válidos...")
    artigos = filtrar_artigos_validos(artigos)
    print(f"   Artigos válidos: {len(artigos)}")
    
    # 3. Ordenar por número
    print("🔄 Ordenando artigos...")
    artigos.sort(key=lambda x: extrair_numero_para_ordenacao(x["artigo"]))
    
    # 4. Salvar arquivos processados
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # JSON
    nome_arquivo_json = 'cpp_corpus927_processado.json'
    with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
        json.dump(artigos, f, ensure_ascii=False, indent=2)
    print(f"💾 Arquivo JSON processado salvo: {nome_arquivo_json}")
    
    # JavaScript
    nome_arquivo_js = 'cpp_corpus927_processado.js'
    js_content = f"""// Código de Processo Penal Brasileiro - Corpus927 Processado
// Gerado em: {timestamp}
// Total de artigos: {len(artigos)}
// Fonte: https://corpus927.enfam.jus.br/legislacao/cpp-41

const codigoProcessoPenalArtigos = {json.dumps(artigos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""
    
    with open(nome_arquivo_js, 'w', encoding='utf-8') as f:
        f.write(js_content)
    print(f"💾 Arquivo JavaScript processado salvo: {nome_arquivo_js}")
    
    # Estatísticas finais
    print(f"\n📊 RESULTADO FINAL:")
    print(f"   • Total de artigos: {len(artigos)}")
    if artigos:
        print(f"   • Primeiro artigo: {artigos[0]['artigo']}")
        print(f"   • Último artigo: {artigos[-1]['artigo']}")
        
        # Contar estruturas
        total_incisos = sum(len(art['incisos']) for art in artigos)
        total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos)
        total_paragrafos_unicos = sum(1 for art in artigos if art['paragrafo_unico'])
        
        print(f"   • Total de incisos: {total_incisos}")
        print(f"   • Total de parágrafos numerados: {total_paragrafos}")
        print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")
    
    print(f"\n✅ PROCESSAMENTO CONCLUÍDO!")
    return artigos

if __name__ == '__main__':
    artigos = processar_corpus927()
    if artigos:
        print(f"🎉 {len(artigos)} artigos processados com sucesso!")
    else:
        print("❌ Falha no processamento!")
