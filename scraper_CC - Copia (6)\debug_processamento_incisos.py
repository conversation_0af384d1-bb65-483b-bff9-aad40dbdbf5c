#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# Carregar HTML
with open('L10406_web_backup.html', 'r', encoding='utf-8') as f:
    conteudo = f.read()

# Simular o processamento dos incisos do artigo 4
print("=== SIMULANDO PROCESSAMENTO DOS INCISOS DO ARTIGO 4 ===")

# Padrão para incisos
padrao_incisos = r'name="(art\d+(?:[a-z](?:\.\d+)?)?(?:p?[ivx]+)\.?)"'
nomes_incisos = re.findall(padrao_incisos, conteudo, re.IGNORECASE)

# Filtrar apenas incisos do artigo 4
incisos_art4 = [name for name in nomes_incisos if name.startswith('art4') and re.match(r'art4[ivx]', name, re.IGNORECASE)]

print(f"Incisos do artigo 4 encontrados: {incisos_art4}")

# Simular o processamento de cada inciso
artigos = {'4': {'incisos': {}}}

for name in incisos_art4:
    print(f"\n--- Processando {name} ---")
    
    # Extrair número e inciso
    match = re.match(r'^art(\d+)([a-z])?(?:\.(\d+))?(p?[ivx]+)\.?$', name, re.IGNORECASE)
    if not match:
        print(f"  ❌ Não fez match com o padrão")
        continue
    
    numero_base = match.group(1)
    sufixo_letra = match.group(2).lower() if match.group(2) else ''
    versao_numerica = match.group(3) if match.group(3) else ''
    inciso_completo = match.group(4)
    tem_ponto = name.endswith('.')
    
    print(f"  numero_base: {numero_base}")
    print(f"  sufixo_letra: {sufixo_letra}")
    print(f"  versao_numerica: {versao_numerica}")
    print(f"  inciso_completo: {inciso_completo}")
    print(f"  tem_ponto: {tem_ponto}")
    
    # Determinar se é inciso de parágrafo único ou normal
    if inciso_completo.startswith('p'):
        inciso_romano = inciso_completo[1:]
        eh_inciso_paragrafo = True
    else:
        inciso_romano = inciso_completo
        eh_inciso_paragrafo = False
    
    print(f"  inciso_romano: {inciso_romano}")
    print(f"  eh_inciso_paragrafo: {eh_inciso_paragrafo}")
    
    chave_inciso = inciso_completo if eh_inciso_paragrafo else inciso_romano
    print(f"  chave_inciso: {chave_inciso}")
    
    # Verificar se já existe e priorizar versão com ponto
    if chave_inciso in artigos['4']['incisos'] and not tem_ponto:
        print(f"  ❌ Pulando: já existe versão com ponto")
        continue
    
    # Verificar se está dentro de <strike>
    padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
    match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)
    
    if match_pos:
        inicio_contexto = max(0, match_pos.start() - 200)
        contexto_anterior = conteudo[inicio_contexto:match_pos.start()]
        
        strike_aberta = contexto_anterior.rfind('<strike>')
        strike_fechada = contexto_anterior.rfind('</strike>')
        
        if strike_aberta > strike_fechada:
            print(f"  ❌ Pulando: está dentro de <strike>")
            continue
        
        print(f"  ✅ Não está dentro de <strike>")
        
        # Extrair texto
        inicio = match_pos.end()
        texto_restante = conteudo[inicio:inicio+1000]
        
        padrao_inciso = rf'{inciso_romano.upper()}\s*-\s*(.*?)(?=<a name="|</p>|[IVX]+\s*-|$)'
        match_inciso = re.search(padrao_inciso, texto_restante, re.DOTALL | re.IGNORECASE)
        
        if match_inciso:
            texto_html = match_inciso.group(1)
            texto = re.sub(r'<[^>]+>', '', texto_html)
            texto = texto.replace('&nbsp;', ' ')
            texto = texto.replace('&amp;', '&')
            texto = re.sub(r'\s+', ' ', texto)
            texto = texto.strip()
            
            # Remover referências legais
            texto = re.sub(r'\s*\([^)]*(?:Redação dada|Vigência|Vide Lei|Incluído|Alterado|Revogado)[^)]*\)\s*', ' ', texto)
            texto = re.sub(r'\s+', ' ', texto).strip()
            
            print(f"  texto extraído: '{texto}'")
            
            # Verificar filtros
            if (texto and len(texto) > 5 and
                not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo)', texto, re.IGNORECASE) and
                not re.match(r'^\s*;?\s*$', texto) and
                'Revogado' not in texto):
                
                print(f"  ✅ Texto aprovado nos filtros")
                artigos['4']['incisos'][chave_inciso] = {
                    'texto': texto,
                    'versao_atual': tem_ponto
                }
            else:
                print(f"  ❌ Texto rejeitado pelos filtros")
        else:
            print(f"  ❌ Não conseguiu extrair texto com o padrão")
    else:
        print(f"  ❌ Não encontrou a tag <a name>")

print(f"\n=== RESULTADO FINAL ===")
print(f"Incisos processados: {list(artigos['4']['incisos'].keys())}")
for inciso, dados in artigos['4']['incisos'].items():
    print(f"  {inciso}: {dados['texto']}")
