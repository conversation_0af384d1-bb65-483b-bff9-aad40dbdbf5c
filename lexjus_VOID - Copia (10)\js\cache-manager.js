/**
 * Gerenciador de Cache do LexJus
 *
 * Este arquivo gerencia o cache no lado do cliente e força atualizações
 * quando necessário, resolvendo problemas de cache do navegador.
 */

class CacheManager {
    constructor() {
        this.version = this.getCurrentVersion();
        this.storageKey = 'lexjus_cache_version';
        this.init();
    }

    /**
     * Inicializa o gerenciador de cache
     */
    init() {
        this.checkForUpdates();
        this.setupServiceWorker();
        this.addCacheBustingToFetch();
    }

    /**
     * Obtém a versão atual do sistema
     */
    getCurrentVersion() {
        // Tentar obter a versão de um meta tag ou variável global
        const metaVersion = document.querySelector('meta[name="app-version"]');
        if (metaVersion) {
            return metaVersion.getAttribute('content');
        }

        // Fallback: usar timestamp atual
        return Date.now().toString();
    }

    /**
     * Verifica se há atualizações disponíveis
     */
    checkForUpdates() {
        const storedVersion = localStorage.getItem(this.storageKey);

        if (storedVersion && storedVersion !== this.version) {
            console.log('🔄 Nova versão detectada, limpando cache...');
            this.clearAllCaches();
            this.showUpdateNotification();
        }

        // Atualizar versão armazenada
        localStorage.setItem(this.storageKey, this.version);
    }

    /**
     * Limpa todos os caches
     */
    clearAllCaches() {
        // Limpar localStorage (exceto dados importantes)
        const importantKeys = ['lexjus_theme', 'lexjus_user_preferences'];
        const keysToRemove = [];

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && !importantKeys.includes(key)) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => localStorage.removeItem(key));

        // Limpar sessionStorage
        sessionStorage.clear();

        // Limpar cache do navegador (se suportado)
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                });
            });
        }
    }

    /**
     * Mostra notificação de atualização
     */
    showUpdateNotification() {
        // Criar notificação discreta
        const notification = document.createElement('div');
        notification.className = 'cache-update-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-sync-alt"></i>
                <span>Sistema atualizado! Recarregando...</span>
            </div>
        `;

        // Adicionar estilos
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: slideIn 0.3s ease-out;
        `;

        // Adicionar animação CSS
        if (!document.querySelector('#cache-notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'cache-notification-styles';
            styles.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                .cache-update-notification .fas {
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(notification);

        // Recarregar página após 2 segundos
        setTimeout(() => {
            window.location.reload(true);
        }, 2000);
    }

    /**
     * Configura Service Worker para cache
     */
    setupServiceWorker() {
        // Desabilitar Service Worker em desenvolvimento
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('ℹ️ Service Worker desabilitado em desenvolvimento');
            return;
        }

        if ('serviceWorker' in navigator) {
            // Usar caminho relativo para o service worker
            const swPath = './sw.js';

            navigator.serviceWorker.register(swPath)
                .then(registration => {
                    console.log('✅ Service Worker registrado');

                    // Verificar atualizações automaticamente
                    registration.addEventListener('updatefound', () => {
                        console.log('🔄 Nova versão do Service Worker encontrada');
                        const newWorker = registration.installing;

                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                console.log('🎉 Nova versão instalada, ativando automaticamente...');
                                // Notificar o novo service worker para tomar controle
                                newWorker.postMessage({ type: 'SKIP_WAITING' });
                            }
                        });
                    });

                    // Detectar quando nova versão toma controle
                    navigator.serviceWorker.addEventListener('controllerchange', () => {
                        console.log('🔄 Service Worker atualizado, recarregando página...');
                        window.location.reload();
                    });

                    // Verificar atualizações a cada 30 segundos
                    setInterval(() => {
                        registration.update();
                    }, 30000);
                })
                .catch(error => {
                    console.log('ℹ️ Service Worker não disponível:', error.message);
                });
        }
    }

    /**
     * Adiciona cache busting automático ao fetch
     */
    addCacheBustingToFetch() {
        // Interceptar fetch para adicionar cache busting
        const originalFetch = window.fetch;

        window.fetch = function(resource, options = {}) {
            // Se for uma requisição para arquivos locais CSS/JS
            if (typeof resource === 'string' &&
                (resource.endsWith('.css') || resource.endsWith('.js')) &&
                !resource.includes('://') &&
                !resource.includes('?v=')) {

                const separator = resource.includes('?') ? '&' : '?';
                resource = `${resource}${separator}v=${Date.now()}`;
            }

            return originalFetch.call(this, resource, options);
        };
    }

    /**
     * Força recarregamento de um arquivo específico
     */
    static reloadAsset(url) {
        if (url.endsWith('.css')) {
            // Recarregar CSS
            const links = document.querySelectorAll(`link[href*="${url.split('?')[0]}"]`);
            links.forEach(link => {
                const newLink = link.cloneNode();
                newLink.href = `${url.split('?')[0]}?v=${Date.now()}`;
                link.parentNode.insertBefore(newLink, link.nextSibling);
                setTimeout(() => link.remove(), 100);
            });
        } else if (url.endsWith('.js')) {
            // Para JS, é necessário recarregar a página
            console.log('🔄 Recarregando página para atualizar JavaScript...');
            window.location.reload(true);
        }
    }

    /**
     * Verifica se há atualizações no servidor
     */
    static async checkServerUpdates() {
        // Desabilitar verificações em desenvolvimento
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            return false;
        }

        try {
            // Usar caminho relativo para funcionar em qualquer estrutura
            const apiPath = './api/version.php';

            const response = await fetch(apiPath, {
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });

            if (response.ok) {
                const data = await response.json();
                const currentVersion = localStorage.getItem('lexjus_cache_version');

                if (data.version && data.version !== currentVersion) {
                    return true; // Há atualizações
                }
            }
        } catch (error) {
            console.log('ℹ️ API de versão não disponível ainda:', error.message);
        }

        return false;
    }
}

// Inicializar gerenciador de cache quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.cacheManager = new CacheManager();
});

// Verificar atualizações periodicamente (a cada 5 minutos)
setInterval(async () => {
    const hasUpdates = await CacheManager.checkServerUpdates();
    if (hasUpdates) {
        window.cacheManager.showUpdateNotification();
    }
}, 5 * 60 * 1000);

// Exportar para uso global
window.CacheManager = CacheManager;
