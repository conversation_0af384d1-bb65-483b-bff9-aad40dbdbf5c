#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para copiar a versão final do CPP para o diretório LexJus
"""

import shutil
import os
import json
from datetime import datetime

def copiar_final_para_lexjus():
    """
    Copia os arquivos CPP v2 para o diretório LexJus
    """
    print("=" * 60)
    print("📁 COPIANDO CPP V2 FINAL PARA LEXJUS")
    print("=" * 60)
    
    # Arquivos de origem (versão v2 corrigida)
    arquivo_origem_json = 'cpp_corpus927_v2.json'
    arquivo_origem_js = 'cpp_corpus927_v2.js'
    
    # Diretório de destino
    diretorio_destino = '../lexjus_VOID'
    
    # Arquivos de destino
    arquivo_destino_json = os.path.join(diretorio_destino, 'codigo_processo_penal.json')
    arquivo_destino_js = os.path.join(diretorio_destino, 'codigo_processo_penal.js')
    
    try:
        # Verificar se os arquivos de origem existem
        if not os.path.exists(arquivo_origem_json):
            print(f"❌ Arquivo {arquivo_origem_json} não encontrado!")
            return
        
        if not os.path.exists(arquivo_origem_js):
            print(f"❌ Arquivo {arquivo_origem_js} não encontrado!")
            return
        
        # Verificar se o diretório de destino existe
        if not os.path.exists(diretorio_destino):
            print(f"❌ Diretório {diretorio_destino} não encontrado!")
            return
        
        # Carregar e verificar dados
        with open(arquivo_origem_json, 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        
        print(f"📊 Dados carregados: {len(artigos)} artigos")
        
        # Fazer backup dos arquivos antigos se existirem
        if os.path.exists(arquivo_destino_json):
            backup_json = arquivo_destino_json + '.backup'
            shutil.copy2(arquivo_destino_json, backup_json)
            print(f"🔄 Backup criado: {backup_json}")
        
        if os.path.exists(arquivo_destino_js):
            backup_js = arquivo_destino_js + '.backup'
            shutil.copy2(arquivo_destino_js, backup_js)
            print(f"🔄 Backup criado: {backup_js}")
        
        # Copiar arquivo JSON
        shutil.copy2(arquivo_origem_json, arquivo_destino_json)
        print(f"✅ Arquivo JSON copiado: {arquivo_destino_json}")
        
        # Copiar arquivo JS
        shutil.copy2(arquivo_origem_js, arquivo_destino_js)
        print(f"✅ Arquivo JS copiado: {arquivo_destino_js}")
        
        # Verificar tamanhos dos arquivos
        tamanho_json = os.path.getsize(arquivo_destino_json)
        tamanho_js = os.path.getsize(arquivo_destino_js)
        
        print(f"\n📊 INFORMAÇÕES DOS ARQUIVOS:")
        print(f"   • JSON: {tamanho_json:,} bytes")
        print(f"   • JS: {tamanho_js:,} bytes")
        print(f"   • Total de artigos: {len(artigos)}")
        
        if artigos:
            print(f"   • Primeiro artigo: {artigos[0]['artigo']}")
            print(f"   • Último artigo: {artigos[-1]['artigo']}")
            
            # Estatísticas de conteúdo
            total_incisos = sum(len(art['incisos']) for art in artigos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos)
            total_paragrafos_unicos = sum(1 for art in artigos if art['paragrafo_unico'])
            
            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")
            
            # Verificar qualidade dos caputs
            caputs_completos = sum(1 for art in artigos if art['caput'] and len(art['caput']) > 20)
            print(f"   • Caputs completos (>20 chars): {caputs_completos}")
        
        print(f"\n✅ CÓPIA FINAL CONCLUÍDA COM SUCESSO!")
        print(f"   📄 Os arquivos do CPP V2 estão prontos para uso no LexJus")
        print(f"   🌐 Fonte: https://corpus927.enfam.jus.br/legislacao/cpp-41")
        print(f"   🔧 Versão: Scraper V2 com caputs completos")
        
        # Criar arquivo de informações atualizado
        info_content = f"""# Código de Processo Penal - Informações V2

## Fonte
- **URL**: https://corpus927.enfam.jus.br/legislacao/cpp-41
- **Extraído em**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Método**: Scraper Corpus927 V2 (Versão Corrigida)

## Estatísticas
- **Total de artigos**: {len(artigos)}
- **Total de incisos**: {total_incisos}
- **Total de parágrafos numerados**: {total_paragrafos}
- **Total de parágrafos únicos**: {total_paragrafos_unicos}
- **Caputs completos**: {caputs_completos}

## Melhorias V2
- ✅ Caputs completos extraídos corretamente
- ✅ Estrutura hierárquica preservada
- ✅ Duplicações removidas
- ✅ Referências legais limpas
- ✅ Texto normalizado e formatado
- ✅ Artigos ordenados numericamente

## Arquivos Gerados
- `codigo_processo_penal.json` - Dados em formato JSON
- `codigo_processo_penal.js` - Dados em formato JavaScript com funções de busca

## Qualidade dos Dados
A versão V2 corrige os problemas de caputs cortados da versão anterior,
garantindo que o texto completo dos artigos seja extraído corretamente.

## Exemplos de Qualidade
- **Art. 1º**: "O processo penal reger-se-á, em todo o território brasileiro, por este Código, ressalvados:"
- **Art. 2º**: "A lei processual penal aplicar-se-á desde logo, sem prejuízo da validade dos atos realizados sob a vigência da lei anterior."
- **Art. 3º**: "A lei processual penal admitirá interpretação extensiva e aplicação analógica, bem como o suplemento dos princípios gerais de direito."

## Uso no LexJus
Os arquivos estão prontos para integração no sistema LexJus e podem ser utilizados
para busca, estudo e consulta dos artigos do Código de Processo Penal.
"""
        
        info_file = os.path.join(diretorio_destino, 'cpp_info_v2.md')
        with open(info_file, 'w', encoding='utf-8') as f:
            f.write(info_content)
        print(f"   📄 Arquivo de informações V2 criado: cpp_info_v2.md")
        
    except Exception as e:
        print(f"❌ Erro ao copiar arquivos: {e}")

if __name__ == '__main__':
    copiar_final_para_lexjus()
