#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json
import os
import requests
import sys
from urllib.parse import urljoin

# Mudar para o diretório do script
os.chdir(os.path.dirname(os.path.abspath(__file__)))

def baixar_html_web(url):
    """Baixa o HTML diretamente da web"""
    print(f"Baixando HTML da web: {url}")

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        # Detectar encoding
        if response.encoding:
            conteudo = response.text
        else:
            # Tentar diferentes encodings
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    conteudo = response.content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                conteudo = response.content.decode('utf-8', errors='ignore')

        print(f"HTML baixado com sucesso: {len(conteudo):,} caracteres")

        # Salvar uma cópia local para backup
        with open('L10406_web_backup.html', 'w', encoding='utf-8') as f:
            f.write(conteudo)
        print("Backup salvo: L10406_web_backup.html")

        return conteudo

    except requests.RequestException as e:
        print(f"ERRO ao baixar da web: {e}")
        return None
    except Exception as e:
        print(f"ERRO inesperado ao baixar: {e}")
        return None

def carregar_html(forcar_local=False):
    """Carrega HTML da web ou arquivo local"""
    url_web = "https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm"
    arquivo_local = "L10406.html"

    print("=== CARREGANDO CÓDIGO CIVIL ===")

    if forcar_local:
        print("🔒 Modo forçado: Usando apenas arquivo local")

        if os.path.exists(arquivo_local):
            print(f"Carregando arquivo local: {arquivo_local}")
            try:
                with open(arquivo_local, 'r', encoding='utf-8', errors='ignore') as f:
                    conteudo = f.read()
                print(f"✅ Arquivo local carregado: {len(conteudo):,} caracteres")
                return conteudo
            except Exception as e:
                print(f"ERRO ao ler arquivo local: {e}")
        else:
            print(f"❌ Arquivo local {arquivo_local} não encontrado")

        print("\n❌ ERRO: Não foi possível carregar o arquivo local!")
        return None

    # Modo normal: tentar web primeiro, depois local
    print("🌐 Tentativa 1: Baixar da web (versão mais atualizada)")

    conteudo = baixar_html_web(url_web)

    if conteudo:
        print("✅ Usando versão da web")
        return conteudo

    # Se falhar, usar arquivo local
    print("\n📁 Tentativa 2: Usar arquivo local")

    if os.path.exists(arquivo_local):
        print(f"Carregando arquivo local: {arquivo_local}")
        try:
            with open(arquivo_local, 'r', encoding='utf-8', errors='ignore') as f:
                conteudo = f.read()
            print(f"✅ Arquivo local carregado: {len(conteudo):,} caracteres")
            return conteudo
        except Exception as e:
            print(f"ERRO ao ler arquivo local: {e}")
    else:
        print(f"❌ Arquivo local {arquivo_local} não encontrado")

    # Se ambos falharem
    print("\n❌ ERRO: Não foi possível carregar o HTML!")
    print("Soluções:")
    print("1. Verifique sua conexão com a internet")
    print("2. Coloque o arquivo L10406.html no diretório atual")
    print("3. Baixe manualmente de: https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm")
    print("4. Use o parâmetro --local para forçar uso do arquivo local")
    return None

print("=== EXTRATOR DE CÓDIGO CIVIL CORRIGIDO ===")

# Verificar parâmetros de linha de comando
forcar_local = '--local' in sys.argv or '-l' in sys.argv

if forcar_local:
    print("Parâmetro detectado: Forçando uso do arquivo local")
elif len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
    print("USO:")
    print("  python executar_extracao_corrigido.py           # Baixa da web (padrão)")
    print("  python executar_extracao_corrigido.py --local   # Usa arquivo local")
    print("  python executar_extracao_corrigido.py -l        # Usa arquivo local")
    print("")
    print("FONTES:")
    print("  Web:   https://www.planalto.gov.br/ccivil_03/Leis/2002/L10406.htm")
    print("  Local: L10406.html")
    print("")
    print("O script tenta baixar da web primeiro (versão mais atualizada),")
    print("e usa o arquivo local como fallback se a web falhar.")
    exit(0)

print("Iniciando extração...")

try:
    # Carregar HTML (web ou local)
    conteudo = carregar_html(forcar_local)

    if not conteudo:
        exit(1)

    # Extrair artigos
    artigos = {}

    # Padrão para artigos - buscar todas as ocorrências
    # Incluindo artigos com sufixos de letras (ex: art48a, art48a.0, art48a.1)
    padrao_artigos = r'name="(art\d+(?:[a-z](?:\.\d+)?)?\.?)"'
    nomes_artigos = re.findall(padrao_artigos, conteudo, re.IGNORECASE)

    print(f"Encontrados {len(nomes_artigos)} referências de artigos")

    # Processar artigos
    for i, name in enumerate(nomes_artigos):
        if i % 100 == 0:
            print(f"Processando artigo {i+1}/{len(nomes_artigos)}: {name}")

        # Extrair número e sufixo (se houver)
        match = re.match(r'^art(\d+)([a-z])?(?:\.(\d+))?\.?$', name, re.IGNORECASE)
        if not match:
            continue

        numero_base = match.group(1)
        sufixo_letra = match.group(2).lower() if match.group(2) else ''
        versao_numerica = match.group(3) if match.group(3) else ''
        tem_ponto = name.endswith('.')

        # Criar chave única para o artigo
        if sufixo_letra:
            if versao_numerica:
                chave_artigo = f"{numero_base}{sufixo_letra}.{versao_numerica}"
                numero_display = f"{numero_base}-{sufixo_letra.upper()}"
            else:
                chave_artigo = f"{numero_base}{sufixo_letra}"
                numero_display = f"{numero_base}-{sufixo_letra.upper()}"
        else:
            chave_artigo = numero_base
            numero_display = numero_base

        # Determinar se é a versão mais atual
        # Para artigos com sufixo de letra, a versão com maior número é a atual
        # Se não tem número, é considerada versão base
        eh_versao_atual = tem_ponto

        # Para artigos com sufixo de letra, verificar se já existe uma versão mais recente
        pular_artigo = False
        if sufixo_letra:
            # Verificar se já existe uma versão mais recente deste artigo
            versoes_existentes = [k for k in artigos.keys() if k.startswith(f"{numero_base}{sufixo_letra}")]
            if versoes_existentes:
                # Se tem versão numérica, comparar com as existentes
                if versao_numerica:
                    for versao_existente in versoes_existentes:
                        if '.' in versao_existente:
                            num_existente = versao_existente.split('.')[1]
                            if num_existente.isdigit() and int(versao_numerica) <= int(num_existente):
                                pular_artigo = True
                                break  # Pular esta versão pois já existe uma mais recente
                else:
                    # Se não tem versão numérica, verificar se já existe uma com versão numérica
                    tem_versao_numerica = any('.' in v for v in versoes_existentes)
                    if tem_versao_numerica and not tem_ponto:
                        pular_artigo = True  # Pular versão sem número se já existe uma com número

        if pular_artigo:
            continue

        # Priorizar versão com ponto (atual) para artigos normais
        if chave_artigo in artigos and not tem_ponto and not sufixo_letra:
            continue

        if chave_artigo in artigos and artigos[chave_artigo].get('versao_atual', False) and not tem_ponto:
            continue

        # Buscar o contexto do artigo de forma mais simples
        # Encontrar a posição do <a name>
        padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
        match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

        if not match_pos:
            continue

        inicio = match_pos.end()

        # Buscar o texto do artigo a partir dessa posição
        texto_restante = conteudo[inicio:inicio+2000]  # Limitar busca a 2000 chars

        # Padrão que funciona para ambas as estruturas HTML
        # Busca qualquer texto após </a> até o próximo elemento
        padrao_art = r'</a>(.*?)(?=<a name="|</p>|<p align="CENTER"|$)'
        match_art = re.search(padrao_art, texto_restante, re.DOTALL | re.IGNORECASE)

        if match_art:
            texto_html = match_art.group(1)

            # Limpar HTML
            texto = re.sub(r'<[^>]+>', '', texto_html)
            texto = texto.replace('&nbsp;', ' ')
            texto = texto.replace('&amp;', '&')
            texto = texto.replace('&lt;', '<')
            texto = texto.replace('&gt;', '>')
            texto = texto.replace('&quot;', '"')
            texto = re.sub(r'\s+', ' ', texto)
            texto = texto.strip()

            # Verificar se o texto contém o número do artigo e extrair apenas o conteúdo após "Art. X."
            # Considerar artigos com sufixos de letras (ex: Art. 48-A.)
            if sufixo_letra:
                padrao_numero = rf'Art\.\s*{re.escape(numero_base)}-{re.escape(sufixo_letra.upper())}[ºo]?\.\s*(.*)'
            else:
                padrao_numero = rf'Art\.\s*{re.escape(numero_base)}[ºo]?\.\s*(.*)'

            match_numero = re.search(padrao_numero, texto, re.IGNORECASE)
            if match_numero:
                texto = match_numero.group(1).strip()

            # Filtrar títulos de capítulos e seções
            if (texto and len(texto) > 15 and
                not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo|Subseo)', texto, re.IGNORECASE) and
                not re.match(r'^[IVX]+\s*-', texto) and
                'CAPTULO' not in texto and 'TTULO' not in texto):

                artigos[chave_artigo] = {
                    'numero': numero_display,
                    'numero_ordenacao': int(numero_base) + (ord(sufixo_letra) - ord('a') + 1) * 0.1 if sufixo_letra else int(numero_base),
                    'caput': texto,
                    'paragrafos': {},
                    'incisos': {},
                    'versao_atual': tem_ponto,
                    'sufixo_letra': sufixo_letra,
                    'versao_numerica': versao_numerica
                }

    print(f"Artigos únicos processados: {len(artigos)}")

    # Processar versões de artigos com sufixos de letras para determinar a versão atual
    print("Processando versões de artigos com sufixos de letras...")

    # Agrupar artigos por base (número + letra)
    grupos_artigos = {}
    for chave, artigo in artigos.items():
        if artigo['sufixo_letra']:
            base = f"{artigo['numero'].split('-')[0]}-{artigo['sufixo_letra'].upper()}"
            if base not in grupos_artigos:
                grupos_artigos[base] = []
            grupos_artigos[base].append((chave, artigo))

    # Para cada grupo, determinar qual é a versão atual
    for base, versoes in grupos_artigos.items():
        if len(versoes) > 1:
            # Ordenar por versão numérica (versões sem número vêm primeiro)
            versoes_ordenadas = sorted(versoes, key=lambda x: (
                x[1]['versao_numerica'] != '',  # Versões sem número primeiro
                int(x[1]['versao_numerica']) if x[1]['versao_numerica'].isdigit() else -1
            ))

            # A última versão (maior número) é a atual
            for i, (chave, artigo) in enumerate(versoes_ordenadas):
                artigo['versao_atual'] = (i == len(versoes_ordenadas) - 1)
                if artigo['versao_atual']:
                    print(f"  {base}: versão atual é {chave} (versão numérica: '{artigo['versao_numerica']}')")

    print(f"Processamento de versões concluído.")

    # Processar incisos de forma mais simples
    print("Processando incisos...")
    padrao_incisos = r'name="(art\d+(?:[a-z](?:\.\d+)?)?(?:p?[ivx]+)\.?)"'
    nomes_incisos = re.findall(padrao_incisos, conteudo, re.IGNORECASE)

    print(f"Encontrados {len(nomes_incisos)} incisos")

    for name in nomes_incisos:
        match = re.match(r'^art(\d+)([a-z])?(?:\.(\d+))?(p?[ivx]+)\.?$', name, re.IGNORECASE)
        if not match:
            continue

        numero_base = match.group(1)
        sufixo_letra = match.group(2).lower() if match.group(2) else ''
        versao_numerica = match.group(3) if match.group(3) else ''
        inciso_completo = match.group(4)
        tem_ponto = name.endswith('.')

        # Criar chave do artigo pai
        if sufixo_letra:
            if versao_numerica:
                chave_artigo_pai = f"{numero_base}{sufixo_letra}.{versao_numerica}"
            else:
                chave_artigo_pai = f"{numero_base}{sufixo_letra}"
        else:
            chave_artigo_pai = numero_base

        # Determinar se é inciso de parágrafo único ou normal
        if inciso_completo.startswith('p'):
            inciso_romano = inciso_completo[1:]
            eh_inciso_paragrafo = True
        else:
            inciso_romano = inciso_completo
            eh_inciso_paragrafo = False

        # Garantir que o artigo existe
        if chave_artigo_pai not in artigos:
            numero_display = f"{numero_base}-{sufixo_letra.upper()}" if sufixo_letra else numero_base
            artigos[chave_artigo_pai] = {
                'numero': numero_display,
                'numero_ordenacao': int(numero_base) + (ord(sufixo_letra) - ord('a') + 1) * 0.1 if sufixo_letra else int(numero_base),
                'caput': '',
                'paragrafos': {},
                'incisos': {},
                'versao_atual': False,
                'sufixo_letra': sufixo_letra,
                'versao_numerica': versao_numerica
            }

        chave_inciso = inciso_completo if eh_inciso_paragrafo else inciso_romano

        # Priorizar versão com ponto
        if chave_inciso in artigos[chave_artigo_pai]['incisos'] and not tem_ponto:
            continue

        # Buscar texto do inciso de forma mais simples
        padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
        match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

        if match_pos:
            inicio = match_pos.end()
            texto_restante = conteudo[inicio:inicio+1000]

            padrao_inciso = rf'{inciso_romano.upper()}\s*-\s*(.*?)(?=<a name="|</p>|[IVX]+\s*-|$)'
            match_inciso = re.search(padrao_inciso, texto_restante, re.DOTALL | re.IGNORECASE)

            if match_inciso:
                texto_html = match_inciso.group(1)
                texto = re.sub(r'<[^>]+>', '', texto_html)
                texto = texto.replace('&nbsp;', ' ')
                texto = texto.replace('&amp;', '&')
                texto = re.sub(r'\s+', ' ', texto)
                texto = texto.strip()

                if (texto and len(texto) > 5 and
                    not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo)', texto, re.IGNORECASE)):

                    artigos[chave_artigo_pai]['incisos'][chave_inciso] = {
                        'texto': texto,
                        'alineas': {},
                        'versao_atual': tem_ponto,
                        'eh_inciso_paragrafo': eh_inciso_paragrafo
                    }

    # Processar parágrafos de forma mais simples
    print("Processando parágrafos...")
    padrao_paragrafos = r'name="(art\d+(?:[a-z](?:\.\d+)?)?(?:p|[§�]\d+)\.?)"'
    nomes_paragrafos = re.findall(padrao_paragrafos, conteudo, re.IGNORECASE)

    print(f"Encontrados {len(nomes_paragrafos)} parágrafos")

    for name in nomes_paragrafos:
        match = re.match(r'^art(\d+)([a-z])?(?:\.(\d+))?(p|[§�]\d+)\.?$', name, re.IGNORECASE)
        if not match:
            continue

        numero_base = match.group(1)
        sufixo_letra = match.group(2).lower() if match.group(2) else ''
        versao_numerica = match.group(3) if match.group(3) else ''
        identificador_paragrafo = match.group(4)
        tem_ponto = name.endswith('.')

        # Criar chave do artigo pai
        if sufixo_letra:
            if versao_numerica:
                chave_artigo_pai = f"{numero_base}{sufixo_letra}.{versao_numerica}"
            else:
                chave_artigo_pai = f"{numero_base}{sufixo_letra}"
        else:
            chave_artigo_pai = numero_base

        # Normalizar identificador do parágrafo
        if identificador_paragrafo == 'p':
            numero_paragrafo = 'unico'
        elif identificador_paragrafo.startswith('§') or identificador_paragrafo.startswith('�'):
            num_match = re.search(r'[§�](\d+)', identificador_paragrafo)
            if num_match:
                numero_paragrafo = num_match.group(1)
            else:
                numero_paragrafo = '1'
        else:
            numero_paragrafo = identificador_paragrafo

        # Garantir que o artigo existe
        if chave_artigo_pai not in artigos:
            numero_display = f"{numero_base}-{sufixo_letra.upper()}" if sufixo_letra else numero_base
            artigos[chave_artigo_pai] = {
                'numero': numero_display,
                'numero_ordenacao': int(numero_base) + (ord(sufixo_letra) - ord('a') + 1) * 0.1 if sufixo_letra else int(numero_base),
                'caput': '',
                'paragrafos': {},
                'incisos': {},
                'versao_atual': False,
                'sufixo_letra': sufixo_letra,
                'versao_numerica': versao_numerica
            }

        # Priorizar versão com ponto
        if numero_paragrafo in artigos[chave_artigo_pai]['paragrafos'] and not tem_ponto:
            continue

        # Buscar texto do parágrafo
        padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
        match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)

        if match_pos:
            inicio = match_pos.end()
            texto_restante = conteudo[inicio:inicio+1500]

            # Extrair texto após o elemento <a name>
            padrao_paragrafo = r'(.*?)(?=<a name="|</p>|<p align="CENTER"|$)'
            match_paragrafo = re.search(padrao_paragrafo, texto_restante, re.DOTALL | re.IGNORECASE)

            if match_paragrafo:
                texto_html = match_paragrafo.group(1)
                texto = re.sub(r'<[^>]+>', '', texto_html)
                texto = texto.replace('&nbsp;', ' ')
                texto = texto.replace('&amp;', '&')
                texto = texto.replace('&lt;', '<')
                texto = texto.replace('&gt;', '>')
                texto = texto.replace('&quot;', '"')
                texto = re.sub(r'\s+', ' ', texto)
                texto = texto.strip()

                if (texto and len(texto) > 10 and
                    not re.match(r'^(CAPTULO|TTULO|LIVRO|Seo)', texto, re.IGNORECASE)):

                    artigos[chave_artigo_pai]['paragrafos'][numero_paragrafo] = {
                        'texto': texto,
                        'versao_atual': tem_ponto
                    }

    # Converter para lista ordenada
    lista_artigos = []
    artigos_vistos = set()

    # Ordenar por numero_ordenacao para manter a sequência correta
    for chave_art in sorted(artigos.keys(), key=lambda x: artigos[x]['numero_ordenacao']):
        artigo = artigos[chave_art]
        numero_ordenacao = artigo['numero_ordenacao']

        # Filtrar artigos com números muito altos
        if numero_ordenacao > 2100:
            continue

        # Evitar duplicatas baseado na chave do artigo
        if chave_art in artigos_vistos:
            continue

        artigos_vistos.add(chave_art)
        lista_artigos.append(artigo)

    # Salvar JSON
    dados = {
        'documento': 'Código Civil - Lei 10.406/2002',
        'total_artigos': len(lista_artigos),
        'artigos': lista_artigos
    }

    with open('codigo_civil_lexjus_corrigido.json', 'w', encoding='utf-8') as f:
        json.dump(dados, f, ensure_ascii=False, indent=2)

    print(f"\n=== EXTRAÇÃO CONCLUÍDA ===")
    print(f"Total de artigos extraídos: {len(lista_artigos)}")
    print(f"Arquivo salvo: codigo_civil_lexjus_corrigido.json")

    # Verificar artigos próximos ao 48 (para testar o novo padrão)
    print(f"\nVerificando artigos 45-50 (incluindo sufixos de letras):")
    for artigo in lista_artigos:
        numero_ordenacao = artigo['numero_ordenacao']
        if 45 <= numero_ordenacao <= 50:
            print(f"Art. {artigo['numero']}: {artigo['caput'][:100]}...")

    # Verificar artigos próximos ao 1070
    print(f"\nVerificando artigos 1070-1075:")
    for artigo in lista_artigos:
        numero_ordenacao = artigo['numero_ordenacao']
        if 1070 <= numero_ordenacao <= 1075:
            print(f"Art. {artigo['numero']}: {artigo['caput'][:100]}...")

except Exception as e:
    print(f"ERRO: {e}")
    import traceback
    traceback.print_exc()
