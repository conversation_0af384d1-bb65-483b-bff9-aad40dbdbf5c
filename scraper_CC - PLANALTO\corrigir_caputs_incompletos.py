#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT PARA CORRIGIR CAPUTS INCOMPLETOS NO CÓDIGO CIVIL
Corrige artigos que tiveram seus caputs cortados durante a extração
"""

import json
import re
import os

def carregar_html():
    """Carrega o arquivo HTML do Código Civil"""
    arquivo_local = "CC.html"
    
    if not os.path.exists(arquivo_local):
        print(f"ERRO: Arquivo {arquivo_local} não encontrado!")
        return None
    
    # Tentar diferentes encodings
    encodings = ['utf-8', 'latin-1', 'windows-1252', 'iso-8859-1']
    
    for encoding in encodings:
        try:
            with open(arquivo_local, 'r', encoding=encoding) as f:
                conteudo_html = f.read()
            print(f"Arquivo HTML carregado com encoding: {encoding}")
            return conteudo_html
        except UnicodeDecodeError:
            continue
    
    print("ERRO: Não foi possível carregar o arquivo HTML!")
    return None

def extrair_texto_completo_artigo(html_content, numero_artigo):
    """Extrai o texto completo de um artigo específico do HTML"""
    # Dividir o HTML em linhas
    linhas = html_content.split('\n')
    
    # Procurar pela linha que contém o artigo
    for i, linha in enumerate(linhas):
        if f"Art. {numero_artigo}." in linha or f"Art. {numero_artigo}º" in linha:
            # Coletar o texto do artigo até encontrar o próximo artigo
            texto_completo = []
            
            # Adicionar a linha atual
            texto_completo.append(linha.strip())
            
            # Coletar linhas seguintes até encontrar outro artigo
            j = i + 1
            while j < len(linhas):
                linha_seguinte = linhas[j].strip()
                
                # Parar se encontrar outro artigo
                if re.search(r'Art\.\s*\d+(?:\.\d+)?(?:º|°)?(?:-[A-Z])?', linha_seguinte):
                    break
                
                # Parar se encontrar título/seção
                if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO|PARTE)', linha_seguinte, re.IGNORECASE):
                    break
                
                # Parar se encontrar div de fechamento
                if '</div>' in linha_seguinte:
                    break
                
                if linha_seguinte:
                    texto_completo.append(linha_seguinte)
                
                j += 1
            
            # Juntar todo o texto
            texto_artigo = ' '.join(texto_completo)
            
            # Extrair apenas o caput (remover tags HTML e referências legais)
            caput = extrair_caput_limpo(texto_artigo, numero_artigo)
            
            return caput
    
    return None

def extrair_caput_limpo(texto_artigo, numero_artigo):
    """Extrai o caput limpo do texto completo do artigo"""
    # Remover tags HTML
    texto_limpo = re.sub(r'<[^>]+>', ' ', texto_artigo)
    
    # Remover quebras de linha e espaços múltiplos
    texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
    
    # Decodificar entidades HTML
    texto_limpo = texto_limpo.replace('&nbsp;', ' ')
    texto_limpo = texto_limpo.replace('&amp;', '&')
    texto_limpo = texto_limpo.replace('&lt;', '<')
    texto_limpo = texto_limpo.replace('&gt;', '>')
    
    # Extrair o caput (texto após o número do artigo)
    patterns = [
        rf'Art\.\s*{re.escape(numero_artigo)}(?:º|°)?\s*(.+?)(?:\s*\([^)]*(?:Lei|Redação|Vigência|Vide|Incluído|Revogado)[^)]*\)\s*)*$',
        rf'Art\.\s*{re.escape(numero_artigo)}(?:º|°)?\s+(.+?)(?:\s*\([^)]*(?:Lei|Redação|Vigência|Vide|Incluído|Revogado)[^)]*\)\s*)*$',
        rf'Art\.\s*{re.escape(numero_artigo)}[.\s]*(.+?)(?:\s*\([^)]*(?:Lei|Redação|Vigência|Vide|Incluído|Revogado)[^)]*\)\s*)*$'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, texto_limpo, re.IGNORECASE | re.DOTALL)
        if match:
            caput = match.group(1).strip()
            
            # Remover referências legais do final
            caput = re.sub(r'\s*\([^)]*(?:Lei|Redação|Vigência|Vide|Incluído|Revogado)[^)]*\)\s*$', '', caput)
            
            # Remover parágrafos, incisos e outras estruturas
            caput = re.sub(r'\s*(Par[aá]grafo\s+[úu]nico|§\s*\d+|[IVX]+\s*[-–])\s*.*$', '', caput, flags=re.IGNORECASE | re.DOTALL)
            
            if caput and len(caput) > 5:
                return caput.strip()
    
    return ""

def corrigir_caputs_incompletos():
    """Função principal para corrigir caputs incompletos"""
    print("=" * 80)
    print("CORREÇÃO DE CAPUTS INCOMPLETOS - CÓDIGO CIVIL")
    print("=" * 80)
    
    # Carregar arquivo JSON atual
    arquivo_json = 'cc_final_perfeito.json'
    
    if not os.path.exists(arquivo_json):
        print(f"ERRO: Arquivo {arquivo_json} não encontrado!")
        return False
    
    with open(arquivo_json, 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    print(f"Carregados {len(artigos)} artigos do JSON")
    
    # Carregar HTML
    html_content = carregar_html()
    if not html_content:
        return False
    
    # Identificar artigos com caputs incompletos (muito curtos ou que terminam abruptamente)
    artigos_corrigidos = 0
    
    for artigo in artigos:
        numero_artigo = artigo['artigo'].replace('Art. ', '').replace('º', '')
        caput_atual = artigo['caput']
        
        # Verificar se o caput parece incompleto
        caput_incompleto = (
            len(caput_atual) < 30 or  # Muito curto
            caput_atual.endswith(('por', 'de', 'da', 'do', 'em', 'com', 'para', 'que', 'se', 'a', 'o')) or  # Termina com preposição/artigo
            caput_atual.count(' ') < 5  # Muito poucas palavras
        )
        
        if caput_incompleto:
            print(f"\n🔍 Verificando {artigo['artigo']}:")
            print(f"   Caput atual: '{caput_atual}'")
            
            # Extrair caput completo do HTML
            caput_completo = extrair_texto_completo_artigo(html_content, numero_artigo)
            
            if caput_completo and caput_completo != caput_atual and len(caput_completo) > len(caput_atual):
                print(f"   ✅ Caput corrigido: '{caput_completo}'")
                artigo['caput'] = caput_completo
                artigos_corrigidos += 1
            else:
                print(f"   ⚠️  Não foi possível corrigir ou caput já está correto")
    
    if artigos_corrigidos > 0:
        # Salvar arquivo corrigido
        arquivo_corrigido = 'cc_final_perfeito_corrigido.json'
        with open(arquivo_corrigido, 'w', encoding='utf-8') as f:
            json.dump(artigos, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ CORREÇÃO CONCLUÍDA!")
        print(f"   Artigos corrigidos: {artigos_corrigidos}")
        print(f"   Arquivo salvo: {arquivo_corrigido}")
        
        return True
    else:
        print(f"\n✅ Nenhum caput precisou ser corrigido!")
        return True

if __name__ == '__main__':
    print("INICIANDO CORREÇÃO DE CAPUTS INCOMPLETOS")
    print("-" * 80)
    
    sucesso = corrigir_caputs_incompletos()
    
    if sucesso:
        print(f"\n🎉 PROCESSO CONCLUÍDO COM SUCESSO!")
    else:
        print(f"\n❌ FALHA no processo de correção!")
