<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Visual - Bo<PERSON><PERSON> Adicionar à Revisão</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        h2 { 
            color: #666; 
            border-bottom: 3px solid #9b59b6; 
            padding-bottom: 10px; 
            margin-bottom: 20px;
        }
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #9b59b6;
        }
        .buttons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .button-demo {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .button-demo h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.1em;
        }
        .button-demo p {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        
        /* Estilos do botão */
        .btn-adicionar-revisao {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: none;
            letter-spacing: 0.5px;
            margin: 0 auto;
        }

        .btn-adicionar-revisao::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-adicionar-revisao:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        }

        .btn-adicionar-revisao:hover::before {
            left: 100%;
        }

        .btn-adicionar-revisao:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
        }

        .btn-adicionar-revisao:disabled {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .btn-adicionar-revisao i {
            font-size: 16px;
            transition: transform 0.3s ease;
        }

        .btn-adicionar-revisao:hover i {
            transform: scale(1.1) rotate(5deg);
        }

        .btn-adicionar-revisao span {
            font-weight: 600;
            position: relative;
        }

        /* Estados especiais */
        .btn-adicionar-revisao.loading {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-adicionar-revisao.loading i {
            animation: spin 1s linear infinite;
        }

        .btn-adicionar-revisao.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-adicionar-revisao.success i {
            animation: bounce 0.6s ease;
        }

        .btn-adicionar-revisao.error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-adicionar-revisao.error i {
            animation: shake 0.5s ease;
        }

        .btn-adicionar-revisao.pulse {
            animation: pulse 2s infinite;
        }

        /* Animações */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0% { box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3); }
            50% { box-shadow: 0 4px 25px rgba(155, 89, 182, 0.6); }
            100% { box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3); }
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }
        .controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 20px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <h1>🎨 Demo Visual - Botão Adicionar à Revisão</h1>

    <div class="container">
        <h2>🌟 Estados do Botão</h2>
        <div class="demo-section">
            <p>Veja todos os estados visuais do botão "Adicionar à Revisão" em ação:</p>
            
            <div class="buttons-grid">
                <div class="button-demo">
                    <h3>Estado Normal</h3>
                    <p>Botão padrão com efeito pulse para chamar atenção</p>
                    <button class="btn-adicionar-revisao pulse">
                        <i class="fas fa-brain"></i>
                        <span>Adicionar à Revisão</span>
                    </button>
                </div>

                <div class="button-demo">
                    <h3>Estado Loading</h3>
                    <p>Quando o usuário clica e está processando</p>
                    <button class="btn-adicionar-revisao loading">
                        <i class="fas fa-spinner"></i>
                        <span>Adicionando...</span>
                    </button>
                </div>

                <div class="button-demo">
                    <h3>Estado Sucesso</h3>
                    <p>Quando o artigo foi adicionado com sucesso</p>
                    <button class="btn-adicionar-revisao success">
                        <i class="fas fa-check"></i>
                        <span>Adicionado!</span>
                    </button>
                </div>

                <div class="button-demo">
                    <h3>Estado Erro</h3>
                    <p>Quando ocorre algum erro no processo</p>
                    <button class="btn-adicionar-revisao error">
                        <i class="fas fa-times"></i>
                        <span>Erro</span>
                    </button>
                </div>

                <div class="button-demo">
                    <h3>Já na Revisão</h3>
                    <p>Quando o artigo já está no sistema</p>
                    <button class="btn-adicionar-revisao" style="opacity: 0.8;">
                        <i class="fas fa-check-circle"></i>
                        <span>Na Revisão</span>
                    </button>
                </div>

                <div class="button-demo">
                    <h3>Estado Desabilitado</h3>
                    <p>Quando o botão está inativo</p>
                    <button class="btn-adicionar-revisao" disabled>
                        <i class="fas fa-brain"></i>
                        <span>Adicionar à Revisão</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🎮 Simulação Interativa</h2>
        <div class="demo-section">
            <p>Clique no botão abaixo para ver a sequência completa de estados:</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <button id="btnDemo" class="btn-adicionar-revisao pulse">
                    <i class="fas fa-brain"></i>
                    <span>Adicionar à Revisão</span>
                </button>
            </div>

            <div class="controls">
                <button onclick="resetDemo()">🔄 Resetar Demo</button>
                <button onclick="showError()">❌ Simular Erro</button>
                <button onclick="showAlreadyAdded()">✅ Já Adicionado</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>💻 Código CSS</h2>
        <div class="demo-section">
            <p>Principais estilos CSS utilizados:</p>
            
            <div class="code-example">
.btn-adicionar-revisao {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-adicionar-revisao:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
}

.btn-adicionar-revisao.pulse {
    animation: pulse 2s infinite;
}
            </div>
        </div>
    </div>

    <script>
        function resetDemo() {
            const btn = document.getElementById('btnDemo');
            btn.className = 'btn-adicionar-revisao pulse';
            btn.innerHTML = '<i class="fas fa-brain"></i> <span>Adicionar à Revisão</span>';
            btn.disabled = false;
            btn.style.opacity = '1';
        }

        function showError() {
            const btn = document.getElementById('btnDemo');
            btn.className = 'btn-adicionar-revisao error';
            btn.innerHTML = '<i class="fas fa-times"></i> <span>Erro</span>';
            
            setTimeout(() => {
                resetDemo();
            }, 3000);
        }

        function showAlreadyAdded() {
            const btn = document.getElementById('btnDemo');
            btn.className = 'btn-adicionar-revisao';
            btn.innerHTML = '<i class="fas fa-check-circle"></i> <span>Na Revisão</span>';
            btn.style.opacity = '0.8';
        }

        // Demo interativo
        document.getElementById('btnDemo').addEventListener('click', function() {
            const btn = this;
            
            // Estado loading
            btn.className = 'btn-adicionar-revisao loading';
            btn.innerHTML = '<i class="fas fa-spinner"></i> <span>Adicionando...</span>';
            btn.disabled = true;
            
            setTimeout(() => {
                // Estado sucesso
                btn.className = 'btn-adicionar-revisao success';
                btn.innerHTML = '<i class="fas fa-check"></i> <span>Adicionado!</span>';
                
                setTimeout(() => {
                    // Estado final
                    btn.className = 'btn-adicionar-revisao';
                    btn.innerHTML = '<i class="fas fa-check-circle"></i> <span>Na Revisão</span>';
                    btn.disabled = false;
                    btn.style.opacity = '0.8';
                }, 2000);
            }, 1500);
        });

        console.log('🎨 Demo visual do botão carregado!');
    </script>
</body>
</html>
