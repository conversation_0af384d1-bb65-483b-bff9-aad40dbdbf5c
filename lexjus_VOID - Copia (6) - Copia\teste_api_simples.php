<?php
session_start();
require_once __DIR__ . '/../conexao_POST.php';

// Simular usuário logado para teste
if (!isset($_SESSION['idusuario'])) {
    $_SESSION['idusuario'] = 1; // ID de teste
}

echo "<h1>🧪 Teste Simples da API de Revisão</h1>";

$usuario_id = $_SESSION['idusuario'];
echo "<p>👤 Usuário: $usuario_id</p>";

// Teste 1: Adicionar artigo
echo "<h2>1. 📝 Adicionando Artigo de Teste</h2>";
try {
    // Verificar se já existe
    $query_check = "SELECT id FROM appestudo.lexjus_revisoes WHERE usuario_id = $1 AND artigo_numero = 'teste'";
    $result_check = pg_query_params($conexao, $query_check, [$usuario_id]);
    
    if (pg_num_rows($result_check) > 0) {
        echo "✅ Artigo 'teste' já existe<br>";
        $row = pg_fetch_assoc($result_check);
        $revisao_id = $row['id'];
    } else {
        // Criar novo
        $query_insert = "INSERT INTO appestudo.lexjus_revisoes (usuario_id, artigo_numero) VALUES ($1, 'teste') RETURNING id";
        $result_insert = pg_query_params($conexao, $query_insert, [$usuario_id]);
        
        if ($result_insert) {
            $row = pg_fetch_assoc($result_insert);
            $revisao_id = $row['id'];
            echo "✅ Artigo 'teste' criado com ID: $revisao_id<br>";
        } else {
            echo "❌ Erro ao criar artigo: " . pg_last_error($conexao) . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "<br>";
}

// Teste 2: Testar algoritmo PHP
echo "<h2>2. 🧮 Testando Algoritmo SM-2 em PHP</h2>";

function calcularProximaRevisaoPhp($facilidade, $intervalo, $qualidade, $repeticoes) {
    // Calcular nova facilidade baseada na qualidade da resposta
    $nova_facilidade = $facilidade + (0.1 - (5 - $qualidade) * (0.08 + (5 - $qualidade) * 0.02));
    
    // Garantir que facilidade está dentro dos limites
    if ($nova_facilidade < 1.30) {
        $nova_facilidade = 1.30;
    }
    if ($nova_facilidade > 3.00) {
        $nova_facilidade = 3.00;
    }
    
    // Calcular novo intervalo e repetições
    if ($qualidade < 3) {
        // Resposta ruim - reiniciar
        $novas_repeticoes = 0;
        $novo_intervalo = 1;
    } else {
        // Resposta boa - avançar
        $novas_repeticoes = $repeticoes + 1;
        
        if ($novas_repeticoes == 1) {
            $novo_intervalo = 1;
        } elseif ($novas_repeticoes == 2) {
            $novo_intervalo = 6;
        } else {
            $novo_intervalo = round($intervalo * $nova_facilidade);
        }
    }
    
    // Calcular nova data de revisão
    $nova_data_revisao = date('Y-m-d H:i:s', strtotime("+{$novo_intervalo} days"));
    
    return [
        'nova_facilidade' => round($nova_facilidade, 2),
        'novo_intervalo' => $novo_intervalo,
        'novas_repeticoes' => $novas_repeticoes,
        'nova_data_revisao' => $nova_data_revisao
    ];
}

$teste_algoritmo = calcularProximaRevisaoPhp(2.5, 1, 4, 0);
echo "<pre>";
print_r($teste_algoritmo);
echo "</pre>";

// Teste 3: Simular resposta
echo "<h2>3. 📖 Simulando Resposta de Revisão</h2>";
try {
    // Buscar revisão atual
    $query_current = "SELECT * FROM appestudo.lexjus_revisoes WHERE usuario_id = $1 AND artigo_numero = 'teste'";
    $result_current = pg_query_params($conexao, $query_current, [$usuario_id]);
    
    if (pg_num_rows($result_current) > 0) {
        $revisao_atual = pg_fetch_assoc($result_current);
        echo "📊 Estado atual:<br>";
        echo "- Facilidade: " . $revisao_atual['facilidade'] . "<br>";
        echo "- Intervalo: " . $revisao_atual['intervalo_dias'] . " dias<br>";
        echo "- Repetições: " . $revisao_atual['repeticoes'] . "<br>";
        echo "- Status: " . $revisao_atual['status'] . "<br>";
        
        // Calcular próxima revisão
        $qualidade = 4; // Simular resposta "fácil"
        $calculo = calcularProximaRevisaoPhp(
            (float)$revisao_atual['facilidade'],
            (int)$revisao_atual['intervalo_dias'],
            $qualidade,
            (int)$revisao_atual['repeticoes']
        );
        
        echo "<br>🎯 Simulando resposta com qualidade $qualidade:<br>";
        echo "- Nova facilidade: " . $calculo['nova_facilidade'] . "<br>";
        echo "- Novo intervalo: " . $calculo['novo_intervalo'] . " dias<br>";
        echo "- Novas repetições: " . $calculo['novas_repeticoes'] . "<br>";
        echo "- Próxima revisão: " . $calculo['nova_data_revisao'] . "<br>";
        
        // Atualizar no banco
        $query_update = "
            UPDATE appestudo.lexjus_revisoes 
            SET facilidade = $1,
                intervalo_dias = $2,
                repeticoes = $3,
                data_proxima_revisao = $4,
                ultima_qualidade = $5,
                total_revisoes = total_revisoes + 1
            WHERE usuario_id = $6 AND artigo_numero = 'teste'";
        
        $result_update = pg_query_params($conexao, $query_update, [
            $calculo['nova_facilidade'],
            $calculo['novo_intervalo'],
            $calculo['novas_repeticoes'],
            $calculo['nova_data_revisao'],
            $qualidade,
            $usuario_id
        ]);
        
        if ($result_update) {
            echo "<br>✅ Revisão atualizada com sucesso!<br>";
        } else {
            echo "<br>❌ Erro ao atualizar: " . pg_last_error($conexao) . "<br>";
        }
        
    } else {
        echo "❌ Revisão não encontrada<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "<br>";
}

// Teste 4: Testar API via cURL
echo "<h2>4. 🌐 Testando API via HTTP</h2>";

$base_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/revisao.php';

// Função para fazer requisição
function testarAPI($url, $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $http_code,
        'response' => $response
    ];
}

// Testar estatísticas
echo "<h3>📊 Testando Estatísticas</h3>";
$result = testarAPI($base_url . '?acao=estatisticas');
echo "HTTP Code: " . $result['http_code'] . "<br>";
echo "Response: <pre>" . htmlspecialchars($result['response']) . "</pre>";

// Testar adicionar artigo
echo "<h3>📝 Testando Adicionar Artigo</h3>";
$result = testarAPI($base_url, [
    'acao' => 'iniciar',
    'artigo_numero' => '1'
]);
echo "HTTP Code: " . $result['http_code'] . "<br>";
echo "Response: <pre>" . htmlspecialchars($result['response']) . "</pre>";

// Testar responder revisão
echo "<h3>📖 Testando Responder Revisão</h3>";
$result = testarAPI($base_url, [
    'acao' => 'responder',
    'artigo_numero' => '1',
    'qualidade' => 4,
    'tempo_resposta' => 30
]);
echo "HTTP Code: " . $result['http_code'] . "<br>";
echo "Response: <pre>" . htmlspecialchars($result['response']) . "</pre>";

echo "<hr>";
echo "<h2>🎯 Conclusão</h2>";
echo "<p>Se todos os testes passaram, a API está funcionando corretamente!</p>";
echo "<p><a href='teste_sistema_revisao.html'>🧪 Ir para teste completo</a></p>";
echo "<p><a href='demo_sistema_revisao.html'>🎯 Ir para demonstração</a></p>";
?>
