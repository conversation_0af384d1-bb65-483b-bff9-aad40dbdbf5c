#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de execução simplificada do Scraper do Código de Processo Penal (CPP)
Executa todo o processo de extração e limpeza automaticamente
"""

import subprocess
import sys
import os
from datetime import datetime

def verificar_arquivos():
    """
    Verifica se os arquivos necessários existem
    """
    arquivos_necessarios = [
        'scraper_planalto_cpp.py',
        'limpar_referencias_finais_cpp.py'
    ]
    
    for arquivo in arquivos_necessarios:
        if not os.path.exists(arquivo):
            print(f"❌ Arquivo não encontrado: {arquivo}")
            return False
    
    return True

def executar_comando(comando, descricao):
    """
    Executa um comando e retorna True se bem-sucedido
    """
    print(f"\n🔄 {descricao}...")
    print(f"   Comando: {comando}")
    print("-" * 50)
    
    try:
        resultado = subprocess.run(
            comando.split(),
            capture_output=False,
            text=True,
            check=True
        )
        print("-" * 50)
        print(f"✅ {descricao} - CONCLUÍDO")
        return True
    except subprocess.CalledProcessError as e:
        print("-" * 50)
        print(f"❌ {descricao} - FALHOU")
        print(f"   Código de erro: {e.returncode}")
        return False
    except Exception as e:
        print("-" * 50)
        print(f"❌ {descricao} - ERRO: {e}")
        return False

def main():
    print("=" * 60)
    print("🏛️  SCRAPER DO CÓDIGO DE PROCESSO PENAL - PLANALTO")
    print("=" * 60)
    print("📋 Este script executa todo o processo automaticamente:")
    print("   1. Extração dos artigos do Código de Processo Penal")
    print("   2. Limpeza das referências legislativas")
    print("   3. Geração dos arquivos finais")
    print("=" * 60)
    
    # Verificar se os arquivos necessários existem
    if not verificar_arquivos():
        print("\n❌ Arquivos necessários não encontrados!")
        print("Certifique-se de que está executando na pasta correta.")
        return
    
    # Passo 1: Executar scraper principal
    sucesso1 = executar_comando(
        "python scraper_planalto_cpp.py",
        "Executando scraper principal (extração dos artigos do CPP)"
    )
    
    if not sucesso1:
        print("\n❌ Falha na extração dos artigos. Verifique a conexão com a internet.")
        return
    
    # Verificar se os arquivos foram gerados
    if not os.path.exists('codigo_processo_penal.json'):
        print("\n❌ Arquivo codigo_processo_penal.json não foi gerado!")
        return
    
    # Passo 2: Executar limpeza final
    sucesso2 = executar_comando(
        "python limpar_referencias_finais_cpp.py",
        "Executando limpeza final (remoção de referências)"
    )
    
    if not sucesso2:
        print("\n⚠️  Limpeza final falhou, mas os arquivos básicos foram gerados.")
        print("   Você pode tentar executar manualmente:")
        print("   python limpar_referencias_finais_cpp.py")
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL")
    print("=" * 60)
    
    arquivos_gerados = []
    
    # Verificar arquivos gerados
    arquivos_esperados = [
        ('codigo_processo_penal.json', 'Arquivo JSON básico'),
        ('codigo_processo_penal.js', 'Arquivo JavaScript básico'),
        ('codigo_processo_penal_limpo.json', 'Arquivo JSON limpo'),
        ('codigo_processo_penal_limpo.js', 'Arquivo JavaScript limpo')
    ]
    
    for arquivo, descricao in arquivos_esperados:
        if os.path.exists(arquivo):
            tamanho = os.path.getsize(arquivo)
            arquivos_gerados.append(arquivo)
            print(f"✅ {descricao}: {arquivo} ({tamanho:,} bytes)")
        else:
            print(f"❌ {descricao}: {arquivo} (não encontrado)")
    
    if len(arquivos_gerados) >= 2:
        print(f"\n🎉 PROCESSO CONCLUÍDO COM SUCESSO!")
        print(f"   📁 {len(arquivos_gerados)} arquivos gerados")
        print(f"   🕒 Concluído em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if 'codigo_processo_penal_limpo.js' in arquivos_gerados:
            print(f"\n🔗 PRÓXIMOS PASSOS:")
            print(f"   1. Integrar 'codigo_processo_penal_limpo.js' ao sistema LexJus")
            print(f"   2. Importar dados do 'codigo_processo_penal_limpo.json' para o banco")
            print(f"   3. Testar as funções JavaScript no sistema")
    else:
        print(f"\n⚠️  PROCESSO INCOMPLETO")
        print(f"   Apenas {len(arquivos_gerados)} arquivos foram gerados")
        print(f"   Verifique os erros acima e tente novamente")

if __name__ == '__main__':
    main()
