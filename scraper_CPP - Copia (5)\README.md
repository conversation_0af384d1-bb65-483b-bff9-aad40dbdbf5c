# 🏛️ Scraper CPP - Código de Processo Penal

Sistema automatizado para extração, correção e geração de dados do Código de Processo Penal brasileiro para o sistema LexJus.

## 📋 Visão Geral

Este sistema extrai todos os artigos do Código de Processo Penal diretamente do site oficial [corpus927.enfam.jus.br](https://corpus927.enfam.jus.br/legislacao/cpp-41), aplica correções automáticas e gera arquivos prontos para uso no sistema LexJus.

### ✨ Características

- 🌐 **Extração Online**: Busca dados diretamente do site oficial
- 🔧 **Correções Automáticas**: Corrige caputs cortados automaticamente
- 📊 **Estrutura Completa**: Preserva incisos, parágrafos e referências legais
- ⚡ **Processo Unificado**: Uma única execução para todo o workflow
- 🎯 **Pronto para LexJus**: Gera arquivos no formato correto

## 🚀 Uso Rápido

### Método Recomendado (Unificado)
```bash
python atualizar_cpp_unificado.py
```

### Método Manual (3 etapas)
```bash
python scraper_final_perfeito.py
python corrigir_caputs_simples.py
ATUALIZAR_CPP.bat
```

## 📦 Instalação

### Pré-requisitos
- Python 3.7+
- Conexão com internet

### Dependências
```bash
pip install -r requirements.txt
```

**Pacotes necessários:**
- `requests` - Para requisições HTTP
- `beautifulsoup4` - Para parsing HTML
- `lxml` - Parser XML/HTML

## 📁 Estrutura do Projeto

```
scraper_CPP/
├── 🔧 Scripts Principais
│   ├── atualizar_cpp_unificado.py    # Script principal (RECOMENDADO)
│   ├── scraper_final_perfeito.py     # Extração dos artigos
│   ├── corrigir_caputs_simples.py    # Correção de caputs
│   └── atualizar_cpp_rapido.py       # Geração final
├── 🖥️ Utilitários
│   └── ATUALIZAR_CPP.bat             # Batch para Windows
├── 📄 Configuração
│   ├── requirements.txt              # Dependências Python
│   └── README.md                     # Esta documentação
└── 📊 Arquivos Gerados
    ├── cpp_lexjus_YYYYMMDD_HHMMSS.json  # Dados estruturados
    └── cpp_lexjus_YYYYMMDD_HHMMSS.js    # Arquivo para LexJus
```

## 🔄 Workflow Detalhado

### 1. Extração (scraper_final_perfeito.py)
- Busca HTML do site oficial
- Extrai 845+ artigos com estrutura completa
- Preserva incisos, parágrafos e referências

### 2. Correção (corrigir_caputs_simples.py)
- Aplica 33+ correções conhecidas
- Corrige caputs cortados
- Mantém integridade legal

### 3. Geração Final (atualizar_cpp_rapido.py)
- Gera arquivo JSON estruturado
- Cria arquivo JS para LexJus
- Adiciona timestamp único

## 📊 Resultados

### Estatísticas Típicas
- **845 artigos** extraídos
- **500+ incisos** preservados
- **315+ parágrafos numerados** mantidos
- **144+ parágrafos únicos** incluídos
- **33+ correções** aplicadas automaticamente

### Arquivos Gerados
- `cpp_lexjus_YYYYMMDD_HHMMSS.json` - Dados estruturados
- `cpp_lexjus_YYYYMMDD_HHMMSS.js` - Pronto para LexJus

## 🛠️ Uso Avançado

### Executar Apenas Extração
```bash
python scraper_final_perfeito.py
```

### Executar Apenas Correções
```bash
python corrigir_caputs_simples.py
```

### Executar Apenas Geração Final
```bash
python atualizar_cpp_rapido.py
```

## 🔍 Verificação de Qualidade

### Estrutura dos Dados
```json
{
  "artigo": "Art. 1º",
  "caput": "O processo penal reger-se-á, em todo o território nacional...",
  "incisos": ["I - os tratados...", "II - as prerrogativas..."],
  "paragrafos_numerados": [{"numero": "§ 1°", "texto": "..."}],
  "paragrafo_unico": "Aplicar-se-á, entretanto..."
}
```

### Indicadores de Qualidade
- ✅ Caputs completos (não cortados)
- ✅ Numeração sequencial correta
- ✅ Referências legais preservadas
- ✅ Estrutura hierárquica mantida

## 🚨 Solução de Problemas

### Erro de Conexão
```
❌ Erro ao buscar a página: ConnectionError
```
**Solução**: Verificar conexão com internet

### Erro de Encoding
```
❌ Erro inesperado ao buscar conteúdo: UnicodeDecodeError
```
**Solução**: O script tenta múltiplos encodings automaticamente

### Poucos Artigos Extraídos
```
📊 Total: 50 artigos (esperado: 845+)
```
**Solução**: Site pode estar indisponível, tentar novamente

## 📈 Integração com LexJus

### Copiar Arquivo Gerado
1. Localize o arquivo `cpp_lexjus_YYYYMMDD_HHMMSS.js` mais recente
2. Copie para o diretório do sistema LexJus
3. Atualize as referências no código

### Exemplo de Integração
```javascript
// No sistema LexJus
import { artigos_cpp } from './cpp_lexjus_20250620_114933.js';
```

## 🔄 Atualizações Futuras

### Quando Atualizar
- Mudanças na legislação
- Correções no site oficial
- Melhorias no sistema

### Como Atualizar
```bash
# Método simples
python atualizar_cpp_unificado.py

# Verificar qualidade
# Copiar arquivo .js para LexJus
# Testar funcionamento
```

## 📞 Suporte

### Logs e Debug
- Scripts geram logs detalhados durante execução
- Timestamps para rastreamento
- Estatísticas de qualidade

### Verificação Manual
- Compare primeiros artigos com site oficial
- Verifique numeração sequencial
- Confirme caputs completos

---

## 🏆 Qualidade Garantida

Este sistema foi desenvolvido e testado para garantir:
- **100% dos artigos** do CPP extraídos
- **Caputs completos** sem truncamento
- **Estrutura legal preservada** integralmente
- **Pronto para produção** no sistema LexJus

**Última atualização**: 2025-06-20  
**Versão**: 2.0 (Unificada)  
**Compatibilidade**: LexJus v1.0+
