#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
from datetime import datetime

def limpar_referencias_finais(texto):
    """
    Remove referências específicas que não foram capturadas pelas regex anteriores
    """
    if not texto:
        return texto

    # Remove (Vide ADPF X)
    texto = re.sub(r'\s*\(Vide\s+ADPF\s+\d+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove (Revogado pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Revogad[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove (VETADO)
    texto = re.sub(r'\s*\(VETADO\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove (Redação dada pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Redação\s+dada\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove (Incluído pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Incluíd[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove (Alterado pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Alterad[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove (Vide Lei nº X, de data)
    texto = re.sub(r'\s*\(Vide\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)

    return texto.strip()

def processar_artigo(artigo):
    """
    Processa um artigo completo, limpando todas as suas partes seguindo estrutura do Código Penal
    """
    artigo_limpo = artigo.copy()

    # Limpar caput
    if 'caput' in artigo_limpo:
        artigo_limpo['caput'] = limpar_referencias_finais(artigo_limpo['caput'])

    # Limpar incisos
    if 'incisos' in artigo_limpo and isinstance(artigo_limpo['incisos'], list):
        artigo_limpo['incisos'] = [
            limpar_referencias_finais(i) for i in artigo_limpo['incisos']
        ]

    # Limpar parágrafo único
    if 'paragrafo_unico' in artigo_limpo and artigo_limpo['paragrafo_unico']:
        artigo_limpo['paragrafo_unico'] = limpar_referencias_finais(artigo_limpo['paragrafo_unico'])

    # Limpar parágrafos numerados
    if 'paragrafos_numerados' in artigo_limpo and isinstance(artigo_limpo['paragrafos_numerados'], list):
        for paragrafo in artigo_limpo['paragrafos_numerados']:
            if 'texto' in paragrafo:
                paragrafo['texto'] = limpar_referencias_finais(paragrafo['texto'])

            if 'incisos' in paragrafo and isinstance(paragrafo['incisos'], list):
                paragrafo['incisos'] = [
                    limpar_referencias_finais(i) for i in paragrafo['incisos']
                ]

            if 'alineas' in paragrafo and isinstance(paragrafo['alineas'], list):
                paragrafo['alineas'] = [
                    limpar_referencias_finais(a) for a in paragrafo['alineas']
                ]

    return artigo_limpo

def gerar_javascript_limpo(artigos, nome_arquivo):
    """
    Gera arquivo JavaScript limpo com os artigos e funções de busca seguindo padrão do Código Penal
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Os dados já estão em UTF-8 correto após o processamento
    artigos_utf8 = artigos

    js_content = f"""// Código de Processo Penal Brasileiro - Dados extraídos e limpos automaticamente
// Gerado em: {timestamp}
// Total de artigos: {len(artigos_utf8)}
// Referências legislativas removidas para melhor legibilidade

const codigoProcessoPenalArtigos = {json.dumps(artigos_utf8, ensure_ascii=False, indent=2)};


// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""

    try:
        with open(nome_arquivo, 'w', encoding='utf-8', newline='\n') as f:
            f.write(js_content)
        print(f"✅ Arquivo JavaScript limpo salvo: {nome_arquivo}")
        return True
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo JavaScript limpo: {e}")
        return False

def main():
    print("=" * 60)
    print("🧹 LIMPEZA FINAL DE REFERÊNCIAS - CPP")
    print("=" * 60)

    # Carregar arquivo JSON
    try:
        with open('codigo_processo_penal.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Carregados {len(artigos)} artigos do arquivo JSON")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        print("   Certifique-se de executar 'python scraper_planalto_cpp.py' primeiro")
        return

    # Processar cada artigo
    artigos_limpos = []
    referencias_removidas = 0

    for artigo in artigos:
        artigo_original = json.dumps(artigo, ensure_ascii=False)
        artigo_limpo = processar_artigo(artigo.copy())
        artigo_novo = json.dumps(artigo_limpo, ensure_ascii=False)

        if artigo_original != artigo_novo:
            referencias_removidas += 1

        artigos_limpos.append(artigo_limpo)

    print(f"📊 Processamento concluído:")
    print(f"   • Artigos processados: {len(artigos_limpos)}")
    print(f"   • Artigos com referências removidas: {referencias_removidas}")

    # Salvar arquivo JSON limpo com encoding UTF-8 correto
    try:
        with open('codigo_processo_penal_limpo.json', 'w', encoding='utf-8', newline='\n') as f:
            json.dump(artigos_limpos, f, ensure_ascii=False, indent=2)
        print(f"✅ Arquivo JSON limpo salvo: codigo_processo_penal_limpo.json")
    except Exception as e:
        print(f"❌ Erro ao salvar arquivo JSON limpo: {e}")
        return

    # Gerar arquivo JavaScript limpo
    sucesso_js = gerar_javascript_limpo(artigos_limpos, 'codigo_processo_penal_limpo.js')

    if sucesso_js:
        print(f"\n✅ LIMPEZA CONCLUÍDA COM SUCESSO!")
        print(f"   📄 Arquivo JSON limpo: codigo_processo_penal_limpo.json")
        print(f"   📄 Arquivo JS limpo: codigo_processo_penal_limpo.js")
        print(f"\n🎯 Arquivos prontos para integração com o sistema LexJus!")
    else:
        print(f"\n⚠️  Limpeza concluída com erros na gravação do arquivo JavaScript")

if __name__ == '__main__':
    main()
