<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo do Sistema de Revisão - LexJus VOID</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        h2 {
            color: #4a5568;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        .step {
            background: #f8f9fa;
            border-left: 5px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }
        .step.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .step.info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 1s ease;
        }
        .demo-flow {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .flow-step {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .flow-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }
        .flow-content {
            flex: 1;
        }
        .flow-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .flow-description {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <h1>🧠 Demo do Sistema de Revisão Inteligente</h1>

    <div class="container">
        <h2>🎯 Demonstração Prática</h2>
        <p>Esta demonstração mostra o fluxo completo do Sistema de Revisão do LexJus VOID.</p>
        
        <div class="demo-flow">
            <div class="flow-step">
                <div class="flow-number">1</div>
                <div class="flow-content">
                    <div class="flow-title">Verificar Autenticação</div>
                    <div class="flow-description">Confirma se você está logado no sistema</div>
                </div>
                <button onclick="executarPasso1()">▶️ Executar</button>
            </div>

            <div class="flow-step">
                <div class="flow-number">2</div>
                <div class="flow-content">
                    <div class="flow-title">Adicionar Artigos ao Sistema</div>
                    <div class="flow-description">Adiciona artigos de exemplo para demonstração</div>
                </div>
                <button onclick="executarPasso2()" id="btnPasso2" disabled>▶️ Executar</button>
            </div>

            <div class="flow-step">
                <div class="flow-number">3</div>
                <div class="flow-content">
                    <div class="flow-title">Ver Estatísticas Iniciais</div>
                    <div class="flow-description">Mostra o estado atual do sistema de revisão</div>
                </div>
                <button onclick="executarPasso3()" id="btnPasso3" disabled>▶️ Executar</button>
            </div>

            <div class="flow-step">
                <div class="flow-number">4</div>
                <div class="flow-content">
                    <div class="flow-title">Simular Sessão de Revisão</div>
                    <div class="flow-description">Responde algumas revisões com diferentes qualidades</div>
                </div>
                <button onclick="executarPasso4()" id="btnPasso4" disabled>▶️ Executar</button>
            </div>

            <div class="flow-step">
                <div class="flow-number">5</div>
                <div class="flow-content">
                    <div class="flow-title">Ver Resultados Finais</div>
                    <div class="flow-description">Mostra como o algoritmo atualizou os intervalos</div>
                </div>
                <button onclick="executarPasso5()" id="btnPasso5" disabled>▶️ Executar</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Resultados da Demonstração</h2>
        <div id="resultados-demo"></div>
    </div>

    <div class="container">
        <h2>📈 Estatísticas em Tempo Real</h2>
        <div id="stats-display" class="stats-grid"></div>
    </div>

    <script>
        let passoAtual = 1;
        let resultados = [];

        async function fazerRequisicao(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                const data = await response.json();
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        function adicionarResultado(passo, titulo, resultado, tipo = 'info') {
            resultados.push({
                passo,
                titulo,
                resultado,
                tipo,
                timestamp: new Date().toLocaleTimeString()
            });
            atualizarDisplayResultados();
        }

        function atualizarDisplayResultados() {
            const container = document.getElementById('resultados-demo');
            container.innerHTML = resultados.map(r => `
                <div class="step ${r.tipo}">
                    <h4>Passo ${r.passo}: ${r.titulo} (${r.timestamp})</h4>
                    <div class="result ${r.resultado.ok ? 'success' : 'error'}">
                        ${JSON.stringify(r.resultado, null, 2)}
                    </div>
                </div>
            `).join('');
        }

        function habilitarProximoPasso() {
            if (passoAtual <= 5) {
                const btnProximo = document.getElementById(`btnPasso${passoAtual}`);
                if (btnProximo) {
                    btnProximo.disabled = false;
                }
                passoAtual++;
            }
        }

        async function executarPasso1() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=estatisticas');
            
            if (resultado.ok) {
                adicionarResultado(1, 'Verificar Autenticação', resultado, 'info');
                habilitarProximoPasso();
            } else {
                adicionarResultado(1, 'Verificar Autenticação', resultado, 'error');
                alert('❌ Você precisa estar logado no sistema principal primeiro!\n\nAcesse: index.php e faça login.');
            }
        }

        async function executarPasso2() {
            const artigos = ['1', '5', '37', '196', '220'];
            const resultadosAdicao = [];
            
            for (const artigo of artigos) {
                const resultado = await fazerRequisicao('./api/revisao.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        acao: 'iniciar',
                        artigo_numero: artigo
                    })
                });
                resultadosAdicao.push({
                    artigo,
                    sucesso: resultado.ok,
                    mensagem: resultado.data?.mensagem || resultado.error
                });
            }
            
            const resultadoFinal = {
                ok: true,
                data: {
                    artigos_adicionados: resultadosAdicao,
                    total_sucessos: resultadosAdicao.filter(r => r.sucesso).length,
                    total_tentativas: artigos.length
                }
            };
            
            adicionarResultado(2, 'Adicionar Artigos', resultadoFinal, 'info');
            habilitarProximoPasso();
        }

        async function executarPasso3() {
            const resultado = await fazerRequisicao('./api/revisao.php?acao=estatisticas');
            
            if (resultado.ok) {
                adicionarResultado(3, 'Estatísticas Iniciais', resultado, 'info');
                atualizarEstatisticasVisuais(resultado.data.estatisticas);
                habilitarProximoPasso();
            } else {
                adicionarResultado(3, 'Estatísticas Iniciais', resultado, 'error');
            }
        }

        async function executarPasso4() {
            const sessoes = [
                { artigo: '1', qualidade: 4, descricao: 'Artigo 1º - Fácil' },
                { artigo: '5', qualidade: 2, descricao: 'Artigo 5º - Difícil' },
                { artigo: '37', qualidade: 5, descricao: 'Artigo 37 - Muito Fácil' },
                { artigo: '196', qualidade: 3, descricao: 'Artigo 196 - Normal' },
                { artigo: '220', qualidade: 1, descricao: 'Artigo 220 - Muito Difícil' }
            ];
            
            const resultadosSessao = [];
            
            for (const sessao of sessoes) {
                const resultado = await fazerRequisicao('./api/revisao.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        acao: 'responder',
                        artigo_numero: sessao.artigo,
                        qualidade: sessao.qualidade,
                        tempo_resposta: Math.floor(Math.random() * 60) + 15
                    })
                });
                
                resultadosSessao.push({
                    ...sessao,
                    sucesso: resultado.ok,
                    nova_facilidade: resultado.data?.nova_facilidade,
                    novo_intervalo: resultado.data?.novo_intervalo,
                    novo_status: resultado.data?.novo_status,
                    proxima_revisao: resultado.data?.data_proxima_revisao
                });
                
                // Pequena pausa entre as respostas
                await new Promise(resolve => setTimeout(resolve, 300));
            }
            
            const resultadoFinal = {
                ok: true,
                data: {
                    sessoes_realizadas: resultadosSessao,
                    total_respostas: sessoes.length,
                    algoritmo_funcionando: resultadosSessao.every(s => s.sucesso)
                }
            };
            
            adicionarResultado(4, 'Sessão de Revisão', resultadoFinal, 'info');
            habilitarProximoPasso();
        }

        async function executarPasso5() {
            const estatisticas = await fazerRequisicao('./api/revisao.php?acao=estatisticas');
            const revisoes = await fazerRequisicao('./api/revisao.php');
            const historico = await fazerRequisicao('./api/revisao.php?acao=historico&limite=10');
            
            const resultadoFinal = {
                ok: true,
                data: {
                    estatisticas_finais: estatisticas.data?.estatisticas,
                    revisoes_atuais: revisoes.data?.revisoes,
                    historico_recente: historico.data?.historico,
                    conclusao: 'Sistema de Revisão funcionando perfeitamente! 🎉'
                }
            };
            
            adicionarResultado(5, 'Resultados Finais', resultadoFinal, 'info');
            
            if (estatisticas.ok) {
                atualizarEstatisticasVisuais(estatisticas.data.estatisticas);
            }
            
            // Mostrar mensagem de sucesso
            setTimeout(() => {
                alert('🎉 Demonstração concluída com sucesso!\n\nO Sistema de Revisão está funcionando perfeitamente. Agora você pode usar o botão "Revisão" no sistema principal.');
            }, 1000);
        }

        function atualizarEstatisticasVisuais(stats) {
            if (!stats) return;
            
            const container = document.getElementById('stats-display');
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_artigos || 0}</div>
                    <div class="stat-label">Total de Artigos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.pendentes || 0}</div>
                    <div class="stat-label">Pendentes</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.aprendendo || 0}</div>
                    <div class="stat-label">Aprendendo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.revisando || 0}</div>
                    <div class="stat-label">Revisando</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.dominados || 0}</div>
                    <div class="stat-label">Dominados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${(stats.facilidade_media || 0).toFixed(1)}</div>
                    <div class="stat-label">Facilidade Média</div>
                </div>
            `;
        }

        // Inicializar
        window.onload = function() {
            console.log('🧠 Demo do Sistema de Revisão carregado!');
            console.log('📋 Siga os passos na ordem para ver o sistema funcionando.');
        };
    </script>
</body>
</html>
