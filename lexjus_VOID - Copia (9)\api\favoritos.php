<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

switch ($metodo) {
    case 'GET':
        // Listar favoritos do usuário
        $query = "SELECT artigo_numero, data_adicionado 
                 FROM appestudo.lexjus_favoritos 
                 WHERE usuario_id = $1 
                 ORDER BY data_adicionado DESC";
        
        $result = pg_query_params($conexao, $query, [$usuario_id]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao consultar favoritos']);
            exit;
        }
        
        $favoritos = [];
        while ($row = pg_fetch_assoc($result)) {
            $favoritos[] = [
                'artigo_numero' => $row['artigo_numero'],
                'data_adicionado' => $row['data_adicionado']
            ];
        }
        
        echo json_encode(['favoritos' => $favoritos]);
        break;
        
    case 'POST':
        // Adicionar artigo aos favoritos
        if (!isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Número do artigo não informado']);
            exit;
        }
        
        $artigo_numero = $dados['artigo_numero'];
        
        $query = "INSERT INTO appestudo.lexjus_favoritos (usuario_id, artigo_numero) 
                 VALUES ($1, $2) 
                 ON CONFLICT (usuario_id, artigo_numero) DO NOTHING";
        
        $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao adicionar favorito']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo adicionado aos favoritos']);
        break;
        
    case 'DELETE':
        // Remover artigo dos favoritos
        if (!isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Número do artigo não informado']);
            exit;
        }
        
        $artigo_numero = $dados['artigo_numero'];
        
        $query = "DELETE FROM appestudo.lexjus_favoritos 
                 WHERE usuario_id = $1 AND artigo_numero = $2";
        
        $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao remover favorito']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo removido dos favoritos']);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>