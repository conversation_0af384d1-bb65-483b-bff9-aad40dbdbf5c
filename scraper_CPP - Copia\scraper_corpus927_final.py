#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper FINAL para o Código de Processo Penal da fonte Corpus927
Versão que analisa o HTML de forma mais precisa para capturar todos os elementos
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def limpar_texto(texto):
    """
    Limpa o texto removendo apenas caracteres desnecessários, mantendo referências legais
    """
    if not texto:
        return ""

    # Remover quebras de linha e espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)

    # NÃO remover referências legais - elas são importantes!
    # Apenas limpar espaços extras

    return texto.strip()

def extrair_numero_artigo(link_artigo):
    """
    Extrai o número do artigo do link
    """
    if not link_artigo:
        return None

    texto_artigo = link_artigo.get_text(strip=True)

    # Padrões para extrair número do artigo
    patterns = [
        r'Art\.\s*(\d+)(?:º|°)?(?:-([A-Z]))?',
        r'Artigo\s+(\d+)(?:º|°)?(?:-([A-Z]))?'
    ]

    for pattern in patterns:
        match = re.search(pattern, texto_artigo, re.IGNORECASE)
        if match:
            numero = int(match.group(1))
            sufixo = match.group(2) if match.group(2) else ""
            return f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")

    return None

def extrair_texto_raw_elemento(elemento):
    """
    Extrai o texto bruto do elemento preservando a estrutura
    """
    if not elemento:
        return ""

    # Usar get_text com separador para preservar estrutura
    texto = elemento.get_text(separator='\n', strip=True)

    # Normalizar quebras de linha múltiplas
    texto = re.sub(r'\n+', '\n', texto)

    return texto

def processar_artigo_final(link_artigo, soup):
    """
    Processa um artigo específico com análise precisa do HTML
    """
    artigo_id = extrair_numero_artigo(link_artigo)
    if not artigo_id:
        return None

    # Encontrar o parágrafo que contém o artigo
    paragrafo_artigo = link_artigo.find_parent('p')
    if not paragrafo_artigo:
        return None

    # Extrair texto completo do parágrafo do artigo
    texto_paragrafo_completo = extrair_texto_raw_elemento(paragrafo_artigo)

    # Extrair caput - tudo após o número do artigo até o primeiro inciso ou fim
    caput_match = re.search(r'Art\.\s*\d+(?:º|°)?(?:-[A-Z])?\s*\.?\s*(.+?)(?=\n[IVX]+\s*[-–]|\n§|\nParágrafo|$)',
                           texto_paragrafo_completo, re.IGNORECASE | re.DOTALL)

    if caput_match:
        caput = limpar_texto(caput_match.group(1))
    else:
        # Fallback: pegar tudo após o número do artigo
        caput_fallback = re.search(r'Art\.\s*\d+(?:º|°)?(?:-[A-Z])?\s*\.?\s*(.+)',
                                  texto_paragrafo_completo, re.IGNORECASE | re.DOTALL)
        caput = limpar_texto(caput_fallback.group(1)) if caput_fallback else ""

    # Inicializar estrutura do artigo
    artigo = {
        "artigo": artigo_id,
        "caput": caput,
        "incisos": [],
        "paragrafos_numerados": [],
        "paragrafo_unico": None
    }

    # Extrair incisos do próprio parágrafo do artigo
    linhas_paragrafo = texto_paragrafo_completo.split('\n')
    for linha in linhas_paragrafo:
        linha = linha.strip()
        if re.match(r'^[IVX]+\s*[-–]', linha):
            inciso_texto = limpar_texto(linha)
            if inciso_texto and inciso_texto not in artigo["incisos"]:
                artigo["incisos"].append(inciso_texto)

    # Buscar elementos seguintes até o próximo artigo
    elemento_atual = paragrafo_artigo

    while True:
        elemento_atual = elemento_atual.find_next_sibling(['p', 'div'])

        if not elemento_atual:
            break

        # Parar se encontrar outro artigo
        if elemento_atual.find('a', class_='numero_artigo'):
            break

        # Extrair texto do elemento
        texto_elemento = extrair_texto_raw_elemento(elemento_atual)

        # Parar se encontrar título/seção
        if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO)', texto_elemento, re.IGNORECASE):
            break

        # Pular elementos muito pequenos
        if len(texto_elemento.strip()) < 3:
            continue

        # Processar diferentes tipos de elementos
        processar_elemento_final(elemento_atual, artigo, texto_elemento)

    return artigo

def processar_elemento_final(elemento, artigo, texto_elemento):
    """
    Processa um elemento específico e adiciona à estrutura do artigo
    """
    # Verificar se é parágrafo único
    if re.search(r'Parágrafo\s+único', texto_elemento, re.IGNORECASE):
        paragrafo_match = re.search(r'Parágrafo\s+único\.?\s*(.+)', texto_elemento, re.IGNORECASE | re.DOTALL)
        if paragrafo_match and not artigo["paragrafo_unico"]:
            texto_paragrafo_unico = paragrafo_match.group(1).strip()
            artigo["paragrafo_unico"] = limpar_texto(texto_paragrafo_unico)

    # Verificar se é parágrafo numerado
    elif re.search(r'§\s*\d+(?:º|°)?', texto_elemento):
        paragrafo_match = re.search(r'(§\s*\d+(?:º|°)?)\s*(.+)', texto_elemento, re.DOTALL)
        if paragrafo_match:
            numero_paragrafo = paragrafo_match.group(1).strip()
            texto_paragrafo = limpar_texto(paragrafo_match.group(2))

            # Verificar se já existe este parágrafo
            paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo["paragrafos_numerados"])
            if not paragrafo_existe and texto_paragrafo and texto_paragrafo != ".":
                # Extrair alíneas do texto do parágrafo
                alineas = extrair_alineas_do_paragrafo(texto_paragrafo)

                artigo["paragrafos_numerados"].append({
                    "numero": numero_paragrafo,
                    "texto": texto_paragrafo,
                    "incisos": [],
                    "alineas": alineas
                })

    # Verificar se é inciso (romano) - evitar duplicações
    elif re.search(r'^[IVX]+\s*[-–]', texto_elemento):
        inciso_texto = limpar_texto(texto_elemento)
        # Verificar se já existe um inciso similar (evitar duplicações)
        inciso_existe = False
        for inciso_existente in artigo["incisos"]:
            if inciso_texto in inciso_existente or inciso_existente in inciso_texto:
                inciso_existe = True
                break

        if inciso_texto and not inciso_existe and len(inciso_texto) > 10:  # Só adicionar incisos com conteúdo substancial
            artigo["incisos"].append(inciso_texto)

    # Verificar se é alínea isolada
    elif re.search(r'^[a-z]\)', texto_elemento):
        alinea_texto = limpar_texto(texto_elemento)
        if alinea_texto:
            # Adicionar à última estrutura disponível (parágrafo)
            if artigo["paragrafos_numerados"]:
                ultimo_paragrafo = artigo["paragrafos_numerados"][-1]
                if alinea_texto not in ultimo_paragrafo["alineas"]:
                    ultimo_paragrafo["alineas"].append(alinea_texto)

    # Processar incisos que podem estar em linhas separadas - com verificação de duplicação
    linhas = texto_elemento.split('\n')
    for linha in linhas:
        linha = linha.strip()
        if re.match(r'^[IVX]+\s*[-–]', linha):
            inciso_texto = limpar_texto(linha)
            # Verificar duplicação
            inciso_existe = False
            for inciso_existente in artigo["incisos"]:
                if inciso_texto in inciso_existente or inciso_existente in inciso_texto:
                    inciso_existe = True
                    break

            if inciso_texto and not inciso_existe and len(inciso_texto) > 10:
                artigo["incisos"].append(inciso_texto)

def extrair_alineas_do_paragrafo(texto_paragrafo):
    """
    Extrai alíneas que podem estar dentro do texto de um parágrafo
    """
    alineas = []

    # Buscar padrões de alíneas no texto
    linhas = texto_paragrafo.split('\n')
    for linha in linhas:
        linha = linha.strip()
        if re.match(r'^[a-z]\)', linha):
            alinea = limpar_texto(linha)
            if alinea and alinea not in alineas:
                alineas.append(alinea)

    return alineas

def scraper_corpus927_final():
    """
    Função principal do scraper final
    """
    print("=" * 60)
    print("🔍 SCRAPER CPP FINAL - FONTE CORPUS927")
    print("=" * 60)

    url = "https://corpus927.enfam.jus.br/legislacao/cpp-41"

    try:
        print(f"📡 Fazendo requisição para: {url}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        print(f"✅ Requisição bem-sucedida. Status: {response.status_code}")
        print(f"📄 Tamanho do conteúdo: {len(response.content):,} bytes")

        # Parse do HTML
        soup = BeautifulSoup(response.content, 'html.parser')

        # Encontrar todos os artigos
        links_artigos = soup.find_all('a', class_='numero_artigo')
        print(f"🔍 Encontrados {len(links_artigos)} links de artigos")

        artigos_extraidos = []
        artigos_processados = set()  # Para evitar duplicações

        for i, link_artigo in enumerate(links_artigos):
            try:
                artigo = processar_artigo_final(link_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])

                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")

            except Exception as e:
                print(f"⚠️ Erro ao processar artigo {i+1}: {e}")
                continue

        # Ordenar por número do artigo
        def extrair_numero_para_ordenacao(artigo_id):
            match = re.search(r'Art\.\s*(\d+)', artigo_id)
            return int(match.group(1)) if match else 0

        artigos_extraidos.sort(key=lambda x: extrair_numero_para_ordenacao(x["artigo"]))

        print(f"✅ Extração concluída. Total: {len(artigos_extraidos)} artigos únicos")

        # Salvar arquivos
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Arquivo JSON
        nome_arquivo_json = 'cpp_corpus927_completo.json'
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_extraidos, f, ensure_ascii=False, indent=2)
        print(f"💾 Arquivo JSON salvo: {nome_arquivo_json}")

        # Estatísticas detalhadas
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   • Total de artigos: {len(artigos_extraidos)}")
        if artigos_extraidos:
            print(f"   • Primeiro artigo: {artigos_extraidos[0]['artigo']}")
            print(f"   • Último artigo: {artigos_extraidos[-1]['artigo']}")

            # Contar estruturas
            total_incisos = sum(len(art['incisos']) for art in artigos_extraidos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos_extraidos)
            total_paragrafos_unicos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'])

            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")

            # Verificar primeiros artigos para debug
            print(f"\n🔍 VERIFICAÇÃO DOS PRIMEIROS ARTIGOS:")
            for i, art in enumerate(artigos_extraidos[:10]):
                print(f"   {art['artigo']}: {len(art['incisos'])} incisos, {len(art['paragrafos_numerados'])} parágrafos")
                if i < 3:  # Mostrar caput dos 3 primeiros
                    print(f"      Caput: {art['caput'][:100]}...")

        return artigos_extraidos

    except requests.RequestException as e:
        print(f"❌ Erro na requisição: {e}")
        return []
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return []

if __name__ == '__main__':
    artigos = scraper_corpus927_final()
    if artigos:
        print(f"\n🎉 Scraping COMPLETO concluído com sucesso!")
        print(f"📁 Arquivo gerado: cpp_corpus927_completo.json")
        print(f"🔧 Versão COMPLETA: Mantém referências legais e evita duplicações!")
    else:
        print(f"\n❌ Falha no scraping COMPLETO!")
