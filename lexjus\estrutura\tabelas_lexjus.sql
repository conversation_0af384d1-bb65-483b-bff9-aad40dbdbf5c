-- Tabela para armazenar artigos favoritos
CREATE TABLE IF NOT EXISTS appestudo.lexjus_favoritos (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE,
    artigo_numero VARCHAR(20) NOT NULL,
    data_adicionado TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(usuario_id, artigo_numero)
);

-- Tabela para armazenar listas personalizadas
CREATE TABLE IF NOT EXISTS appestudo.lexjus_listas (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE,
    nome VARCHAR(100) NOT NULL,
    descricao TEXT,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela para armazenar artigos em listas
CREATE TABLE IF NOT EXISTS appestudo.lexjus_lista_artigos (
    id SERIAL PRIMARY KEY,
    lista_id INTEGER NOT NULL REFERENCES appestudo.lexjus_listas(id) ON DELETE CASCADE,
    artigo_numero VARCHAR(20) NOT NULL,
    data_adicionado TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(lista_id, artigo_numero)
);

-- Tabela para armazenar progresso de leitura
CREATE TABLE IF NOT EXISTS appestudo.lexjus_progresso (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE,
    artigo_numero VARCHAR(20) NOT NULL,
    lido BOOLEAN DEFAULT TRUE,
    data_leitura TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(usuario_id, artigo_numero)
);

-- Tabela para armazenar anotações em artigos
CREATE TABLE IF NOT EXISTS appestudo.lexjus_anotacoes (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE,
    artigo_numero VARCHAR(20) NOT NULL,
    texto TEXT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


