#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

print("=== VERIFICAÇÃO DA LIMPEZA DE PARÁGRAFOS ===")

# Carregar arquivo final
with open('codigo_civil_formato_lexjus_final.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"Total de artigos: {len(data)}")

# Buscar artigos com parágrafos para mostrar exemplos
exemplos_encontrados = 0
for artigo in data:
    if 'paragrafos_numerados' in artigo and artigo['paragrafos_numerados'] and exemplos_encontrados < 3:
        print(f"\n=== {artigo['artigo']} ===")
        for paragrafo in artigo['paragrafos_numerados'][:2]:  # Mostrar apenas os 2 primeiros
            print(f"Número: {paragrafo['numero']}")
            print(f"Texto: {paragrafo['texto'][:100]}...")
            print()
        exemplos_encontrados += 1

print("✅ Verificação concluída! Os parágrafos agora estão limpos.")
print("📁 Arquivo final: codigo_civil_formato_lexjus_final.json")
