import requests
from bs4 import BeautifulSoup
import json
import re

# URL da Constituição no site do Planalto
URL_CONSTITUICAO = 'http://www.planalto.gov.br/ccivil_03/constituicao/ConstituicaoCompilado.htm'
ARQUIVO_SAIDA_JSON = 'artigos.json'

def buscar_conteudo_pagina(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        return response.text
    except requests.exceptions.Timeout:
        print(f"Erro ao buscar a página {url}: Timeout após 30 segundos.")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Erro ao buscar a página {url}: {e}")
        return None

def limpar_texto(texto):
    if texto:
        texto = texto.strip()
        texto = re.sub(r'\s+', ' ', texto) # Remove espaços múltiplos, quebras de linha etc.
        texto = texto.replace("\xa0", " ") # Substitui non-breaking spaces
        return texto.strip()
    return ""

def remover_referencias(texto):
    if not texto:
        return texto
    # Remove (Vide...), (Incluído por...), (Redação dada por...), etc. e espaços ao redor
    texto = re.sub(r'\s*\(Vide.*?\)\s*', ' ', texto)
    texto = re.sub(r'\s*\(Incluíd[oa]\s+pela\s+Emenda.*?\)\s*', ' ', texto)
    texto = re.sub(r'\s*\(Incluíd[oa]\s+pelo\s+Decreto.*?\)\s*', ' ', texto)
    texto = re.sub(r'\s*\(Incluíd[oa].*?\)\s*', ' ', texto) # Genérico para outros "Incluído"
    texto = re.sub(r'\s*\(Redação\s+dada\s+pela\s+Emenda.*?\)\s*', ' ', texto)
    texto = re.sub(r'\s*\(Regulamento\)\s*', ' ', texto)
    # Tenta remover links soltos que podem ter sobrado, se estiverem entre parênteses
    texto = re.sub(r'\s*\(\s*<a\s+href=.*?>.*?<\/a>\s*\)\s*', ' ', texto)
    return limpar_texto(texto)


def analisar_e_extrair_artigos(html_content):
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    ultimo_item_principal_do_artigo = None # Para associar alíneas e incisos hierarquicamente
    contexto_atual = "caput"  # Pode ser: "caput", "paragrafo_unico", "paragrafo_numerado"

    # Itera sobre todos os parágrafos, que parecem ser as unidades básicas no HTML do Planalto
    for p_tag in soup.find_all('p'):
        # MUDANÇA AQUI: Obter todo o texto do parágrafo e depois limpar.
        texto_paragrafo_raw = p_tag.get_text(separator=' ', strip=False) # strip=False para limpar depois
        texto_limpo = limpar_texto(texto_paragrafo_raw) # limpar_texto já faz strip()

        # DEBUG ADICIONAL: Imprimir cada parágrafo limpo que está sendo processado
        if texto_limpo: # S�� imprimir se houver conteúdo após a limpeza
            print(f"DEBUG PARAGRAFO PROCESSANDO: \"{texto_limpo[:100]}\"...") # Imprime os primeiros 100 caracteres

        if not texto_limpo: # Pula parágrafos vazios após limpeza
            continue

        match_artigo = re.match(r'^(Art\.\s*\d+[-\w]*º?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL) # Regex de artigo melhorada
        match_inciso = re.match(r'^(M{0,3}(?:CM|CD|D?C{0,3})(?:XC|XL|L?X{0,3})(?:IX|IV|V?I{0,3})\s*-.*)', texto_limpo, re.IGNORECASE)
        match_paragrafo_unico = re.match(r'^Parágrafo único\.\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_paragrafo_numerado = re.match(r'^(§\s*\d+º?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_alinea = re.match(r'^([a-z]\))\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        if match_artigo:
            if artigo_atual:
                artigos_extraidos.append(artigo_atual)

            numero_art_bruto = limpar_texto(match_artigo.group(1)) # Pegar o número bruto primeiro
            numero_art = remover_referencias(numero_art_bruto)
            caput_texto = remover_referencias(limpar_texto(match_artigo.group(2)))

            # Print de depuração para ver qual artigo está sendo processado
            print(f"DEBUG: Encontrado Artigo: '{numero_art_bruto}' -> Limpo: '{numero_art}'")

            artigo_atual = {
                "artigo": numero_art,
                "caput": caput_texto,
                "incisos": [],
                "paragrafos_numerados": [],
                "paragrafo_unico": None,
            }
            ultimo_item_principal_do_artigo = {"tipo": "caput", "referencia": artigo_atual}
            contexto_atual = "caput"

        elif artigo_atual:
            if match_inciso:
                texto_inciso = remover_referencias(limpar_texto(match_inciso.group(1)))

                # Determinar onde adicionar o inciso baseado no contexto atual
                if contexto_atual == "caput":
                    # Inciso do caput principal
                    artigo_atual["incisos"].append(texto_inciso)
                    ultimo_item_principal_do_artigo = {"tipo": "inciso_caput", "referencia": artigo_atual["incisos"][-1], "index": len(artigo_atual["incisos"]) - 1}
                elif contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    # Inciso de um parágrafo numerado
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "alineas" not in paragrafo_atual:
                        paragrafo_atual["alineas"] = []
                    paragrafo_atual["alineas"].append(texto_inciso)
                    ultimo_item_principal_do_artigo = {"tipo": "inciso_paragrafo", "referencia": paragrafo_atual, "index": len(paragrafo_atual["alineas"]) - 1}
                else:
                    # Fallback: adicionar ao caput
                    artigo_atual["incisos"].append(texto_inciso)
                    ultimo_item_principal_do_artigo = {"tipo": "inciso_caput", "referencia": artigo_atual["incisos"][-1], "index": len(artigo_atual["incisos"]) - 1}

            elif match_paragrafo_unico:
                texto_pu = remover_referencias(limpar_texto(match_paragrafo_unico.group(1)))
                artigo_atual["paragrafo_unico"] = texto_pu
                ultimo_item_principal_do_artigo = {"tipo": "paragrafo_unico", "referencia": artigo_atual["paragrafo_unico"]}
                contexto_atual = "paragrafo_unico"

            elif match_paragrafo_numerado:
                num_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(1)))
                texto_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(2)))
                paragrafo_obj = {"numero": num_parag, "texto": texto_parag, "alineas": []}
                artigo_atual["paragrafos_numerados"].append(paragrafo_obj)
                ultimo_item_principal_do_artigo = {"tipo": "paragrafo_numerado", "referencia": paragrafo_obj}
                contexto_atual = "paragrafo_numerado"

            elif match_alinea:
                texto_alinea_completo = remover_referencias(limpar_texto(match_alinea.group(0)))

                # Adicionar alínea ao contexto apropriado
                if ultimo_item_principal_do_artigo:
                    tipo_pai = ultimo_item_principal_do_artigo["tipo"]
                    ref_pai = ultimo_item_principal_do_artigo["referencia"]

                    if tipo_pai == "paragrafo_numerado" and isinstance(ref_pai, dict):
                        if "alineas" not in ref_pai:
                             ref_pai["alineas"] = []
                        ref_pai["alineas"].append(texto_alinea_completo)
                    elif tipo_pai == "inciso_paragrafo" and isinstance(ref_pai, dict):
                        # Alínea de um inciso dentro de um parágrafo
                        if "alineas" not in ref_pai:
                             ref_pai["alineas"] = []
                        ref_pai["alineas"].append(texto_alinea_completo)
                    elif tipo_pai == "inciso_caput":
                        # Alínea de um inciso do caput - criar estrutura especial
                        if "alineas_do_artigo" not in artigo_atual:
                            artigo_atual["alineas_do_artigo"] = []
                        artigo_atual["alineas_do_artigo"].append(texto_alinea_completo + f" (pertencente a: inciso - {str(ref_pai)[:30]}...)")
                    else:
                        if "alineas_do_artigo" not in artigo_atual:
                            artigo_atual["alineas_do_artigo"] = []
                        artigo_atual["alineas_do_artigo"].append(texto_alinea_completo + f" (pertencente a: {tipo_pai} - {str(ref_pai)[:30]}...)")

            elif texto_limpo.isupper() and \
                 ("TÍTULO" in texto_limpo or "CAPÍTULO" in texto_limpo or \
                  "SEÇÃO" in texto_limpo or "LIVRO" in texto_limpo or \
                  "DISPOSIÇÕES GERAIS" in texto_limpo or "PREÂMBULO" in texto_limpo or "ATO DAS DISPOSIÇÕES" in texto_limpo ):
                if artigo_atual:
                    artigos_extraidos.append(artigo_atual)
                    artigo_atual = None
                    ultimo_item_principal_do_artigo = None
                    contexto_atual = "caput"

    if artigo_atual:
        artigos_extraidos.append(artigo_atual)

    if not artigos_extraidos:
        print("AVISO: Nenhum artigo foi extraído com a lógica atual. Verifique os seletores e a lógica de parsing no HTML.")
        return [
            {"artigo": "Art. X", "caput": "Falha na extração. Verifique o script scraper_planalto.py e o HTML de origem.", "incisos": [], "paragrafo_unico": None}
        ]

    print(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def salvar_em_json(dados, nome_arquivo):
    try:
        with open(nome_arquivo, 'w', encoding='utf-8') as f:
            json.dump(dados, f, ensure_ascii=False, indent=2)
        print(f"Dados salvos com sucesso em {nome_arquivo}")
    except IOError as e:
        print(f"Erro ao salvar o arquivo {nome_arquivo}: {e}")

def main():
    print(f"Iniciando scraper para a URL: {URL_CONSTITUICAO}")
    html_content = buscar_conteudo_pagina(URL_CONSTITUICAO)

    if html_content:
        print("Conteúdo da página obtido. Analisando...")
        artigos = analisar_e_extrair_artigos(html_content)

        if artigos:
            salvar_em_json(artigos, ARQUIVO_SAIDA_JSON)
        else:
            print("Nenhum artigo foi extraído ou a lista de artigos está vazia.")
    else:
        print("Não foi possível obter o conteúdo da página para análise.")

if __name__ == '__main__':
    main()
