# Cache Busting para LexJus - VERSÃO PRODUÇÃO
# Use este arquivo como .htaccess no servidor

# Habilitar compressão GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configurações de cache otimizadas
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache longo para arquivos estáticos
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    
    # Cache médio para CSS e JS (temos cache busting)
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    
    # Cache curto para HTML e PHP
    ExpiresByType text/html "access plus 4 hours"
    ExpiresByType application/x-httpd-php "access plus 4 hours"
    
    # Cache para JSON
    ExpiresByType application/json "access plus 1 day"
</IfModule>

# Headers de cache
<IfModule mod_headers.c>
    # Para arquivos CSS e JS
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=604800"
        Header append Vary "Accept-Encoding"
    </FilesMatch>
    
    # Para arquivos PHP
    <FilesMatch "\.php$">
        Header set Cache-Control "public, max-age=14400, must-revalidate"
    </FilesMatch>
    
    # Segurança
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# Bloquear acesso a arquivos sensíveis
<FilesMatch "\.(htaccess|htpasswd|ini|log|sql|py|md|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acesso a ferramentas de desenvolvimento
<FilesMatch "(clear-cache|debug|test-cache|dev-tools|diagnostico)\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Configurações de charset
AddDefaultCharset UTF-8

# MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType application/json .json
</IfModule>

# Forçar HTTPS (descomente se tiver SSL)
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteCond %{HTTPS} off
#     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>
