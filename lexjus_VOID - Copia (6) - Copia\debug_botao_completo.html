<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Completo - Bo<PERSON>ão de Revisão</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-section {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <h1>🔧 Debug Completo - Botão de Revisão</h1>

    <div class="container">
        <h2>🎯 Teste de Verificação de Status</h2>
        <div class="test-section">
            <p>Teste a verificação de status de artigos específicos:</p>
            
            <div class="controls">
                <div>
                    <label>Número do Artigo:</label>
                    <input type="text" id="artigoTeste" value="5" placeholder="Ex: 5, Art. 24, 37">
                </div>
                <div>
                    <button onclick="verificarArtigo()">🔍 Verificar Status</button>
                    <button onclick="adicionarArtigo()">➕ Adicionar à Revisão</button>
                </div>
            </div>
            
            <div id="verificacao-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📋 Lista de Artigos na Revisão</h2>
        <div class="test-section">
            <button onclick="listarArtigos()">📋 Carregar Lista Atual</button>
            <button onclick="limparCache()">🗑️ Limpar Cache</button>
            
            <div id="lista-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Teste de Cenários</h2>
        <div class="test-section">
            <p>Teste cenários específicos que estavam causando problemas:</p>
            
            <div class="controls">
                <button onclick="testarCenario1()">📝 Cenário 1: Adicionar Novo</button>
                <button onclick="testarCenario2()">🔄 Cenário 2: Verificar Existente</button>
                <button onclick="testarCenario3()">🎯 Cenário 3: Recarregar Página</button>
                <button onclick="testarCenario4()">🔍 Cenário 4: Diferentes Formatos</button>
            </div>
            
            <div id="cenarios-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Log de Debug</h2>
        <div class="test-section">
            <button onclick="limparLog()">🗑️ Limpar Log</button>
            <button onclick="exportarLog()">💾 Exportar Log</button>
            
            <div id="debug-log" class="log-area"></div>
        </div>
    </div>

    <div class="container">
        <h2>⚙️ Status do Sistema</h2>
        <div class="test-section">
            <div id="system-status">
                <p><span class="status-indicator status-warning"></span> Carregando status...</p>
            </div>
            <button onclick="verificarSistema()">🔄 Atualizar Status</button>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            debugLog.push(logEntry);
            
            const logArea = document.getElementById('debug-log');
            logArea.textContent = debugLog.slice(-50).join('\n'); // Últimas 50 entradas
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(logEntry);
        }

        async function fazerRequisicao(url, options = {}) {
            try {
                log(`Fazendo requisição para: ${url}`);
                log(`Dados enviados: ${JSON.stringify(options.body || 'GET')}`);
                
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                const data = await response.json();
                
                log(`Resposta recebida (${response.status}): ${JSON.stringify(data)}`);
                
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                log(`Erro na requisição: ${error.message}`, 'error');
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        async function verificarArtigo() {
            const artigo = document.getElementById('artigoTeste').value.trim();
            if (!artigo) {
                alert('Digite um número de artigo');
                return;
            }

            log(`Verificando status do artigo: ${artigo}`);

            const resultado = await fazerRequisicao('./api/revisao.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'verificar',
                    artigo_numero: artigo
                })
            });

            const element = document.getElementById('verificacao-result');
            if (resultado.ok) {
                element.className = 'result success';
                element.textContent = `✅ VERIFICAÇÃO CONCLUÍDA\n\n` +
                    `Artigo: ${resultado.data.artigo_numero}\n` +
                    `Existe: ${resultado.data.existe ? 'SIM' : 'NÃO'}\n` +
                    `Artigo Limpo: ${resultado.data.artigo_numero_limpo}\n` +
                    `Linhas Encontradas: ${resultado.data.debug?.linhas_encontradas || 0}\n\n` +
                    `Detalhes: ${JSON.stringify(resultado.data.detalhes, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ ERRO NA VERIFICAÇÃO\n\n${resultado.error || 'Erro desconhecido'}`;
            }
        }

        async function adicionarArtigo() {
            const artigo = document.getElementById('artigoTeste').value.trim();
            if (!artigo) {
                alert('Digite um número de artigo');
                return;
            }

            log(`Adicionando artigo à revisão: ${artigo}`);

            const resultado = await fazerRequisicao('./api/revisao.php', {
                method: 'POST',
                body: JSON.stringify({
                    acao: 'iniciar',
                    artigo_numero: artigo
                })
            });

            const element = document.getElementById('verificacao-result');
            if (resultado.ok) {
                element.className = 'result success';
                element.textContent = `✅ ADIÇÃO CONCLUÍDA\n\n` +
                    `Sucesso: ${resultado.data.sucesso}\n` +
                    `Mensagem: ${resultado.data.mensagem}\n` +
                    `ID da Revisão: ${resultado.data.revisao_id || 'N/A'}\n\n` +
                    `Resposta completa: ${JSON.stringify(resultado.data, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ ERRO NA ADIÇÃO\n\n${resultado.error || 'Erro desconhecido'}`;
            }
        }

        async function listarArtigos() {
            log('Carregando lista de artigos na revisão');

            const resultado = await fazerRequisicao('./api/revisao.php');

            const element = document.getElementById('lista-result');
            if (resultado.ok && resultado.data.revisoes) {
                element.className = 'result info';
                element.textContent = `📋 ARTIGOS NA REVISÃO (${resultado.data.revisoes.length})\n\n` +
                    resultado.data.revisoes.map(r => 
                        `• ${r.artigo_numero} - Status: ${r.status} - Facilidade: ${r.facilidade} - Repetições: ${r.repeticoes}`
                    ).join('\n');
            } else {
                element.className = 'result error';
                element.textContent = `❌ ERRO AO CARREGAR LISTA\n\n${resultado.error || 'Erro desconhecido'}`;
            }
        }

        async function testarCenario1() {
            log('=== TESTANDO CENÁRIO 1: ADICIONAR NOVO ===');
            
            // Usar um número aleatório para garantir que é novo
            const artigoNovo = `teste_${Date.now()}`;
            document.getElementById('artigoTeste').value = artigoNovo;
            
            // Verificar se não existe
            await verificarArtigo();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Adicionar
            await adicionarArtigo();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Verificar novamente
            await verificarArtigo();
            
            log('=== CENÁRIO 1 CONCLUÍDO ===');
        }

        async function testarCenario2() {
            log('=== TESTANDO CENÁRIO 2: VERIFICAR EXISTENTE ===');
            
            // Primeiro listar para pegar um artigo existente
            await listarArtigos();
            
            log('=== CENÁRIO 2 CONCLUÍDO ===');
        }

        async function testarCenario3() {
            log('=== TESTANDO CENÁRIO 3: SIMULAÇÃO DE RECARREGAR ===');
            
            // Simular limpeza de cache
            limparCache();
            
            // Verificar um artigo
            const artigo = document.getElementById('artigoTeste').value || '5';
            document.getElementById('artigoTeste').value = artigo;
            await verificarArtigo();
            
            log('=== CENÁRIO 3 CONCLUÍDO ===');
        }

        async function testarCenario4() {
            log('=== TESTANDO CENÁRIO 4: DIFERENTES FORMATOS ===');
            
            const formatos = ['5', 'Art. 5', 'Art. 5.', 'Artigo 5', 'Artigo 5.'];
            
            for (const formato of formatos) {
                log(`Testando formato: "${formato}"`);
                document.getElementById('artigoTeste').value = formato;
                await verificarArtigo();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('=== CENÁRIO 4 CONCLUÍDO ===');
        }

        function limparCache() {
            log('Cache local limpo (simulação)');
            // Aqui você pode adicionar lógica para limpar cache se houver
        }

        function limparLog() {
            debugLog = [];
            document.getElementById('debug-log').textContent = '';
            log('Log limpo');
        }

        function exportarLog() {
            const logText = debugLog.join('\n');
            const blob = new Blob([logText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('Log exportado');
        }

        async function verificarSistema() {
            const statusDiv = document.getElementById('system-status');
            
            try {
                // Verificar se a API está respondendo
                const testApi = await fazerRequisicao('./api/revisao.php?acao=estatisticas');
                
                let status = '';
                if (testApi.ok) {
                    status += '<p><span class="status-indicator status-ok"></span> API funcionando</p>';
                } else {
                    status += '<p><span class="status-indicator status-error"></span> API com problemas</p>';
                }
                
                // Verificar autenticação
                if (testApi.data && !testApi.data.erro) {
                    status += '<p><span class="status-indicator status-ok"></span> Usuário autenticado</p>';
                } else {
                    status += '<p><span class="status-indicator status-error"></span> Problema de autenticação</p>';
                }
                
                statusDiv.innerHTML = status;
                
            } catch (error) {
                statusDiv.innerHTML = '<p><span class="status-indicator status-error"></span> Erro ao verificar sistema</p>';
                log(`Erro na verificação do sistema: ${error.message}`, 'error');
            }
        }

        // Inicializar
        window.onload = function() {
            log('Debug completo carregado');
            verificarSistema();
        };
    </script>
</body>
</html>
