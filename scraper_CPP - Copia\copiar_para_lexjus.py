#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para copiar o arquivo CPP processado para o diretório LexJus
"""

import shutil
import os
from datetime import datetime

def copiar_para_lexjus():
    """
    Copia o arquivo CPP simplificado para o diretório LexJus
    """
    print("=" * 60)
    print("📁 COPIANDO CPP PARA LEXJUS")
    print("=" * 60)
    
    # Arquivos de origem
    arquivo_origem_json = 'codigo_processo_penal_simplificado.json'
    arquivo_origem_js = 'codigo_processo_penal_simplificado.js'
    
    # Diretório de destino
    diretorio_destino = '../lexjus_VOID'
    
    # Arquivos de destino
    arquivo_destino_json = os.path.join(diretorio_destino, 'codigo_processo_penal.json')
    arquivo_destino_js = os.path.join(diretorio_destino, 'codigo_processo_penal.js')
    
    try:
        # Verificar se os arquivos de origem existem
        if not os.path.exists(arquivo_origem_json):
            print(f"❌ Arquivo {arquivo_origem_json} não encontrado!")
            return
        
        if not os.path.exists(arquivo_origem_js):
            print(f"❌ Arquivo {arquivo_origem_js} não encontrado!")
            return
        
        # Verificar se o diretório de destino existe
        if not os.path.exists(diretorio_destino):
            print(f"❌ Diretório {diretorio_destino} não encontrado!")
            return
        
        # Copiar arquivo JSON
        shutil.copy2(arquivo_origem_json, arquivo_destino_json)
        print(f"✅ Arquivo JSON copiado: {arquivo_destino_json}")
        
        # Copiar arquivo JS
        shutil.copy2(arquivo_origem_js, arquivo_destino_js)
        print(f"✅ Arquivo JS copiado: {arquivo_destino_js}")
        
        # Verificar tamanhos dos arquivos
        tamanho_json = os.path.getsize(arquivo_destino_json)
        tamanho_js = os.path.getsize(arquivo_destino_js)
        
        print(f"\n📊 INFORMAÇÕES DOS ARQUIVOS:")
        print(f"   • JSON: {tamanho_json:,} bytes")
        print(f"   • JS: {tamanho_js:,} bytes")
        
        print(f"\n✅ CÓPIA CONCLUÍDA COM SUCESSO!")
        print(f"   📄 Os arquivos do CPP estão prontos para uso no LexJus")
        
    except Exception as e:
        print(f"❌ Erro ao copiar arquivos: {e}")

if __name__ == '__main__':
    copiar_para_lexjus()
