import json
import re

# Carregar o arquivo
with open('codigo_civil_formato_lexjus_final.json', 'r', encoding='utf-8') as f:
    artigos = json.load(f)

print("=== VERIFICAÇÃO DO ART. 1403º ===")

# Encontrar o Art. 1403º
for i, artigo in enumerate(artigos):
    if '1403' in artigo.get('artigo', ''):
        print(f"Artigo encontrado no índice {i}: {artigo['artigo']}")
        print(f"Caput: {artigo['caput']}")
        print(f"Incisos: {len(artigo.get('incisos', []))}")
        
        for j, inciso in enumerate(artigo.get('incisos', [])):
            print(f"  {j+1}. {inciso}")
        
        # Verificar se o caput começa com número
        if re.match(r'^[0-9]+\s+', artigo['caput']):
            print("❌ PROBLEMA: Caput ainda começa com número!")
        else:
            print("✅ Caput está correto - não começa com número!")
        
        # Verificar se tem incisos
        if len(artigo.get('incisos', [])) > 0:
            print("✅ Artigo tem incisos!")
        else:
            print("⚠️  Artigo não tem incisos!")
        
        break
else:
    print("❌ Art. 1403º não encontrado!")

print("\n=== VERIFICAÇÃO DE OUTROS PROBLEMAS ===")

# Verificar outros artigos com problemas similares
problemas = 0
for i, artigo in enumerate(artigos):
    caput = artigo.get('caput', '')
    if re.match(r'^[0-9]+\s+', caput):
        problemas += 1
        if problemas <= 3:  # Mostrar apenas os primeiros 3
            print(f"Problema {problemas}: {artigo.get('artigo', 'N/A')} - {caput[:50]}...")

if problemas == 0:
    print("✅ Nenhum artigo com caput começando com número encontrado!")
else:
    print(f"⚠️  Total de {problemas} artigos com problemas encontrados!")

print("\n=== VERIFICAÇÃO CONCLUÍDA ===")
