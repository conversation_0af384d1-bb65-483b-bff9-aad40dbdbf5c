<?php
session_start();
require_once __DIR__ . '/../conexao_POST.php';

// Simular usuário logado para teste
if (!isset($_SESSION['idusuario'])) {
    $_SESSION['idusuario'] = 1; // ID de teste
    echo "⚠️ Usuário de teste criado (ID: 1)<br><br>";
}

echo "<h2>🔍 Teste de Conexão e Estrutura do Sistema de Revisão</h2>";

// Verificar se as tabelas existem
$tabelas = [
    'lexjus_revisoes',
    'lexjus_historico_revisoes', 
    'lexjus_config_revisao'
];

echo "<h3>📊 Verificação das Tabelas:</h3>";
foreach ($tabelas as $tabela) {
    $query = "SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'appestudo' 
        AND table_name = '$tabela'
    )";
    
    $result = pg_query($conexao, $query);
    $exists = pg_fetch_result($result, 0, 0);
    
    if ($exists === 't') {
        echo "✅ Tabela <strong>$tabela</strong> existe<br>";
    } else {
        echo "❌ Tabela <strong>$tabela</strong> NÃO existe<br>";
    }
}

// Verificar se a função existe
echo "<h3>🧮 Verificação da Função:</h3>";
$query_func = "SELECT EXISTS (
    SELECT FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND routine_name = 'calcular_proxima_revisao'
)";

$result_func = pg_query($conexao, $query_func);
$func_exists = pg_fetch_result($result_func, 0, 0);

if ($func_exists === 't') {
    echo "✅ Função <strong>calcular_proxima_revisao</strong> existe<br>";
} else {
    echo "❌ Função <strong>calcular_proxima_revisao</strong> NÃO existe<br>";
}

// Testar inserção básica
echo "<h3>🧪 Teste de Inserção:</h3>";
try {
    // Verificar se já existe configuração para o usuário
    $query_config = "SELECT id FROM appestudo.lexjus_config_revisao WHERE usuario_id = 1";
    $result_config = pg_query($conexao, $query_config);
    
    if (pg_num_rows($result_config) === 0) {
        $insert_config = "INSERT INTO appestudo.lexjus_config_revisao (usuario_id) VALUES (1)";
        $result_insert = pg_query($conexao, $insert_config);
        
        if ($result_insert) {
            echo "✅ Configuração padrão criada para usuário teste<br>";
        } else {
            echo "❌ Erro ao criar configuração: " . pg_last_error($conexao) . "<br>";
        }
    } else {
        echo "✅ Configuração já existe para usuário teste<br>";
    }
    
    // Testar inserção de revisão
    $query_revisao = "SELECT id FROM appestudo.lexjus_revisoes WHERE usuario_id = 1 AND artigo_numero = 'teste'";
    $result_revisao = pg_query($conexao, $query_revisao);
    
    if (pg_num_rows($result_revisao) === 0) {
        $insert_revisao = "INSERT INTO appestudo.lexjus_revisoes (usuario_id, artigo_numero) VALUES (1, 'teste')";
        $result_insert_rev = pg_query($conexao, $insert_revisao);
        
        if ($result_insert_rev) {
            echo "✅ Revisão de teste criada<br>";
        } else {
            echo "❌ Erro ao criar revisão: " . pg_last_error($conexao) . "<br>";
        }
    } else {
        echo "✅ Revisão de teste já existe<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erro no teste: " . $e->getMessage() . "<br>";
}

// Testar API
echo "<h3>🔗 Teste da API:</h3>";
echo "<a href='api/revisao.php?acao=estatisticas' target='_blank'>🔗 Testar API de Estatísticas</a><br>";
echo "<a href='api/revisao.php?acao=configuracao' target='_blank'>🔗 Testar API de Configuração</a><br>";

echo "<h3>📋 Próximos Passos:</h3>";
echo "<ol>";
echo "<li>Se alguma tabela não existe, execute o arquivo SQL: <code>estrutura/adicionar_sistema_revisao.sql</code></li>";
echo "<li>Faça login no sistema principal antes de testar</li>";
echo "<li>Use o arquivo <code>teste_sistema_revisao.html</code> para testes completos</li>";
echo "</ol>";

echo "<br><strong>🎯 Status do Sistema:</strong> ";
$todas_tabelas_existem = true;
foreach ($tabelas as $tabela) {
    $query = "SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'appestudo' 
        AND table_name = '$tabela'
    )";
    
    $result = pg_query($conexao, $query);
    $exists = pg_fetch_result($result, 0, 0);
    
    if ($exists !== 't') {
        $todas_tabelas_existem = false;
        break;
    }
}

if ($todas_tabelas_existem && $func_exists === 't') {
    echo "<span style='color: green; font-weight: bold;'>✅ SISTEMA PRONTO PARA USO!</span>";
} else {
    echo "<span style='color: red; font-weight: bold;'>❌ SISTEMA PRECISA SER CONFIGURADO</span>";
}
?>
