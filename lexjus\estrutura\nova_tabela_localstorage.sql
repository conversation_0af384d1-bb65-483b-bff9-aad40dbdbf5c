-- Tabela para armazenar dados do localStorage
CREATE TABLE appestudo.user_localstorage (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE,
    chave VARCHAR(100) NOT NULL,
    valor TEXT,
    tipo_dado VARCHAR(20) DEFAULT 'string',
    ultima_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(usuario_id, chave)
);

-- Índice para melhorar a performance das consultas
CREATE INDEX idx_user_localstorage_usuario ON appestudo.user_localstorage(usuario_id);