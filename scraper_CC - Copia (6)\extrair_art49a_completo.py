#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def extrair_art49a_completo():
    """Extrai o texto completo do Art. 49-A do arquivo HTML"""
    
    print("=== EXTRAÇÃO COMPLETA DO ART. 49-A ===")
    
    # Carregar o arquivo HTML
    with open('L10406_web_backup.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Buscar o Art. 49-A com contexto amplo
    padrao = r'<a name="art49a"[^>]*></a>(.*?)(?=<a name="art50"|<a name="c50"|$)'
    match = re.search(padrao, content, re.IGNORECASE | re.DOTALL)
    
    if match:
        texto_html = match.group(1)
        print("TEXTO HTML BRUTO:")
        print("=" * 50)
        print(texto_html[:1000])
        print("=" * 50)
        
        # Limpar o HTML
        texto_limpo = re.sub(r'<[^>]+>', '', texto_html)
        texto_limpo = texto_limpo.replace('&nbsp;', ' ')
        texto_limpo = texto_limpo.replace('&amp;', '&')
        texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
        texto_limpo = texto_limpo.strip()
        
        print("\nTEXTO LIMPO:")
        print("=" * 50)
        print(texto_limpo)
        print("=" * 50)
        
        # Verificar se há parágrafo único
        if 'Parágrafo único' in texto_limpo:
            print("\n✅ PARÁGRAFO ÚNICO ENCONTRADO")
            
            # Separar caput e parágrafo único
            partes = texto_limpo.split('Parágrafo único')
            if len(partes) >= 2:
                caput = partes[0].strip()
                paragrafo_unico = 'Parágrafo único' + partes[1].strip()
                
                print(f"\nCAPUT: {caput}")
                print(f"\nPARÁGRAFO ÚNICO: {paragrafo_unico}")
        else:
            print("\n❌ PARÁGRAFO ÚNICO NÃO ENCONTRADO")
        
        # Verificar se há incisos
        if re.search(r'\b[IVX]+\s*-', texto_limpo):
            print("\n✅ INCISOS ENCONTRADOS")
            incisos = re.findall(r'([IVX]+\s*-[^IVX]*?)(?=[IVX]+\s*-|Parágrafo único|$)', texto_limpo)
            for i, inciso in enumerate(incisos, 1):
                print(f"  Inciso {i}: {inciso.strip()}")
        else:
            print("\n❌ INCISOS NÃO ENCONTRADOS")
            
    else:
        print("❌ Art. 49-A não encontrado!")
    
    # Comparar com o que foi extraído pelo nosso scraper
    print("\n" + "=" * 60)
    print("COMPARAÇÃO COM O SCRAPER")
    print("=" * 60)
    
    # Verificar o que nosso scraper extraiu
    try:
        import json
        with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Buscar Art. 49-A no JSON
        art_49a_json = None
        for artigo in data['artigos']:
            if artigo['numero'] == '49-A':
                art_49a_json = artigo
                break
        
        if art_49a_json:
            print("SCRAPER EXTRAIU:")
            print(f"Caput: {art_49a_json['caput']}")
            print(f"Parágrafo único: {art_49a_json.get('paragrafo_unico', 'None')}")
            print(f"Incisos: {len(art_49a_json.get('incisos', []))}")
            print(f"Parágrafos numerados: {len(art_49a_json.get('paragrafos_numerados', []))}")
        else:
            print("❌ Art. 49-A não encontrado no JSON do scraper")
            
    except Exception as e:
        print(f"Erro ao carregar JSON: {e}")

if __name__ == "__main__":
    extrair_art49a_completo()
