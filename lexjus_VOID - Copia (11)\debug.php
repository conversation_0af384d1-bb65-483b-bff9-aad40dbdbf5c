<?php
// Debug simples
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug LexJus</title>
</head>
<body>
    <h1>🔍 Debug LexJus</h1>
    
    <h2>Informações Básicas</h2>
    <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
    <p><strong>Data/Hora:</strong> <?php echo date('d/m/Y H:i:s'); ?></p>
    <p><strong>Diretório Atual:</strong> <?php echo __DIR__; ?></p>
    
    <h2>Verificação de Arquivos</h2>
    <?php
    $files = [
        'includes/cache_buster.php',
        'style.css',
        'script.js',
        'index.php',
        '.htaccess'
    ];
    
    foreach ($files as $file) {
        $exists = file_exists($file);
        $icon = $exists ? '✅' : '❌';
        echo "<p>$icon <strong>$file:</strong> " . ($exists ? 'Existe' : 'Não encontrado') . "</p>";
        
        if ($exists) {
            $size = filesize($file);
            $modified = date('d/m/Y H:i:s', filemtime($file));
            echo "<p style='margin-left: 20px; color: #666;'>Tamanho: " . number_format($size) . " bytes | Modificado: $modified</p>";
        }
    }
    ?>
    
    <h2>Teste do Sistema de Cache</h2>
    <?php
    if (file_exists('includes/cache_buster.php')) {
        try {
            require_once 'includes/cache_buster.php';
            echo "<p>✅ <strong>Sistema carregado com sucesso!</strong></p>";
            echo "<p>Versão Global: " . cache_version() . "</p>";
            echo "<p>Data: " . date('d/m/Y H:i:s', cache_version()) . "</p>";
        } catch (Exception $e) {
            echo "<p>❌ <strong>Erro ao carregar sistema:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p>❌ <strong>Arquivo cache_buster.php não encontrado</strong></p>";
    }
    ?>
    
    <h2>Informações do Servidor</h2>
    <p><strong>Servidor:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Desconhecido'; ?></p>
    <p><strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Desconhecido'; ?></p>
    <p><strong>Script Name:</strong> <?php echo $_SERVER['SCRIPT_NAME'] ?? 'Desconhecido'; ?></p>
    <p><strong>Request URI:</strong> <?php echo $_SERVER['REQUEST_URI'] ?? 'Desconhecido'; ?></p>
    
    <h2>Links de Teste</h2>
    <p><a href="test-cache.php">🧪 Teste Simples</a></p>
    <p><a href="clear-cache.php">🧹 Página de Cache</a></p>
    <p><a href="index.php">🏠 Sistema Principal</a></p>
    <p><a href="api/version.php">📡 API de Versão</a></p>
    
</body>
</html>
