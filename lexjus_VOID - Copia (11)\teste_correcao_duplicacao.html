<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Correção de Duplicação de Artigos</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-button.danger:hover {
            background: #c82333;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .scenario {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .scenario h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background: #f8f9fa;
        }
        .log-entry.success {
            border-left-color: #28a745;
        }
        .log-entry.error {
            border-left-color: #dc3545;
        }
        .log-entry.warning {
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Teste - Correção de Duplicação de Artigos</h1>
        <p>Esta página testa se a correção para evitar duplicação de artigos na revisão está funcionando.</p>

        <!-- Problema Identificado -->
        <div class="test-section">
            <h2>🚨 Problema Identificado</h2>
            <div class="scenario">
                <h4>Cenário Problemático:</h4>
                <ol>
                    <li>Artigo já estava marcado como lido antes da implementação</li>
                    <li>Usuário desmarca o artigo (para reorganizar)</li>
                    <li>Sistema detecta como "mudança" e adiciona à revisão</li>
                    <li>Resultado: Artigo duplicado na revisão</li>
                </ol>
            </div>
            
            <div class="scenario">
                <h4>Log do Problema:</h4>
                <div class="log-entry error">
                    <strong>Detectada mudança de status:</strong> {artigo_numero: 'Art. 17.', lido: true}
                </div>
                <div class="log-entry error">
                    <strong>Processando mudança:</strong> Artigo Art. 17. - Lido: true
                </div>
                <div class="log-entry error">
                    <strong>Resultado:</strong> Artigo 17 garantido na revisão: Revisão iniciada com sucesso
                </div>
            </div>
        </div>

        <!-- Solução Implementada -->
        <div class="test-section">
            <h2>✅ Solução Implementada</h2>
            <div class="scenario">
                <h4>Nova Lógica:</h4>
                <ol>
                    <li><strong>Verificação prévia:</strong> Antes de adicionar, verifica se já existe</li>
                    <li><strong>Consulta local:</strong> Verifica na lista carregada primeiro</li>
                    <li><strong>Consulta servidor:</strong> Se não encontrar, consulta o banco</li>
                    <li><strong>Decisão inteligente:</strong> Só adiciona se realmente não existir</li>
                </ol>
            </div>
            
            <div class="scenario">
                <h4>Código da Correção:</h4>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">
if (lido) {
    // Verificar se já está na revisão
    const jaEstaRevisao = await this.verificarArtigoNaRevisao(artigoNumero);
    
    if (!jaEstaRevisao) {
        await this.garantirArtigoNaRevisao(artigoNumero);
        this.mostrarNotificacao('📚 Artigo adicionado à revisão!', 'sucesso');
    } else {
        console.log('Artigo já está na revisão, ignorando');
    }
}</pre>
            </div>
        </div>

        <!-- Teste Simulado -->
        <div class="test-section">
            <h2>🧪 Teste Simulado</h2>
            <p>Simule o cenário problemático para verificar se a correção funciona:</p>
            
            <div>
                <label>Artigo para teste:</label>
                <input type="text" id="artigoTeste" value="17" placeholder="Ex: 17, 5º, 1">
                <button class="test-button" onclick="simularCenarioProblematico()">🔄 Simular Cenário</button>
            </div>
            
            <div id="resultadoSimulacao" class="result-box">
                <p>Clique no botão para simular o cenário...</p>
            </div>
        </div>

        <!-- Teste Real -->
        <div class="test-section">
            <h2>🔗 Teste Real</h2>
            <p>Teste com o sistema real carregado:</p>
            
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="test-button" onclick="verificarSistemaCarregado()">🔍 Verificar Sistema</button>
                <button class="test-button" onclick="testarVerificacaoArtigo()">🧪 Testar Verificação</button>
                <button class="test-button" onclick="simularMudancaStatus()">📝 Simular Mudança</button>
                <button class="test-button danger" onclick="limparRevisoes()">🗑️ Limpar Revisões</button>
            </div>
            
            <div id="resultadoTeste" class="result-box">
                <p>Use os botões acima para testar...</p>
            </div>
        </div>

        <!-- Monitoramento -->
        <div class="test-section">
            <h2>📊 Monitoramento de Logs</h2>
            <p>Logs em tempo real do sistema:</p>
            
            <div style="display: flex; gap: 10px;">
                <button class="test-button" onclick="iniciarMonitoramento()">▶️ Iniciar Monitoramento</button>
                <button class="test-button" onclick="pararMonitoramento()">⏹️ Parar</button>
                <button class="test-button" onclick="limparLogs()">🧹 Limpar Logs</button>
            </div>
            
            <div id="logsMonitoramento" class="result-box">
                <p>Logs aparecerão aqui quando o monitoramento estiver ativo...</p>
            </div>
        </div>

        <!-- Instruções -->
        <div class="test-section">
            <h2>📋 Como Testar Manualmente</h2>
            <ol>
                <li><strong>Abra o LexJus:</strong> Vá para a página principal</li>
                <li><strong>Marque um artigo como lido:</strong> Clique no checkbox de qualquer artigo</li>
                <li><strong>Verifique o console:</strong> Abra F12 e veja os logs</li>
                <li><strong>Demarque o mesmo artigo:</strong> Clique novamente no checkbox</li>
                <li><strong>Observe o comportamento:</strong> 
                    <ul>
                        <li>✅ <strong>Correto:</strong> "Artigo já está na revisão, ignorando"</li>
                        <li>❌ <strong>Incorreto:</strong> "Artigo adicionado à revisão automática!"</li>
                    </ul>
                </li>
            </ol>
            
            <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <h4 style="margin: 0 0 10px 0; color: #155724;">✅ Resultado Esperado:</h4>
                <ul style="margin: 0;">
                    <li>Artigos não devem ser duplicados na revisão</li>
                    <li>Sistema deve verificar antes de adicionar</li>
                    <li>Logs devem mostrar "ignorando" para artigos existentes</li>
                    <li>Apenas mudanças reais devem gerar notificações</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let monitoramentoAtivo = false;
        let intervalMonitoramento = null;

        // Simular a nova lógica de verificação
        function simularVerificacaoArtigo(artigoNumero) {
            // Simular lista de artigos na revisão
            const artigosNaRevisao = ['5', '17', '37', '142'];
            
            const numeroLimpo = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();
            const jaExiste = artigosNaRevisao.includes(numeroLimpo);
            
            return {
                existe: jaExiste,
                numeroLimpo: numeroLimpo,
                artigosNaRevisao: artigosNaRevisao
            };
        }

        function simularCenarioProblematico() {
            const artigo = document.getElementById('artigoTeste').value;
            const resultado = document.getElementById('resultadoSimulacao');
            
            resultado.innerHTML = '<h4>🔄 Simulando cenário...</h4>';
            
            setTimeout(() => {
                const verificacao = simularVerificacaoArtigo(artigo);
                
                let html = `
                    <h4>📊 Resultado da Simulação:</h4>
                    <div class="log-entry">
                        <strong>Artigo testado:</strong> ${artigo}
                    </div>
                    <div class="log-entry">
                        <strong>Número limpo:</strong> ${verificacao.numeroLimpo}
                    </div>
                    <div class="log-entry">
                        <strong>Artigos na revisão:</strong> [${verificacao.artigosNaRevisao.join(', ')}]
                    </div>
                `;
                
                if (verificacao.existe) {
                    html += `
                        <div class="log-entry success">
                            <strong>✅ Resultado:</strong> Artigo já existe na revisão - IGNORADO
                        </div>
                        <div class="log-entry success">
                            <strong>Log esperado:</strong> "Artigo ${artigo} já está na revisão, ignorando"
                        </div>
                        <div class="log-entry success">
                            <strong>Ação:</strong> Nenhuma duplicação ocorreu ✅
                        </div>
                    `;
                } else {
                    html += `
                        <div class="log-entry warning">
                            <strong>⚠️ Resultado:</strong> Artigo não existe na revisão - SERÁ ADICIONADO
                        </div>
                        <div class="log-entry warning">
                            <strong>Log esperado:</strong> "📚 Artigo ${artigo} adicionado à revisão automática!"
                        </div>
                        <div class="log-entry warning">
                            <strong>Ação:</strong> Adição legítima ✅
                        </div>
                    `;
                }
                
                resultado.innerHTML = html;
            }, 1000);
        }

        function verificarSistemaCarregado() {
            const resultado = document.getElementById('resultadoTeste');
            
            const verificacoes = {
                'Sistema de Revisão': !!window.sistemaRevisao,
                'Função verificarArtigoNaRevisao': !!(window.sistemaRevisao && window.sistemaRevisao.verificarArtigoNaRevisao),
                'Função processarMudancaStatusLeitura': !!(window.sistemaRevisao && window.sistemaRevisao.processarMudancaStatusLeitura),
                'Console disponível': !!window.console
            };
            
            let html = '<h4>🔍 Verificação do Sistema:</h4>';
            
            Object.entries(verificacoes).forEach(([nome, status]) => {
                const classe = status ? 'success' : 'error';
                const icon = status ? '✅' : '❌';
                html += `<div class="log-entry ${classe}">${icon} <strong>${nome}:</strong> ${status ? 'OK' : 'Não encontrado'}</div>`;
            });
            
            if (Object.values(verificacoes).every(v => v)) {
                html += '<div class="log-entry success"><strong>🎉 Sistema pronto para teste!</strong></div>';
            } else {
                html += '<div class="log-entry error"><strong>❌ Abra a página principal do LexJus primeiro.</strong></div>';
            }
            
            resultado.innerHTML = html;
        }

        function testarVerificacaoArtigo() {
            const resultado = document.getElementById('resultadoTeste');
            
            if (!window.sistemaRevisao) {
                resultado.innerHTML = '<div class="log-entry error">❌ Sistema de revisão não carregado</div>';
                return;
            }
            
            const artigo = document.getElementById('artigoTeste').value;
            
            resultado.innerHTML = '<div class="log-entry">🔄 Testando verificação...</div>';
            
            // Testar a função real
            window.sistemaRevisao.verificarArtigoNaRevisao(artigo)
                .then(existe => {
                    const classe = existe ? 'warning' : 'success';
                    const icon = existe ? '⚠️' : '✅';
                    resultado.innerHTML = `
                        <div class="log-entry ${classe}">
                            ${icon} <strong>Artigo ${artigo}:</strong> ${existe ? 'JÁ EXISTE na revisão' : 'NÃO EXISTE na revisão'}
                        </div>
                        <div class="log-entry info">
                            <strong>Comportamento esperado:</strong> ${existe ? 'Será ignorado se marcado novamente' : 'Será adicionado se marcado como lido'}
                        </div>
                    `;
                })
                .catch(error => {
                    resultado.innerHTML = `<div class="log-entry error">❌ Erro: ${error.message}</div>`;
                });
        }

        function simularMudancaStatus() {
            const resultado = document.getElementById('resultadoTeste');
            
            if (!window.sistemaRevisao) {
                resultado.innerHTML = '<div class="log-entry error">❌ Sistema de revisão não carregado</div>';
                return;
            }
            
            const artigo = document.getElementById('artigoTeste').value;
            
            resultado.innerHTML = '<div class="log-entry">🔄 Simulando mudança de status...</div>';
            
            // Simular mudança de status
            window.sistemaRevisao.processarMudancaStatusLeitura(artigo, true)
                .then(() => {
                    resultado.innerHTML += '<div class="log-entry success">✅ Mudança processada com sucesso</div>';
                })
                .catch(error => {
                    resultado.innerHTML += `<div class="log-entry error">❌ Erro: ${error.message}</div>`;
                });
        }

        function limparRevisoes() {
            if (!confirm('⚠️ Tem certeza que deseja limpar TODAS as revisões?')) {
                return;
            }
            
            const resultado = document.getElementById('resultadoTeste');
            
            if (!window.sistemaRevisao) {
                resultado.innerHTML = '<div class="log-entry error">❌ Sistema de revisão não carregado</div>';
                return;
            }
            
            resultado.innerHTML = '<div class="log-entry warning">🗑️ Limpando revisões...</div>';
            
            window.sistemaRevisao.limparTodasRevisoes()
                .then(() => {
                    resultado.innerHTML += '<div class="log-entry success">✅ Todas as revisões foram removidas</div>';
                })
                .catch(error => {
                    resultado.innerHTML += `<div class="log-entry error">❌ Erro: ${error.message}</div>`;
                });
        }

        function iniciarMonitoramento() {
            if (monitoramentoAtivo) return;
            
            monitoramentoAtivo = true;
            const logsDiv = document.getElementById('logsMonitoramento');
            
            logsDiv.innerHTML = '<div class="log-entry success">▶️ Monitoramento iniciado...</div>';
            
            // Interceptar console.log
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                
                if (monitoramentoAtivo) {
                    const timestamp = new Date().toLocaleTimeString();
                    const message = args.join(' ');
                    
                    if (message.includes('Processando mudança') || 
                        message.includes('já está na revisão') || 
                        message.includes('garantido na revisão')) {
                        
                        const logEntry = document.createElement('div');
                        logEntry.className = 'log-entry';
                        logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
                        
                        logsDiv.appendChild(logEntry);
                        logsDiv.scrollTop = logsDiv.scrollHeight;
                    }
                }
            };
        }

        function pararMonitoramento() {
            monitoramentoAtivo = false;
            document.getElementById('logsMonitoramento').innerHTML += 
                '<div class="log-entry warning">⏹️ Monitoramento parado</div>';
        }

        function limparLogs() {
            document.getElementById('logsMonitoramento').innerHTML = 
                '<p>Logs limpos. Inicie o monitoramento para ver novos logs...</p>';
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Página de teste de duplicação carregada');
        });
    </script>
</body>
</html>
