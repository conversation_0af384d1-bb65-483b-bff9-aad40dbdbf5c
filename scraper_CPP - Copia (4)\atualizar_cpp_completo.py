#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SISTEMA DE ATUALIZAÇÃO COMPLETA DO CPP
======================================

Este script executa todo o processo de atualização do Código de Processo Penal:
1. Extrai os artigos do site do Planalto
2. Corrige caputs cortados automaticamente
3. Gera arquivos finais prontos para uso no LexJus

USO:
    python atualizar_cpp_completo.py

ARQUIVOS GERADOS:
    - cpp_atualizado_YYYYMMDD_HHMMSS.json
    - cpp_atualizado_YYYYMMDD_HHMMSS.js
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from datetime import datetime
import os

# Configurações
URL_CPP = 'https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689.htm'

def log_info(mensagem):
    """Exibe mensagem com timestamp"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {mensagem}")

def buscar_conteudo_pagina(url):
    """Busca o conteúdo HTML de uma página web"""
    try:
        log_info(f"Buscando conteúdo da URL: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        content = response.text
        if content:
            log_info(f"✅ Página carregada com sucesso! Tamanho: {len(content)} caracteres")
            return content
        else:
            log_info(f"❌ Falha ao decodificar o conteúdo da página")
            return None

    except requests.RequestException as e:
        log_info(f"❌ Erro ao buscar a página: {e}")
        return None

def limpar_texto(texto):
    """Limpa e normaliza o texto extraído"""
    if not texto:
        return ""

    # Remove quebras de linha e espaços extras
    texto = re.sub(r'\s+', ' ', texto)
    texto = texto.strip()

    # Remove apenas caracteres de controle, preservando acentos portugueses
    texto = re.sub(r'[\u00a0\u2000-\u200f\u2028-\u202f\ufeff]', ' ', texto)

    # Remove espaços múltiplos novamente após limpeza
    texto = re.sub(r'\s+', ' ', texto)

    return texto

def extrair_artigos_cpp(html_content):
    """Extrai todos os artigos do CPP do HTML"""
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    paragrafo_atual = None
    artigos_processados = set()

    log_info("Analisando estrutura do CPP...")

    # Buscar todos os elementos <p>
    elementos_p = soup.find_all('p')
    log_info(f"Total de elementos <p> encontrados: {len(elementos_p)}")

    for i, p_tag in enumerate(elementos_p):
        texto_paragrafo_raw = p_tag.get_text(separator=' ', strip=False)
        texto_limpo = limpar_texto(texto_paragrafo_raw)

        if not texto_limpo or len(texto_limpo) < 3:
            continue

        # Pular títulos e cabeçalhos
        if re.search(r'^(LIVRO|TÍTULO|CAPÍTULO|SEÇÃO|DO\s|DA\s|DOS\s|DAS\s)', texto_limpo, re.IGNORECASE):
            continue

        # DEBUG: Mostrar progresso
        if i % 100 == 0:
            log_info(f"Processando parágrafo {i}/{len(elementos_p)}")

        # Detectar início de artigo
        if re.search(r'^Art\.?\s*\d+[ºo°]?[-\s]*[A-Z]?\.?\s', texto_limpo, re.IGNORECASE):
            # Salvar artigo anterior se existir
            if artigo_atual and artigo_atual["artigo"] not in artigos_processados:
                artigos_extraidos.append(artigo_atual)
                artigos_processados.add(artigo_atual["artigo"])

            # Extrair número e sufixo do artigo
            match = re.search(r'Art\.?\s*(\d+)[ºo°]?[-\s]*([A-Z])?\.?\s*(.+)', texto_limpo, re.IGNORECASE)
            if match:
                numero = int(match.group(1))
                sufixo = match.group(2).upper() if match.group(2) else ""
                caput_texto = match.group(3).strip()

                # Limpar caput
                caput_texto = re.sub(r'^[ºo°\.\s]*', '', caput_texto)

                # Criar identificador único do artigo
                artigo_id = f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")

                # Verificar se já processamos este artigo
                if artigo_id in artigos_processados:
                    continue

                # Criar novo artigo
                artigo_atual = {
                    "artigo": artigo_id,
                    "caput": caput_texto,
                    "incisos": [],
                    "paragrafos_numerados": [],
                    "paragrafo_unico": None
                }
                paragrafo_atual = None

                if len(artigos_extraidos) % 50 == 0:
                    log_info(f"📋 {artigo_id} extraído ({len(artigos_extraidos)} artigos processados)")

        elif artigo_atual:
            # Detectar parágrafo único
            if re.search(r'Parágrafo\s+único', texto_limpo, re.IGNORECASE):
                match = re.search(r'Parágrafo\s+único\.?\s*[-–]?\s*(.+)', texto_limpo, re.IGNORECASE)
                if match and not artigo_atual["paragrafo_unico"]:
                    artigo_atual["paragrafo_unico"] = match.group(1).strip()
                paragrafo_atual = None

            # Detectar parágrafos numerados
            elif re.search(r'§\s*\d+[ºo°]?', texto_limpo):
                match = re.search(r'(§\s*\d+[ºo°]?)\s*[-–]?\s*(.+)', texto_limpo)
                if match:
                    numero_paragrafo = match.group(1).strip()
                    texto_paragrafo = match.group(2).strip()

                    # Verificar se já existe este parágrafo
                    paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo_atual["paragrafos_numerados"])
                    if not paragrafo_existe:
                        paragrafo_atual = {
                            "numero": numero_paragrafo,
                            "texto": texto_paragrafo,
                            "incisos": [],
                            "alineas": []
                        }
                        artigo_atual["paragrafos_numerados"].append(paragrafo_atual)

            # Detectar incisos (romanos)
            elif re.search(r'^[IVX]+\s*[-–]', texto_limpo):
                inciso_texto = texto_limpo.strip()
                if paragrafo_atual:
                    if inciso_texto not in paragrafo_atual["incisos"]:
                        paragrafo_atual["incisos"].append(inciso_texto)
                else:
                    if inciso_texto not in artigo_atual["incisos"]:
                        artigo_atual["incisos"].append(inciso_texto)

            # Detectar alíneas
            elif re.search(r'^[a-z]\)', texto_limpo):
                alinea_texto = texto_limpo.strip()
                if paragrafo_atual:
                    if alinea_texto not in paragrafo_atual["alineas"]:
                        paragrafo_atual["alineas"].append(alinea_texto)

            # Continuação do caput
            elif (not artigo_atual["incisos"] and
                  not artigo_atual["paragrafos_numerados"] and
                  not artigo_atual["paragrafo_unico"] and
                  len(artigo_atual["caput"]) < 500):
                artigo_atual["caput"] += " " + texto_limpo

    # Adicionar último artigo se existir
    if artigo_atual and artigo_atual["artigo"] not in artigos_processados:
        artigos_extraidos.append(artigo_atual)

    log_info(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def corrigir_caputs_conhecidos(artigos):
    """Corrige caputs cortados com base em padrões conhecidos"""
    log_info("Aplicando correções de caputs conhecidos...")

    # Dicionário com correções conhecidas
    correcoes_conhecidas = {
        "Art. 1º": "O processo penal reger-se-á, em todo o território nacional, por este Código, ressalvados:",
        "Art. 2º": "A lei processual penal aplicar-se-á desde logo, sem prejuízo da validade dos atos realizados sob a vigência da lei anterior.",
        "Art. 3º": "A lei processual penal admitirá interpretação extensiva e aplicação analógica, bem como o suplemento dos princípios gerais de direito.",
        "Art. 3º-A": "O juiz das garantias é responsável pelo controle da legalidade da investigação criminal e pela salvaguarda dos direitos individuais cuja franquia tenha sido reservada à autorização prévia do Poder Judiciário, competindo-lhe especialmente:",
        "Art. 3º-B": "O juiz das garantias é responsável pelo controle da legalidade da investigação criminal e pela salvaguarda dos direitos individuais cuja franquia tenha sido reservada à autorização prévia do Poder Judiciário, competindo-lhe especialmente:",
        "Art. 3º-C": "A competência do juiz das garantias cessará com o recebimento da denúncia ou queixa.",
        "Art. 3º-D": "O juiz que, na fase de investigação, praticar qualquer ato incluído nas competências do art. 3º-B ficará impedido de funcionar no processo.",
        "Art. 3º-E": "O juiz das garantias será designado conforme as normas de organização judiciária da União e dos Estados.",
        "Art. 3º-F": "O juiz das garantias deverá assegurar o cumprimento das normas relativas à publicidade, à motivação e à comunicação dos atos processuais.",
        "Art. 4º": "A polícia judiciária será exercida pelas autoridades policiais no território de suas respectivas circunscrições e terá por fim a apuração das infrações penais e da sua autoria.",
        "Art. 5º": "Nos crimes de ação pública o inquérito policial será iniciado:",
        "Art. 7º": "Para verificar a possibilidade de haver a infração sido praticada de determinado modo, a autoridade policial poderá proceder à reprodução simulada dos fatos, desde que esta não contrarie a moralidade ou a ordem pública.",
        "Art. 8º": "Havendo prisão em flagrante, será observado o disposto no Capítulo II do Título IX deste Livro.",
        "Art. 9º": "Todas as peças do inquérito policial serão, num só processado, reduzidas a escrito ou datilografadas e, neste caso, rubricadas pela autoridade.",
        "Art. 10º": "O inquérito deverá terminar no prazo de 10 dias, se o indiciado tiver sido preso em flagrante, ou estiver preso preventivamente, contado o prazo, nesta hipótese, a partir do dia em que se executar a ordem de prisão, ou no prazo de 30 dias, quando estiver solto, mediante fiança ou sem ela.",
        "Art. 11º": "Os instrumentos do crime, bem como os objetos que interessarem à prova, acompanharão os autos do inquérito.",
        "Art. 12º": "O inquérito policial acompanhará a denúncia ou queixa, sempre que servir de base a uma ou outra.",
        "Art. 14º": "O ofendido, ou seu representante legal, e o indiciado poderão requerer qualquer diligência, que será realizada, ou não, a juízo da autoridade.",
        "Art. 15º": "Se o indiciado for menor, ser-lhe-á nomeado curador pela autoridade policial.",
        "Art. 16º": "O Ministério Público não poderá requerer a devolução do inquérito à autoridade policial, senão para novas diligências, imprescindíveis ao oferecimento da denúncia.",
        "Art. 17º": "A autoridade policial não poderá mandar arquivar autos de inquérito.",
        "Art. 18º": "Depois de ordenado o arquivamento do inquérito pela autoridade judiciária, por falta de base para a denúncia, a autoridade policial poderá proceder a novas pesquisas, se de outras provas tiver notícia.",
        "Art. 19º": "Nos crimes em que não couber ação pública, os autos do inquérito serão remetidos ao juízo competente, onde aguardarão a iniciativa do ofendido ou de seu representante legal, ou serão entregues ao requerente, se o pedir, mediante traslado.",
        "Art. 20º": "A autoridade assegurará no inquérito o sigilo necessário à elucidação do fato ou exigido pelo interesse da sociedade.",
        "Art. 21º": "A incomunicabilidade do indiciado dependerá sempre de despacho nos autos e somente será permitida quando o interesse da sociedade ou a conveniência da investigação o exigir.",
        "Art. 22º": "No Distrito Federal e nas comarcas em que houver mais de uma circunscrição policial, a autoridade com exercício em uma delas poderá, nos inquéritos a seu cargo, ordenar diligências em circunscrição de outra, independentemente de precatórias ou requisições, e bem assim providenciar, até que compareça a autoridade competente, sobre qualquer fato que ocorra em sua presença, noutra circunscrição.",
        "Art. 23º": "Ao fazer a remessa dos autos do inquérito ao juiz competente, a autoridade policial oficiará ao Instituto de Identificação e Estatística, ou repartição congênere, mencionando o juízo a que foram distribuídos, para o efeito da estatística judiciária criminal.",
        "Art. 24º": "Nos crimes de ação pública, esta será promovida por denúncia do Ministério Público, mas dependerá, quando a lei o exigir, de requisição do Ministro da Justiça, ou de representação do ofendido ou de quem tiver qualidade para representá-lo.",
        "Art. 25º": "A representação será irretratável, depois de oferecida a denúncia.",
        "Art. 26º": "A ação penal, nas contravenções, será iniciada com o auto de prisão em flagrante ou por meio de portaria expedida pela autoridade judiciária ou policial.",
        "Art. 27º": "Qualquer pessoa do povo poderá provocar a iniciativa do Ministério Público, nos casos em que caiba a ação pública, fornecendo-lhe, por escrito, informações sobre o fato e a autoria e indicando o tempo, o lugar e os elementos de convicção.",
        "Art. 28º": "Se o órgão do Ministério Público, ao invés de apresentar a denúncia, requerer o arquivamento do inquérito policial ou de quaisquer peças de informação, o juiz, no caso de considerar improcedentes as razões invocadas, fará remessa do inquérito ou peças de informação ao procurador-geral, e este oferecerá a denúncia, designará outro órgão do Ministério Público para oferecê-la, ou insistirá no pedido de arquivamento, ao qual só então estará o juiz obrigado a atender.",
        "Art. 28º-A": "Não sendo caso de arquivamento e tendo o investigado confessado formal e circunstancialmente a autoria, o Ministério Público poderá propor acordo de não persecução penal, desde que necessário e suficiente para reprovação e prevenção do crime, mediante as seguintes condições ajustadas cumulativa e alternativamente:"
    }

    artigos_corrigidos = 0

    for artigo in artigos:
        artigo_id = artigo['artigo']
        caput_atual = artigo['caput']

        if artigo_id in correcoes_conhecidas:
            caput_correto = correcoes_conhecidas[artigo_id]
            if len(caput_correto) > len(caput_atual):
                artigo['caput'] = caput_correto
                artigos_corrigidos += 1

    log_info(f"✅ {artigos_corrigidos} caputs corrigidos")
    return artigos

def gerar_arquivos_finais(artigos):
    """Gera os arquivos JSON e JavaScript finais"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Arquivo JSON
    arquivo_json = f'cpp_atualizado_{timestamp}.json'
    try:
        with open(arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos, f, ensure_ascii=False, indent=2)
        log_info(f"✅ Arquivo JSON salvo: {arquivo_json}")
    except Exception as e:
        log_info(f"❌ Erro ao salvar JSON: {e}")
        return None, None

    # Arquivo JavaScript
    arquivo_js = f'cpp_atualizado_{timestamp}.js'
    js_content = f"""// Código de Processo Penal Brasileiro - Atualizado
// Gerado automaticamente em: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
// Total de artigos: {len(artigos)}
// URL fonte: {URL_CPP}

const codigoProcessoPenalArtigos = {json.dumps(artigos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""

    try:
        with open(arquivo_js, 'w', encoding='utf-8') as f:
            f.write(js_content)
        log_info(f"✅ Arquivo JavaScript salvo: {arquivo_js}")
    except Exception as e:
        log_info(f"❌ Erro ao salvar JavaScript: {e}")
        return arquivo_json, None

    return arquivo_json, arquivo_js

def main():
    """Função principal - executa todo o processo de atualização usando o método comprovado"""
    print("=" * 70)
    print("🔄 SISTEMA DE ATUALIZAÇÃO COMPLETA DO CPP - MÉTODO COMPROVADO")
    print("=" * 70)
    print("📋 Este script irá:")
    print("   1. Executar scraper_final_perfeito.py")
    print("   2. Aplicar correções de caputs")
    print("   3. Gerar arquivos finais prontos para uso")
    print("=" * 70)

    import subprocess
    import os

    try:
        # Passo 1: Executar scraper_final_perfeito.py
        log_info("🚀 Executando scraper_final_perfeito.py...")
        result1 = subprocess.run(['python', 'scraper_final_perfeito.py'],
                                capture_output=True, text=True, cwd='.')
        if result1.returncode != 0:
            log_info(f"❌ Erro no scraper: {result1.stderr}")
            return
        log_info("✅ Scraper executado com sucesso")

        # Passo 2: Executar corrigir_caputs_simples.py
        log_info("🔧 Aplicando correções de caputs...")
        result2 = subprocess.run(['python', 'corrigir_caputs_simples.py'],
                                capture_output=True, text=True, cwd='.')
        if result2.returncode != 0:
            log_info(f"❌ Erro nas correções: {result2.stderr}")
            return
        log_info("✅ Correções aplicadas com sucesso")

        # Passo 3: Executar atualizar_cpp_rapido.py
        log_info("📦 Gerando arquivos finais...")
        result3 = subprocess.run(['python', 'atualizar_cpp_rapido.py'],
                                capture_output=True, text=True, cwd='.')
        if result3.returncode != 0:
            log_info(f"❌ Erro na geração final: {result3.stderr}")
            return
        log_info("✅ Arquivos finais gerados com sucesso")

        # Buscar o arquivo mais recente gerado
        import glob
        arquivos_js = glob.glob('cpp_lexjus_*.js')
        arquivos_json = glob.glob('cpp_lexjus_*.json')

        if arquivos_js and arquivos_json:
            arquivo_js_mais_recente = max(arquivos_js, key=os.path.getctime)
            arquivo_json_mais_recente = max(arquivos_json, key=os.path.getctime)

            print("\n" + "=" * 70)
            print("🎉 ATUALIZAÇÃO CONCLUÍDA COM SUCESSO!")
            print("=" * 70)
            print(f"📄 Arquivo JSON: {arquivo_json_mais_recente}")
            print(f"📄 Arquivo JS: {arquivo_js_mais_recente}")
            print("=" * 70)
            print("💡 PRÓXIMOS PASSOS:")
            print("   1. Copie o arquivo .js para o sistema LexJus")
            print("   2. Atualize as referências no código")
            print("   3. Teste o funcionamento")
            print("=" * 70)
        else:
            log_info("❌ Arquivos finais não encontrados")

    except Exception as e:
        log_info(f"❌ Erro durante a execução: {e}")

if __name__ == '__main__':
    main()
