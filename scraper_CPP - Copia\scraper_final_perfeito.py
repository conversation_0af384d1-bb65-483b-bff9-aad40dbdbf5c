#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Scraper FINAL PERFEITO para o Código de Processo Penal
Resolve todos os problemas: caputs completos, todos os incisos, referências legais mantidas
"""

import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def limpar_texto(texto):
    """
    Limpa o texto mantendo TODAS as referências legais
    """
    if not texto:
        return ""

    # Remover quebras de linha e espaços múltiplos
    texto = re.sub(r'\s+', ' ', texto)

    # MANTER todas as referências legais - não remover nada!

    return texto.strip()

def extrair_numero_artigo(elemento_artigo):
    """
    Extrai o número do artigo do elemento
    """
    if not elemento_artigo:
        return None

    texto_artigo = elemento_artigo.get_text(strip=True)

    # Padrões para extrair número do artigo
    patterns = [
        r'Art\.\s*(\d+)(?:º|°)?(?:-([A-Z]))?',
        r'Artigo\s+(\d+)(?:º|°)?(?:-([A-Z]))?'
    ]

    for pattern in patterns:
        match = re.search(pattern, texto_artigo, re.IGNORECASE)
        if match:
            numero = int(match.group(1))
            sufixo = match.group(2) if match.group(2) else ""
            return f"Art. {numero}º" + (f"-{sufixo}" if sufixo else "")

    return None

def extrair_todos_paragrafos_artigo(elemento_artigo, soup):
    """
    Extrai TODOS os parágrafos relacionados ao artigo até encontrar outro artigo
    """
    paragrafos_relacionados = []

    # Começar do parágrafo que contém o artigo
    paragrafo_atual = elemento_artigo.find_parent('p')
    if not paragrafo_atual:
        return paragrafos_relacionados

    # Adicionar o parágrafo do artigo
    paragrafos_relacionados.append(paragrafo_atual)

    # Buscar próximos parágrafos até encontrar outro artigo
    while True:
        paragrafo_atual = paragrafo_atual.find_next_sibling('p')

        if not paragrafo_atual:
            break

        # Parar se encontrar outro artigo
        if (paragrafo_atual.find('a', class_='numero_artigo') or
            paragrafo_atual.find('span', id=re.compile(r'art-\d+'))):
            break

        # Parar se encontrar título/seção
        texto_paragrafo = paragrafo_atual.get_text(separator=' ', strip=True)
        if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO)', texto_paragrafo, re.IGNORECASE):
            break

        # Pular elementos muito pequenos ou vazios
        if len(texto_paragrafo.strip()) < 3:
            continue

        paragrafos_relacionados.append(paragrafo_atual)

    return paragrafos_relacionados

def extrair_caput_completo(paragrafos_relacionados):
    """
    Extrai o caput completo, mesmo quando dividido em múltiplos parágrafos
    """
    if not paragrafos_relacionados:
        return ""

    # Começar com o primeiro parágrafo (que contém o artigo)
    primeiro_paragrafo = paragrafos_relacionados[0]
    texto_primeiro = primeiro_paragrafo.get_text(separator=' ', strip=True)

    # Extrair a parte do caput do primeiro parágrafo
    caput_match = re.search(r'Art\.\s*\d+(?:º|°)?(?:-[A-Z])?\s*\.?\s*(.+)', texto_primeiro, re.IGNORECASE)
    if not caput_match:
        return ""

    caput_partes = [caput_match.group(1)]

    # Verificar próximos parágrafos para continuar o caput
    # Ser mais agressivo na captura de continuações
    for i, paragrafo in enumerate(paragrafos_relacionados[1:], 1):
        texto_paragrafo = paragrafo.get_text(separator=' ', strip=True)

        # Parar se encontrar inciso, parágrafo ou alínea
        if (re.search(r'^[IVX]+\s*[-–]', texto_paragrafo) or
            re.search(r'^§\s*\d+', texto_paragrafo) or
            re.search(r'^[a-z]\)', texto_paragrafo) or
            re.search(r'^Parágrafo\s+único', texto_paragrafo, re.IGNORECASE)):
            break

        # Parar se encontrar títulos/seções
        if re.search(r'^(TÍTULO|CAPÍTULO|SEÇÃO|LIVRO)', texto_paragrafo, re.IGNORECASE):
            break

        # Pular linhas muito pequenas ou vazias
        if len(texto_paragrafo.strip()) < 3:
            continue

        # Verificar se é continuação do caput
        # Ser mais liberal na primeira linha de continuação
        if i == 1:  # Primeira linha após o artigo
            # Capturar quase tudo que não seja inciso/parágrafo
            if (not re.search(r'(Incluído|Redação|Vigência|VETADO)', texto_paragrafo) and
                len(texto_paragrafo) < 500):  # limite generoso
                caput_partes.append(texto_paragrafo)
                continue

        # Para linhas subsequentes, ser mais conservador
        if (len(texto_paragrafo) < 200 and
            not re.search(r'(Incluído|Redação|Vigência|VETADO|Vide)', texto_paragrafo)):

            # Verificar se parece ser continuação natural do texto
            ultimo_texto = caput_partes[-1] if caput_partes else ""
            if (ultimo_texto.endswith((',', ':', 'território', 'logo,', 'interpretação', 'sem', 'à')) or
                not ultimo_texto.endswith(('.', ';', '!', '?')) or
                len(ultimo_texto) < 80):  # texto curto provavelmente continua
                caput_partes.append(texto_paragrafo)
            else:
                break
        else:
            break

    caput_completo = ' '.join(caput_partes)
    return limpar_texto(caput_completo)

def processar_artigo_final_perfeito(elemento_artigo, soup):
    """
    Processa um artigo específico de forma PERFEITA
    """
    artigo_id = extrair_numero_artigo(elemento_artigo)
    if not artigo_id:
        return None

    # Extrair todos os parágrafos relacionados ao artigo
    paragrafos_relacionados = extrair_todos_paragrafos_artigo(elemento_artigo, soup)
    if not paragrafos_relacionados:
        return None

    # Extrair caput completo
    caput = extrair_caput_completo(paragrafos_relacionados)

    # Inicializar estrutura do artigo
    artigo = {
        "artigo": artigo_id,
        "caput": caput,
        "incisos": [],
        "paragrafos_numerados": [],
        "paragrafo_unico": None
    }

    # Processar cada parágrafo relacionado
    for paragrafo in paragrafos_relacionados:
        processar_paragrafo_completo(paragrafo, artigo)

    return artigo

def processar_paragrafo_completo(paragrafo, artigo):
    """
    Processa um parágrafo completo, extraindo todos os elementos
    """
    texto_paragrafo = paragrafo.get_text(separator=' ', strip=True)

    # Verificar se é parágrafo único
    if re.search(r'Parágrafo\s+único', texto_paragrafo, re.IGNORECASE):
        paragrafo_match = re.search(r'Parágrafo\s+único\.?\s*(.+)', texto_paragrafo, re.IGNORECASE | re.DOTALL)
        if paragrafo_match and not artigo["paragrafo_unico"]:
            texto_paragrafo_unico = paragrafo_match.group(1).strip()
            artigo["paragrafo_unico"] = limpar_texto(texto_paragrafo_unico)

    # Verificar se é parágrafo numerado
    elif re.search(r'^§\s*\d+(?:º|°)?', texto_paragrafo):
        paragrafo_match = re.search(r'^(§\s*\d+(?:º|°)?)\s*(.+)', texto_paragrafo, re.DOTALL)
        if paragrafo_match:
            numero_paragrafo = paragrafo_match.group(1).strip()
            texto_paragrafo_num = limpar_texto(paragrafo_match.group(2))

            # Verificar se já existe este parágrafo
            paragrafo_existe = any(p["numero"] == numero_paragrafo for p in artigo["paragrafos_numerados"])
            if not paragrafo_existe and texto_paragrafo_num and texto_paragrafo_num not in [".", "(VETADO)."]:
                # Extrair alíneas do texto do parágrafo
                alineas = extrair_alineas_do_texto(texto_paragrafo_num)

                artigo["paragrafos_numerados"].append({
                    "numero": numero_paragrafo,
                    "texto": texto_paragrafo_num,
                    "incisos": [],
                    "alineas": alineas
                })

    # Verificar se é inciso (romano)
    elif re.search(r'^[IVX]+\s*[-–]', texto_paragrafo):
        inciso_texto = limpar_texto(texto_paragrafo)
        if inciso_texto and inciso_texto not in artigo["incisos"]:
            artigo["incisos"].append(inciso_texto)

    # Verificar se é alínea isolada
    elif re.search(r'^[a-z]\)', texto_paragrafo):
        alinea_texto = limpar_texto(texto_paragrafo)
        if alinea_texto:
            # Adicionar à última estrutura disponível (parágrafo)
            if artigo["paragrafos_numerados"]:
                ultimo_paragrafo = artigo["paragrafos_numerados"][-1]
                if alinea_texto not in ultimo_paragrafo["alineas"]:
                    ultimo_paragrafo["alineas"].append(alinea_texto)

def extrair_alineas_do_texto(texto):
    """
    Extrai alíneas que podem estar dentro do texto
    """
    alineas = []

    # Buscar padrões de alíneas no texto
    linhas = texto.split('\n')
    for linha in linhas:
        linha = linha.strip()
        if re.match(r'^[a-z]\)', linha):
            alinea = limpar_texto(linha)
            if alinea and alinea not in alineas:
                alineas.append(alinea)

    return alineas

def scraper_final_perfeito():
    """
    Função principal do scraper FINAL PERFEITO
    """
    print("=" * 60)
    print("🔍 SCRAPER CPP FINAL PERFEITO")
    print("=" * 60)

    arquivo_html = "CÓDIGO DE PROCESSO PENAL - DEL 3689_1941.html"

    try:
        print(f"📄 Lendo arquivo: {arquivo_html}")

        with open(arquivo_html, 'r', encoding='utf-8') as f:
            conteudo_html = f.read()

        print(f"✅ Arquivo lido com sucesso. Tamanho: {len(conteudo_html):,} caracteres")

        # Parse do HTML
        soup = BeautifulSoup(conteudo_html, 'html.parser')

        # Encontrar todos os artigos - buscar por class="numero_artigo" e id que começam com "art-"
        links_artigos = soup.find_all('a', class_='numero_artigo')
        spans_artigos = soup.find_all('span', id=re.compile(r'art-\d+'))

        print(f"🔍 Encontrados {len(links_artigos)} links de artigos")
        print(f"🔍 Encontrados {len(spans_artigos)} spans de artigos")

        artigos_extraidos = []
        artigos_processados = set()  # Para evitar duplicações

        # Processar links de artigos
        for i, link_artigo in enumerate(links_artigos):
            try:
                artigo = processar_artigo_final_perfeito(link_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])

                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")

            except Exception as e:
                print(f"⚠️ Erro ao processar link artigo {i+1}: {e}")
                continue

        # Processar spans de artigos (para casos como Art. 3°-C, 3°-D, 3°-E)
        for i, span_artigo in enumerate(spans_artigos):
            try:
                artigo = processar_artigo_final_perfeito(span_artigo, soup)
                if artigo and artigo["artigo"] not in artigos_processados:
                    artigos_extraidos.append(artigo)
                    artigos_processados.add(artigo["artigo"])

                    if len(artigos_extraidos) % 10 == 0:
                        print(f"📋 Processados {len(artigos_extraidos)} artigos únicos...")

            except Exception as e:
                print(f"⚠️ Erro ao processar span artigo {i+1}: {e}")
                continue

        # Ordenar por número do artigo
        def extrair_numero_para_ordenacao(artigo_id):
            match = re.search(r'Art\.\s*(\d+)', artigo_id)
            return int(match.group(1)) if match else 0

        artigos_extraidos.sort(key=lambda x: extrair_numero_para_ordenacao(x["artigo"]))

        print(f"✅ Extração concluída. Total: {len(artigos_extraidos)} artigos únicos")

        # Salvar arquivo
        nome_arquivo_json = 'cpp_final_perfeito.json'
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_extraidos, f, ensure_ascii=False, indent=2)
        print(f"💾 Arquivo JSON salvo: {nome_arquivo_json}")

        # Estatísticas detalhadas
        print(f"\n📊 ESTATÍSTICAS:")
        print(f"   • Total de artigos: {len(artigos_extraidos)}")
        if artigos_extraidos:
            print(f"   • Primeiro artigo: {artigos_extraidos[0]['artigo']}")
            print(f"   • Último artigo: {artigos_extraidos[-1]['artigo']}")

            # Contar estruturas
            total_incisos = sum(len(art['incisos']) for art in artigos_extraidos)
            total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos_extraidos)
            total_paragrafos_unicos = sum(1 for art in artigos_extraidos if art['paragrafo_unico'])

            print(f"   • Total de incisos: {total_incisos}")
            print(f"   • Total de parágrafos numerados: {total_paragrafos}")
            print(f"   • Total de parágrafos únicos: {total_paragrafos_unicos}")

            # Verificar primeiros artigos para debug
            print(f"\n🔍 VERIFICAÇÃO DOS PRIMEIROS ARTIGOS:")
            for i, art in enumerate(artigos_extraidos[:10]):
                print(f"   {art['artigo']}: {len(art['incisos'])} incisos, {len(art['paragrafos_numerados'])} parágrafos")
                if i < 3:  # Mostrar caput dos 3 primeiros
                    print(f"      Caput: {art['caput'][:100]}...")
                    if art['incisos']:
                        print(f"      Primeiro inciso: {art['incisos'][0][:80]}...")

        return artigos_extraidos

    except FileNotFoundError:
        print(f"❌ Arquivo não encontrado: {arquivo_html}")
        return []
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return []

if __name__ == '__main__':
    artigos = scraper_final_perfeito()
    if artigos:
        print(f"\n🎉 Scraping FINAL PERFEITO concluído com sucesso!")
        print(f"📁 Arquivo gerado: cpp_final_perfeito.json")
        print(f"🔧 VERSÃO PERFEITA: Caputs completos, todos os incisos, referências mantidas!")
    else:
        print(f"\n❌ Falha no scraping FINAL PERFEITO!")
