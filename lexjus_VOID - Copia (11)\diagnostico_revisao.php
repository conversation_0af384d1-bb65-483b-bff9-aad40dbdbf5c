<?php
session_start();
require_once __DIR__ . '/../conexao_POST.php';

// Verificar autenticação
if (!isset($_SESSION['idusuario'])) {
    die("❌ Usuário não autenticado. Faça login primeiro.");
}

$usuario_id = $_SESSION['idusuario'];

echo "<h1>🔍 Diagnóstico do Sistema de Revisão</h1>";
echo "<p><strong>Usuário ID:</strong> $usuario_id</p>";
echo "<hr>";

// 1. Verificar estrutura do banco
echo "<h2>📊 1. Verificação da Estrutura do Banco</h2>";

$tabelas_necessarias = [
    'lexjus_revisoes' => 'Tabela principal de revisões',
    'lexjus_historico_revisoes' => 'Histórico de revisões',
    'lexjus_config_revisao' => 'Configurações de revisão'
];

foreach ($tabelas_necessarias as $tabela => $descricao) {
    $query = "SELECT COUNT(*) as existe FROM information_schema.tables
              WHERE table_schema = 'appestudo' AND table_name = '$tabela'";
    $result = pg_query($conexao, $query);
    $row = pg_fetch_assoc($result);

    if ($row['existe'] > 0) {
        echo "✅ <strong>$tabela</strong> - $descricao<br>";
    } else {
        echo "❌ <strong>$tabela</strong> - $descricao (TABELA NÃO EXISTE)<br>";
    }
}

// 2. Verificar dados do usuário
echo "<h2>👤 2. Dados do Usuário no Sistema</h2>";

// Revisões do usuário
$query_revisoes = "SELECT COUNT(*) as total,
                          COUNT(CASE WHEN status = 'novo' THEN 1 END) as novos,
                          COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
                          COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
                          COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
                          COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis,
                          COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes
                   FROM appestudo.lexjus_revisoes
                   WHERE usuario_id = $1";

$result_revisoes = pg_query_params($conexao, $query_revisoes, [$usuario_id]);

if ($result_revisoes) {
    $stats = pg_fetch_assoc($result_revisoes);
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>📈 Estatísticas de Revisão:</h3>";
    echo "<ul>";
    echo "<li><strong>Total de artigos:</strong> {$stats['total']}</li>";
    echo "<li><strong>Novos:</strong> {$stats['novos']}</li>";
    echo "<li><strong>Aprendendo:</strong> {$stats['aprendendo']}</li>";
    echo "<li><strong>Revisando:</strong> {$stats['revisando']}</li>";
    echo "<li><strong>Dominados:</strong> {$stats['dominados']}</li>";
    echo "<li><strong>Difíceis:</strong> {$stats['dificeis']}</li>";
    echo "<li><strong>Pendentes hoje:</strong> {$stats['pendentes']}</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "❌ Erro ao consultar revisões: " . pg_last_error($conexao);
}

// 3. Listar artigos específicos
echo "<h2>📚 3. Artigos na Revisão</h2>";

$query_artigos = "SELECT artigo_numero, status, facilidade, repeticoes,
                         data_proxima_revisao, total_revisoes
                  FROM appestudo.lexjus_revisoes
                  WHERE usuario_id = $1
                  ORDER BY data_proxima_revisao ASC
                  LIMIT 10";

$result_artigos = pg_query_params($conexao, $query_artigos, [$usuario_id]);

if ($result_artigos && pg_num_rows($result_artigos) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th>Artigo</th><th>Status</th><th>Facilidade</th><th>Repetições</th><th>Próxima Revisão</th><th>Total Revisões</th>";
    echo "</tr>";

    while ($artigo = pg_fetch_assoc($result_artigos)) {
        $proxima = new DateTime($artigo['data_proxima_revisao']);
        $agora = new DateTime();
        $pendente = $proxima <= $agora ? '🔴 PENDENTE' : '⏰ Agendado';

        echo "<tr>";
        echo "<td>{$artigo['artigo_numero']}</td>";
        echo "<td>{$artigo['status']}</td>";
        echo "<td>{$artigo['facilidade']}</td>";
        echo "<td>{$artigo['repeticoes']}</td>";
        echo "<td>{$proxima->format('d/m/Y H:i')} ($pendente)</td>";
        echo "<td>{$artigo['total_revisoes']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>ℹ️ Nenhum artigo encontrado na revisão.</p>";
}

// 4. Verificar configuração
echo "<h2>⚙️ 4. Configuração do Usuário</h2>";

$query_config = "SELECT * FROM appestudo.lexjus_config_revisao WHERE usuario_id = $1";
$result_config = pg_query_params($conexao, $query_config, [$usuario_id]);

if ($result_config && pg_num_rows($result_config) > 0) {
    $config = pg_fetch_assoc($result_config);
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px;'>";
    echo "<h3>✅ Configuração encontrada:</h3>";
    echo "<ul>";
    echo "<li><strong>Max revisões/dia:</strong> {$config['max_revisoes_dia']}</li>";
    echo "<li><strong>Max novos/dia:</strong> {$config['max_novos_dia']}</li>";
    echo "<li><strong>Horário preferido:</strong> {$config['horario_preferido_inicio']} - {$config['horario_preferido_fim']}</li>";
    echo "<li><strong>Notificações:</strong> " . ($config['notificar_revisoes'] ? 'Ativadas' : 'Desativadas') . "</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
    echo "<h3>⚠️ Configuração não encontrada</h3>";
    echo "<p>Será criada automaticamente na primeira utilização.</p>";
    echo "</div>";
}

// 5. Testar funcionalidades das APIs diretamente no banco
echo "<h2>🔗 5. Teste das Funcionalidades das APIs</h2>";

// Testar estatísticas
echo "<h4>🧪 Testando funcionalidade: estatisticas</h4>";
try {
    $query_stats = "
        SELECT
            COUNT(*) as total_artigos,
            COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
            COUNT(CASE WHEN status = 'novo' THEN 1 END) as novos,
            COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
            COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
            COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
            COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis,
            COALESCE(AVG(facilidade), 0) as facilidade_media
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1";

    $result_stats = pg_query_params($conexao, $query_stats, [$usuario_id]);

    if ($result_stats) {
        $stats = pg_fetch_assoc($result_stats);
        echo "✅ <strong>estatisticas:</strong> Consulta funcionando<br>";
        echo "<details><summary>Ver dados</summary><pre>" . json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre></details>";
    } else {
        echo "❌ <strong>estatisticas:</strong> Erro na consulta: " . pg_last_error($conexao) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>estatisticas:</strong> Exceção: " . $e->getMessage() . "<br>";
}
echo "<br>";

// Testar pendentes
echo "<h4>🧪 Testando funcionalidade: pendentes</h4>";
try {
    $query_pendentes = "
        SELECT artigo_numero, status, facilidade, repeticoes,
               data_proxima_revisao, total_revisoes
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1
        AND data_proxima_revisao <= CURRENT_TIMESTAMP
        ORDER BY data_proxima_revisao ASC
        LIMIT 10";

    $result_pendentes = pg_query_params($conexao, $query_pendentes, [$usuario_id]);

    if ($result_pendentes) {
        $pendentes = [];
        while ($row = pg_fetch_assoc($result_pendentes)) {
            $pendentes[] = $row;
        }
        echo "✅ <strong>pendentes:</strong> Consulta funcionando (" . count($pendentes) . " artigos)<br>";
        echo "<details><summary>Ver dados</summary><pre>" . json_encode($pendentes, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre></details>";
    } else {
        echo "❌ <strong>pendentes:</strong> Erro na consulta: " . pg_last_error($conexao) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>pendentes:</strong> Exceção: " . $e->getMessage() . "<br>";
}
echo "<br>";

// Testar configuração
echo "<h4>🧪 Testando funcionalidade: configuracao</h4>";
try {
    $query_config = "SELECT * FROM appestudo.lexjus_config_revisao WHERE usuario_id = $1";
    $result_config = pg_query_params($conexao, $query_config, [$usuario_id]);

    if ($result_config) {
        if (pg_num_rows($result_config) > 0) {
            $config = pg_fetch_assoc($result_config);
            echo "✅ <strong>configuracao:</strong> Configuração encontrada<br>";
            echo "<details><summary>Ver dados</summary><pre>" . json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre></details>";
        } else {
            echo "⚠️ <strong>configuracao:</strong> Nenhuma configuração encontrada (será criada automaticamente)<br>";

            // Tentar criar configuração padrão
            $query_create = "INSERT INTO appestudo.lexjus_config_revisao (usuario_id) VALUES ($1) RETURNING *";
            $result_create = pg_query_params($conexao, $query_create, [$usuario_id]);

            if ($result_create) {
                $config = pg_fetch_assoc($result_create);
                echo "✅ <strong>configuracao:</strong> Configuração padrão criada<br>";
                echo "<details><summary>Ver dados</summary><pre>" . json_encode($config, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre></details>";
            } else {
                echo "❌ <strong>configuracao:</strong> Erro ao criar configuração: " . pg_last_error($conexao) . "<br>";
            }
        }
    } else {
        echo "❌ <strong>configuracao:</strong> Erro na consulta: " . pg_last_error($conexao) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>configuracao:</strong> Exceção: " . $e->getMessage() . "<br>";
}
echo "<br>";

// Teste adicional: verificar se a API existe
echo "<h4>🔍 Verificação do arquivo da API</h4>";
$api_file = __DIR__ . '/api/revisao.php';
if (file_exists($api_file)) {
    echo "✅ <strong>Arquivo API:</strong> api/revisao.php existe<br>";
    echo "<small>Tamanho: " . filesize($api_file) . " bytes | Modificado: " . date('d/m/Y H:i:s', filemtime($api_file)) . "</small><br>";
} else {
    echo "❌ <strong>Arquivo API:</strong> api/revisao.php não encontrado<br>";
}

// 6. Verificar JavaScript
echo "<h2>🔧 6. Verificação do Frontend</h2>";

$arquivos_js = [
    'js/sistema-revisao.js' => 'Sistema de revisão principal',
    'css/sistema-revisao.css' => 'Estilos do sistema'
];

foreach ($arquivos_js as $arquivo => $descricao) {
    if (file_exists($arquivo)) {
        echo "✅ <strong>$arquivo</strong> - $descricao<br>";
    } else {
        echo "❌ <strong>$arquivo</strong> - $descricao (ARQUIVO NÃO ENCONTRADO)<br>";
    }
}

echo "<hr>";
echo "<h2>🎯 Resumo do Diagnóstico</h2>";
echo "<p>Use as informações acima para identificar problemas específicos.</p>";
echo "<p><strong>Data/Hora do diagnóstico:</strong> " . date('d/m/Y H:i:s') . "</p>";

// Botões de ação
echo "<div style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;'>";
echo "<h3>🛠️ Ações Disponíveis:</h3>";
echo "<a href='./api/revisao.php?acao=estatisticas' target='_blank' style='margin-right: 10px;'>📊 Ver Estatísticas</a>";
echo "<a href='teste_sistema_revisao.html' target='_blank' style='margin-right: 10px;'>🧪 Teste Completo</a>";
echo "<a href='instalar_sistema_revisao.php' target='_blank' style='margin-right: 10px;'>⚙️ Reinstalar Sistema</a>";
echo "</div>";
?>
