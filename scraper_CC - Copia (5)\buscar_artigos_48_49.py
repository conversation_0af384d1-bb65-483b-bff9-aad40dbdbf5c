#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def buscar_artigos_48_49():
    """Busca os artigos 48-A e 49-A no arquivo HTML"""
    
    print("=== BUSCA DOS ARTIGOS 48-A E 49-A NO HTML ===")
    
    # Carregar o arquivo HTML
    with open('L10406_web_backup.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Tamanho do arquivo: {len(content):,} caracteres")
    
    # Buscar todas as ocorrências de artigos 48 e 49 com sufixos
    print("\n1. BUSCANDO ARTIGOS 48 COM SUFIXOS:")
    padrao_48 = r'<a name="(art48[a-z](?:\.\d+)?)"[^>]*></a>([^<]*(?:<[^>]*>[^<]*)*?)(?=<a name="|</p>|$)'
    matches_48 = re.findall(padrao_48, content, re.IGNORECASE | re.DOTALL)
    
    for i, (name, texto) in enumerate(matches_48, 1):
        # Limpar o texto
        texto_limpo = re.sub(r'<[^>]+>', '', texto)
        texto_limpo = texto_limpo.replace('&nbsp;', ' ')
        texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
        texto_limpo = texto_limpo.strip()
        
        print(f"  {i}. name=\"{name}\"")
        print(f"     Texto: {texto_limpo[:100]}...")
        print()
    
    print(f"Total de artigos 48 com sufixos encontrados: {len(matches_48)}")
    
    # Buscar todas as ocorrências de artigos 49 com sufixos
    print("\n2. BUSCANDO ARTIGOS 49 COM SUFIXOS:")
    padrao_49 = r'<a name="(art49[a-z](?:\.\d+)?)"[^>]*></a>([^<]*(?:<[^>]*>[^<]*)*?)(?=<a name="|</p>|$)'
    matches_49 = re.findall(padrao_49, content, re.IGNORECASE | re.DOTALL)
    
    for i, (name, texto) in enumerate(matches_49, 1):
        # Limpar o texto
        texto_limpo = re.sub(r'<[^>]+>', '', texto)
        texto_limpo = texto_limpo.replace('&nbsp;', ' ')
        texto_limpo = re.sub(r'\s+', ' ', texto_limpo)
        texto_limpo = texto_limpo.strip()
        
        print(f"  {i}. name=\"{name}\"")
        print(f"     Texto: {texto_limpo[:100]}...")
        print()
    
    print(f"Total de artigos 49 com sufixos encontrados: {len(matches_49)}")
    
    # Buscar contexto mais amplo dos artigos 49
    print("\n3. CONTEXTO AMPLO DO ARTIGO 49:")
    padrao_contexto_49 = r'<a name="art49"[^>]*></a>.*?(?=<a name="art50"|$)'
    match_contexto = re.search(padrao_contexto_49, content, re.IGNORECASE | re.DOTALL)
    
    if match_contexto:
        contexto = match_contexto.group(0)
        # Extrair todos os <a name> dentro deste contexto
        nomes_dentro = re.findall(r'<a name="([^"]*)"[^>]*></a>', contexto)
        print("Names encontrados no contexto do Art. 49:")
        for nome in nomes_dentro:
            print(f"  - {nome}")
        
        # Mostrar o texto limpo do contexto
        contexto_limpo = re.sub(r'<[^>]+>', '', contexto)
        contexto_limpo = contexto_limpo.replace('&nbsp;', ' ')
        contexto_limpo = re.sub(r'\s+', ' ', contexto_limpo)
        contexto_limpo = contexto_limpo.strip()
        
        print(f"\nTexto do contexto (primeiros 500 caracteres):")
        print(contexto_limpo[:500])
        print("...")
    
    # Verificar se existe art49a especificamente
    print("\n4. VERIFICAÇÃO ESPECÍFICA DO ART. 49-A:")
    if 'art49a' in content.lower():
        print("✅ Encontrado 'art49a' no arquivo")
        
        # Buscar todas as ocorrências
        ocorrencias = re.finditer(r'art49a[^"]*', content, re.IGNORECASE)
        for i, match in enumerate(ocorrencias, 1):
            inicio = max(0, match.start() - 50)
            fim = min(len(content), match.end() + 100)
            contexto_pequeno = content[inicio:fim]
            print(f"  Ocorrência {i}: ...{contexto_pequeno}...")
    else:
        print("❌ NÃO encontrado 'art49a' no arquivo")
    
    return len(matches_48), len(matches_49)

if __name__ == "__main__":
    total_48, total_49 = buscar_artigos_48_49()
    print(f"\n=== RESUMO ===")
    print(f"Artigos 48 com sufixos: {total_48}")
    print(f"Artigos 49 com sufixos: {total_49}")
