<?php
session_start();
require_once __DIR__ . '/../conexao_POST.php';

echo "<h1>🚀 Instalação do Sistema de Revisão - LexJus VOID</h1>";

// Verificar se o usuário tem permissão (você pode ajustar essa verificação)
if (!isset($_SESSION['idusuario'])) {
    echo "<p style='color: red;'>❌ Você precisa estar logado para instalar o sistema.</p>";
    echo "<p><a href='../index.php'>← Voltar para o sistema</a></p>";
    exit;
}

$usuario_id = $_SESSION['idusuario'];
echo "<p>👤 Usuário logado: ID $usuario_id</p>";

// Função para executar SQL e mostrar resultado
function executarSQL($conexao, $sql, $descricao) {
    echo "<h3>$descricao</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 200px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($sql, 0, 500)) . (strlen($sql) > 500 ? "..." : "");
    echo "</pre>";
    
    $result = pg_query($conexao, $sql);
    
    if ($result) {
        echo "<p style='color: green;'>✅ $descricao executado com sucesso!</p>";
        return true;
    } else {
        echo "<p style='color: red;'>❌ Erro ao executar $descricao:</p>";
        echo "<p style='color: red; font-family: monospace;'>" . pg_last_error($conexao) . "</p>";
        return false;
    }
}

echo "<h2>📊 Criando Estrutura do Banco de Dados</h2>";

// 1. Criar tabela de revisões
$sql_revisoes = "
CREATE TABLE IF NOT EXISTS appestudo.lexjus_revisoes (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    artigo_numero VARCHAR(20) NOT NULL,
    
    -- Controle de dificuldade e performance
    facilidade DECIMAL(3,2) DEFAULT 2.50,
    intervalo_dias INTEGER DEFAULT 1,
    repeticoes INTEGER DEFAULT 0,
    
    -- Datas importantes
    data_primeira_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_ultima_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_proxima_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Status e qualidade da última revisão
    status VARCHAR(20) DEFAULT 'novo',
    ultima_qualidade INTEGER DEFAULT 0,
    
    -- Estatísticas
    total_revisoes INTEGER DEFAULT 0,
    acertos_consecutivos INTEGER DEFAULT 0,
    tempo_medio_resposta INTEGER DEFAULT 0,
    
    -- Metadados
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_usuario_artigo_revisao 
        UNIQUE (usuario_id, artigo_numero),
        
    CONSTRAINT check_facilidade 
        CHECK (facilidade >= 1.30 AND facilidade <= 3.00),
        
    CONSTRAINT check_qualidade 
        CHECK (ultima_qualidade >= 0 AND ultima_qualidade <= 5),
        
    CONSTRAINT check_status 
        CHECK (status IN ('novo', 'aprendendo', 'revisando', 'dominado', 'dificil'))
);";

executarSQL($conexao, $sql_revisoes, "🗂️ Tabela lexjus_revisoes");

// 2. Criar tabela de histórico
$sql_historico = "
CREATE TABLE IF NOT EXISTS appestudo.lexjus_historico_revisoes (
    id SERIAL PRIMARY KEY,
    revisao_id INTEGER NOT NULL,
    usuario_id INTEGER NOT NULL,
    artigo_numero VARCHAR(20) NOT NULL,
    
    -- Dados da sessão de revisão
    qualidade_resposta INTEGER NOT NULL,
    tempo_resposta INTEGER,
    tipo_revisao VARCHAR(20) NOT NULL,
    
    -- Estado antes da revisão
    facilidade_anterior DECIMAL(3,2),
    intervalo_anterior INTEGER,
    
    -- Estado após a revisão
    facilidade_nova DECIMAL(3,2),
    intervalo_novo INTEGER,
    
    -- Contexto da revisão
    dispositivo VARCHAR(50),
    hora_do_dia INTEGER,
    dia_da_semana INTEGER,
    
    -- Metadados
    data_revisao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT check_qualidade_historico 
        CHECK (qualidade_resposta >= 0 AND qualidade_resposta <= 5),
        
    CONSTRAINT check_tipo_revisao 
        CHECK (tipo_revisao IN ('inicial', 'revisao', 'reforco', 'teste'))
);";

executarSQL($conexao, $sql_historico, "📚 Tabela lexjus_historico_revisoes");

// 3. Criar tabela de configuração
$sql_config = "
CREATE TABLE IF NOT EXISTS appestudo.lexjus_config_revisao (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    
    -- Configurações do algoritmo
    facilidade_inicial DECIMAL(3,2) DEFAULT 2.50,
    intervalo_inicial INTEGER DEFAULT 1,
    multiplicador_facil DECIMAL(3,2) DEFAULT 1.30,
    multiplicador_dificil DECIMAL(3,2) DEFAULT 0.80,
    
    -- Configurações de estudo
    max_revisoes_dia INTEGER DEFAULT 20,
    max_novos_dia INTEGER DEFAULT 10,
    horario_preferido_inicio TIME DEFAULT '08:00:00',
    horario_preferido_fim TIME DEFAULT '22:00:00',
    
    -- Configurações de notificação
    notificar_revisoes BOOLEAN DEFAULT TRUE,
    notificar_antecedencia INTEGER DEFAULT 60,
    
    -- Configurações de dificuldade
    auto_promover_faceis BOOLEAN DEFAULT TRUE,
    auto_rebaixar_dificeis BOOLEAN DEFAULT TRUE,
    limite_acertos_promocao INTEGER DEFAULT 3,
    limite_erros_rebaixamento INTEGER DEFAULT 2,
    
    -- Metadados
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_config_usuario 
        UNIQUE (usuario_id)
);";

executarSQL($conexao, $sql_config, "⚙️ Tabela lexjus_config_revisao");

// 4. Criar índices
echo "<h2>🔍 Criando Índices para Performance</h2>";

$indices = [
    "CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_usuario_id ON appestudo.lexjus_revisoes(usuario_id);",
    "CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_proxima_revisao ON appestudo.lexjus_revisoes(data_proxima_revisao);",
    "CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_status ON appestudo.lexjus_revisoes(status);",
    "CREATE INDEX IF NOT EXISTS idx_lexjus_revisoes_usuario_status ON appestudo.lexjus_revisoes(usuario_id, status);",
    "CREATE INDEX IF NOT EXISTS idx_lexjus_historico_usuario_data ON appestudo.lexjus_historico_revisoes(usuario_id, data_revisao);"
];

foreach ($indices as $i => $sql_indice) {
    executarSQL($conexao, $sql_indice, "📇 Índice " . ($i + 1));
}

// 5. Criar função do algoritmo
echo "<h2>🧮 Criando Função do Algoritmo SM-2</h2>";

$sql_funcao = "
CREATE OR REPLACE FUNCTION calcular_proxima_revisao(
    p_facilidade DECIMAL(3,2),
    p_intervalo INTEGER,
    p_qualidade INTEGER,
    p_repeticoes INTEGER
) RETURNS TABLE (
    nova_facilidade DECIMAL(3,2),
    novo_intervalo INTEGER,
    novas_repeticoes INTEGER,
    nova_data_revisao TIMESTAMP
) AS \$\$
DECLARE
    v_facilidade DECIMAL(3,2);
    v_intervalo INTEGER;
    v_repeticoes INTEGER;
BEGIN
    -- Calcular nova facilidade baseada na qualidade da resposta
    v_facilidade := p_facilidade + (0.1 - (5 - p_qualidade) * (0.08 + (5 - p_qualidade) * 0.02));
    
    -- Garantir que facilidade está dentro dos limites
    IF v_facilidade < 1.30 THEN
        v_facilidade := 1.30;
    END IF;
    
    -- Calcular novo intervalo e repetições
    IF p_qualidade < 3 THEN
        -- Resposta ruim - reiniciar
        v_repeticoes := 0;
        v_intervalo := 1;
    ELSE
        -- Resposta boa - avançar
        v_repeticoes := p_repeticoes + 1;
        
        IF v_repeticoes = 1 THEN
            v_intervalo := 1;
        ELSIF v_repeticoes = 2 THEN
            v_intervalo := 6;
        ELSE
            v_intervalo := ROUND(p_intervalo * v_facilidade);
        END IF;
    END IF;
    
    -- Retornar resultados
    RETURN QUERY SELECT 
        v_facilidade,
        v_intervalo,
        v_repeticoes,
        (CURRENT_TIMESTAMP + INTERVAL '1 day' * v_intervalo)::TIMESTAMP;
END;
\$\$ LANGUAGE plpgsql;";

executarSQL($conexao, $sql_funcao, "🧮 Função calcular_proxima_revisao");

// 6. Criar trigger para timestamps
echo "<h2>⏰ Criando Triggers para Timestamps</h2>";

$sql_trigger_func = "
CREATE OR REPLACE FUNCTION update_revisao_timestamp()
RETURNS TRIGGER AS \$\$
BEGIN
    NEW.data_atualizacao = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
\$\$ LANGUAGE plpgsql;";

executarSQL($conexao, $sql_trigger_func, "⚙️ Função de trigger");

$sql_triggers = [
    "DROP TRIGGER IF EXISTS trigger_update_revisao_timestamp ON appestudo.lexjus_revisoes;",
    "CREATE TRIGGER trigger_update_revisao_timestamp
        BEFORE UPDATE ON appestudo.lexjus_revisoes
        FOR EACH ROW
        EXECUTE FUNCTION update_revisao_timestamp();",
    "DROP TRIGGER IF EXISTS trigger_update_config_revisao_timestamp ON appestudo.lexjus_config_revisao;",
    "CREATE TRIGGER trigger_update_config_revisao_timestamp
        BEFORE UPDATE ON appestudo.lexjus_config_revisao
        FOR EACH ROW
        EXECUTE FUNCTION update_revisao_timestamp();"
];

foreach ($sql_triggers as $i => $sql_trigger) {
    executarSQL($conexao, $sql_trigger, "⏰ Trigger " . ($i + 1));
}

// 7. Inserir configuração padrão para o usuário atual
echo "<h2>👤 Configurando Usuário Atual</h2>";

$sql_config_usuario = "
INSERT INTO appestudo.lexjus_config_revisao (usuario_id)
SELECT $usuario_id
WHERE NOT EXISTS (
    SELECT 1 FROM appestudo.lexjus_config_revisao 
    WHERE usuario_id = $usuario_id
);";

executarSQL($conexao, $sql_config_usuario, "⚙️ Configuração padrão para usuário $usuario_id");

// 8. Teste final
echo "<h2>🧪 Teste Final do Sistema</h2>";

$sql_teste = "
SELECT 
    (SELECT COUNT(*) FROM appestudo.lexjus_revisoes WHERE usuario_id = $usuario_id) as revisoes,
    (SELECT COUNT(*) FROM appestudo.lexjus_config_revisao WHERE usuario_id = $usuario_id) as config,
    (SELECT EXISTS(SELECT 1 FROM information_schema.routines WHERE routine_name = 'calcular_proxima_revisao')) as funcao_existe;";

$result_teste = pg_query($conexao, $sql_teste);
if ($result_teste) {
    $teste = pg_fetch_assoc($result_teste);
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h3>✅ Sistema Instalado com Sucesso!</h3>";
    echo "<ul>";
    echo "<li>📊 Revisões do usuário: " . $teste['revisoes'] . "</li>";
    echo "<li>⚙️ Configuração criada: " . ($teste['config'] > 0 ? 'Sim' : 'Não') . "</li>";
    echo "<li>🧮 Função do algoritmo: " . ($teste['funcao_existe'] === 't' ? 'Criada' : 'Erro') . "</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<p style='color: red;'>❌ Erro no teste final: " . pg_last_error($conexao) . "</p>";
}

echo "<h2>🎯 Próximos Passos</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
echo "<ol>";
echo "<li>✅ <strong>Sistema instalado!</strong> Todas as tabelas e funções foram criadas.</li>";
echo "<li>🧪 <a href='teste_conexao_revisao.php' target='_blank'>Testar conexão e estrutura</a></li>";
echo "<li>🔧 <a href='teste_sistema_revisao.html' target='_blank'>Testar funcionalidades completas</a></li>";
echo "<li>🎓 <a href='index.php'>Voltar ao sistema principal</a> e usar o botão 'Revisão'</li>";
echo "</ol>";
echo "</div>";

echo "<h2>📚 Documentação</h2>";
echo "<p>📖 Leia o arquivo <code>SISTEMA_REVISAO_IMPLEMENTADO.md</code> para documentação completa.</p>";

echo "<br><hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "🧠 <strong>Sistema de Revisão Inteligente</strong> instalado com sucesso!<br>";
echo "Desenvolvido para o LexJus VOID - " . date('Y-m-d H:i:s');
echo "</p>";
?>
