# Scraper do Código de Processo Penal (CPP) - Planalto

Este diretório contém todos os arquivos necessários para extrair o Código de Processo Penal Brasileiro do site do Planalto e gerar arquivos JavaScript e JSON limpos.

## 📁 Arquivos Incluídos

### Scripts Principais:
- **`scraper_planalto_cpp.py`** - Script principal que extrai os artigos do Código de Processo Penal
- **`limpar_referencias_finais_cpp.py`** - Script para limpeza final das referências legislativas

### Arquivos de Exemplo (Gerados):
- **`codigo_processo_penal_limpo.js`** - Arquivo JavaScript pronto para uso
- **`codigo_processo_penal_limpo.json`** - Arquivo JSON para backup

## 🚀 Como Usar

### 1. Executar o Scraper Principal
```bash
python scraper_planalto_cpp.py
```

**O que faz:**
- Busca o Código de Processo Penal da URL: `https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689.htm`
- Compara estrutura local vs online
- Extrai todos os artigos do CPP completo
- Gera arquivos `codigo_processo_penal.js` e `codigo_processo_penal.json`

### 2. Limpeza Final (Opcional)
```bash
python limpar_referencias_finais_cpp.py
```

**O que faz:**
- Remove referências legislativas como "(Redação dada pela Lei nº X)"
- Remove "(Vide ADPF X)", "(VETADO)", etc.
- Gera arquivos limpos: `codigo_processo_penal_limpo.js` e `codigo_processo_penal_limpo.json`

## 📊 Resultados Esperados

### Estatísticas:
- **Código de Processo Penal completo** (Livro I ao Livro III)
- **Texto limpo** sem referências legislativas
- **Estrutura organizada** por livros, títulos e capítulos

### Exemplo de Estrutura:
```json
{
  "numero": 1,
  "titulo": "Art. 1º",
  "conteudo": "O processo penal reger-se-á, em todo o território nacional, por este Código...",
  "livro": "Livro I",
  "titulo_livro": "Do Processo em Geral",
  "capitulo": "Título I - Disposições Preliminares"
}
```

## 🔧 Dependências

### Python Packages:
```bash
pip install requests beautifulsoup4
```

### Imports Necessários:
- `requests` - Para buscar páginas web
- `beautifulsoup4` - Para parsing HTML
- `json` - Para manipulação JSON
- `re` - Para expressões regulares
- `datetime` - Para timestamps

## 📝 Funcionalidades do JavaScript Gerado

### Buscar Artigo por Número:
```javascript
const art1 = buscarArtigoCPP(1); // Retorna artigo sobre processo penal
```

### Buscar Artigos por Texto:
```javascript
const artigos = buscarArtigosCPPPorTexto("inquérito"); // Busca em todo o conteúdo
```

### Usar no HTML:
```html
<script src="codigo_processo_penal_limpo.js"></script>
<script>
  // Agora você pode usar as funções
  console.log(buscarArtigoCPP(1));
</script>
```

## 🎯 URL Fonte

**URL Principal:** `https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689.htm`

O scraper foi configurado para extrair da versão oficial do Código de Processo Penal Brasileiro, garantindo que todos os artigos e atualizações estejam incluídos.

## ⚠️ Observações

1. **Conexão Internet:** O scraper precisa de conexão para buscar a página online
2. **Estrutura HTML:** Se o Planalto alterar a estrutura da página, pode ser necessário ajustar as regex
3. **Encoding:** Todos os arquivos são salvos em UTF-8 para suportar caracteres especiais
4. **Limpeza:** As referências legislativas são removidas para melhor legibilidade no sistema de estudos

## 📞 Suporte

Para dúvidas ou problemas, verifique:
1. Se as dependências estão instaladas
2. Se há conexão com a internet
3. Se a estrutura da página do Planalto não foi alterada

## 🔄 Integração com LexJus

Os arquivos gerados são compatíveis com o sistema LexJus e podem ser integrados diretamente ao banco de dados do sistema de estudos jurídicos.
