#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
VERIFICAÇÃO FINAL DO SISTEMA SCRAPER CC
=======================================

Este script faz uma verificação completa do arquivo final gerado
pelo sistema scraper do Código Civil, incluindo:

1. Verificação do Art. 1403º (problema específico corrigido)
2. Verificação geral de problemas de caput
3. Estatísticas do arquivo
4. Validação da estrutura JSON
"""

import json
import re
import os

def verificar_arquivo():
    """Verifica o arquivo final do Código Civil"""

    print("🔍 VERIFICAÇÃO FINAL DO CÓDIGO CIVIL LEXJUS")
    print("=" * 60)

    arquivo = "scraper_CC/codigo_civil_formato_lexjus_final.json"

    if not os.path.exists(arquivo):
        print(f"❌ Arquivo {arquivo} não encontrado!")
        return False

    try:
        # Carregar arquivo JSON
        with open(arquivo, 'r', encoding='utf-8') as f:
            artigos = json.load(f)

        print(f"✅ Arquivo JSON carregado com sucesso")
        print(f"📊 Total de artigos: {len(artigos)}")

        # Verificação específica do Art. 1403º
        print(f"\n🎯 VERIFICAÇÃO ESPECÍFICA DO ART. 1403º:")
        art_1403_ok = verificar_art_1403(artigos)

        # Verificação geral de problemas
        print(f"\n🔍 VERIFICAÇÃO GERAL DE PROBLEMAS:")
        problemas_gerais = verificar_problemas_gerais(artigos)

        # Estatísticas do arquivo
        print(f"\n📈 ESTATÍSTICAS DO ARQUIVO:")
        gerar_estatisticas(artigos)

        # Resumo final
        print(f"\n" + "=" * 60)
        if art_1403_ok and problemas_gerais == 0:
            print("🎉 VERIFICAÇÃO FINAL: SUCESSO COMPLETO!")
            print("✅ Arquivo pronto para uso no sistema LexJus")
        else:
            print("⚠️  VERIFICAÇÃO FINAL: PROBLEMAS ENCONTRADOS")
            if not art_1403_ok:
                print("❌ Art. 1403º ainda tem problemas")
            if problemas_gerais > 0:
                print(f"❌ {problemas_gerais} problemas gerais encontrados")

        return art_1403_ok and problemas_gerais == 0

    except Exception as e:
        print(f"❌ Erro ao processar arquivo: {e}")
        return False

def verificar_art_1403(artigos):
    """Verifica especificamente o Art. 1403º"""

    for i, artigo in enumerate(artigos):
        if '1403' in artigo.get('artigo', ''):
            print(f"  📍 Encontrado no índice {i}: {artigo['artigo']}")

            caput = artigo.get('caput', '')
            incisos = artigo.get('incisos', [])

            print(f"  📝 Caput: {caput}")
            print(f"  📋 Incisos: {len(incisos)}")

            # Verificar se caput começa com número
            if re.match(r'^[0-9]+\s+', caput):
                print("  ❌ PROBLEMA: Caput começa com número!")
                return False
            else:
                print("  ✅ Caput correto (não começa com número)")

            # Verificar se tem incisos
            if len(incisos) == 0:
                print("  ⚠️  AVISO: Artigo sem incisos")
            else:
                print("  ✅ Artigo tem incisos:")
                for j, inciso in enumerate(incisos):
                    print(f"    {j+1}. {inciso[:60]}...")

            # Verificar conteúdo esperado
            if 'Incumbem ao usufrutuário' in caput:
                print("  ✅ Conteúdo do caput está correto")
                return True
            else:
                print("  ❌ PROBLEMA: Conteúdo do caput incorreto")
                return False

    print("  ❌ Art. 1403º não encontrado!")
    return False

def verificar_problemas_gerais(artigos):
    """Verifica problemas gerais no arquivo"""

    problemas = 0

    # Verificar caputs que começam com número
    caputs_com_numero = []
    for artigo in artigos:
        caput = artigo.get('caput', '')
        if re.match(r'^[0-9]+\s+', caput):
            caputs_com_numero.append({
                'artigo': artigo.get('artigo', 'N/A'),
                'caput': caput[:50] + '...'
            })

    if caputs_com_numero:
        print(f"  ❌ {len(caputs_com_numero)} artigos com caput começando com número:")
        for problema in caputs_com_numero[:3]:  # Mostrar apenas os primeiros 3
            print(f"    - {problema['artigo']}: {problema['caput']}")
        if len(caputs_com_numero) > 3:
            print(f"    ... e mais {len(caputs_com_numero) - 3} artigos")
        problemas += len(caputs_com_numero)
    else:
        print("  ✅ Nenhum caput começando com número encontrado")

    # Verificar artigos sem caput
    sem_caput = [art for art in artigos if not art.get('caput', '').strip()]
    if sem_caput:
        print(f"  ❌ {len(sem_caput)} artigos sem caput")
        problemas += len(sem_caput)
    else:
        print("  ✅ Todos os artigos têm caput")

    return problemas

def gerar_estatisticas(artigos):
    """Gera estatísticas do arquivo"""

    total = len(artigos)
    com_incisos = len([art for art in artigos if art.get('incisos') and len(art['incisos']) > 0])
    com_paragrafos = len([art for art in artigos if art.get('paragrafos_numerados') and len(art['paragrafos_numerados']) > 0])
    com_paragrafo_unico = len([art for art in artigos if art.get('paragrafo_unico')])

    print(f"  📊 Total de artigos: {total}")
    print(f"  📋 Com incisos: {com_incisos} ({com_incisos/total*100:.1f}%)")
    print(f"  📄 Com parágrafos numerados: {com_paragrafos} ({com_paragrafos/total*100:.1f}%)")
    print(f"  📝 Com parágrafo único: {com_paragrafo_unico} ({com_paragrafo_unico/total*100:.1f}%)")

    # Verificar alguns artigos específicos importantes
    artigos_importantes = ['1º', '4º', '1403º', '2046º']
    print(f"\n  🎯 VERIFICAÇÃO DE ARTIGOS IMPORTANTES:")

    for num in artigos_importantes:
        encontrado = False
        for artigo in artigos:
            if num in artigo.get('artigo', ''):
                print(f"    ✅ Art. {num}: {artigo['caput'][:50]}...")
                encontrado = True
                break
        if not encontrado:
            print(f"    ❌ Art. {num}: Não encontrado")

if __name__ == "__main__":
    verificar_arquivo()
