#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON
with open('codigo_civil_formato_lexjus_final.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("=== ANÁLISE DE PROBLEMAS NO ARQUIVO ===")

# 1. Procurar artigos 48
print("\n1. ARTIGOS 48:")
artigos_48 = []
for i, art in enumerate(data):
    if '48' in art['artigo']:
        artigos_48.append((i, art))
        print(f"  Posição {i}: {art['artigo']} - {art['caput'][:80]}...")

# 2. Procurar artigos inexistentes como 46-V
print("\n2. ARTIGOS SUSPEITOS (com sufixos estranhos):")
suspeitos = []
for i, art in enumerate(data):
    artigo = art['artigo']
    # Procurar padrões suspeitos como X-V, X-I sem contexto
    if '-V' in artigo or '-I' in artigo:
        if art['caput'] == '' or len(art['caput']) < 10:
            suspeitos.append((i, art))
            print(f"  Posição {i}: {artigo} - Caput: '{art['caput']}'")

# 3. Procurar duplicatas
print("\n3. POSSÍVEIS DUPLICATAS:")
artigos_vistos = {}
for i, art in enumerate(data):
    artigo_base = art['artigo'].replace('º', '').replace('-Pº', '').replace('-Iº', '')
    if artigo_base in artigos_vistos:
        print(f"  DUPLICATA: {art['artigo']} (posição {i}) já existe como {artigos_vistos[artigo_base][1]} (posição {artigos_vistos[artigo_base][0]})")
    else:
        artigos_vistos[artigo_base] = (i, art['artigo'])

# 4. Verificar estrutura dos artigos 48 especificamente
print("\n4. DETALHES DOS ARTIGOS 48:")
for i, art in artigos_48:
    print(f"\nPosição {i}: {art['artigo']}")
    print(f"  Caput: {art['caput'][:100]}...")
    print(f"  Parágrafo único: {art['paragrafo_unico']}")
    print(f"  Incisos: {len(art['incisos'])}")
    print(f"  Parágrafos numerados: {len(art['paragrafos_numerados'])}")
