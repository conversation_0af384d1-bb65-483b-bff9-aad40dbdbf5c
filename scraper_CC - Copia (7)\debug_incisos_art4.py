#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# Carregar HTML
with open('L10406_web_backup.html', 'r', encoding='utf-8') as f:
    conteudo = f.read()

# Buscar todos os incisos do artigo 4
padrao_incisos = r'name="(art4[^"]*)"'
incisos_art4 = re.findall(padrao_incisos, conteudo, re.IGNORECASE)

print("=== INCISOS DO ARTIGO 4 ENCONTRADOS ===")
for inciso in incisos_art4:
    print(f"- {inciso}")

print("\n=== VERIFICANDO CONTEÚDO DOS INCISOS ===")

# Verificar conteúdo de cada inciso
for name in incisos_art4:
    padrao_busca = rf'<a name="{re.escape(name)}"[^>]*>'
    match_pos = re.search(padrao_busca, conteudo, re.IGNORECASE)
    
    if match_pos:
        # Verificar se está dentro de <strike>
        inicio_contexto = max(0, match_pos.start() - 200)
        contexto_anterior = conteudo[inicio_contexto:match_pos.start()]
        
        strike_aberta = contexto_anterior.rfind('<strike>')
        strike_fechada = contexto_anterior.rfind('</strike>')
        
        dentro_strike = strike_aberta > strike_fechada
        
        # Extrair texto
        inicio = match_pos.end()
        texto_restante = conteudo[inicio:inicio+500]
        
        print(f"\n{name}:")
        print(f"  Dentro de <strike>: {dentro_strike}")
        print(f"  Texto: {texto_restante[:200]}...")
