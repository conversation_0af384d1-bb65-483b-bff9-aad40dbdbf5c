#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def verificar_art49a_convertido():
    """Verifica como o Art. 49-A foi convertido"""
    
    print("=== VERIFICAÇÃO DO ART. 49-A CONVERTIDO ===")
    
    # Carregar o JSON convertido
    with open('codigo_civil_formato_lexjus.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Total de artigos no arquivo convertido: {len(data)}")
    
    # Buscar artigos 49
    artigos_49 = []
    for artigo in data:
        if '49' in artigo['artigo']:
            artigos_49.append(artigo)
    
    print(f"\nArtigos encontrados com '49': {len(artigos_49)}")
    
    for artigo in artigos_49:
        print(f"\n{artigo['artigo']}:")
        print(f"  Caput: {artigo['caput'][:100]}...")
        print(f"  Parágrafo único: {artigo.get('paragrafo_unico', 'None')}")
        print(f"  Incisos: {len(artigo.get('incisos', []))}")
        print(f"  Parágrafos numerados: {len(artigo.get('paragrafos_numerados', []))}")
    
    # Verificar especificamente Art. 49-A
    art_49a = None
    for artigo in data:
        if artigo['artigo'] in ['Art. 49-Aº', 'Art. 49-A']:
            art_49a = artigo
            break
    
    if art_49a:
        print(f"\n=== ART. 49-A ENCONTRADO ===")
        print(json.dumps(art_49a, indent=2, ensure_ascii=False))
    else:
        print(f"\n❌ ART. 49-A NÃO ENCONTRADO")
        
        # Verificar se foi ignorado
        print("\nVerificando arquivo original...")
        with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
            data_original = json.load(f)
        
        for artigo in data_original['artigos']:
            if artigo['numero'] == '49-A':
                print(f"Art. 49-A encontrado no arquivo original:")
                print(f"  Versão atual: {artigo.get('versao_atual', 'N/A')}")
                print(f"  Sufixo letra: '{artigo.get('sufixo_letra', 'N/A')}'")
                print(f"  Caput: {artigo['caput'][:100]}...")
                break

if __name__ == "__main__":
    verificar_art49a_convertido()
