--
-- PostgreSQL database dump
--

-- Dumped from database version 15.6
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: appestudo; Type: SCHEMA; Schema: -; Owner: appestudo
--

CREATE SCHEMA appestudo;


ALTER SCHEMA appestudo OWNER TO appestudo;

--
-- Name: atualizar_acesso_modulos(); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.atualizar_acesso_modulos() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Atualiza o status do módulo no usuário
    UPDATE appestudo.usuario
    SET cronograma_inteligente = 
        EXISTS (
            SELECT 1 
            FROM appestudo.assinaturas 
            WHERE usuario_id = NEW.usuario_id 
            AND modulo = 'cronograma_inteligente'
            AND status = true 
            AND data_inicio <= CURRENT_TIMESTAMP
            AND data_fim > CURRENT_TIMESTAMP
        )
    WHERE idusuario = NEW.usuario_id;

    -- Desativa assinaturas expiradas
    UPDATE appestudo.assinaturas
    SET status = false
    WHERE data_fim < CURRENT_TIMESTAMP
    AND status = true;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION appestudo.atualizar_acesso_modulos() OWNER TO appestudo;

--
-- Name: atualizar_data_conclusao(); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.atualizar_data_conclusao() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF NEW.status = 'concluido' AND OLD.status != 'concluido' THEN
        NEW.data_conclusao = CURRENT_TIMESTAMP;
    ELSIF NEW.status != 'concluido' THEN
        NEW.data_conclusao = NULL;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION appestudo.atualizar_data_conclusao() OWNER TO appestudo;

--
-- Name: calculate_next_review(integer, integer, numeric); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.calculate_next_review(p_avaliacao integer, p_intervalo_atual integer, p_fator_facilidade numeric) RETURNS TABLE(novo_intervalo integer, novo_fator numeric)
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_novo_intervalo INTEGER;
    v_novo_fator DECIMAL;
BEGIN
    -- Implementação do algoritmo SM-2
    IF p_intervalo_atual = 0 THEN
        v_novo_intervalo := 1;
    ELSIF p_intervalo_atual = 1 THEN
        v_novo_intervalo := 6;
    ELSE
        v_novo_intervalo := ROUND(p_intervalo_atual * p_fator_facilidade);
    END IF;

    -- Ajuste do fator de facilidade baseado na avaliação
    v_novo_fator := p_fator_facilidade + (0.1 - (5 - p_avaliacao) * (0.08 + (5 - p_avaliacao) * 0.02));
    
    -- Limites do fator de facilidade
    IF v_novo_fator < 1.3 THEN
        v_novo_fator := 1.3;
    END IF;

    -- Se a avaliação for "Difícil", reduz o intervalo
    IF p_avaliacao = 1 THEN
        v_novo_intervalo := 1;
    END IF;

    RETURN QUERY SELECT v_novo_intervalo, v_novo_fator;
END;
$$;


ALTER FUNCTION appestudo.calculate_next_review(p_avaliacao integer, p_intervalo_atual integer, p_fator_facilidade numeric) OWNER TO appestudo;

--
-- Name: gerar_proxima_revisao(); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.gerar_proxima_revisao() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Só gera próxima revisão se a atual foi concluída (não ignorada) e confiança < 5
    IF NEW.status_revisao = 'concluida' AND NEW.confianca < 5 THEN
        INSERT INTO appestudo.revisoes (
            conteudo_id,
            usuario_id,
            nivel_revisao,
            proxima_revisao,
            status_revisao,
            edital_id
        ) VALUES (
            NEW.conteudo_id,
            NEW.usuario_id,
            NEW.nivel_revisao + 1,
            CASE NEW.nivel_revisao
                WHEN 1 THEN CURRENT_TIMESTAMP + INTERVAL '3 days'
                WHEN 2 THEN CURRENT_TIMESTAMP + INTERVAL '7 days'
                WHEN 3 THEN CURRENT_TIMESTAMP + INTERVAL '14 days'
                WHEN 4 THEN CURRENT_TIMESTAMP + INTERVAL '30 days'
                ELSE CURRENT_TIMESTAMP + INTERVAL '60 days'
            END,
            'pendente',
            NEW.edital_id
        );
    -- Se foi ignorada temporariamente, cria próxima revisão com intervalo maior
    ELSIF NEW.status_revisao = 'ignorado' THEN
        INSERT INTO appestudo.revisoes (
            conteudo_id,
            usuario_id,
            nivel_revisao,
            proxima_revisao,
            status_revisao,
            edital_id
        ) VALUES (
            NEW.conteudo_id,
            NEW.usuario_id,
            NEW.nivel_revisao,
            CASE NEW.nivel_revisao
                WHEN 1 THEN CURRENT_TIMESTAMP + INTERVAL '7 days'
                WHEN 2 THEN CURRENT_TIMESTAMP + INTERVAL '14 days'
                WHEN 3 THEN CURRENT_TIMESTAMP + INTERVAL '30 days'
                WHEN 4 THEN CURRENT_TIMESTAMP + INTERVAL '60 days'
                ELSE CURRENT_TIMESTAMP + INTERVAL '90 days'
            END,
            'pendente',
            NEW.edital_id
        );
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION appestudo.gerar_proxima_revisao() OWNER TO appestudo;

--
-- Name: gerar_revisao_automatica(); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.gerar_revisao_automatica() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Quando marca como estudado
    IF NEW.status_estudo = 'Estudado' AND (OLD.status_estudo IS NULL OR OLD.status_estudo != 'Estudado') THEN
        -- Remove quaisquer revisões ignoradas permanentemente para este conteúdo e edital específico
        UPDATE appestudo.revisoes r
        SET status_revisao = 'pendente'
        FROM appestudo.conteudo_edital ce
        JOIN appestudo.usuario_edital ue ON ce.edital_id = ue.edital_id
        WHERE r.conteudo_id = NEW.conteudo_id 
        AND r.usuario_id = NEW.usuario_id
        AND ce.id_conteudo = NEW.conteudo_id
        AND ue.usuario_id = NEW.usuario_id
        AND r.status_revisao = 'ignorado_permanente';
        
        -- Cria nova revisão considerando o edital atual do usuário
        INSERT INTO appestudo.revisoes (
            conteudo_id,
            usuario_id,
            nivel_revisao,
            proxima_revisao,
            edital_id
        )
        SELECT 
            NEW.conteudo_id,
            NEW.usuario_id,
            1,
            CURRENT_TIMESTAMP + INTERVAL '1 day',
            ue.edital_id
        FROM appestudo.usuario_edital ue
        WHERE ue.usuario_id = NEW.usuario_id
        AND ue.edital_id = (
            SELECT ce.edital_id 
            FROM appestudo.conteudo_edital ce 
            WHERE ce.id_conteudo = NEW.conteudo_id
        );

    -- Quando desmarca como estudado
    ELSIF (NEW.status_estudo IS NULL OR NEW.status_estudo != 'Estudado') AND OLD.status_estudo = 'Estudado' THEN
        -- Remove todas as revisões pendentes para este conteúdo e edital específico
        DELETE FROM appestudo.revisoes r
        USING appestudo.conteudo_edital ce
        JOIN appestudo.usuario_edital ue ON ce.edital_id = ue.edital_id
        WHERE r.conteudo_id = NEW.conteudo_id 
        AND r.usuario_id = NEW.usuario_id
        AND ce.id_conteudo = NEW.conteudo_id
        AND ue.usuario_id = NEW.usuario_id
        AND r.status_revisao = 'pendente';
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION appestudo.gerar_revisao_automatica() OWNER TO appestudo;

--
-- Name: update_data_atualizacao_column(); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.update_data_atualizacao_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.data_atualizacao = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION appestudo.update_data_atualizacao_column() OWNER TO appestudo;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: appestudo; Owner: appestudo
--

CREATE FUNCTION appestudo.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION appestudo.update_updated_at_column() OWNER TO appestudo;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agenda; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.agenda (
    id integer NOT NULL,
    usuario_idusuario integer NOT NULL,
    titulo character varying(45) NOT NULL,
    data_inicio timestamp without time zone NOT NULL,
    data_fim timestamp without time zone NOT NULL,
    detalhes character varying(250),
    tipo_evento character varying(50),
    realizado boolean DEFAULT false
);


ALTER TABLE appestudo.agenda OWNER TO appestudo;

--
-- Name: agenda_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.agenda_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.agenda_id_seq OWNER TO appestudo;

--
-- Name: agenda_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.agenda_id_seq OWNED BY appestudo.agenda.id;


--
-- Name: ajustes_plano; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.ajustes_plano (
    id integer NOT NULL,
    prova_id integer,
    usuario_id integer,
    data_inicio date,
    data_fim date,
    dias_estudo_semana integer DEFAULT 6,
    horas_estudo_dia integer,
    intensificar_revisoes boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.ajustes_plano OWNER TO appestudo;

--
-- Name: ajustes_plano_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.ajustes_plano_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.ajustes_plano_id_seq OWNER TO appestudo;

--
-- Name: ajustes_plano_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.ajustes_plano_id_seq OWNED BY appestudo.ajustes_plano.id;


--
-- Name: anotacao; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.anotacao (
    planejamento_idplanejamento integer NOT NULL,
    planejamento_usuario_idusuario integer NOT NULL,
    materia_idmateria integer NOT NULL,
    detalhe text
);


ALTER TABLE appestudo.anotacao OWNER TO appestudo;

--
-- Name: anotacoes; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.anotacoes (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    pagina_url text NOT NULL,
    elemento_id text NOT NULL,
    texto_anotacao text NOT NULL,
    conteudo_anotado text NOT NULL,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    posicao_inicio integer,
    posicao_fim integer
);


ALTER TABLE appestudo.anotacoes OWNER TO appestudo;

--
-- Name: anotacoes_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.anotacoes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.anotacoes_id_seq OWNER TO appestudo;

--
-- Name: anotacoes_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.anotacoes_id_seq OWNED BY appestudo.anotacoes.id;


--
-- Name: assinaturas; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.assinaturas (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    plano_id integer NOT NULL,
    modulo character varying(50) NOT NULL,
    data_inicio timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    data_fim timestamp without time zone NOT NULL,
    status boolean DEFAULT true,
    valor_pago numeric(10,2) NOT NULL,
    forma_pagamento character varying(50),
    codigo_transacao character varying(100),
    tipo_assinatura character varying(20),
    renovacao_automatica boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT assinaturas_tipo_assinatura_check CHECK (((tipo_assinatura)::text = ANY ((ARRAY['mensal'::character varying, 'anual'::character varying])::text[])))
);


ALTER TABLE appestudo.assinaturas OWNER TO appestudo;

--
-- Name: assinaturas_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.assinaturas_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.assinaturas_id_seq OWNER TO appestudo;

--
-- Name: assinaturas_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.assinaturas_id_seq OWNED BY appestudo.assinaturas.id;


--
-- Name: conteudo_edital; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.conteudo_edital (
    id_conteudo integer NOT NULL,
    edital_id integer NOT NULL,
    materia_id integer NOT NULL,
    descricao text NOT NULL,
    capitulo character varying(50),
    ordem integer
);


ALTER TABLE appestudo.conteudo_edital OWNER TO appestudo;

--
-- Name: conteudo_edital_id_conteudo_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.conteudo_edital_id_conteudo_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.conteudo_edital_id_conteudo_seq OWNER TO appestudo;

--
-- Name: conteudo_edital_id_conteudo_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.conteudo_edital_id_conteudo_seq OWNED BY appestudo.conteudo_edital.id_conteudo;


--
-- Name: curso; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.curso (
    idcurso integer NOT NULL,
    nome character varying(500),
    logo_url character varying(255)
);


ALTER TABLE appestudo.curso OWNER TO appestudo;

--
-- Name: curso_idcurso_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.curso_idcurso_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.curso_idcurso_seq OWNER TO appestudo;

--
-- Name: curso_idcurso_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.curso_idcurso_seq OWNED BY appestudo.curso.idcurso;


--
-- Name: edital; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.edital (
    id_edital integer NOT NULL,
    nome character varying(255) NOT NULL,
    descricao text,
    ano integer,
    orgao character varying(255),
    logo_url character varying(255)
);


ALTER TABLE appestudo.edital OWNER TO appestudo;

--
-- Name: edital_id_edital_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.edital_id_edital_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.edital_id_edital_seq OWNER TO appestudo;

--
-- Name: edital_id_edital_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.edital_id_edital_seq OWNED BY appestudo.edital.id_edital;


--
-- Name: estudos; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.estudos (
    idestudos integer NOT NULL,
    data date NOT NULL,
    tempo_liquido time without time zone NOT NULL,
    tempo_bruto time without time zone NOT NULL,
    tempo_perdido time without time zone NOT NULL,
    ponto_estudado character varying(500),
    hora_inicio time without time zone NOT NULL,
    hora_fim time without time zone NOT NULL,
    metodo character varying(45),
    q_total integer,
    q_errada integer,
    q_certa integer,
    planejamento_idplanejamento integer NOT NULL,
    planejamento_usuario_idusuario integer NOT NULL,
    materia_idmateria integer NOT NULL,
    idcurso integer,
    revisoes date,
    descricao text
);

ALTER TABLE appestudo.estudos 
ADD COLUMN link_conteudo VARCHAR(1000);

COMMENT ON COLUMN appestudo.estudos.link_conteudo IS 'Link do conteúdo estudado (URL do vídeo, documento, site, etc)';


ALTER TABLE appestudo.estudos OWNER TO appestudo;

--
-- Name: estudos_idestudos_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.estudos_idestudos_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.estudos_idestudos_seq OWNER TO appestudo;

--
-- Name: estudos_idestudos_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.estudos_idestudos_seq OWNED BY appestudo.estudos.idestudos;


--
-- Name: eventos_calendario; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.eventos_calendario (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    titulo character varying(255) NOT NULL,
    data_inicio date NOT NULL,
    data_fim date NOT NULL,
    tipo character varying(50) NOT NULL,
    materia character varying(100) NOT NULL
);


ALTER TABLE appestudo.eventos_calendario OWNER TO appestudo;

--
-- Name: eventos_calendario_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.eventos_calendario_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.eventos_calendario_id_seq OWNER TO appestudo;

--
-- Name: eventos_calendario_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.eventos_calendario_id_seq OWNED BY appestudo.eventos_calendario.id;


--
-- Name: flashcard_categories; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_categories (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true
);


ALTER TABLE appestudo.flashcard_categories OWNER TO appestudo;

--
-- Name: flashcard_categories_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcard_categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcard_categories_id_seq OWNER TO appestudo;

--
-- Name: flashcard_categories_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcard_categories_id_seq OWNED BY appestudo.flashcard_categories.id;


--
-- Name: flashcard_deck_association; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_deck_association (
    flashcard_id integer NOT NULL,
    deck_id integer NOT NULL
);


ALTER TABLE appestudo.flashcard_deck_association OWNER TO appestudo;

--
-- Name: flashcard_decks; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_decks (
    id integer NOT NULL,
    category_id integer,
    usuario_id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true,
    materia_id integer
);


ALTER TABLE appestudo.flashcard_decks OWNER TO appestudo;

--
-- Name: flashcard_decks_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcard_decks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcard_decks_id_seq OWNER TO appestudo;

--
-- Name: flashcard_decks_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcard_decks_id_seq OWNED BY appestudo.flashcard_decks.id;


--
-- Name: flashcard_materias; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_materias (
    flashcard_id integer NOT NULL,
    materia_id integer NOT NULL
);


ALTER TABLE appestudo.flashcard_materias OWNER TO appestudo;

--
-- Name: flashcard_mindmaps; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_mindmaps (
    id integer NOT NULL,
    flashcard_id integer NOT NULL,
    imagem_url text,
    imagem_base64 text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.flashcard_mindmaps OWNER TO appestudo;

--
-- Name: flashcard_mindmaps_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcard_mindmaps_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcard_mindmaps_id_seq OWNER TO appestudo;

--
-- Name: flashcard_mindmaps_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcard_mindmaps_id_seq OWNED BY appestudo.flashcard_mindmaps.id;


--
-- Name: flashcard_progress; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_progress (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    flashcard_id integer NOT NULL,
    nivel_conhecimento integer DEFAULT 0,
    ultima_revisao timestamp without time zone,
    proxima_revisao timestamp without time zone,
    total_revisoes integer DEFAULT 0,
    revisoes_corretas integer DEFAULT 0,
    intervalo_atual integer DEFAULT 0,
    fator_facilidade numeric(4,2) DEFAULT 2.5,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.flashcard_progress OWNER TO appestudo;

--
-- Name: flashcard_progress_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcard_progress_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcard_progress_id_seq OWNER TO appestudo;

--
-- Name: flashcard_progress_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcard_progress_id_seq OWNED BY appestudo.flashcard_progress.id;


--
-- Name: flashcard_review_history; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_review_history (
    id integer NOT NULL,
    progress_id integer NOT NULL,
    avaliacao integer NOT NULL,
    tempo_resposta integer,
    data_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.flashcard_review_history OWNER TO appestudo;

--
-- Name: flashcard_review_history_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcard_review_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcard_review_history_id_seq OWNER TO appestudo;

--
-- Name: flashcard_review_history_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcard_review_history_id_seq OWNED BY appestudo.flashcard_review_history.id;


--
-- Name: flashcard_topic_association; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_topic_association (
    flashcard_id integer NOT NULL,
    topic_id integer NOT NULL
);


ALTER TABLE appestudo.flashcard_topic_association OWNER TO appestudo;

--
-- Name: flashcard_topics; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcard_topics (
    id integer NOT NULL,
    deck_id integer NOT NULL,
    nome character varying(255) NOT NULL,
    descricao text,
    ordem integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true
);


ALTER TABLE appestudo.flashcard_topics OWNER TO appestudo;

--
-- Name: flashcard_topics_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcard_topics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcard_topics_id_seq OWNER TO appestudo;

--
-- Name: flashcard_topics_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcard_topics_id_seq OWNED BY appestudo.flashcard_topics.id;


--
-- Name: flashcards; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.flashcards (
    id integer NOT NULL,
    pergunta text NOT NULL,
    resposta text NOT NULL,
    resumo text,
    previsao_legal text,
    jurisprudencia text,
    mapa_mental text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true
);


ALTER TABLE appestudo.flashcards OWNER TO appestudo;

--
-- Name: flashcards_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.flashcards_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.flashcards_id_seq OWNER TO appestudo;

--
-- Name: flashcards_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.flashcards_id_seq OWNED BY appestudo.flashcards.id;


--
-- Name: forum_categorias; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.forum_categorias (
    id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    ordem integer,
    status boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.forum_categorias OWNER TO appestudo;

--
-- Name: forum_categorias_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.forum_categorias_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.forum_categorias_id_seq OWNER TO appestudo;

--
-- Name: forum_categorias_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.forum_categorias_id_seq OWNED BY appestudo.forum_categorias.id;


--
-- Name: forum_respostas; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.forum_respostas (
    id integer NOT NULL,
    topico_id integer,
    usuario_id integer,
    conteudo text NOT NULL,
    is_solucao boolean DEFAULT false,
    status boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.forum_respostas OWNER TO appestudo;

--
-- Name: forum_respostas_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.forum_respostas_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.forum_respostas_id_seq OWNER TO appestudo;

--
-- Name: forum_respostas_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.forum_respostas_id_seq OWNED BY appestudo.forum_respostas.id;


--
-- Name: forum_topicos; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.forum_topicos (
    id integer NOT NULL,
    categoria_id integer,
    usuario_id integer,
    titulo character varying(200) NOT NULL,
    conteudo text NOT NULL,
    views integer DEFAULT 0,
    status boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.forum_topicos OWNER TO appestudo;

--
-- Name: forum_topicos_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.forum_topicos_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.forum_topicos_id_seq OWNER TO appestudo;

--
-- Name: forum_topicos_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.forum_topicos_id_seq OWNED BY appestudo.forum_topicos.id;


--
-- Name: forum_votos; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.forum_votos (
    id integer NOT NULL,
    usuario_id integer,
    resposta_id integer,
    tipo_voto smallint NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.forum_votos OWNER TO appestudo;

--
-- Name: forum_votos_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.forum_votos_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.forum_votos_id_seq OWNER TO appestudo;

--
-- Name: forum_votos_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.forum_votos_id_seq OWNED BY appestudo.forum_votos.id;


--
-- Name: itens; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.itens (
    id integer NOT NULL,
    nome character varying(255) NOT NULL,
    quantidade integer DEFAULT 1 NOT NULL,
    unidade character varying(50) DEFAULT 'unidade'::character varying NOT NULL,
    preco numeric(10,2) DEFAULT 0.00 NOT NULL,
    categoria character varying(50) DEFAULT 'mercearia'::character varying NOT NULL,
    urgente boolean DEFAULT false NOT NULL,
    status character varying(20) DEFAULT 'pendente'::character varying NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_compra timestamp without time zone
);


ALTER TABLE appestudo.itens OWNER TO appestudo;

--
-- Name: itens_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.itens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.itens_id_seq OWNER TO appestudo;

--
-- Name: itens_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.itens_id_seq OWNED BY appestudo.itens.id;


--
-- Name: kanban_etiquetas; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.kanban_etiquetas (
    id integer NOT NULL,
    nome character varying(50) NOT NULL,
    cor character varying(7) NOT NULL,
    usuario_id integer
);


ALTER TABLE appestudo.kanban_etiquetas OWNER TO appestudo;

--
-- Name: kanban_etiquetas_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.kanban_etiquetas_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.kanban_etiquetas_id_seq OWNER TO appestudo;

--
-- Name: kanban_etiquetas_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.kanban_etiquetas_id_seq OWNED BY appestudo.kanban_etiquetas.id;


--
-- Name: kanban_tarefa_etiqueta; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.kanban_tarefa_etiqueta (
    tarefa_id integer NOT NULL,
    etiqueta_id integer NOT NULL
);


ALTER TABLE appestudo.kanban_tarefa_etiqueta OWNER TO appestudo;

--
-- Name: kanban_tarefas; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.kanban_tarefas (
    id integer NOT NULL,
    titulo character varying(100) NOT NULL,
    descricao text,
    status character varying(20) DEFAULT 'backlog'::character varying NOT NULL,
    prioridade character varying(10) DEFAULT 'media'::character varying NOT NULL,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_limite date,
    data_conclusao timestamp without time zone,
    usuario_id integer,
    CONSTRAINT chk_prioridade CHECK (((prioridade)::text = ANY ((ARRAY['baixa'::character varying, 'media'::character varying, 'alta'::character varying])::text[]))),
    CONSTRAINT chk_status CHECK (((status)::text = ANY ((ARRAY['backlog'::character varying, 'em_andamento'::character varying, 'concluido'::character varying])::text[])))
);


ALTER TABLE appestudo.kanban_tarefas OWNER TO appestudo;

--
-- Name: kanban_tarefas_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.kanban_tarefas_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.kanban_tarefas_id_seq OWNER TO appestudo;

--
-- Name: kanban_tarefas_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.kanban_tarefas_id_seq OWNED BY appestudo.kanban_tarefas.id;


--
-- Name: leitura_lei_seca; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.leitura_lei_seca (
    id integer NOT NULL,
    id_usuario integer NOT NULL,
    dia character varying(10),
    materia character varying(255) NOT NULL,
    conteudo text NOT NULL,
    lido boolean DEFAULT false
);


ALTER TABLE appestudo.leitura_lei_seca OWNER TO appestudo;

--
-- Name: leitura_lei_seca_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.leitura_lei_seca_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.leitura_lei_seca_id_seq OWNER TO appestudo;

--
-- Name: leitura_lei_seca_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.leitura_lei_seca_id_seq OWNED BY appestudo.leitura_lei_seca.id;


--
-- Name: log_seguranca; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.log_seguranca (
    id integer NOT NULL,
    usuario_id integer,
    tipo_evento character varying(20) NOT NULL,
    ip character varying(45) NOT NULL,
    data_evento timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    detalhes jsonb
);


ALTER TABLE appestudo.log_seguranca OWNER TO appestudo;

--
-- Name: log_seguranca_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.log_seguranca_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.log_seguranca_id_seq OWNER TO appestudo;

--
-- Name: log_seguranca_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.log_seguranca_id_seq OWNED BY appestudo.log_seguranca.id;


--
-- Name: marcacoes; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.marcacoes (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    pagina_url text NOT NULL,
    elemento_id text NOT NULL,
    tipo_marcacao text NOT NULL,
    texto_marcado text,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    posicao_inicio integer,
    posicao_fim integer
);


ALTER TABLE appestudo.marcacoes OWNER TO appestudo;

--
-- Name: marcacoes_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.marcacoes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.marcacoes_id_seq OWNER TO appestudo;

--
-- Name: marcacoes_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.marcacoes_id_seq OWNED BY appestudo.marcacoes.id;


--
-- Name: materia; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.materia (
    idmateria integer NOT NULL,
    nome character varying(100),
    cor character varying(10)
);


ALTER TABLE appestudo.materia OWNER TO appestudo;

--
-- Name: materia_idmateria_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.materia_idmateria_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.materia_idmateria_seq OWNER TO appestudo;

--
-- Name: materia_idmateria_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.materia_idmateria_seq OWNED BY appestudo.materia.idmateria;


--
-- Name: materias_estudadas; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.materias_estudadas (
    id integer NOT NULL,
    nome character varying(100) NOT NULL,
    cor character varying(20) NOT NULL,
    detalhe text
);


ALTER TABLE appestudo.materias_estudadas OWNER TO appestudo;

--
-- Name: materias_estudadas_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.materias_estudadas_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.materias_estudadas_id_seq OWNER TO appestudo;

--
-- Name: materias_estudadas_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.materias_estudadas_id_seq OWNED BY appestudo.materias_estudadas.id;


--
-- Name: medicamento; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.medicamento (
    id_medicamento integer NOT NULL,
    nome character varying(450),
    detalhes character varying(1000),
    data_inicio timestamp without time zone,
    dosagem character varying(256),
    usuario_idusuario integer NOT NULL,
    foto bytea
);


ALTER TABLE appestudo.medicamento OWNER TO appestudo;

--
-- Name: medicamento_id_medicamento_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.medicamento_id_medicamento_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.medicamento_id_medicamento_seq OWNER TO appestudo;

--
-- Name: medicamento_id_medicamento_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.medicamento_id_medicamento_seq OWNED BY appestudo.medicamento.id_medicamento;


--
-- Name: medicamentos; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.medicamentos (
    id integer NOT NULL,
    nome character varying(100) NOT NULL,
    dosagem character varying(50) NOT NULL,
    intervalo_horas integer NOT NULL,
    data_inicio date NOT NULL,
    dias_tratamento integer NOT NULL,
    horario_inicial time without time zone NOT NULL,
    observacoes text,
    status boolean DEFAULT true,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.medicamentos OWNER TO appestudo;

--
-- Name: medicamentos_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.medicamentos_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.medicamentos_id_seq OWNER TO appestudo;

--
-- Name: medicamentos_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.medicamentos_id_seq OWNED BY appestudo.medicamentos.id;


--
-- Name: notificacoes; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.notificacoes (
    id integer NOT NULL,
    titulo character varying(255) NOT NULL COLLATE pg_catalog."pt-BR-x-icu",
    mensagem text NOT NULL COLLATE pg_catalog."pt-BR-x-icu",
    tipo character varying(50) NOT NULL COLLATE pg_catalog."pt-BR-x-icu",
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_expiracao timestamp without time zone NOT NULL,
    status boolean DEFAULT true,
    is_global boolean DEFAULT false,
    criado_por integer,
    CONSTRAINT notificacoes_tipo_check CHECK (((tipo)::text = ANY ((ARRAY['info'::character varying, 'warning'::character varying, 'danger'::character varying])::text[])))
);


ALTER TABLE appestudo.notificacoes OWNER TO appestudo;

--
-- Name: notificacoes_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.notificacoes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.notificacoes_id_seq OWNER TO appestudo;

--
-- Name: notificacoes_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.notificacoes_id_seq OWNED BY appestudo.notificacoes.id;


--
-- Name: notificacoes_usuarios; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.notificacoes_usuarios (
    id integer NOT NULL,
    notificacao_id integer,
    usuario_id integer,
    lida boolean DEFAULT false,
    data_leitura timestamp without time zone
);


ALTER TABLE appestudo.notificacoes_usuarios OWNER TO appestudo;

--
-- Name: notificacoes_usuarios_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.notificacoes_usuarios_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.notificacoes_usuarios_id_seq OWNER TO appestudo;

--
-- Name: notificacoes_usuarios_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.notificacoes_usuarios_id_seq OWNED BY appestudo.notificacoes_usuarios.id;


--
-- Name: pesos_materias; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.pesos_materias (
    id integer NOT NULL,
    prova_id integer,
    materia_id integer,
    peso integer DEFAULT 1,
    nivel_dificuldade integer DEFAULT 3,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.pesos_materias OWNER TO appestudo;

--
-- Name: pesos_materias_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.pesos_materias_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.pesos_materias_id_seq OWNER TO appestudo;

--
-- Name: pesos_materias_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.pesos_materias_id_seq OWNED BY appestudo.pesos_materias.id;


--
-- Name: planejamento; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.planejamento (
    idplanejamento integer NOT NULL,
    usuario_idusuario integer NOT NULL,
    nome character varying(45) NOT NULL,
    data_inicio date NOT NULL,
    data_fim date,
    tempo_planejamento time without time zone
);


ALTER TABLE appestudo.planejamento OWNER TO appestudo;

--
-- Name: planejamento_idplanejamento_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.planejamento_idplanejamento_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.planejamento_idplanejamento_seq OWNER TO appestudo;

--
-- Name: planejamento_idplanejamento_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.planejamento_idplanejamento_seq OWNED BY appestudo.planejamento.idplanejamento;


--
-- Name: planejamento_materia; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.planejamento_materia (
    idplanejamento_materia integer NOT NULL,
    planejamento_idplanejamento integer NOT NULL,
    materia_idmateria integer NOT NULL,
    ordem integer,
    ativo boolean DEFAULT true
);


ALTER TABLE appestudo.planejamento_materia OWNER TO appestudo;

--
-- Name: planejamento_materia_backup; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.planejamento_materia_backup (
    idplanejamento_materia integer,
    planejamento_idplanejamento integer,
    materia_idmateria integer
);


ALTER TABLE appestudo.planejamento_materia_backup OWNER TO appestudo;

--
-- Name: planejamento_materia_idplanejamento_materia_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.planejamento_materia_idplanejamento_materia_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.planejamento_materia_idplanejamento_materia_seq OWNER TO appestudo;

--
-- Name: planejamento_materia_idplanejamento_materia_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.planejamento_materia_idplanejamento_materia_seq OWNED BY appestudo.planejamento_materia.idplanejamento_materia;


--
-- Name: planos; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.planos (
    id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    valor_mensal numeric(10,2),
    valor_anual numeric(10,2),
    modulo character varying(50) NOT NULL,
    ativo boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.planos OWNER TO appestudo;

--
-- Name: planos_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.planos_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.planos_id_seq OWNER TO appestudo;

--
-- Name: planos_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.planos_id_seq OWNED BY appestudo.planos.id;


--
-- Name: provas; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.provas (
    id integer NOT NULL,
    usuario_id integer,
    nome character varying(255),
    data_prova date,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true,
    data_inicio_estudo date,
    replanejado boolean DEFAULT false,
    data_replanejamento timestamp without time zone
);


ALTER TABLE appestudo.provas OWNER TO appestudo;

--
-- Name: COLUMN provas.data_replanejamento; Type: COMMENT; Schema: appestudo; Owner: appestudo
--

COMMENT ON COLUMN appestudo.provas.data_replanejamento IS 'Data em que o cronograma foi replanejado';


--
-- Name: provas_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.provas_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.provas_id_seq OWNER TO appestudo;

--
-- Name: provas_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.provas_id_seq OWNED BY appestudo.provas.id;


--
-- Name: registros_uso; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.registros_uso (
    id integer NOT NULL,
    medicamento_id integer,
    data_hora timestamp without time zone NOT NULL,
    confirmado boolean DEFAULT false,
    observacao text,
    data_confirmacao timestamp without time zone
);


ALTER TABLE appestudo.registros_uso OWNER TO appestudo;

--
-- Name: registros_uso_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.registros_uso_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.registros_uso_id_seq OWNER TO appestudo;

--
-- Name: registros_uso_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.registros_uso_id_seq OWNED BY appestudo.registros_uso.id;


--
-- Name: revisoes; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.revisoes (
    id integer NOT NULL,
    conteudo_id integer,
    usuario_id integer,
    nivel_revisao integer DEFAULT 1,
    ultima_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    proxima_revisao timestamp without time zone,
    status_revisao character varying(20) DEFAULT 'pendente'::character varying,
    confianca integer DEFAULT 0,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    edital_id integer NOT NULL
);


ALTER TABLE appestudo.revisoes OWNER TO appestudo;

--
-- Name: revisoes_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.revisoes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.revisoes_id_seq OWNER TO appestudo;

--
-- Name: revisoes_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.revisoes_id_seq OWNED BY appestudo.revisoes.id;


--
-- Name: usuario; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.usuario (
    idusuario integer NOT NULL,
    nome character varying(45) NOT NULL,
    senha character varying(45) NOT NULL,
    usuario character varying(255),
    email character varying(255) NOT NULL,
    is_admin boolean DEFAULT false,
    cronograma_inteligente boolean DEFAULT false,
    status character varying(20) DEFAULT 'ativo'::character varying,
    tentativas_falhas integer DEFAULT 0,
    CONSTRAINT chk_status_valido CHECK (((status)::text = ANY ((ARRAY['ativo'::character varying, 'inativo'::character varying, 'bloqueado'::character varying, 'pendente'::character varying])::text[])))
);


ALTER TABLE appestudo.usuario OWNER TO appestudo;

--
-- Name: usuario_conteudo; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.usuario_conteudo (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    conteudo_id integer NOT NULL,
    status boolean DEFAULT false,
    data_revisao date,
    status_revisao character varying(50) DEFAULT 'Não Iniciado'::character varying,
    status_estudo character varying(15) DEFAULT 'Não Estudado'::character varying,
    replanejado_em timestamp without time zone
);


ALTER TABLE appestudo.usuario_conteudo OWNER TO appestudo;

--
-- Name: COLUMN usuario_conteudo.replanejado_em; Type: COMMENT; Schema: appestudo; Owner: appestudo
--

COMMENT ON COLUMN appestudo.usuario_conteudo.replanejado_em IS 'Timestamp que indica quando o conteúdo foi marcado durante um replanejamento';


--
-- Name: usuario_conteudo_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.usuario_conteudo_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.usuario_conteudo_id_seq OWNER TO appestudo;

--
-- Name: usuario_conteudo_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.usuario_conteudo_id_seq OWNED BY appestudo.usuario_conteudo.id;


--
-- Name: usuario_dias_estudo; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.usuario_dias_estudo (
    id integer NOT NULL,
    usuario_id integer,
    dia_semana integer,
    horas_estudo numeric(4,2),
    ativo boolean DEFAULT true
);


ALTER TABLE appestudo.usuario_dias_estudo OWNER TO appestudo;

--
-- Name: usuario_dias_estudo_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.usuario_dias_estudo_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.usuario_dias_estudo_id_seq OWNER TO appestudo;

--
-- Name: usuario_dias_estudo_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.usuario_dias_estudo_id_seq OWNED BY appestudo.usuario_dias_estudo.id;


--
-- Name: usuario_edital; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.usuario_edital (
    id integer NOT NULL,
    usuario_id integer NOT NULL,
    edital_id integer NOT NULL,
    data_inscricao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE appestudo.usuario_edital OWNER TO appestudo;

--
-- Name: usuario_edital_id_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.usuario_edital_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.usuario_edital_id_seq OWNER TO appestudo;

--
-- Name: usuario_edital_id_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.usuario_edital_id_seq OWNED BY appestudo.usuario_edital.id;


--
-- Name: usuario_has_curso; Type: TABLE; Schema: appestudo; Owner: appestudo
--

CREATE TABLE appestudo.usuario_has_curso (
    usuario_idusuario integer NOT NULL,
    curso_idcurso integer NOT NULL
);


ALTER TABLE appestudo.usuario_has_curso OWNER TO appestudo;

--
-- Name: usuario_idusuario_seq; Type: SEQUENCE; Schema: appestudo; Owner: appestudo
--

CREATE SEQUENCE appestudo.usuario_idusuario_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE appestudo.usuario_idusuario_seq OWNER TO appestudo;

--
-- Name: usuario_idusuario_seq; Type: SEQUENCE OWNED BY; Schema: appestudo; Owner: appestudo
--

ALTER SEQUENCE appestudo.usuario_idusuario_seq OWNED BY appestudo.usuario.idusuario;


--
-- Name: vw_registros_completos; Type: VIEW; Schema: appestudo; Owner: appestudo
--

CREATE VIEW appestudo.vw_registros_completos AS
 SELECT r.id,
    r.medicamento_id,
    m.nome,
    m.dosagem,
    r.data_hora,
    r.confirmado,
    r.observacao
   FROM (appestudo.registros_uso r
     JOIN appestudo.medicamentos m ON ((r.medicamento_id = m.id)));


ALTER VIEW appestudo.vw_registros_completos OWNER TO appestudo;

--
-- Name: cronograma; Type: TABLE; Schema: public; Owner: appestudo
--

CREATE TABLE public.cronograma (
    id integer NOT NULL,
    semana integer,
    dia_semana character varying(50),
    materia character varying(255),
    pontos_edital text,
    material_base text,
    julgados text,
    lei_seca text,
    questoes text,
    feito boolean DEFAULT false,
    id_usuario integer,
    idmateria integer,
    controle_questoes boolean DEFAULT false
);


ALTER TABLE public.cronograma OWNER TO appestudo;

--
-- Name: cronograma_id_seq; Type: SEQUENCE; Schema: public; Owner: appestudo
--

CREATE SEQUENCE public.cronograma_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cronograma_id_seq OWNER TO appestudo;

--
-- Name: cronograma_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: appestudo
--

ALTER SEQUENCE public.cronograma_id_seq OWNED BY public.cronograma.id;


--
-- Name: estudos_id_seq; Type: SEQUENCE; Schema: public; Owner: appestudo
--

CREATE SEQUENCE public.estudos_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.estudos_id_seq OWNER TO appestudo;

--
-- Name: estudos; Type: TABLE; Schema: public; Owner: appestudo
--

CREATE TABLE public.estudos (
    id integer DEFAULT nextval('public.estudos_id_seq'::regclass) NOT NULL,
    materia character varying(255) NOT NULL,
    data_estudo date NOT NULL,
    tempo_estudo time without time zone NOT NULL,
    tempo_bruto time without time zone NOT NULL,
    tempo_perdido time without time zone NOT NULL,
    ponto_estudado character varying(255) NOT NULL,
    tempo_inicio_estudo time without time zone NOT NULL,
    tempo_fim_estudo time without time zone NOT NULL,
    metodo character varying(256) NOT NULL,
    q_total integer,
    q_errada integer,
    q_certa integer
);


ALTER TABLE public.estudos OWNER TO appestudo;

--
-- Name: estudos_thiago; Type: TABLE; Schema: public; Owner: appestudo
--

CREATE TABLE public.estudos_thiago (
    id integer NOT NULL,
    materia character varying(255) NOT NULL,
    data_estudo date NOT NULL,
    tempo_estudo time without time zone NOT NULL,
    tempo_bruto time without time zone NOT NULL,
    tempo_perdido time without time zone NOT NULL,
    ponto_estudado character varying(255) NOT NULL,
    tempo_inicio_estudo time without time zone NOT NULL,
    tempo_fim_estudo time without time zone NOT NULL,
    metodo character varying(256) NOT NULL,
    q_total integer,
    q_errada integer,
    q_certa integer
);


ALTER TABLE public.estudos_thiago OWNER TO appestudo;

--
-- Name: estudos_thiago_id_seq; Type: SEQUENCE; Schema: public; Owner: appestudo
--

CREATE SEQUENCE public.estudos_thiago_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.estudos_thiago_id_seq OWNER TO appestudo;

--
-- Name: estudos_thiago_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: appestudo
--

ALTER SEQUENCE public.estudos_thiago_id_seq OWNED BY public.estudos_thiago.id;


--
-- Name: id_seq; Type: SEQUENCE; Schema: public; Owner: appestudo
--

CREATE SEQUENCE public.id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.id_seq OWNER TO appestudo;

--
-- Name: materia; Type: TABLE; Schema: public; Owner: appestudo
--

CREATE TABLE public.materia (
    idmateria integer NOT NULL,
    nome character varying(45) NOT NULL,
    detalhe character varying(250),
    usuario_id integer NOT NULL
);


ALTER TABLE public.materia OWNER TO appestudo;

--
-- Name: usuario; Type: TABLE; Schema: public; Owner: appestudo
--

CREATE TABLE public.usuario (
    id integer NOT NULL,
    nome character varying(45) NOT NULL,
    senha character varying(255)
);


ALTER TABLE public.usuario OWNER TO appestudo;

--
-- Name: usuario_id_seq; Type: SEQUENCE; Schema: public; Owner: appestudo
--

CREATE SEQUENCE public.usuario_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.usuario_id_seq OWNER TO appestudo;

--
-- Name: usuario_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: appestudo
--

ALTER SEQUENCE public.usuario_id_seq OWNED BY public.usuario.id;


--
-- Name: agenda id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.agenda ALTER COLUMN id SET DEFAULT nextval('appestudo.agenda_id_seq'::regclass);


--
-- Name: ajustes_plano id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.ajustes_plano ALTER COLUMN id SET DEFAULT nextval('appestudo.ajustes_plano_id_seq'::regclass);


--
-- Name: anotacoes id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.anotacoes ALTER COLUMN id SET DEFAULT nextval('appestudo.anotacoes_id_seq'::regclass);


--
-- Name: assinaturas id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.assinaturas ALTER COLUMN id SET DEFAULT nextval('appestudo.assinaturas_id_seq'::regclass);


--
-- Name: conteudo_edital id_conteudo; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.conteudo_edital ALTER COLUMN id_conteudo SET DEFAULT nextval('appestudo.conteudo_edital_id_conteudo_seq'::regclass);


--
-- Name: curso idcurso; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.curso ALTER COLUMN idcurso SET DEFAULT nextval('appestudo.curso_idcurso_seq'::regclass);


--
-- Name: edital id_edital; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.edital ALTER COLUMN id_edital SET DEFAULT nextval('appestudo.edital_id_edital_seq'::regclass);


--
-- Name: estudos idestudos; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.estudos ALTER COLUMN idestudos SET DEFAULT nextval('appestudo.estudos_idestudos_seq'::regclass);


--
-- Name: eventos_calendario id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.eventos_calendario ALTER COLUMN id SET DEFAULT nextval('appestudo.eventos_calendario_id_seq'::regclass);


--
-- Name: flashcard_categories id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_categories ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcard_categories_id_seq'::regclass);


--
-- Name: flashcard_decks id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_decks ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcard_decks_id_seq'::regclass);


--
-- Name: flashcard_mindmaps id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_mindmaps ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcard_mindmaps_id_seq'::regclass);


--
-- Name: flashcard_progress id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_progress ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcard_progress_id_seq'::regclass);


--
-- Name: flashcard_review_history id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_review_history ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcard_review_history_id_seq'::regclass);


--
-- Name: flashcard_topics id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_topics ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcard_topics_id_seq'::regclass);


--
-- Name: flashcards id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcards ALTER COLUMN id SET DEFAULT nextval('appestudo.flashcards_id_seq'::regclass);


--
-- Name: forum_categorias id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_categorias ALTER COLUMN id SET DEFAULT nextval('appestudo.forum_categorias_id_seq'::regclass);


--
-- Name: forum_respostas id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_respostas ALTER COLUMN id SET DEFAULT nextval('appestudo.forum_respostas_id_seq'::regclass);


--
-- Name: forum_topicos id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_topicos ALTER COLUMN id SET DEFAULT nextval('appestudo.forum_topicos_id_seq'::regclass);


--
-- Name: forum_votos id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_votos ALTER COLUMN id SET DEFAULT nextval('appestudo.forum_votos_id_seq'::regclass);


--
-- Name: itens id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.itens ALTER COLUMN id SET DEFAULT nextval('appestudo.itens_id_seq'::regclass);


--
-- Name: kanban_etiquetas id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_etiquetas ALTER COLUMN id SET DEFAULT nextval('appestudo.kanban_etiquetas_id_seq'::regclass);


--
-- Name: kanban_tarefas id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_tarefas ALTER COLUMN id SET DEFAULT nextval('appestudo.kanban_tarefas_id_seq'::regclass);


--
-- Name: leitura_lei_seca id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.leitura_lei_seca ALTER COLUMN id SET DEFAULT nextval('appestudo.leitura_lei_seca_id_seq'::regclass);


--
-- Name: log_seguranca id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.log_seguranca ALTER COLUMN id SET DEFAULT nextval('appestudo.log_seguranca_id_seq'::regclass);


--
-- Name: marcacoes id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.marcacoes ALTER COLUMN id SET DEFAULT nextval('appestudo.marcacoes_id_seq'::regclass);


--
-- Name: materia idmateria; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.materia ALTER COLUMN idmateria SET DEFAULT nextval('appestudo.materia_idmateria_seq'::regclass);


--
-- Name: materias_estudadas id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.materias_estudadas ALTER COLUMN id SET DEFAULT nextval('appestudo.materias_estudadas_id_seq'::regclass);


--
-- Name: medicamento id_medicamento; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.medicamento ALTER COLUMN id_medicamento SET DEFAULT nextval('appestudo.medicamento_id_medicamento_seq'::regclass);


--
-- Name: medicamentos id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.medicamentos ALTER COLUMN id SET DEFAULT nextval('appestudo.medicamentos_id_seq'::regclass);


--
-- Name: notificacoes id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes ALTER COLUMN id SET DEFAULT nextval('appestudo.notificacoes_id_seq'::regclass);


--
-- Name: notificacoes_usuarios id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes_usuarios ALTER COLUMN id SET DEFAULT nextval('appestudo.notificacoes_usuarios_id_seq'::regclass);


--
-- Name: pesos_materias id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.pesos_materias ALTER COLUMN id SET DEFAULT nextval('appestudo.pesos_materias_id_seq'::regclass);


--
-- Name: planejamento idplanejamento; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento ALTER COLUMN idplanejamento SET DEFAULT nextval('appestudo.planejamento_idplanejamento_seq'::regclass);


--
-- Name: planejamento_materia idplanejamento_materia; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento_materia ALTER COLUMN idplanejamento_materia SET DEFAULT nextval('appestudo.planejamento_materia_idplanejamento_materia_seq'::regclass);


--
-- Name: planos id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planos ALTER COLUMN id SET DEFAULT nextval('appestudo.planos_id_seq'::regclass);


--
-- Name: provas id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.provas ALTER COLUMN id SET DEFAULT nextval('appestudo.provas_id_seq'::regclass);


--
-- Name: registros_uso id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.registros_uso ALTER COLUMN id SET DEFAULT nextval('appestudo.registros_uso_id_seq'::regclass);


--
-- Name: revisoes id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.revisoes ALTER COLUMN id SET DEFAULT nextval('appestudo.revisoes_id_seq'::regclass);


--
-- Name: usuario idusuario; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario ALTER COLUMN idusuario SET DEFAULT nextval('appestudo.usuario_idusuario_seq'::regclass);


--
-- Name: usuario_conteudo id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_conteudo ALTER COLUMN id SET DEFAULT nextval('appestudo.usuario_conteudo_id_seq'::regclass);


--
-- Name: usuario_dias_estudo id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_dias_estudo ALTER COLUMN id SET DEFAULT nextval('appestudo.usuario_dias_estudo_id_seq'::regclass);


--
-- Name: usuario_edital id; Type: DEFAULT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_edital ALTER COLUMN id SET DEFAULT nextval('appestudo.usuario_edital_id_seq'::regclass);


--
-- Name: cronograma id; Type: DEFAULT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.cronograma ALTER COLUMN id SET DEFAULT nextval('public.cronograma_id_seq'::regclass);


--
-- Name: estudos_thiago id; Type: DEFAULT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.estudos_thiago ALTER COLUMN id SET DEFAULT nextval('public.estudos_thiago_id_seq'::regclass);


--
-- Name: usuario id; Type: DEFAULT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.usuario ALTER COLUMN id SET DEFAULT nextval('public.usuario_id_seq'::regclass);


--
-- Name: agenda agenda_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.agenda
    ADD CONSTRAINT agenda_pkey PRIMARY KEY (id);


--
-- Name: ajustes_plano ajustes_plano_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.ajustes_plano
    ADD CONSTRAINT ajustes_plano_pkey PRIMARY KEY (id);


--
-- Name: anotacoes anotacoes_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.anotacoes
    ADD CONSTRAINT anotacoes_pkey PRIMARY KEY (id);


--
-- Name: assinaturas assinaturas_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.assinaturas
    ADD CONSTRAINT assinaturas_pkey PRIMARY KEY (id);


--
-- Name: conteudo_edital conteudo_edital_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.conteudo_edital
    ADD CONSTRAINT conteudo_edital_pkey PRIMARY KEY (id_conteudo);


--
-- Name: curso curso_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.curso
    ADD CONSTRAINT curso_pkey PRIMARY KEY (idcurso);


--
-- Name: edital edital_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.edital
    ADD CONSTRAINT edital_pkey PRIMARY KEY (id_edital);


--
-- Name: estudos estudos_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.estudos
    ADD CONSTRAINT estudos_pkey PRIMARY KEY (idestudos);


--
-- Name: eventos_calendario eventos_calendario_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.eventos_calendario
    ADD CONSTRAINT eventos_calendario_pkey PRIMARY KEY (id);


--
-- Name: flashcard_categories flashcard_categories_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_categories
    ADD CONSTRAINT flashcard_categories_pkey PRIMARY KEY (id);


--
-- Name: flashcard_deck_association flashcard_deck_association_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_deck_association
    ADD CONSTRAINT flashcard_deck_association_pkey PRIMARY KEY (flashcard_id, deck_id);


--
-- Name: flashcard_decks flashcard_decks_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_decks
    ADD CONSTRAINT flashcard_decks_pkey PRIMARY KEY (id);


--
-- Name: flashcard_materias flashcard_materias_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_materias
    ADD CONSTRAINT flashcard_materias_pkey PRIMARY KEY (flashcard_id, materia_id);


--
-- Name: flashcard_mindmaps flashcard_mindmaps_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_mindmaps
    ADD CONSTRAINT flashcard_mindmaps_pkey PRIMARY KEY (id);


--
-- Name: flashcard_progress flashcard_progress_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_progress
    ADD CONSTRAINT flashcard_progress_pkey PRIMARY KEY (id);


--
-- Name: flashcard_progress flashcard_progress_usuario_id_flashcard_id_key; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_progress
    ADD CONSTRAINT flashcard_progress_usuario_id_flashcard_id_key UNIQUE (usuario_id, flashcard_id);


--
-- Name: flashcard_review_history flashcard_review_history_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_review_history
    ADD CONSTRAINT flashcard_review_history_pkey PRIMARY KEY (id);


--
-- Name: flashcard_topic_association flashcard_topic_association_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_topic_association
    ADD CONSTRAINT flashcard_topic_association_pkey PRIMARY KEY (flashcard_id, topic_id);


--
-- Name: flashcard_topics flashcard_topics_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_topics
    ADD CONSTRAINT flashcard_topics_pkey PRIMARY KEY (id);


--
-- Name: flashcards flashcards_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcards
    ADD CONSTRAINT flashcards_pkey PRIMARY KEY (id);


--
-- Name: forum_categorias forum_categorias_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_categorias
    ADD CONSTRAINT forum_categorias_pkey PRIMARY KEY (id);


--
-- Name: forum_respostas forum_respostas_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_respostas
    ADD CONSTRAINT forum_respostas_pkey PRIMARY KEY (id);


--
-- Name: forum_topicos forum_topicos_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_topicos
    ADD CONSTRAINT forum_topicos_pkey PRIMARY KEY (id);


--
-- Name: forum_votos forum_votos_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_votos
    ADD CONSTRAINT forum_votos_pkey PRIMARY KEY (id);


--
-- Name: forum_votos forum_votos_usuario_id_resposta_id_key; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_votos
    ADD CONSTRAINT forum_votos_usuario_id_resposta_id_key UNIQUE (usuario_id, resposta_id);


--
-- Name: itens itens_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.itens
    ADD CONSTRAINT itens_pkey PRIMARY KEY (id);


--
-- Name: kanban_etiquetas kanban_etiquetas_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_etiquetas
    ADD CONSTRAINT kanban_etiquetas_pkey PRIMARY KEY (id);


--
-- Name: kanban_tarefa_etiqueta kanban_tarefa_etiqueta_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_tarefa_etiqueta
    ADD CONSTRAINT kanban_tarefa_etiqueta_pkey PRIMARY KEY (tarefa_id, etiqueta_id);


--
-- Name: kanban_tarefas kanban_tarefas_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_tarefas
    ADD CONSTRAINT kanban_tarefas_pkey PRIMARY KEY (id);


--
-- Name: leitura_lei_seca leitura_lei_seca_id_usuario_dia_materia_key; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.leitura_lei_seca
    ADD CONSTRAINT leitura_lei_seca_id_usuario_dia_materia_key UNIQUE (id_usuario, dia, materia);


--
-- Name: leitura_lei_seca leitura_lei_seca_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.leitura_lei_seca
    ADD CONSTRAINT leitura_lei_seca_pkey PRIMARY KEY (id);


--
-- Name: log_seguranca log_seguranca_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.log_seguranca
    ADD CONSTRAINT log_seguranca_pkey PRIMARY KEY (id);


--
-- Name: marcacoes marcacoes_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.marcacoes
    ADD CONSTRAINT marcacoes_pkey PRIMARY KEY (id);


--
-- Name: materia materia_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.materia
    ADD CONSTRAINT materia_pkey PRIMARY KEY (idmateria);


--
-- Name: materias_estudadas materias_estudadas_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.materias_estudadas
    ADD CONSTRAINT materias_estudadas_pkey PRIMARY KEY (id);


--
-- Name: medicamento medicamento_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.medicamento
    ADD CONSTRAINT medicamento_pkey PRIMARY KEY (id_medicamento);


--
-- Name: medicamentos medicamentos_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.medicamentos
    ADD CONSTRAINT medicamentos_pkey PRIMARY KEY (id);


--
-- Name: notificacoes notificacoes_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes
    ADD CONSTRAINT notificacoes_pkey PRIMARY KEY (id);


--
-- Name: notificacoes_usuarios notificacoes_usuarios_notificacao_id_usuario_id_key; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes_usuarios
    ADD CONSTRAINT notificacoes_usuarios_notificacao_id_usuario_id_key UNIQUE (notificacao_id, usuario_id);


--
-- Name: notificacoes_usuarios notificacoes_usuarios_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes_usuarios
    ADD CONSTRAINT notificacoes_usuarios_pkey PRIMARY KEY (id);


--
-- Name: pesos_materias pesos_materias_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.pesos_materias
    ADD CONSTRAINT pesos_materias_pkey PRIMARY KEY (id);


--
-- Name: planejamento_materia planejamento_materia_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento_materia
    ADD CONSTRAINT planejamento_materia_pkey PRIMARY KEY (idplanejamento_materia);


--
-- Name: planejamento planejamento_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento
    ADD CONSTRAINT planejamento_pkey PRIMARY KEY (idplanejamento);


--
-- Name: planos planos_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planos
    ADD CONSTRAINT planos_pkey PRIMARY KEY (id);


--
-- Name: provas provas_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.provas
    ADD CONSTRAINT provas_pkey PRIMARY KEY (id);


--
-- Name: registros_uso registros_uso_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.registros_uso
    ADD CONSTRAINT registros_uso_pkey PRIMARY KEY (id);


--
-- Name: revisoes revisoes_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.revisoes
    ADD CONSTRAINT revisoes_pkey PRIMARY KEY (id);


--
-- Name: ajustes_plano unique_ajustes_prova_id; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.ajustes_plano
    ADD CONSTRAINT unique_ajustes_prova_id UNIQUE (prova_id);


--
-- Name: marcacoes unique_marcacao_posicao; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.marcacoes
    ADD CONSTRAINT unique_marcacao_posicao UNIQUE (usuario_id, pagina_url, elemento_id, posicao_inicio, posicao_fim);


--
-- Name: pesos_materias unique_prova_materia; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.pesos_materias
    ADD CONSTRAINT unique_prova_materia UNIQUE (prova_id, materia_id);


--
-- Name: usuario_conteudo usuario_conteudo_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_conteudo
    ADD CONSTRAINT usuario_conteudo_pkey PRIMARY KEY (id);


--
-- Name: usuario_conteudo usuario_conteudo_unico; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_conteudo
    ADD CONSTRAINT usuario_conteudo_unico UNIQUE (usuario_id, conteudo_id);


--
-- Name: usuario_dias_estudo usuario_dias_estudo_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_dias_estudo
    ADD CONSTRAINT usuario_dias_estudo_pkey PRIMARY KEY (id);


--
-- Name: usuario_edital usuario_edital_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_edital
    ADD CONSTRAINT usuario_edital_pkey PRIMARY KEY (id);


--
-- Name: usuario usuario_email_key; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario
    ADD CONSTRAINT usuario_email_key UNIQUE (email);


--
-- Name: usuario_has_curso usuario_has_curso_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_has_curso
    ADD CONSTRAINT usuario_has_curso_pkey PRIMARY KEY (usuario_idusuario, curso_idcurso);


--
-- Name: usuario usuario_pkey; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario
    ADD CONSTRAINT usuario_pkey PRIMARY KEY (idusuario);


--
-- Name: usuario usuario_usuario_key; Type: CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario
    ADD CONSTRAINT usuario_usuario_key UNIQUE (usuario);


--
-- Name: cronograma cronograma_pkey; Type: CONSTRAINT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.cronograma
    ADD CONSTRAINT cronograma_pkey PRIMARY KEY (id);


--
-- Name: estudos estudos_pkey; Type: CONSTRAINT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.estudos
    ADD CONSTRAINT estudos_pkey PRIMARY KEY (id);


--
-- Name: estudos_thiago estudos_thiago_pkey; Type: CONSTRAINT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.estudos_thiago
    ADD CONSTRAINT estudos_thiago_pkey PRIMARY KEY (id);


--
-- Name: materia materia_pkey; Type: CONSTRAINT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.materia
    ADD CONSTRAINT materia_pkey PRIMARY KEY (idmateria);


--
-- Name: usuario usuario_pkey; Type: CONSTRAINT; Schema: public; Owner: appestudo
--

ALTER TABLE ONLY public.usuario
    ADD CONSTRAINT usuario_pkey PRIMARY KEY (id);


--
-- Name: idx_anotacoes_composto; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_anotacoes_composto ON appestudo.anotacoes USING btree (usuario_id, pagina_url, elemento_id);


--
-- Name: idx_anotacoes_elemento; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_anotacoes_elemento ON appestudo.anotacoes USING btree (elemento_id);


--
-- Name: idx_anotacoes_pagina; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_anotacoes_pagina ON appestudo.anotacoes USING btree (pagina_url);


--
-- Name: idx_anotacoes_usuario; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_anotacoes_usuario ON appestudo.anotacoes USING btree (usuario_id);


--
-- Name: idx_conteudo_edital_cap_pattern; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_cap_pattern ON appestudo.conteudo_edital USING btree (capitulo text_pattern_ops);


--
-- Name: idx_conteudo_edital_hierarchy; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_hierarchy ON appestudo.conteudo_edital USING btree (materia_id, edital_id, ((string_to_array((capitulo)::text, '.'::text))::integer[]));


--
-- Name: idx_conteudo_edital_hierarquia; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_hierarquia ON appestudo.conteudo_edital USING btree (materia_id, edital_id, capitulo);


--
-- Name: idx_conteudo_edital_materia_capitulo; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_materia_capitulo ON appestudo.conteudo_edital USING btree (materia_id, capitulo);


--
-- Name: idx_conteudo_edital_nivel_1; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_nivel_1 ON appestudo.conteudo_edital USING btree (materia_id, capitulo) WHERE ((capitulo)::text ~ '^[0-9]+$'::text);


--
-- Name: idx_conteudo_edital_nivel_2; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_nivel_2 ON appestudo.conteudo_edital USING btree (materia_id, capitulo) WHERE ((capitulo)::text ~ '^[0-9]+\.[0-9]+$'::text);


--
-- Name: idx_conteudo_edital_nivel_3; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_nivel_3 ON appestudo.conteudo_edital USING btree (materia_id, capitulo) WHERE ((capitulo)::text ~ '^[0-9]+\.[0-9]+\.[0-9]+$'::text);


--
-- Name: idx_conteudo_edital_ordem; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_ordem ON appestudo.conteudo_edital USING btree (materia_id, ordem);


--
-- Name: idx_conteudo_edital_sorting; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_conteudo_edital_sorting ON appestudo.conteudo_edital USING btree (materia_id, capitulo text_pattern_ops);


--
-- Name: idx_flashcard_categories_user; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_flashcard_categories_user ON appestudo.flashcard_categories USING btree (usuario_id);


--
-- Name: idx_flashcard_deck_user; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_flashcard_deck_user ON appestudo.flashcard_decks USING btree (usuario_id);


--
-- Name: idx_flashcard_progress_next_review; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_flashcard_progress_next_review ON appestudo.flashcard_progress USING btree (proxima_revisao);


--
-- Name: idx_flashcard_progress_user; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_flashcard_progress_user ON appestudo.flashcard_progress USING btree (usuario_id);


--
-- Name: idx_flashcard_topics_deck; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_flashcard_topics_deck ON appestudo.flashcard_topics USING btree (deck_id);


--
-- Name: idx_kanban_etiquetas_usuario; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_kanban_etiquetas_usuario ON appestudo.kanban_etiquetas USING btree (usuario_id);


--
-- Name: idx_kanban_tarefas_status; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_kanban_tarefas_status ON appestudo.kanban_tarefas USING btree (status);


--
-- Name: idx_kanban_tarefas_usuario; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_kanban_tarefas_usuario ON appestudo.kanban_tarefas USING btree (usuario_id);


--
-- Name: idx_marcacoes_composto; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_marcacoes_composto ON appestudo.marcacoes USING btree (usuario_id, pagina_url, elemento_id);


--
-- Name: idx_marcacoes_elemento; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_marcacoes_elemento ON appestudo.marcacoes USING btree (elemento_id);


--
-- Name: idx_marcacoes_pagina; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_marcacoes_pagina ON appestudo.marcacoes USING btree (pagina_url);


--
-- Name: idx_marcacoes_usuario; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_marcacoes_usuario ON appestudo.marcacoes USING btree (usuario_id);


--
-- Name: idx_medicamento_status; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_medicamento_status ON appestudo.medicamentos USING btree (status);


--
-- Name: idx_notificacoes_data_criacao; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_notificacoes_data_criacao ON appestudo.notificacoes USING btree (data_criacao);


--
-- Name: idx_notificacoes_data_exp; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_notificacoes_data_exp ON appestudo.notificacoes USING btree (data_expiracao);


--
-- Name: idx_notificacoes_global; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_notificacoes_global ON appestudo.notificacoes USING btree (is_global);


--
-- Name: idx_notificacoes_status; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_notificacoes_status ON appestudo.notificacoes USING btree (status);


--
-- Name: idx_notificacoes_usuarios; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_notificacoes_usuarios ON appestudo.notificacoes_usuarios USING btree (usuario_id);


--
-- Name: idx_provas_status; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_provas_status ON appestudo.provas USING btree (usuario_id, status);


--
-- Name: idx_provas_status_data; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_provas_status_data ON appestudo.provas USING btree (usuario_id, status, data_prova);


--
-- Name: idx_registros_confirmado; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_registros_confirmado ON appestudo.registros_uso USING btree (confirmado);


--
-- Name: idx_registros_data_hora; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_registros_data_hora ON appestudo.registros_uso USING btree (data_hora);


--
-- Name: idx_registros_medicamento; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_registros_medicamento ON appestudo.registros_uso USING btree (medicamento_id);


--
-- Name: idx_revisoes_proxima; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_revisoes_proxima ON appestudo.revisoes USING btree (proxima_revisao);


--
-- Name: idx_revisoes_status; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_revisoes_status ON appestudo.revisoes USING btree (status_revisao);


--
-- Name: idx_revisoes_usuario; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_revisoes_usuario ON appestudo.revisoes USING btree (usuario_id);


--
-- Name: idx_unique_active_subscription; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE UNIQUE INDEX idx_unique_active_subscription ON appestudo.assinaturas USING btree (usuario_id, modulo) WHERE (status = true);


--
-- Name: idx_usuario_conteudo_composto; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_usuario_conteudo_composto ON appestudo.usuario_conteudo USING btree (usuario_id, status, conteudo_id);


--
-- Name: idx_usuario_conteudo_lookup; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_usuario_conteudo_lookup ON appestudo.usuario_conteudo USING btree (usuario_id, status, conteudo_id);


--
-- Name: idx_usuario_conteudo_status; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_usuario_conteudo_status ON appestudo.usuario_conteudo USING btree (usuario_id, status);


--
-- Name: idx_usuario_idusuario; Type: INDEX; Schema: appestudo; Owner: appestudo
--

CREATE INDEX idx_usuario_idusuario ON appestudo.medicamento USING btree (usuario_idusuario);


--
-- Name: kanban_tarefas trg_atualizar_data_conclusao; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER trg_atualizar_data_conclusao BEFORE UPDATE ON appestudo.kanban_tarefas FOR EACH ROW EXECUTE FUNCTION appestudo.atualizar_data_conclusao();


--
-- Name: usuario_conteudo trig_gerar_revisao; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER trig_gerar_revisao AFTER UPDATE ON appestudo.usuario_conteudo FOR EACH ROW EXECUTE FUNCTION appestudo.gerar_revisao_automatica();


--
-- Name: revisoes trig_proxima_revisao; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER trig_proxima_revisao AFTER UPDATE ON appestudo.revisoes FOR EACH ROW WHEN (((new.status_revisao)::text = 'concluida'::text)) EXECUTE FUNCTION appestudo.gerar_proxima_revisao();


--
-- Name: assinaturas trigger_atualizar_acesso_modulos; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER trigger_atualizar_acesso_modulos AFTER INSERT OR UPDATE ON appestudo.assinaturas FOR EACH ROW EXECUTE FUNCTION appestudo.atualizar_acesso_modulos();


--
-- Name: anotacoes update_anotacoes_data_atualizacao; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_anotacoes_data_atualizacao BEFORE UPDATE ON appestudo.anotacoes FOR EACH ROW EXECUTE FUNCTION appestudo.update_data_atualizacao_column();


--
-- Name: assinaturas update_assinaturas_updated_at; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_assinaturas_updated_at BEFORE UPDATE ON appestudo.assinaturas FOR EACH ROW EXECUTE FUNCTION appestudo.update_updated_at_column();


--
-- Name: flashcard_categories update_flashcard_categories_updated_at; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_flashcard_categories_updated_at BEFORE UPDATE ON appestudo.flashcard_categories FOR EACH ROW EXECUTE FUNCTION appestudo.update_updated_at_column();


--
-- Name: flashcard_decks update_flashcard_decks_updated_at; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_flashcard_decks_updated_at BEFORE UPDATE ON appestudo.flashcard_decks FOR EACH ROW EXECUTE FUNCTION appestudo.update_updated_at_column();


--
-- Name: flashcard_mindmaps update_flashcard_mindmaps_updated_at; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_flashcard_mindmaps_updated_at BEFORE UPDATE ON appestudo.flashcard_mindmaps FOR EACH ROW EXECUTE FUNCTION appestudo.update_updated_at_column();


--
-- Name: flashcard_progress update_flashcard_progress_updated_at; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_flashcard_progress_updated_at BEFORE UPDATE ON appestudo.flashcard_progress FOR EACH ROW EXECUTE FUNCTION appestudo.update_updated_at_column();


--
-- Name: flashcards update_flashcards_updated_at; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_flashcards_updated_at BEFORE UPDATE ON appestudo.flashcards FOR EACH ROW EXECUTE FUNCTION appestudo.update_updated_at_column();


--
-- Name: marcacoes update_marcacoes_data_atualizacao; Type: TRIGGER; Schema: appestudo; Owner: appestudo
--

CREATE TRIGGER update_marcacoes_data_atualizacao BEFORE UPDATE ON appestudo.marcacoes FOR EACH ROW EXECUTE FUNCTION appestudo.update_data_atualizacao_column();


--
-- Name: ajustes_plano ajustes_plano_prova_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.ajustes_plano
    ADD CONSTRAINT ajustes_plano_prova_id_fkey FOREIGN KEY (prova_id) REFERENCES appestudo.provas(id);


--
-- Name: assinaturas assinaturas_plano_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.assinaturas
    ADD CONSTRAINT assinaturas_plano_id_fkey FOREIGN KEY (plano_id) REFERENCES appestudo.planos(id);


--
-- Name: assinaturas assinaturas_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.assinaturas
    ADD CONSTRAINT assinaturas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: conteudo_edital conteudo_edital_edital_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.conteudo_edital
    ADD CONSTRAINT conteudo_edital_edital_id_fkey FOREIGN KEY (edital_id) REFERENCES appestudo.edital(id_edital) ON DELETE CASCADE;


--
-- Name: conteudo_edital conteudo_edital_materia_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.conteudo_edital
    ADD CONSTRAINT conteudo_edital_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria) ON DELETE CASCADE;


--
-- Name: agenda fk_agenda_usuario1; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.agenda
    ADD CONSTRAINT fk_agenda_usuario1 FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);


--
-- Name: medicamento fk_medicamento_usuario1; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.medicamento
    ADD CONSTRAINT fk_medicamento_usuario1 FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);


--
-- Name: planejamento_materia fk_planejamento_materia_materia; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento_materia
    ADD CONSTRAINT fk_planejamento_materia_materia FOREIGN KEY (materia_idmateria) REFERENCES appestudo.materia(idmateria) ON DELETE CASCADE;


--
-- Name: planejamento_materia fk_planejamento_materia_planejamento; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento_materia
    ADD CONSTRAINT fk_planejamento_materia_planejamento FOREIGN KEY (planejamento_idplanejamento) REFERENCES appestudo.planejamento(idplanejamento) ON DELETE CASCADE;


--
-- Name: planejamento fk_planejamento_usuario1; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.planejamento
    ADD CONSTRAINT fk_planejamento_usuario1 FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);


--
-- Name: flashcard_categories flashcard_categories_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_categories
    ADD CONSTRAINT flashcard_categories_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE;


--
-- Name: flashcard_deck_association flashcard_deck_association_deck_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_deck_association
    ADD CONSTRAINT flashcard_deck_association_deck_id_fkey FOREIGN KEY (deck_id) REFERENCES appestudo.flashcard_decks(id) ON DELETE CASCADE;


--
-- Name: flashcard_deck_association flashcard_deck_association_flashcard_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_deck_association
    ADD CONSTRAINT flashcard_deck_association_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id) ON DELETE CASCADE;


--
-- Name: flashcard_decks flashcard_decks_category_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_decks
    ADD CONSTRAINT flashcard_decks_category_id_fkey FOREIGN KEY (category_id) REFERENCES appestudo.flashcard_categories(id) ON DELETE SET NULL;


--
-- Name: flashcard_decks flashcard_decks_materia_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_decks
    ADD CONSTRAINT flashcard_decks_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria);


--
-- Name: flashcard_decks flashcard_decks_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_decks
    ADD CONSTRAINT flashcard_decks_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE;


--
-- Name: flashcard_materias flashcard_materias_flashcard_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_materias
    ADD CONSTRAINT flashcard_materias_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id) ON DELETE CASCADE;


--
-- Name: flashcard_materias flashcard_materias_materia_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_materias
    ADD CONSTRAINT flashcard_materias_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria) ON DELETE CASCADE;


--
-- Name: flashcard_mindmaps flashcard_mindmaps_flashcard_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_mindmaps
    ADD CONSTRAINT flashcard_mindmaps_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id) ON DELETE CASCADE;


--
-- Name: flashcard_progress flashcard_progress_flashcard_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_progress
    ADD CONSTRAINT flashcard_progress_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id) ON DELETE CASCADE;


--
-- Name: flashcard_progress flashcard_progress_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_progress
    ADD CONSTRAINT flashcard_progress_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE;


--
-- Name: flashcard_review_history flashcard_review_history_progress_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_review_history
    ADD CONSTRAINT flashcard_review_history_progress_id_fkey FOREIGN KEY (progress_id) REFERENCES appestudo.flashcard_progress(id) ON DELETE CASCADE;


--
-- Name: flashcard_topic_association flashcard_topic_association_flashcard_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_topic_association
    ADD CONSTRAINT flashcard_topic_association_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id) ON DELETE CASCADE;


--
-- Name: flashcard_topic_association flashcard_topic_association_topic_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_topic_association
    ADD CONSTRAINT flashcard_topic_association_topic_id_fkey FOREIGN KEY (topic_id) REFERENCES appestudo.flashcard_topics(id) ON DELETE CASCADE;


--
-- Name: flashcard_topics flashcard_topics_deck_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.flashcard_topics
    ADD CONSTRAINT flashcard_topics_deck_id_fkey FOREIGN KEY (deck_id) REFERENCES appestudo.flashcard_decks(id) ON DELETE CASCADE;


--
-- Name: forum_respostas forum_respostas_topico_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_respostas
    ADD CONSTRAINT forum_respostas_topico_id_fkey FOREIGN KEY (topico_id) REFERENCES appestudo.forum_topicos(id);


--
-- Name: forum_respostas forum_respostas_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_respostas
    ADD CONSTRAINT forum_respostas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: forum_topicos forum_topicos_categoria_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_topicos
    ADD CONSTRAINT forum_topicos_categoria_id_fkey FOREIGN KEY (categoria_id) REFERENCES appestudo.forum_categorias(id);


--
-- Name: forum_topicos forum_topicos_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_topicos
    ADD CONSTRAINT forum_topicos_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: forum_votos forum_votos_resposta_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_votos
    ADD CONSTRAINT forum_votos_resposta_id_fkey FOREIGN KEY (resposta_id) REFERENCES appestudo.forum_respostas(id);


--
-- Name: forum_votos forum_votos_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.forum_votos
    ADD CONSTRAINT forum_votos_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: kanban_etiquetas kanban_etiquetas_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_etiquetas
    ADD CONSTRAINT kanban_etiquetas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: kanban_tarefa_etiqueta kanban_tarefa_etiqueta_etiqueta_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_tarefa_etiqueta
    ADD CONSTRAINT kanban_tarefa_etiqueta_etiqueta_id_fkey FOREIGN KEY (etiqueta_id) REFERENCES appestudo.kanban_etiquetas(id) ON DELETE CASCADE;


--
-- Name: kanban_tarefa_etiqueta kanban_tarefa_etiqueta_tarefa_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_tarefa_etiqueta
    ADD CONSTRAINT kanban_tarefa_etiqueta_tarefa_id_fkey FOREIGN KEY (tarefa_id) REFERENCES appestudo.kanban_tarefas(id) ON DELETE CASCADE;


--
-- Name: kanban_tarefas kanban_tarefas_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.kanban_tarefas
    ADD CONSTRAINT kanban_tarefas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: log_seguranca log_seguranca_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.log_seguranca
    ADD CONSTRAINT log_seguranca_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: notificacoes notificacoes_criado_por_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes
    ADD CONSTRAINT notificacoes_criado_por_fkey FOREIGN KEY (criado_por) REFERENCES appestudo.usuario(idusuario);


--
-- Name: notificacoes_usuarios notificacoes_usuarios_notificacao_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes_usuarios
    ADD CONSTRAINT notificacoes_usuarios_notificacao_id_fkey FOREIGN KEY (notificacao_id) REFERENCES appestudo.notificacoes(id) ON DELETE CASCADE;


--
-- Name: notificacoes_usuarios notificacoes_usuarios_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.notificacoes_usuarios
    ADD CONSTRAINT notificacoes_usuarios_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: pesos_materias pesos_materias_materia_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.pesos_materias
    ADD CONSTRAINT pesos_materias_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria);


--
-- Name: pesos_materias pesos_materias_prova_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.pesos_materias
    ADD CONSTRAINT pesos_materias_prova_id_fkey FOREIGN KEY (prova_id) REFERENCES appestudo.provas(id);


--
-- Name: registros_uso registros_uso_medicamento_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.registros_uso
    ADD CONSTRAINT registros_uso_medicamento_id_fkey FOREIGN KEY (medicamento_id) REFERENCES appestudo.medicamentos(id);


--
-- Name: revisoes revisoes_conteudo_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.revisoes
    ADD CONSTRAINT revisoes_conteudo_id_fkey FOREIGN KEY (conteudo_id) REFERENCES appestudo.conteudo_edital(id_conteudo);


--
-- Name: revisoes revisoes_edital_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.revisoes
    ADD CONSTRAINT revisoes_edital_id_fkey FOREIGN KEY (edital_id) REFERENCES appestudo.edital(id_edital);


--
-- Name: usuario_conteudo usuario_conteudo_conteudo_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_conteudo
    ADD CONSTRAINT usuario_conteudo_conteudo_id_fkey FOREIGN KEY (conteudo_id) REFERENCES appestudo.conteudo_edital(id_conteudo) ON DELETE CASCADE;


--
-- Name: usuario_conteudo usuario_conteudo_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_conteudo
    ADD CONSTRAINT usuario_conteudo_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE;


--
-- Name: usuario_dias_estudo usuario_dias_estudo_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_dias_estudo
    ADD CONSTRAINT usuario_dias_estudo_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);


--
-- Name: usuario_edital usuario_edital_edital_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_edital
    ADD CONSTRAINT usuario_edital_edital_id_fkey FOREIGN KEY (edital_id) REFERENCES appestudo.edital(id_edital) ON DELETE CASCADE;


--
-- Name: usuario_edital usuario_edital_usuario_id_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_edital
    ADD CONSTRAINT usuario_edital_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE;


--
-- Name: usuario_has_curso usuario_has_curso_curso_idcurso_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_has_curso
    ADD CONSTRAINT usuario_has_curso_curso_idcurso_fkey FOREIGN KEY (curso_idcurso) REFERENCES appestudo.curso(idcurso);


--
-- Name: usuario_has_curso usuario_has_curso_usuario_idusuario_fkey; Type: FK CONSTRAINT; Schema: appestudo; Owner: appestudo
--

ALTER TABLE ONLY appestudo.usuario_has_curso
    ADD CONSTRAINT usuario_has_curso_usuario_idusuario_fkey FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO PUBLIC;

-- Primeiro, remova a constraint existente (o nome pode variar ligeiramente se foi auto-gerado, verifique o nome exato no seu DB)
ALTER TABLE appestudo.revisoes
DROP CONSTRAINT IF EXISTS revisoes_conteudo_id_fkey; -- Use IF EXISTS para segurança

-- Depois, adicione a nova constraint com ON DELETE CASCADE
ALTER TABLE appestudo.revisoes
ADD CONSTRAINT revisoes_conteudo_id_fkey
    FOREIGN KEY (conteudo_id)
    REFERENCES appestudo.conteudo_edital(id_conteudo)
    ON DELETE CASCADE;
    
--
-- PostgreSQL database dump complete
--

