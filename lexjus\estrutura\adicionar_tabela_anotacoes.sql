-- Adicionar tabela de anotações para o LexJus VOID
-- Execute este script para adicionar a funcionalidade de anotações

CREATE TABLE IF NOT EXISTS appestudo.lexjus_anotacoes (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    artigo_numero VARCHAR(10) NOT NULL,
    texto TEXT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Chave estrangeira para o usuário
    CONSTRAINT fk_anotacoes_usuario 
        FOREIGN KEY (usuario_id) 
        REFERENCES appestudo.usuarios(idusuario) 
        ON DELETE CASCADE,
    
    -- Índices para melhor performance
    CONSTRAINT idx_anotacoes_usuario_artigo 
        UNIQUE (usuario_id, artigo_numero, id)
);

-- Criar índices para otimizar consultas
CREATE INDEX IF NOT EXISTS idx_lexjus_anotacoes_usuario_id 
    ON appestudo.lexjus_anotacoes(usuario_id);

CREATE INDEX IF NOT EXISTS idx_lexjus_anotacoes_artigo_numero 
    ON appestudo.lexjus_anotacoes(artigo_numero);

CREATE INDEX IF NOT EXISTS idx_lexjus_anotacoes_data_atualizacao 
    ON appestudo.lexjus_anotacoes(data_atualizacao);

-- Trigger para atualizar automaticamente data_atualizacao
CREATE OR REPLACE FUNCTION update_anotacoes_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.data_atualizacao = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_anotacoes_timestamp
    BEFORE UPDATE ON appestudo.lexjus_anotacoes
    FOR EACH ROW
    EXECUTE FUNCTION update_anotacoes_timestamp();

-- Comentários para documentação
COMMENT ON TABLE appestudo.lexjus_anotacoes IS 'Tabela para armazenar anotações dos usuários sobre artigos da Constituição';
COMMENT ON COLUMN appestudo.lexjus_anotacoes.id IS 'Identificador único da anotação';
COMMENT ON COLUMN appestudo.lexjus_anotacoes.usuario_id IS 'ID do usuário que criou a anotação';
COMMENT ON COLUMN appestudo.lexjus_anotacoes.artigo_numero IS 'Número do artigo da Constituição';
COMMENT ON COLUMN appestudo.lexjus_anotacoes.texto IS 'Conteúdo da anotação em HTML';
COMMENT ON COLUMN appestudo.lexjus_anotacoes.data_criacao IS 'Data e hora de criação da anotação';
COMMENT ON COLUMN appestudo.lexjus_anotacoes.data_atualizacao IS 'Data e hora da última atualização da anotação';
