#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON gerado
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Procurar artigo 4
art4 = [a for a in data['artigos'] if a['numero'] == '4']

if art4:
    print("=== ARTIGO 4 ENCONTRADO ===")
    artigo = art4[0]
    print(f"Número: {artigo['numero']}")
    print(f"Caput: {artigo['caput']}")
    
    # Mostrar detalhes dos incisos
    if artigo['incisos']:
        print("\n=== INCISOS ===")
        for inciso, dados in artigo['incisos'].items():
            print(f"{inciso}: {dados['texto']}")
    
    # Mostrar detalhes dos parágrafos
    if artigo['paragrafos']:
        print("\n=== PARÁGRAFOS ===")
        for paragrafo, dados in artigo['paragrafos'].items():
            print(f"{paragrafo}: {dados['texto']}")
            
else:
    print("=== ARTIGO 4 NÃO ENCONTRADO ===")
