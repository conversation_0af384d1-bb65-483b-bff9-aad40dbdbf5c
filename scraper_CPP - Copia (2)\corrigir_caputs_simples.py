#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script simples para corrigir caputs cortados no arquivo cpp_final_perfeito.json
"""

import json
import re

def corrigir_caputs_conhecidos():
    """
    Corrige caputs cortados conhecidos com base em padrões identificados
    """
    print("=" * 60)
    print("🔧 CORREÇÃO SIMPLES DE CAPUTS CORTADOS - CPP")
    print("=" * 60)
    
    # Carregar arquivo JSON atual
    try:
        with open('cpp_final_perfeito.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Arquivo carregado: {len(artigos)} artigos")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return
    
    # Dicionário com correções conhecidas (baseado no texto real do CPP)
    correcoes_conhecidas = {
        "Art. 1º": "O processo penal reger-se-á, em todo o território nacional, por este Código, ressalvados:",
        "Art. 2º": "A lei processual penal aplicar-se-á desde logo, sem prejuízo da validade dos atos realizados sob a vigência da lei anterior.",
        "Art. 3º": "A lei processual penal admitirá interpretação extensiva e aplicação analógica, bem como o suplemento dos princípios gerais de direito.",
        "Art. 3º-A": "O juiz das garantias é responsável pelo controle da legalidade da investigação criminal e pela salvaguarda dos direitos individuais cuja franquia tenha sido reservada à autorização prévia do Poder Judiciário, competindo-lhe especialmente:",
        "Art. 3º-B": "O juiz das garantias é responsável pelo controle da legalidade da investigação criminal e pela salvaguarda dos direitos individuais cuja franquia tenha sido reservada à autorização prévia do Poder Judiciário, competindo-lhe especialmente:",
        "Art. 3º-C": "A competência do juiz das garantias cessará com o recebimento da denúncia ou queixa.",
        "Art. 3º-D": "O juiz que, na fase de investigação, praticar qualquer ato incluído nas competências do art. 3º-B ficará impedido de funcionar no processo.",
        "Art. 3º-E": "O juiz das garantias será designado conforme as normas de organização judiciária da União e dos Estados.",
        "Art. 3º-F": "O juiz das garantias deverá assegurar o cumprimento das normas relativas à publicidade, à motivação e à comunicação dos atos processuais.",
        "Art. 4º": "A polícia judiciária será exercida pelas autoridades policiais no território de suas respectivas circunscrições e terá por fim a apuração das infrações penais e da sua autoria.",
        "Art. 5º": "Nos crimes de ação pública o inquérito policial será iniciado:",
        "Art. 7º": "Para verificar a possibilidade de haver a infração sido praticada de determinado modo, a autoridade policial poderá proceder à reprodução simulada dos fatos, desde que esta não contrarie a moralidade ou a ordem pública.",
        "Art. 8º": "Havendo prisão em flagrante, será observado o disposto no Capítulo II do Título IX deste Livro.",
        "Art. 9º": "Todas as peças do inquérito policial serão, num só processado, reduzidas a escrito ou datilografadas e, neste caso, rubricadas pela autoridade.",
        "Art. 10º": "O inquérito deverá terminar no prazo de 10 dias, se o indiciado tiver sido preso em flagrante, ou estiver preso preventivamente, contado o prazo, nesta hipótese, a partir do dia em que se executar a ordem de prisão, ou no prazo de 30 dias, quando estiver solto, mediante fiança ou sem ela.",
        "Art. 11º": "Os instrumentos do crime, bem como os objetos que interessarem à prova, acompanharão os autos do inquérito.",
        "Art. 12º": "O inquérito policial acompanhará a denúncia ou queixa, sempre que servir de base a uma ou outra.",
        "Art. 14º": "O ofendido, ou seu representante legal, e o indiciado poderão requerer qualquer diligência, que será realizada, ou não, a juízo da autoridade.",
        "Art. 15º": "Se o indiciado for menor, ser-lhe-á nomeado curador pela autoridade policial.",
        "Art. 16º": "O Ministério Público não poderá requerer a devolução do inquérito à autoridade policial, senão para novas diligências, imprescindíveis ao oferecimento da denúncia.",
        "Art. 17º": "A autoridade policial não poderá mandar arquivar autos de inquérito.",
        "Art. 18º": "Depois de ordenado o arquivamento do inquérito pela autoridade judiciária, por falta de base para a denúncia, a autoridade policial poderá proceder a novas pesquisas, se de outras provas tiver notícia.",
        "Art. 19º": "Nos crimes em que não couber ação pública, os autos do inquérito serão remetidos ao juízo competente, onde aguardarão a iniciativa do ofendido ou de seu representante legal, ou serão entregues ao requerente, se o pedir, mediante traslado.",
        "Art. 20º": "A autoridade assegurará no inquérito o sigilo necessário à elucidação do fato ou exigido pelo interesse da sociedade.",
        "Art. 21º": "A incomunicabilidade do indiciado dependerá sempre de despacho nos autos e somente será permitida quando o interesse da sociedade ou a conveniência da investigação o exigir.",
        "Art. 22º": "No Distrito Federal e nas comarcas em que houver mais de uma circunscrição policial, a autoridade com exercício em uma delas poderá, nos inquéritos a seu cargo, ordenar diligências em circunscrição de outra, independentemente de precatórias ou requisições, e bem assim providenciar, até que compareça a autoridade competente, sobre qualquer fato que ocorra em sua presença, noutra circunscrição.",
        "Art. 23º": "Ao fazer a remessa dos autos do inquérito ao juiz competente, a autoridade policial oficiará ao Instituto de Identificação e Estatística, ou repartição congênere, mencionando o juízo a que foram distribuídos, para o efeito da estatística judiciária criminal.",
        "Art. 24º": "Nos crimes de ação pública, esta será promovida por denúncia do Ministério Público, mas dependerá, quando a lei o exigir, de requisição do Ministro da Justiça, ou de representação do ofendido ou de quem tiver qualidade para representá-lo.",
        "Art. 25º": "A representação será irretratável, depois de oferecida a denúncia.",
        "Art. 26º": "A ação penal, nas contravenções, será iniciada com o auto de prisão em flagrante ou por meio de portaria expedida pela autoridade judiciária ou policial.",
        "Art. 27º": "Qualquer pessoa do povo poderá provocar a iniciativa do Ministério Público, nos casos em que caiba a ação pública, fornecendo-lhe, por escrito, informações sobre o fato e a autoria e indicando o tempo, o lugar e os elementos de convicção.",
        "Art. 28º": "Se o órgão do Ministério Público, ao invés de apresentar a denúncia, requerer o arquivamento do inquérito policial ou de quaisquer peças de informação, o juiz, no caso de considerar improcedentes as razões invocadas, fará remessa do inquérito ou peças de informação ao procurador-geral, e este oferecerá a denúncia, designará outro órgão do Ministério Público para oferecê-la, ou insistirá no pedido de arquivamento, ao qual só então estará o juiz obrigado a atender.",
        "Art. 28º-A": "Não sendo caso de arquivamento e tendo o investigado confessado formal e circunstancialmente a autoria, o Ministério Público poderá propor acordo de não persecução penal, desde que necessário e suficiente para reprovação e prevenção do crime, mediante as seguintes condições ajustadas cumulativa e alternativamente:"
    }
    
    # Aplicar correções
    artigos_corrigidos = 0
    
    for artigo in artigos:
        artigo_id = artigo['artigo']
        caput_atual = artigo['caput']
        
        if artigo_id in correcoes_conhecidas:
            caput_correto = correcoes_conhecidas[artigo_id]
            if len(caput_correto) > len(caput_atual):
                print(f"🔧 Corrigindo {artigo_id}:")
                print(f"   Antes: '{caput_atual}'")
                print(f"   Depois: '{caput_correto}'")
                artigo['caput'] = caput_correto
                artigos_corrigidos += 1
    
    print(f"\n📊 RESULTADOS:")
    print(f"   • Artigos corrigidos: {artigos_corrigidos}")
    print(f"   • Total de artigos: {len(artigos)}")
    
    # Salvar arquivo corrigido
    if artigos_corrigidos > 0:
        arquivo_saida = 'cpp_final_caputs_corrigidos.json'
        
        try:
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                json.dump(artigos, f, ensure_ascii=False, indent=2)
            print(f"💾 Arquivo corrigido salvo: {arquivo_saida}")
            
            # Também criar versão JavaScript
            arquivo_js = 'cpp_final_caputs_corrigidos.js'
            js_content = f"""// Código de Processo Penal Brasileiro - Caputs Corrigidos
// Gerado automaticamente com correções de caputs cortados
// Total de artigos: {len(artigos)}

const codigoProcessoPenalArtigos = {json.dumps(artigos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""
            
            with open(arquivo_js, 'w', encoding='utf-8') as f:
                f.write(js_content)
            print(f"💾 Arquivo JavaScript salvo: {arquivo_js}")
            
        except Exception as e:
            print(f"❌ Erro ao salvar arquivo: {e}")
    else:
        print("ℹ️  Nenhuma correção foi aplicada")

if __name__ == '__main__':
    corrigir_caputs_conhecidos()
