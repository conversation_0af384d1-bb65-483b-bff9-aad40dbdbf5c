#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON gerado
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Verificar alguns artigos que tiveram incisos detectados
artigos_teste = ['1134', '1187', '1353', '1358']

for num in artigos_teste:
    artigo = [a for a in data['artigos'] if a['numero'] == num]
    
    if artigo:
        art = artigo[0]
        print(f"=== ARTIGO {num} ===")
        print(f"Caput: {art['caput'][:80]}...")
        print(f"Incisos: {len(art['incisos'])} - {list(art['incisos'].keys())}")
        print(f"Parágrafos: {len(art['paragrafos'])} - {list(art['paragrafos'].keys())}")
        
        # Mostrar alguns incisos se existirem
        if art['incisos']:
            print("Primeiros incisos:")
            for i, (inciso, dados) in enumerate(art['incisos'].items()):
                if i < 3:  # Mostrar apenas os 3 primeiros
                    print(f"  {inciso}: {dados['texto'][:60]}...")
        print()
    else:
        print(f"=== ARTIGO {num} NÃO ENCONTRADO ===")
        print()

print(f"Total de artigos extraídos: {len(data['artigos'])}")
