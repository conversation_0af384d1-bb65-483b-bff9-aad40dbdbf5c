#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script simplificado para corrigir o Código de Processo Penal
Remove apenas duplicações mantendo estrutura original
"""

import json
import re
from datetime import datetime

def limpar_caput(caput):
    """
    Limpa o caput removendo caracteres estranhos
    """
    if not caput:
        return ""
    
    # Remover caracteres estranhos no início
    caput = re.sub(r'^[ºo°\.\s]*', '', caput)
    
    # Remover espaços múltiplos
    caput = re.sub(r'\s+', ' ', caput)
    
    return caput.strip()

def extrair_numero_artigo(artigo_id):
    """
    Extrai o número do artigo para ordenação
    """
    match = re.search(r'Art\.\s*(\d+)', artigo_id)
    return int(match.group(1)) if match else 0

def simplificar_cpp():
    """
    Função principal de simplificação
    """
    print("=" * 60)
    print("🔧 SIMPLIFICAÇÃO DO CÓDIGO DE PROCESSO PENAL")
    print("=" * 60)
    
    # Carregar arquivo JSON original limpo
    try:
        with open('codigo_processo_penal_limpo.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Carregados {len(artigos)} artigos do arquivo JSON")
    except FileNotFoundError:
        print("❌ Arquivo codigo_processo_penal_limpo.json não encontrado!")
        return
    
    print(f"📊 Processando {len(artigos)} artigos...")
    
    # Remover duplicações simples
    artigos_unicos = {}
    
    for artigo in artigos:
        artigo_id = artigo["artigo"]
        
        # Limpar caput
        artigo["caput"] = limpar_caput(artigo["caput"])
        
        # Se já existe este artigo, mesclar conteúdo
        if artigo_id in artigos_unicos:
            existente = artigos_unicos[artigo_id]
            
            # Mesclar caput se o existente estiver vazio ou muito curto
            if len(existente["caput"]) < len(artigo["caput"]):
                existente["caput"] = artigo["caput"]
            
            # Mesclar incisos (evitar duplicações)
            for inciso in artigo["incisos"]:
                if inciso not in existente["incisos"]:
                    existente["incisos"].append(inciso)
            
            # Mesclar parágrafos (evitar duplicações)
            for paragrafo in artigo["paragrafos_numerados"]:
                existe = any(p["numero"] == paragrafo["numero"] for p in existente["paragrafos_numerados"])
                if not existe:
                    existente["paragrafos_numerados"].append(paragrafo)
            
            # Mesclar parágrafo único
            if not existente["paragrafo_unico"] and artigo["paragrafo_unico"]:
                existente["paragrafo_unico"] = artigo["paragrafo_unico"]
        else:
            # Adicionar artigo novo
            artigos_unicos[artigo_id] = artigo
    
    # Converter de volta para lista
    artigos_finais = list(artigos_unicos.values())
    
    # Ordenar por número do artigo
    artigos_finais.sort(key=lambda x: extrair_numero_artigo(x["artigo"]))
    
    print(f"   Artigos após remoção de duplicações: {len(artigos_finais)}")
    
    # Filtrar artigos com caput vazio ou muito curto
    artigos_validos = []
    for artigo in artigos_finais:
        # Manter artigo se tem caput válido OU tem incisos/parágrafos
        if (artigo["caput"] and len(artigo["caput"]) > 5) or \
           artigo["incisos"] or \
           artigo["paragrafos_numerados"] or \
           artigo["paragrafo_unico"]:
            artigos_validos.append(artigo)
    
    print(f"   Artigos válidos após filtragem: {len(artigos_validos)}")
    
    # Salvar arquivo simplificado
    nome_arquivo_json = 'codigo_processo_penal_simplificado.json'
    nome_arquivo_js = 'codigo_processo_penal_simplificado.js'
    
    try:
        # Salvar JSON
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos_validos, f, ensure_ascii=False, indent=2)
        print(f"✅ Arquivo JSON simplificado salvo: {nome_arquivo_json}")
        
        # Gerar JavaScript
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        js_content = f"""// Código de Processo Penal Brasileiro - Versão Simplificada
// Gerado em: {timestamp}
// Total de artigos: {len(artigos_validos)}
// Processado para remover duplicações e manter estrutura limpa

const codigoProcessoPenalArtigos = {json.dumps(artigos_validos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""
        
        with open(nome_arquivo_js, 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"✅ Arquivo JavaScript simplificado salvo: {nome_arquivo_js}")
        
        print(f"\n📊 RESULTADO FINAL:")
        print(f"   • Total de artigos processados: {len(artigos_validos)}")
        if artigos_validos:
            print(f"   • Primeiro artigo: {artigos_validos[0]['artigo']}")
            print(f"   • Último artigo: {artigos_validos[-1]['artigo']}")
        
        print(f"\n✅ SIMPLIFICAÇÃO CONCLUÍDA COM SUCESSO!")
        print(f"   📄 Arquivo JSON simplificado: {nome_arquivo_json}")
        print(f"   📄 Arquivo JS simplificado: {nome_arquivo_js}")
        
    except Exception as e:
        print(f"❌ Erro ao salvar arquivos: {e}")

if __name__ == '__main__':
    simplificar_cpp()
