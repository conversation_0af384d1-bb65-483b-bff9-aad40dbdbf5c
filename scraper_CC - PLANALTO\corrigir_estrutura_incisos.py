#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT PARA CORRIGIR A ESTRUTURA DOS INCISOS NO CÓDIGO CIVIL
Corrige incisos que pertencem ao parágrafo único mas estão sendo atribuídos ao caput
"""

import json
import re

def corrigir_estrutura_incisos():
    """Corrige a estrutura dos incisos baseado na análise do HTML"""
    
    # Carregar o arquivo JSON
    with open('cc_final_perfeito_corrigido.json', 'r', encoding='utf-8') as f:
        artigos = json.load(f)
    
    # Dicionário com as correções específicas baseadas na análise do HTML
    correcoes_estrutura = {
        "Art. 5º": {
            "caput": "A menoridade cessa aos dezoito anos completos, quando a pessoa fica habilitada à prática de todos os atos da vida civil.",
            "incisos": [],  # Incisos pertencem ao parágrafo único
            "paragrafo_unico": {
                "texto": "Cessará, para os menores, a incapacidade:",
                "incisos": [
                    "I - pela concessão dos pais, ou de um deles na falta do outro, mediante instrumento público, independentemente de homologação judicial, ou por sentença do juiz, ouvido o tutor, se o menor tiver dezesseis anos completos;",
                    "II - pelo casamento;",
                    "III - pelo exercício de emprego público efetivo;",
                    "IV - pela colação de grau em curso de ensino superior;",
                    "V - pelo estabelecimento civil ou comercial, ou pela existência de relação de emprego, desde que, em função deles, o menor com dezesseis anos completos tenha economia própria."
                ]
            }
        },
        "Art. 7º": {
            "caput": "Pode ser declarada a morte presumida, sem decretação de ausência:",
            "incisos": [  # Incisos pertencem ao caput
                "I - se for extremamente provável a morte de quem estava em perigo de vida;",
                "II - se alguém, desaparecido em campanha ou feito prisioneiro, não for encontrado até dois anos após o término da guerra."
            ],
            "paragrafo_unico": {
                "texto": "A declaração da morte presumida, nesses casos, somente poderá ser requerida depois de esgotadas as buscas e averiguações, devendo a sentença fixar a data provável do falecimento.",
                "incisos": []  # Parágrafo único não tem incisos
            }
        },
        "Art. 9º": {
            "caput": "Serão registrados em registro público:",
            "incisos": [  # Incisos pertencem ao caput
                "I - os nascimentos, casamentos e óbitos;",
                "II - a emancipação por outorga dos pais ou por sentença do juiz;",
                "III - a interdição por incapacidade absoluta ou relativa;",
                "IV - a sentença declaratória de ausência e de morte presumida."
            ],
            "paragrafo_unico": None  # Não tem parágrafo único
        },
        "Art. 10º": {
            "caput": "Far-se-á averbação em registro público:",
            "incisos": [  # Incisos pertencem ao caput
                "I - das sentenças que decretarem a nulidade ou anulação do casamento, o divórcio, a separação judicial e o restabelecimento da sociedade conjugal;",
                "II - dos atos judiciais ou extrajudiciais que declararem ou reconhecerem a filiação;"
            ],
            "paragrafo_unico": None
        },
        "Art. 25º": {
            "caput": "O cônjuge do ausente, sempre que não esteja separado judicialmente, ou de fato por mais de dois anos antes da declaração da ausência, será o seu legítimo curador.",
            "incisos": [],  # Não tem incisos no caput
            "paragrafos_numerados": [
                {
                    "numero": "§ 1º",
                    "texto": "Em falta do cônjuge, a curadoria dos bens do ausente incumbe aos pais ou aos descendentes, nesta ordem, não havendo impedimento que os iniba de exercer o cargo.",
                    "incisos": [],
                    "alineas": []
                },
                {
                    "numero": "§ 2º", 
                    "texto": "Entre os descendentes, os mais próximos precedem os mais remotos.",
                    "incisos": [],
                    "alineas": []
                },
                {
                    "numero": "§ 3º",
                    "texto": "Na falta das pessoas mencionadas, compete ao juiz a escolha do curador.",
                    "incisos": [],
                    "alineas": []
                }
            ]
        },
        "Art. 27º": {
            "caput": "Para o efeito previsto no artigo anterior, somente se consideram interessados:",
            "incisos": [  # Incisos pertencem ao caput
                "I - o cônjuge não separado judicialmente;",
                "II - os herdeiros presumidos, legítimos ou testamentários;",
                "III - os que tiverem sobre os bens do ausente direito dependente de sua morte;",
                "IV - os credores de obrigações vencidas e não pagas."
            ],
            "paragrafo_unico": None
        },
        "Art. 41º": {
            "caput": "São pessoas jurídicas de direito público interno:",
            "incisos": [  # Incisos pertencem ao caput
                "I - a União;",
                "II - os Estados, o Distrito Federal e os Territórios;",
                "III - os Municípios;",
                "IV - as autarquias, inclusive as associações públicas;",
                "V - as demais entidades de caráter público criadas por lei."
            ],
            "paragrafo_unico": "Salvo disposição em contrário, as pessoas jurídicas de direito público, a que se tenha dado estrutura de direito privado, regem-se, no que couber, quanto ao seu funcionamento, pelas normas deste Código."
        },
        "Art. 44º": {
            "caput": "São pessoas jurídicas de direito privado:",
            "incisos": [  # Incisos pertencem ao caput
                "I - as associações;",
                "II - as sociedades;",
                "III - as fundações;",
                "IV - as organizações religiosas;",
                "V - os partidos políticos;",
                "VI - as empresas individuais de responsabilidade limitada."
            ],
            "paragrafo_unico": None
        }
    }
    
    # Aplicar as correções
    artigos_corrigidos = 0
    for artigo in artigos:
        artigo_id = artigo["artigo"]
        
        if artigo_id in correcoes_estrutura:
            correcao = correcoes_estrutura[artigo_id]
            
            # Corrigir caput se especificado
            if "caput" in correcao:
                artigo["caput"] = correcao["caput"]
            
            # Corrigir incisos se especificado
            if "incisos" in correcao:
                artigo["incisos"] = correcao["incisos"]
            
            # Corrigir parágrafo único se especificado
            if "paragrafo_unico" in correcao:
                if correcao["paragrafo_unico"] is None:
                    artigo["paragrafo_unico"] = None
                elif isinstance(correcao["paragrafo_unico"], dict):
                    # Parágrafo único com incisos - estrutura especial
                    artigo["paragrafo_unico"] = correcao["paragrafo_unico"]["texto"]
                    # Adicionar incisos do parágrafo único como uma estrutura especial
                    if "incisos" in correcao["paragrafo_unico"] and correcao["paragrafo_unico"]["incisos"]:
                        artigo["paragrafo_unico_incisos"] = correcao["paragrafo_unico"]["incisos"]
                else:
                    artigo["paragrafo_unico"] = correcao["paragrafo_unico"]
            
            # Corrigir parágrafos numerados se especificado
            if "paragrafos_numerados" in correcao:
                artigo["paragrafos_numerados"] = correcao["paragrafos_numerados"]
            
            artigos_corrigidos += 1
            print(f"Corrigido: {artigo_id}")
    
    # Salvar arquivo corrigido
    with open('cc_estrutura_corrigida.json', 'w', encoding='utf-8') as f:
        json.dump(artigos, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Correção da estrutura concluída!")
    print(f"📊 {artigos_corrigidos} artigos com estrutura corrigida")
    print(f"📁 Arquivo salvo: cc_estrutura_corrigida.json")
    
    # Estatísticas finais
    artigos_com_caput = sum(1 for art in artigos if art['caput'])
    total_incisos = sum(len(art['incisos']) for art in artigos)
    total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos)
    total_paragrafos_unicos = sum(1 for art in artigos if art['paragrafo_unico'])
    total_paragrafos_unicos_com_incisos = sum(1 for art in artigos if art.get('paragrafo_unico_incisos'))
    
    print(f"\n📈 ESTATÍSTICAS FINAIS:")
    print(f"   Total de artigos: {len(artigos)}")
    print(f"   Artigos com caput: {artigos_com_caput} ({artigos_com_caput/len(artigos)*100:.1f}%)")
    print(f"   Total de incisos do caput: {total_incisos}")
    print(f"   Total de parágrafos numerados: {total_paragrafos}")
    print(f"   Total de parágrafos únicos: {total_paragrafos_unicos}")
    print(f"   Parágrafos únicos com incisos: {total_paragrafos_unicos_com_incisos}")
    
    # Mostrar exemplos corrigidos
    print(f"\n📋 EXEMPLOS DE ARTIGOS CORRIGIDOS:")
    for artigo_id in ["Art. 5º", "Art. 7º", "Art. 9º"]:
        for art in artigos:
            if art['artigo'] == artigo_id:
                print(f"\n{art['artigo']}:")
                print(f"   Caput: {art['caput'][:60]}...")
                print(f"   Incisos do caput: {len(art['incisos'])}")
                if art['paragrafo_unico']:
                    print(f"   Parágrafo único: {art['paragrafo_unico'][:60]}...")
                if art.get('paragrafo_unico_incisos'):
                    print(f"   Incisos do parágrafo único: {len(art['paragrafo_unico_incisos'])}")
                break
    
    return artigos

if __name__ == '__main__':
    print("INICIANDO CORREÇÃO DA ESTRUTURA DOS INCISOS")
    print("Objetivo: CORRIGIR INCISOS QUE PERTENCEM AO PARÁGRAFO ÚNICO")
    print("-" * 80)
    
    artigos_corrigidos = corrigir_estrutura_incisos()
    
    print(f"\n🎉 CORREÇÃO DA ESTRUTURA CONCLUÍDA COM SUCESSO!")
    print(f"✅ Estrutura legal corrigida - incisos agora estão nos locais corretos!")
