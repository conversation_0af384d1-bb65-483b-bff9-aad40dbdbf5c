<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste das APIs - LexJus VOID</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste das APIs - LexJus VOID</h1>

    <div class="container">
        <h2>📊 Status da Autenticação</h2>
        <button onclick="verificarAutenticacao()">Verificar Autenticação</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div class="container">
        <h2>⭐ API de Favoritos</h2>
        <button onclick="listarFavoritos()">Listar Favoritos</button>
        <button onclick="adicionarFavorito()">Adicionar Favorito (Art. 1)</button>
        <button onclick="removerFavorito()">Remover Favorito (Art. 1)</button>
        <div id="favoritos-result" class="result"></div>
    </div>

    <div class="container">
        <h2>📖 API de Progresso</h2>
        <button onclick="listarProgresso()">Listar Progresso</button>
        <button onclick="marcarLido()">Marcar Art. 1 como Lido</button>
        <button onclick="desmarcarLido()">Desmarcar Art. 1 como Lido</button>
        <div id="progresso-result" class="result"></div>
    </div>

    <div class="container">
        <h2>📝 API de Listas</h2>
        <button onclick="listarListas()">Listar Listas</button>
        <button onclick="criarLista()">Criar Lista Teste</button>
        <button onclick="editarUltimaLista()">Editar Última Lista</button>
        <button onclick="excluirUltimaLista()">Excluir Última Lista</button>
        <div id="listas-result" class="result"></div>
    </div>

    <div class="container">
        <h2>📋 API de Anotações</h2>
        <button onclick="listarAnotacoes()">Listar Anotações</button>
        <button onclick="criarAnotacao()">Criar Anotação Teste</button>
        <button onclick="excluirUltimaAnotacao()">Excluir Última Anotação</button>
        <div id="anotacoes-result" class="result"></div>
    </div>

    <script>
        let ultimaListaId = null;
        let ultimaAnotacaoId = null;

        async function fazerRequisicao(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });

                const data = await response.json();
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        function mostrarResultado(elementId, resultado) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (resultado.ok ? 'success' : 'error');
            element.textContent = JSON.stringify(resultado, null, 2);
        }

        async function verificarAutenticacao() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/favoritos.php');
            mostrarResultado('auth-result', resultado);
        }

        async function listarFavoritos() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/favoritos.php');
            mostrarResultado('favoritos-result', resultado);
        }

        async function adicionarFavorito() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/favoritos.php', {
                method: 'POST',
                body: JSON.stringify({ artigo_numero: '1' })
            });
            mostrarResultado('favoritos-result', resultado);
        }

        async function removerFavorito() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/favoritos.php', {
                method: 'DELETE',
                body: JSON.stringify({ artigo_numero: '1' })
            });
            mostrarResultado('favoritos-result', resultado);
        }

        async function listarProgresso() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/progresso.php');
            mostrarResultado('progresso-result', resultado);
        }

        async function marcarLido() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/progresso.php', {
                method: 'POST',
                body: JSON.stringify({ artigo_numero: '1', lido: true })
            });
            mostrarResultado('progresso-result', resultado);
        }

        async function desmarcarLido() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/progresso.php', {
                method: 'POST',
                body: JSON.stringify({ artigo_numero: '1', lido: false })
            });
            mostrarResultado('progresso-result', resultado);
        }

        async function listarListas() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/listas.php');
            if (resultado.ok && resultado.data.listas && resultado.data.listas.length > 0) {
                ultimaListaId = resultado.data.listas[resultado.data.listas.length - 1].id;
            }
            mostrarResultado('listas-result', resultado);
        }

        async function criarLista() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/listas.php', {
                method: 'POST',
                body: JSON.stringify({
                    nome: 'Lista Teste ' + new Date().getTime(),
                    descricao: 'Lista criada para teste das APIs',
                    cor: '#3498db'
                })
            });
            if (resultado.ok && resultado.data.lista_id) {
                ultimaListaId = resultado.data.lista_id;
            }
            mostrarResultado('listas-result', resultado);
        }

        async function editarUltimaLista() {
            if (!ultimaListaId) {
                mostrarResultado('listas-result', { ok: false, error: 'Nenhuma lista para editar. Crie uma lista primeiro.' });
                return;
            }

            const resultado = await fazerRequisicao('/lexjus_VOID/api/listas.php', {
                method: 'PUT',
                body: JSON.stringify({
                    id: ultimaListaId,
                    nome: 'Lista Editada ' + new Date().getTime(),
                    descricao: 'Lista editada para teste',
                    cor: '#e74c3c'
                })
            });
            mostrarResultado('listas-result', resultado);
        }

        async function excluirUltimaLista() {
            if (!ultimaListaId) {
                mostrarResultado('listas-result', { ok: false, error: 'Nenhuma lista para excluir. Crie uma lista primeiro.' });
                return;
            }

            const resultado = await fazerRequisicao('/lexjus_VOID/api/listas.php', {
                method: 'DELETE',
                body: JSON.stringify({ id: ultimaListaId })
            });
            mostrarResultado('listas-result', resultado);
        }

        async function listarAnotacoes() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/anotacoes.php');
            if (resultado.ok && resultado.data.anotacoes && resultado.data.anotacoes.length > 0) {
                ultimaAnotacaoId = resultado.data.anotacoes[resultado.data.anotacoes.length - 1].id;
            }
            mostrarResultado('anotacoes-result', resultado);
        }

        async function criarAnotacao() {
            const resultado = await fazerRequisicao('/lexjus_VOID/api/anotacoes.php', {
                method: 'POST',
                body: JSON.stringify({
                    artigo_numero: '1',
                    texto: 'Anotação de teste criada em ' + new Date().toLocaleString()
                })
            });
            if (resultado.ok && resultado.data.id) {
                ultimaAnotacaoId = resultado.data.id;
            }
            mostrarResultado('anotacoes-result', resultado);
        }

        async function excluirUltimaAnotacao() {
            if (!ultimaAnotacaoId) {
                mostrarResultado('anotacoes-result', { ok: false, error: 'Nenhuma anotação para excluir. Crie uma anotação primeiro.' });
                return;
            }

            const resultado = await fazerRequisicao('/lexjus_VOID/api/anotacoes.php', {
                method: 'DELETE',
                body: JSON.stringify({ id: ultimaAnotacaoId })
            });
            mostrarResultado('anotacoes-result', resultado);
        }

        // Verificar autenticação ao carregar a página
        window.onload = function() {
            verificarAutenticacao();
        };
    </script>
</body>
</html>
