if(function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){var n=[],i=e.document,r=n.slice,o=n.concat,a=n.push,s=n.indexOf,l={},u=l.toString,c=l.hasOwnProperty,p={},d="2.2.4",f=function(e,t){return new f.fn.init(e,t)},h=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,m=/^-ms-/,g=/-([\da-z])/gi,v=function(e,t){return t.toUpperCase()};function $(e){var t=!!e&&"length"in e&&e.length,n=f.type(e);return"function"!==n&&!f.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}f.fn=f.prototype={jquery:d,constructor:f,selector:"",length:0,toArray:function(){return r.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:r.call(this)},pushStack:function(e){var t=f.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e){return f.each(this,e)},map:function(e){return this.pushStack(f.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(r.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:n.sort,splice:n.splice},f.extend=f.fn.extend=function(){var e,t,n,i,r,o,a=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof a&&(u=a,a=arguments[s]||{},s++),"object"==typeof a||f.isFunction(a)||(a={}),s===l&&(a=this,s--);l>s;s++)if(null!=(e=arguments[s]))for(t in e)n=a[t],a!==(i=e[t])&&(u&&i&&(f.isPlainObject(i)||(r=f.isArray(i)))?(r?(r=!1,o=n&&f.isArray(n)?n:[]):o=n&&f.isPlainObject(n)?n:{},a[t]=f.extend(u,o,i)):void 0!==i&&(a[t]=i));return a},f.extend({expando:"jQuery"+(d+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===f.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){var t=e&&e.toString();return!f.isArray(e)&&t-parseFloat(t)+1>=0},isPlainObject:function(e){var t;if("object"!==f.type(e)||e.nodeType||f.isWindow(e))return!1;if(e.constructor&&!c.call(e,"constructor")&&!c.call(e.constructor.prototype||{},"isPrototypeOf"))return!1;for(t in e);return void 0===t||c.call(e,t)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?l[u.call(e)]||"object":typeof e},globalEval:function(e){var t,n=eval;(e=f.trim(e))&&(1===e.indexOf("use strict")?((t=i.createElement("script")).text=e,i.head.appendChild(t).parentNode.removeChild(t)):n(e))},camelCase:function(e){return e.replace(m,"ms-").replace(g,v)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,i=0;if($(e))for(n=e.length;n>i&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(h,"")},makeArray:function(e,t){var n=t||[];return null!=e&&($(Object(e))?f.merge(n,"string"==typeof e?[e]:e):a.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:s.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;n>i;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,a=!n;o>r;r++)!t(e[r],r)!==a&&i.push(e[r]);return i},map:function(e,t,n){var i,r,a=0,s=[];if($(e))for(i=e.length;i>a;a++)null!=(r=t(e[a],a,n))&&s.push(r);else for(a in e)null!=(r=t(e[a],a,n))&&s.push(r);return o.apply([],s)},guid:1,proxy:function(e,t){var n,i,o;return"string"==typeof t&&(n=e[t],t=e,e=n),f.isFunction(e)?(i=r.call(arguments,2),(o=function(){return e.apply(t||this,i.concat(r.call(arguments)))}).guid=e.guid=e.guid||f.guid++,o):void 0},now:Date.now,support:p}),"function"==typeof Symbol&&(f.fn[Symbol.iterator]=n[Symbol.iterator]),f.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){l["[object "+t+"]"]=t.toLowerCase()});var b=function(e){var t,n,i,r,o,a,s,l,u,c,p,d,f,h,m,g,v,$,b,y="sizzle"+1*new Date,w=e.document,x=0,k=0,C=oe(),T=oe(),D=oe(),S=function(e,t){return e===t&&(p=!0),0},E=1<<31,O={}.hasOwnProperty,A=[],M=A.pop,N=A.push,I=A.push,P=A.slice,j=function(e,t){for(var n=0,i=e.length;i>n;n++)if(e[n]===t)return n;return-1},V="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",R="[\\x20\\t\\r\\n\\f]",F="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",U="\\["+R+"*("+F+")(?:"+R+"*([*^$|!~]?=)"+R+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+F+"))|)"+R+"*\\]",L=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+U+")*)|.*)\\)|)",H=new RegExp(R+"+","g"),q=new RegExp("^"+R+"+|((?:^|[^\\\\])(?:\\\\.)*)"+R+"+$","g"),B=new RegExp("^"+R+"*,"+R+"*"),_=new RegExp("^"+R+"*([>+~]|"+R+")"+R+"*"),W=new RegExp("="+R+"*([^\\]'\"]*?)"+R+"*\\]","g"),z=new RegExp(L),Y=new RegExp("^"+F+"$"),G={ID:new RegExp("^#("+F+")"),CLASS:new RegExp("^\\.("+F+")"),TAG:new RegExp("^("+F+"|[*])"),ATTR:new RegExp("^"+U),PSEUDO:new RegExp("^"+L),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+R+"*(even|odd|(([+-]|)(\\d*)n|)"+R+"*(?:([+-]|)"+R+"*(\\d+)|))"+R+"*\\)|)","i"),bool:new RegExp("^(?:"+V+")$","i"),needsContext:new RegExp("^"+R+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+R+"*((?:-\\d)?\\d*)"+R+"*\\)|)(?=[^-]|$)","i")},K=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,X=/^[^{]+\{\s*\[native \w/,Q=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Z=/[+~]/,ee=/'|\\/g,te=new RegExp("\\\\([\\da-f]{1,6}"+R+"?|("+R+")|.)","ig"),ne=function(e,t,n){var i="0x"+t-65536;return i!=i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},ie=function(){d()};try{I.apply(A=P.call(w.childNodes),w.childNodes),A[w.childNodes.length].nodeType}catch(e){I={apply:A.length?function(e,t){N.apply(e,P.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function re(e,t,i,r){var o,s,u,c,p,h,v,$,x=t&&t.ownerDocument,k=t?t.nodeType:9;if(i=i||[],"string"!=typeof e||!e||1!==k&&9!==k&&11!==k)return i;if(!r&&((t?t.ownerDocument||t:w)!==f&&d(t),t=t||f,m)){if(11!==k&&(h=Q.exec(e)))if(o=h[1]){if(9===k){if(!(u=t.getElementById(o)))return i;if(u.id===o)return i.push(u),i}else if(x&&(u=x.getElementById(o))&&b(t,u)&&u.id===o)return i.push(u),i}else{if(h[2])return I.apply(i,t.getElementsByTagName(e)),i;if((o=h[3])&&n.getElementsByClassName&&t.getElementsByClassName)return I.apply(i,t.getElementsByClassName(o)),i}if(n.qsa&&!D[e+" "]&&(!g||!g.test(e))){if(1!==k)x=t,$=e;else if("object"!==t.nodeName.toLowerCase()){for((c=t.getAttribute("id"))?c=c.replace(ee,"\\$&"):t.setAttribute("id",c=y),s=(v=a(e)).length,p=Y.test(c)?"#"+c:"[id='"+c+"']";s--;)v[s]=p+" "+me(v[s]);$=v.join(","),x=Z.test(e)&&fe(t.parentNode)||t}if($)try{return I.apply(i,x.querySelectorAll($)),i}catch(e){}finally{c===y&&t.removeAttribute("id")}}}return l(e.replace(q,"$1"),t,i,r)}function oe(){var e=[];return function t(n,r){return e.push(n+" ")>i.cacheLength&&delete t[e.shift()],t[n+" "]=r}}function ae(e){return e[y]=!0,e}function se(e){var t=f.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function le(e,t){for(var n=e.split("|"),r=n.length;r--;)i.attrHandle[n[r]]=t}function ue(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||E)-(~e.sourceIndex||E);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function ce(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function pe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function de(e){return ae(function(t){return t=+t,ae(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function fe(e){return e&&void 0!==e.getElementsByTagName&&e}for(t in n=re.support={},o=re.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},d=re.setDocument=function(e){var t,r,a=e?e.ownerDocument||e:w;return a!==f&&9===a.nodeType&&a.documentElement?(h=(f=a).documentElement,m=!o(f),(r=f.defaultView)&&r.top!==r&&(r.addEventListener?r.addEventListener("unload",ie,!1):r.attachEvent&&r.attachEvent("onunload",ie)),n.attributes=se(function(e){return e.className="i",!e.getAttribute("className")}),n.getElementsByTagName=se(function(e){return e.appendChild(f.createComment("")),!e.getElementsByTagName("*").length}),n.getElementsByClassName=X.test(f.getElementsByClassName),n.getById=se(function(e){return h.appendChild(e).id=y,!f.getElementsByName||!f.getElementsByName(y).length}),n.getById?(i.find.ID=function(e,t){if(void 0!==t.getElementById&&m){var n=t.getElementById(e);return n?[n]:[]}},i.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}}):(delete i.find.ID,i.filter.ID=function(e){var t=e.replace(te,ne);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),i.find.TAG=n.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},i.find.CLASS=n.getElementsByClassName&&function(e,t){return void 0!==t.getElementsByClassName&&m?t.getElementsByClassName(e):void 0},v=[],g=[],(n.qsa=X.test(f.querySelectorAll))&&(se(function(e){h.appendChild(e).innerHTML="<a id='"+y+"'></a><select id='"+y+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&g.push("[*^$]="+R+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||g.push("\\["+R+"*(?:value|"+V+")"),e.querySelectorAll("[id~="+y+"-]").length||g.push("~="),e.querySelectorAll(":checked").length||g.push(":checked"),e.querySelectorAll("a#"+y+"+*").length||g.push(".#.+[+~]")}),se(function(e){var t=f.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&g.push("name"+R+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||g.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),g.push(",.*:")})),(n.matchesSelector=X.test($=h.matches||h.webkitMatchesSelector||h.mozMatchesSelector||h.oMatchesSelector||h.msMatchesSelector))&&se(function(e){n.disconnectedMatch=$.call(e,"div"),$.call(e,"[s!='']:x"),v.push("!=",L)}),g=g.length&&new RegExp(g.join("|")),v=v.length&&new RegExp(v.join("|")),t=X.test(h.compareDocumentPosition),b=t||X.test(h.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},S=t?function(e,t){if(e===t)return p=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i||(1&(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!n.sortDetached&&t.compareDocumentPosition(e)===i?e===f||e.ownerDocument===w&&b(w,e)?-1:t===f||t.ownerDocument===w&&b(w,t)?1:c?j(c,e)-j(c,t):0:4&i?-1:1)}:function(e,t){if(e===t)return p=!0,0;var n,i=0,r=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!r||!o)return e===f?-1:t===f?1:r?-1:o?1:c?j(c,e)-j(c,t):0;if(r===o)return ue(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[i]===s[i];)i++;return i?ue(a[i],s[i]):a[i]===w?-1:s[i]===w?1:0},f):f},re.matches=function(e,t){return re(e,null,null,t)},re.matchesSelector=function(e,t){if((e.ownerDocument||e)!==f&&d(e),t=t.replace(W,"='$1']"),n.matchesSelector&&m&&!D[t+" "]&&(!v||!v.test(t))&&(!g||!g.test(t)))try{var i=$.call(e,t);if(i||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(e){}return re(t,f,null,[e]).length>0},re.contains=function(e,t){return(e.ownerDocument||e)!==f&&d(e),b(e,t)},re.attr=function(e,t){(e.ownerDocument||e)!==f&&d(e);var r=i.attrHandle[t.toLowerCase()],o=r&&O.call(i.attrHandle,t.toLowerCase())?r(e,t,!m):void 0;return void 0!==o?o:n.attributes||!m?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},re.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},re.uniqueSort=function(e){var t,i=[],r=0,o=0;if(p=!n.detectDuplicates,c=!n.sortStable&&e.slice(0),e.sort(S),p){for(;t=e[o++];)t===e[o]&&(r=i.push(o));for(;r--;)e.splice(i[r],1)}return c=null,e},r=re.getText=function(e){var t,n="",i=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=r(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[i++];)n+=r(t);return n},(i=re.selectors={cacheLength:50,createPseudo:ae,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||re.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&re.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return G.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&z.test(n)&&(t=a(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=C[e+" "];return t||(t=new RegExp("(^|"+R+")"+e+"("+R+"|$)"))&&C(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(i){var r=re.attr(i,e);return null==r?"!="===t:!t||(r+="","="===t?r===n:"!="===t?r!==n:"^="===t?n&&0===r.indexOf(n):"*="===t?n&&r.indexOf(n)>-1:"$="===t?n&&r.slice(-n.length)===n:"~="===t?(" "+r.replace(H," ")+" ").indexOf(n)>-1:"|="===t&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var u,c,p,d,f,h,m=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),$=!l&&!s,b=!1;if(g){if(o){for(;m;){for(d=t;d=d[m];)if(s?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;h=m="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&$){for(b=(f=(u=(c=(p=(d=g)[y]||(d[y]={}))[d.uniqueID]||(p[d.uniqueID]={}))[e]||[])[0]===x&&u[1])&&u[2],d=f&&g.childNodes[f];d=++f&&d&&d[m]||(b=f=0)||h.pop();)if(1===d.nodeType&&++b&&d===t){c[e]=[x,f,b];break}}else if($&&(b=f=(u=(c=(p=(d=t)[y]||(d[y]={}))[d.uniqueID]||(p[d.uniqueID]={}))[e]||[])[0]===x&&u[1]),!1===b)for(;(d=++f&&d&&d[m]||(b=f=0)||h.pop())&&((s?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++b||($&&((c=(p=d[y]||(d[y]={}))[d.uniqueID]||(p[d.uniqueID]={}))[e]=[x,b]),d!==t)););return(b-=r)===i||b%i==0&&b/i>=0}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||re.error("unsupported pseudo: "+e);return r[y]?r(t):r.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?ae(function(e,n){for(var i,o=r(e,t),a=o.length;a--;)e[i=j(e,o[a])]=!(n[i]=o[a])}):function(e){return r(e,0,n)}):r}},pseudos:{not:ae(function(e){var t=[],n=[],i=s(e.replace(q,"$1"));return i[y]?ae(function(e,t,n,r){for(var o,a=i(e,null,r,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,r,o){return t[0]=e,i(t,null,o,n),t[0]=null,!n.pop()}}),has:ae(function(e){return function(t){return re(e,t).length>0}}),contains:ae(function(e){return e=e.replace(te,ne),function(t){return(t.textContent||t.innerText||r(t)).indexOf(e)>-1}}),lang:ae(function(e){return Y.test(e||"")||re.error("unsupported lang: "+e),e=e.replace(te,ne).toLowerCase(),function(t){var n;do{if(n=m?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===h},focus:function(e){return e===f.activeElement&&(!f.hasFocus||f.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!i.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return K.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:de(function(){return[0]}),last:de(function(e,t){return[t-1]}),eq:de(function(e,t,n){return[0>n?n+t:n]}),even:de(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:de(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:de(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:de(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=i.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})i.pseudos[t]=ce(t);for(t in{submit:!0,reset:!0})i.pseudos[t]=pe(t);function he(){}function me(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function ge(e,t,n){var i=t.dir,r=n&&"parentNode"===i,o=k++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||r)return e(t,n,o)}:function(t,n,a){var s,l,u,c=[x,o];if(a){for(;t=t[i];)if((1===t.nodeType||r)&&e(t,n,a))return!0}else for(;t=t[i];)if(1===t.nodeType||r){if((s=(l=(u=t[y]||(t[y]={}))[t.uniqueID]||(u[t.uniqueID]={}))[i])&&s[0]===x&&s[1]===o)return c[2]=s[2];if(l[i]=c,c[2]=e(t,n,a))return!0}}}function ve(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function $e(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,u=null!=t;l>s;s++)(o=e[s])&&(n&&!n(o,i,r)||(a.push(o),u&&t.push(s)));return a}function be(e,t,n,i,r,o){return i&&!i[y]&&(i=be(i)),r&&!r[y]&&(r=be(r,o)),ae(function(o,a,s,l){var u,c,p,d=[],f=[],h=a.length,m=o||function(e,t,n){for(var i=0,r=t.length;r>i;i++)re(e,t[i],n);return n}(t||"*",s.nodeType?[s]:s,[]),g=!e||!o&&t?m:$e(m,d,e,s,l),v=n?r||(o?e:h||i)?[]:a:g;if(n&&n(g,v,s,l),i)for(u=$e(v,f),i(u,[],s,l),c=u.length;c--;)(p=u[c])&&(v[f[c]]=!(g[f[c]]=p));if(o){if(r||e){if(r){for(u=[],c=v.length;c--;)(p=v[c])&&u.push(g[c]=p);r(null,v=[],u,l)}for(c=v.length;c--;)(p=v[c])&&(u=r?j(o,p):d[c])>-1&&(o[u]=!(a[u]=p))}}else v=$e(v===a?v.splice(h,v.length):v),r?r(null,a,v,l):I.apply(a,v)})}function ye(e){for(var t,n,r,o=e.length,a=i.relative[e[0].type],s=a||i.relative[" "],l=a?1:0,c=ge(function(e){return e===t},s,!0),p=ge(function(e){return j(t,e)>-1},s,!0),d=[function(e,n,i){var r=!a&&(i||n!==u)||((t=n).nodeType?c(e,n,i):p(e,n,i));return t=null,r}];o>l;l++)if(n=i.relative[e[l].type])d=[ge(ve(d),n)];else{if((n=i.filter[e[l].type].apply(null,e[l].matches))[y]){for(r=++l;o>r&&!i.relative[e[r].type];r++);return be(l>1&&ve(d),l>1&&me(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(q,"$1"),n,r>l&&ye(e.slice(l,r)),o>r&&ye(e=e.slice(r)),o>r&&me(e))}d.push(n)}return ve(d)}function we(e,t){var n=t.length>0,r=e.length>0,o=function(o,a,s,l,c){var p,h,g,v=0,$="0",b=o&&[],y=[],w=u,k=o||r&&i.find.TAG("*",c),C=x+=null==w?1:Math.random()||.1,T=k.length;for(c&&(u=a===f||a||c);$!==T&&null!=(p=k[$]);$++){if(r&&p){for(h=0,a||p.ownerDocument===f||(d(p),s=!m);g=e[h++];)if(g(p,a||f,s)){l.push(p);break}c&&(x=C)}n&&((p=!g&&p)&&v--,o&&b.push(p))}if(v+=$,n&&$!==v){for(h=0;g=t[h++];)g(b,y,a,s);if(o){if(v>0)for(;$--;)b[$]||y[$]||(y[$]=M.call(l));y=$e(y)}I.apply(l,y),c&&!o&&y.length>0&&v+t.length>1&&re.uniqueSort(l)}return c&&(x=C,u=w),b};return n?ae(o):o}return he.prototype=i.filters=i.pseudos,i.setFilters=new he,a=re.tokenize=function(e,t){var n,r,o,a,s,l,u,c=T[e+" "];if(c)return t?0:c.slice(0);for(s=e,l=[],u=i.preFilter;s;){for(a in n&&!(r=B.exec(s))||(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),n=!1,(r=_.exec(s))&&(n=r.shift(),o.push({value:n,type:r[0].replace(q," ")}),s=s.slice(n.length)),i.filter)!(r=G[a].exec(s))||u[a]&&!(r=u[a](r))||(n=r.shift(),o.push({value:n,type:a,matches:r}),s=s.slice(n.length));if(!n)break}return t?s.length:s?re.error(e):T(e,l).slice(0)},s=re.compile=function(e,t){var n,i=[],r=[],o=D[e+" "];if(!o){for(t||(t=a(e)),n=t.length;n--;)(o=ye(t[n]))[y]?i.push(o):r.push(o);(o=D(e,we(r,i))).selector=e}return o},l=re.select=function(e,t,r,o){var l,u,c,p,d,f="function"==typeof e&&e,h=!o&&a(e=f.selector||e);if(r=r||[],1===h.length){if((u=h[0]=h[0].slice(0)).length>2&&"ID"===(c=u[0]).type&&n.getById&&9===t.nodeType&&m&&i.relative[u[1].type]){if(!(t=(i.find.ID(c.matches[0].replace(te,ne),t)||[])[0]))return r;f&&(t=t.parentNode),e=e.slice(u.shift().value.length)}for(l=G.needsContext.test(e)?0:u.length;l--&&(c=u[l],!i.relative[p=c.type]);)if((d=i.find[p])&&(o=d(c.matches[0].replace(te,ne),Z.test(u[0].type)&&fe(t.parentNode)||t))){if(u.splice(l,1),!(e=o.length&&me(u)))return I.apply(r,o),r;break}}return(f||s(e,h))(o,t,!m,r,!t||Z.test(e)&&fe(t.parentNode)||t),r},n.sortStable=y.split("").sort(S).join("")===y,n.detectDuplicates=!!p,d(),n.sortDetached=se(function(e){return 1&e.compareDocumentPosition(f.createElement("div"))}),se(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||le("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),n.attributes&&se(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||le("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),se(function(e){return null==e.getAttribute("disabled")})||le(V,function(e,t,n){var i;return n?void 0:!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),re}(e);f.find=b,f.expr=b.selectors,f.expr[":"]=f.expr.pseudos,f.uniqueSort=f.unique=b.uniqueSort,f.text=b.getText,f.isXMLDoc=b.isXML,f.contains=b.contains;var y=function(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&f(e).is(n))break;i.push(e)}return i},w=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},x=f.expr.match.needsContext,k=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,C=/^.[^:#\[\.,]*$/;function T(e,t,n){if(f.isFunction(t))return f.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return f.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(C.test(t))return f.filter(t,e,n);t=f.filter(t,e)}return f.grep(e,function(e){return s.call(t,e)>-1!==n})}f.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?f.find.matchesSelector(i,e)?[i]:[]:f.find.matches(e,f.grep(t,function(e){return 1===e.nodeType}))},f.fn.extend({find:function(e){var t,n=this.length,i=[],r=this;if("string"!=typeof e)return this.pushStack(f(e).filter(function(){for(t=0;n>t;t++)if(f.contains(r[t],this))return!0}));for(t=0;n>t;t++)f.find(e,r[t],i);return(i=this.pushStack(n>1?f.unique(i):i)).selector=this.selector?this.selector+" "+e:e,i},filter:function(e){return this.pushStack(T(this,e||[],!1))},not:function(e){return this.pushStack(T(this,e||[],!0))},is:function(e){return!!T(this,"string"==typeof e&&x.test(e)?f(e):e||[],!1).length}});var D,S=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(f.fn.init=function(e,t,n){var r,o;if(!e)return this;if(n=n||D,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:S.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof f?t[0]:t,f.merge(this,f.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:i,!0)),k.test(r[1])&&f.isPlainObject(t))for(r in t)f.isFunction(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(o=i.getElementById(r[2]))&&o.parentNode&&(this.length=1,this[0]=o),this.context=i,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):f.isFunction(e)?void 0!==n.ready?n.ready(e):e(f):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),f.makeArray(e,this))}).prototype=f.fn,D=f(i);var E=/^(?:parents|prev(?:Until|All))/,O={children:!0,contents:!0,next:!0,prev:!0};function A(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}f.fn.extend({has:function(e){var t=f(e,this),n=t.length;return this.filter(function(){for(var e=0;n>e;e++)if(f.contains(this,t[e]))return!0})},closest:function(e,t){for(var n,i=0,r=this.length,o=[],a=x.test(e)||"string"!=typeof e?f(e,t||this.context):0;r>i;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&f.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?f.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?s.call(f(e),this[0]):s.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(f.uniqueSort(f.merge(this.get(),f(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),f.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return y(e,"parentNode")},parentsUntil:function(e,t,n){return y(e,"parentNode",n)},next:function(e){return A(e,"nextSibling")},prev:function(e){return A(e,"previousSibling")},nextAll:function(e){return y(e,"nextSibling")},prevAll:function(e){return y(e,"previousSibling")},nextUntil:function(e,t,n){return y(e,"nextSibling",n)},prevUntil:function(e,t,n){return y(e,"previousSibling",n)},siblings:function(e){return w((e.parentNode||{}).firstChild,e)},children:function(e){return w(e.firstChild)},contents:function(e){return e.contentDocument||f.merge([],e.childNodes)}},function(e,t){f.fn[e]=function(n,i){var r=f.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=f.filter(i,r)),this.length>1&&(O[e]||f.uniqueSort(r),E.test(e)&&r.reverse()),this.pushStack(r)}});var M,N=/\S+/g;function I(){i.removeEventListener("DOMContentLoaded",I),e.removeEventListener("load",I),f.ready()}f.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return f.each(e.match(N)||[],function(e,n){t[n]=!0}),t}(e):f.extend({},e);var t,n,i,r,o=[],a=[],s=-1,l=function(){for(r=e.once,i=t=!0;a.length;s=-1)for(n=a.shift();++s<o.length;)!1===o[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=o.length,n=!1);e.memory||(n=!1),t=!1,r&&(o=n?[]:"")},u={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function t(n){f.each(n,function(n,i){f.isFunction(i)?e.unique&&u.has(i)||o.push(i):i&&i.length&&"string"!==f.type(i)&&t(i)})}(arguments),n&&!t&&l()),this},remove:function(){return f.each(arguments,function(e,t){for(var n;(n=f.inArray(t,o,n))>-1;)o.splice(n,1),s>=n&&s--}),this},has:function(e){return e?f.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return r=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return r=a=[],n||(o=n=""),this},locked:function(){return!!r},fireWith:function(e,n){return r||(n=[e,(n=n||[]).slice?n.slice():n],a.push(n),t||l()),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!i}};return u},f.extend({Deferred:function(e){var t=[["resolve","done",f.Callbacks("once memory"),"resolved"],["reject","fail",f.Callbacks("once memory"),"rejected"],["notify","progress",f.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},then:function(){var e=arguments;return f.Deferred(function(n){f.each(t,function(t,o){var a=f.isFunction(e[t])&&e[t];r[o[1]](function(){var e=a&&a.apply(this,arguments);e&&f.isFunction(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[o[0]+"With"](this===i?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?f.extend(e,i):i}},r={};return i.pipe=i.then,f.each(t,function(e,o){var a=o[2],s=o[3];i[o[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),r[o[0]]=function(){return r[o[0]+"With"](this===r?i:this,arguments),this},r[o[0]+"With"]=a.fireWith}),i.promise(r),e&&e.call(r,r),r},when:function(e){var t,n,i,o=0,a=r.call(arguments),s=a.length,l=1!==s||e&&f.isFunction(e.promise)?s:0,u=1===l?e:f.Deferred(),c=function(e,n,i){return function(o){n[e]=this,i[e]=arguments.length>1?r.call(arguments):o,i===t?u.notifyWith(n,i):--l||u.resolveWith(n,i)}};if(s>1)for(t=new Array(s),n=new Array(s),i=new Array(s);s>o;o++)a[o]&&f.isFunction(a[o].promise)?a[o].promise().progress(c(o,n,t)).done(c(o,i,a)).fail(u.reject):--l;return l||u.resolveWith(i,a),u.promise()}}),f.fn.ready=function(e){return f.ready.promise().done(e),this},f.extend({isReady:!1,readyWait:1,holdReady:function(e){e?f.readyWait++:f.ready(!0)},ready:function(e){(!0===e?--f.readyWait:f.isReady)||(f.isReady=!0,!0!==e&&--f.readyWait>0||(M.resolveWith(i,[f]),f.fn.triggerHandler&&(f(i).triggerHandler("ready"),f(i).off("ready"))))}}),f.ready.promise=function(t){return M||(M=f.Deferred(),"complete"===i.readyState||"loading"!==i.readyState&&!i.documentElement.doScroll?e.setTimeout(f.ready):(i.addEventListener("DOMContentLoaded",I),e.addEventListener("load",I))),M.promise(t)},f.ready.promise();var P=function(e,t,n,i,r,o,a){var s=0,l=e.length,u=null==n;if("object"===f.type(n))for(s in r=!0,n)P(e,t,s,n[s],!0,o,a);else if(void 0!==i&&(r=!0,f.isFunction(i)||(a=!0),u&&(a?(t.call(e,i),t=null):(u=t,t=function(e,t,n){return u.call(f(e),n)})),t))for(;l>s;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return r?e:u?t.call(e):l?t(e[0],n):o},j=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function V(){this.expando=f.expando+V.uid++}V.uid=1,V.prototype={register:function(e,t){var n=t||{};return e.nodeType?e[this.expando]=n:Object.defineProperty(e,this.expando,{value:n,writable:!0,configurable:!0}),e[this.expando]},cache:function(e){if(!j(e))return{};var t=e[this.expando];return t||(t={},j(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[t]=n;else for(i in t)r[i]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][t]},access:function(e,t,n){var i;return void 0===t||t&&"string"==typeof t&&void 0===n?void 0!==(i=this.get(e,t))?i:this.get(e,f.camelCase(t)):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i,r,o=e[this.expando];if(void 0!==o){if(void 0===t)this.register(e);else{f.isArray(t)?i=t.concat(t.map(f.camelCase)):(r=f.camelCase(t),t in o?i=[t,r]:i=(i=r)in o?[i]:i.match(N)||[]),n=i.length;for(;n--;)delete o[i[n]]}(void 0===t||f.isEmptyObject(o))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!f.isEmptyObject(t)}};var R=new V,F=new V,U=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,L=/[A-Z]/g;function H(e,t,n){var i;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(L,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:U.test(n)?f.parseJSON(n):n)}catch(e){}F.set(e,t,n)}else n=void 0;return n}f.extend({hasData:function(e){return F.hasData(e)||R.hasData(e)},data:function(e,t,n){return F.access(e,t,n)},removeData:function(e,t){F.remove(e,t)},_data:function(e,t,n){return R.access(e,t,n)},_removeData:function(e,t){R.remove(e,t)}}),f.fn.extend({data:function(e,t){var n,i,r,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(r=F.get(o),1===o.nodeType&&!R.get(o,"hasDataAttrs"))){for(n=a.length;n--;)a[n]&&(0===(i=a[n].name).indexOf("data-")&&(i=f.camelCase(i.slice(5)),H(o,i,r[i])));R.set(o,"hasDataAttrs",!0)}return r}return"object"==typeof e?this.each(function(){F.set(this,e)}):P(this,function(t){var n,i;if(o&&void 0===t){if(void 0!==(n=F.get(o,e)||F.get(o,e.replace(L,"-$&").toLowerCase())))return n;if(i=f.camelCase(e),void 0!==(n=F.get(o,i)))return n;if(void 0!==(n=H(o,i,void 0)))return n}else i=f.camelCase(e),this.each(function(){var n=F.get(this,i);F.set(this,i,t),e.indexOf("-")>-1&&void 0!==n&&F.set(this,e,t)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){F.remove(this,e)})}}),f.extend({queue:function(e,t,n){var i;return e?(t=(t||"fx")+"queue",i=R.get(e,t),n&&(!i||f.isArray(n)?i=R.access(e,t,f.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=f.queue(e,t),i=n.length,r=n.shift(),o=f._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){f.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return R.get(e,n)||R.access(e,n,{empty:f.Callbacks("once memory").add(function(){R.remove(e,[t+"queue",n])})})}}),f.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?f.queue(this[0],e):void 0===t?this:this.each(function(){var n=f.queue(this,e,t);f._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&f.dequeue(this,e)})},dequeue:function(e){return this.each(function(){f.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=f.Deferred(),o=this,a=this.length,s=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(n=R.get(o[a],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(t)}});var q=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,B=new RegExp("^(?:([+-])=|)("+q+")([a-z%]*)$","i"),_=["Top","Right","Bottom","Left"],W=function(e,t){return e=t||e,"none"===f.css(e,"display")||!f.contains(e.ownerDocument,e)};function z(e,t,n,i){var r,o=1,a=20,s=i?function(){return i.cur()}:function(){return f.css(e,t,"")},l=s(),u=n&&n[3]||(f.cssNumber[t]?"":"px"),c=(f.cssNumber[t]||"px"!==u&&+l)&&B.exec(f.css(e,t));if(c&&c[3]!==u){u=u||c[3],n=n||[],c=+l||1;do{c/=o=o||".5",f.style(e,t,c+u)}while(o!==(o=s()/l)&&1!==o&&--a)}return n&&(c=+c||+l||0,r=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=c,i.end=r)),r}var Y=/^(?:checkbox|radio)$/i,G=/<([\w:-]+)/,K=/^$|\/(?:java|ecma)script/i,J={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function X(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&f.nodeName(e,t)?f.merge([e],n):n}function Q(e,t){for(var n=0,i=e.length;i>n;n++)R.set(e[n],"globalEval",!t||R.get(t[n],"globalEval"))}J.optgroup=J.option,J.tbody=J.tfoot=J.colgroup=J.caption=J.thead,J.th=J.td;var Z=/<|&#?\w+;/;function ee(e,t,n,i,r){for(var o,a,s,l,u,c,p=t.createDocumentFragment(),d=[],h=0,m=e.length;m>h;h++)if((o=e[h])||0===o)if("object"===f.type(o))f.merge(d,o.nodeType?[o]:o);else if(Z.test(o)){for(a=a||p.appendChild(t.createElement("div")),s=(G.exec(o)||["",""])[1].toLowerCase(),l=J[s]||J._default,a.innerHTML=l[1]+f.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;f.merge(d,a.childNodes),(a=p.firstChild).textContent=""}else d.push(t.createTextNode(o));for(p.textContent="",h=0;o=d[h++];)if(i&&f.inArray(o,i)>-1)r&&r.push(o);else if(u=f.contains(o.ownerDocument,o),a=X(p.appendChild(o),"script"),u&&Q(a),n)for(c=0;o=a[c++];)K.test(o.type||"")&&n.push(o);return p}!function(){var e=i.createDocumentFragment().appendChild(i.createElement("div")),t=i.createElement("input");t.setAttribute("type","radio"),t.setAttribute("checked","checked"),t.setAttribute("name","t"),e.appendChild(t),p.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,e.innerHTML="<textarea>x</textarea>",p.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue}();var te=/^key/,ne=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ie=/^([^.]*)(?:\.(.+)|)/;function re(){return!0}function oe(){return!1}function ae(){try{return i.activeElement}catch(e){}}function se(e,t,n,i,r,o){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(i=i||n,n=void 0),t)se(e,s,n,i,t[s],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=oe;else if(!r)return e;return 1===o&&(a=r,(r=function(e){return f().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=f.guid++)),e.each(function(){f.event.add(this,t,r,i,n)})}f.event={global:{},add:function(e,t,n,i,r){var o,a,s,l,u,c,p,d,h,m,g,v=R.get(e);if(v)for(n.handler&&(n=(o=n).handler,r=o.selector),n.guid||(n.guid=f.guid++),(l=v.events)||(l=v.events={}),(a=v.handle)||(a=v.handle=function(t){return void 0!==f&&f.event.triggered!==t.type?f.event.dispatch.apply(e,arguments):void 0}),u=(t=(t||"").match(N)||[""]).length;u--;)h=g=(s=ie.exec(t[u])||[])[1],m=(s[2]||"").split(".").sort(),h&&(p=f.event.special[h]||{},h=(r?p.delegateType:p.bindType)||h,p=f.event.special[h]||{},c=f.extend({type:h,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&f.expr.match.needsContext.test(r),namespace:m.join(".")},o),(d=l[h])||((d=l[h]=[]).delegateCount=0,p.setup&&!1!==p.setup.call(e,i,m,a)||e.addEventListener&&e.addEventListener(h,a)),p.add&&(p.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,c):d.push(c),f.event.global[h]=!0)},remove:function(e,t,n,i,r){var o,a,s,l,u,c,p,d,h,m,g,v=R.hasData(e)&&R.get(e);if(v&&(l=v.events)){for(u=(t=(t||"").match(N)||[""]).length;u--;)if(h=g=(s=ie.exec(t[u])||[])[1],m=(s[2]||"").split(".").sort(),h){for(p=f.event.special[h]||{},d=l[h=(i?p.delegateType:p.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!r&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,p.remove&&p.remove.call(e,c));a&&!d.length&&(p.teardown&&!1!==p.teardown.call(e,m,v.handle)||f.removeEvent(e,h,v.handle),delete l[h])}else for(h in l)f.event.remove(e,h+t[u],n,i,!0);f.isEmptyObject(l)&&R.remove(e,"handle events")}},dispatch:function(e){e=f.event.fix(e);var t,n,i,o,a,s=[],l=r.call(arguments),u=(R.get(this,"events")||{})[e.type]||[],c=f.event.special[e.type]||{};if(l[0]=e,e.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,e)){for(s=f.event.handlers.call(this,e,u),t=0;(o=s[t++])&&!e.isPropagationStopped();)for(e.currentTarget=o.elem,n=0;(a=o.handlers[n++])&&!e.isImmediatePropagationStopped();)e.rnamespace&&!e.rnamespace.test(a.namespace)||(e.handleObj=a,e.data=a.data,void 0!==(i=((f.event.special[a.origType]||{}).handle||a.handler).apply(o.elem,l))&&!1===(e.result=i)&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,r,o,a=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&(!0!==l.disabled||"click"!==e.type)){for(i=[],n=0;s>n;n++)void 0===i[r=(o=t[n]).selector+" "]&&(i[r]=o.needsContext?f(r,this).index(l)>-1:f.find(r,this,null,[l]).length),i[r]&&i.push(o);i.length&&a.push({elem:l,handlers:i})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,o,a=t.button;return null==e.pageX&&null!=t.clientX&&(r=(n=e.target.ownerDocument||i).documentElement,o=n.body,e.pageX=t.clientX+(r&&r.scrollLeft||o&&o.scrollLeft||0)-(r&&r.clientLeft||o&&o.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||o&&o.scrollTop||0)-(r&&r.clientTop||o&&o.clientTop||0)),e.which||void 0===a||(e.which=1&a?1:2&a?3:4&a?2:0),e}},fix:function(e){if(e[f.expando])return e;var t,n,r,o=e.type,a=e,s=this.fixHooks[o];for(s||(this.fixHooks[o]=s=ne.test(o)?this.mouseHooks:te.test(o)?this.keyHooks:{}),r=s.props?this.props.concat(s.props):this.props,e=new f.Event(a),t=r.length;t--;)e[n=r[t]]=a[n];return e.target||(e.target=i),3===e.target.nodeType&&(e.target=e.target.parentNode),s.filter?s.filter(e,a):e},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==ae()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===ae()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&f.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(e){return f.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},f.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},f.Event=function(e,t){return this instanceof f.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?re:oe):this.type=e,t&&f.extend(this,t),this.timeStamp=e&&e.timeStamp||f.now(),void(this[f.expando]=!0)):new f.Event(e,t)},f.Event.prototype={constructor:f.Event,isDefaultPrevented:oe,isPropagationStopped:oe,isImmediatePropagationStopped:oe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=re,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=re,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=re,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},f.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){f.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=e.relatedTarget,r=e.handleObj;return i&&(i===this||f.contains(this,i))||(e.type=r.origType,n=r.handler.apply(this,arguments),e.type=t),n}}}),f.fn.extend({on:function(e,t,n,i){return se(this,e,t,n,i)},one:function(e,t,n,i){return se(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,f(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=oe),this.each(function(){f.event.remove(this,e,n,t)})}});var le=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,ue=/<script|<style|<link/i,ce=/checked\s*(?:[^=]|=\s*.checked.)/i,pe=/^true\/(.*)/,de=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function fe(e,t){return f.nodeName(e,"table")&&f.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function he(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function me(e){var t=pe.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function ge(e,t){var n,i,r,o,a,s,l,u;if(1===t.nodeType){if(R.hasData(e)&&(o=R.access(e),a=R.set(t,o),u=o.events))for(r in delete a.handle,a.events={},u)for(n=0,i=u[r].length;i>n;n++)f.event.add(t,r,u[r][n]);F.hasData(e)&&(s=F.access(e),l=f.extend({},s),F.set(t,l))}}function ve(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Y.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function $e(e,t,n,i){t=o.apply([],t);var r,a,s,l,u,c,d=0,h=e.length,m=h-1,g=t[0],v=f.isFunction(g);if(v||h>1&&"string"==typeof g&&!p.checkClone&&ce.test(g))return e.each(function(r){var o=e.eq(r);v&&(t[0]=g.call(this,r,o.html())),$e(o,t,n,i)});if(h&&(a=(r=ee(t,e[0].ownerDocument,!1,e,i)).firstChild,1===r.childNodes.length&&(r=a),a||i)){for(l=(s=f.map(X(r,"script"),he)).length;h>d;d++)u=r,d!==m&&(u=f.clone(u,!0,!0),l&&f.merge(s,X(u,"script"))),n.call(e[d],u,d);if(l)for(c=s[s.length-1].ownerDocument,f.map(s,me),d=0;l>d;d++)u=s[d],K.test(u.type||"")&&!R.access(u,"globalEval")&&f.contains(c,u)&&(u.src?f._evalUrl&&f._evalUrl(u.src):f.globalEval(u.textContent.replace(de,"")))}return e}function be(e,t,n){for(var i,r=t?f.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||f.cleanData(X(i)),i.parentNode&&(n&&f.contains(i.ownerDocument,i)&&Q(X(i,"script")),i.parentNode.removeChild(i));return e}f.extend({htmlPrefilter:function(e){return e.replace(le,"<$1></$2>")},clone:function(e,t,n){var i,r,o,a,s=e.cloneNode(!0),l=f.contains(e.ownerDocument,e);if(!(p.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||f.isXMLDoc(e)))for(a=X(s),i=0,r=(o=X(e)).length;r>i;i++)ve(o[i],a[i]);if(t)if(n)for(o=o||X(e),a=a||X(s),i=0,r=o.length;r>i;i++)ge(o[i],a[i]);else ge(e,s);return(a=X(s,"script")).length>0&&Q(a,!l&&X(e,"script")),s},cleanData:function(e){for(var t,n,i,r=f.event.special,o=0;void 0!==(n=e[o]);o++)if(j(n)){if(t=n[R.expando]){if(t.events)for(i in t.events)r[i]?f.event.remove(n,i):f.removeEvent(n,i,t.handle);n[R.expando]=void 0}n[F.expando]&&(n[F.expando]=void 0)}}}),f.fn.extend({domManip:$e,detach:function(e){return be(this,e,!0)},remove:function(e){return be(this,e)},text:function(e){return P(this,function(e){return void 0===e?f.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return $e(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||fe(this,e).appendChild(e)})},prepend:function(){return $e(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=fe(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return $e(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return $e(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(f.cleanData(X(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return f.clone(this,e,t)})},html:function(e){return P(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ue.test(e)&&!J[(G.exec(e)||["",""])[1].toLowerCase()]){e=f.htmlPrefilter(e);try{for(;i>n;n++)1===(t=this[n]||{}).nodeType&&(f.cleanData(X(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return $e(this,arguments,function(t){var n=this.parentNode;f.inArray(this,e)<0&&(f.cleanData(X(this)),n&&n.replaceChild(t,this))},e)}}),f.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){f.fn[e]=function(e){for(var n,i=[],r=f(e),o=r.length-1,s=0;o>=s;s++)n=s===o?this:this.clone(!0),f(r[s])[t](n),a.apply(i,n.get());return this.pushStack(i)}});var ye,we={HTML:"block",BODY:"block"};function xe(e,t){var n=f(t.createElement(e)).appendTo(t.body),i=f.css(n[0],"display");return n.detach(),i}function ke(e){var t=i,n=we[e];return n||("none"!==(n=xe(e,t))&&n||((t=(ye=(ye||f("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement))[0].contentDocument).write(),t.close(),n=xe(e,t),ye.detach()),we[e]=n),n}var Ce=/^margin/,Te=new RegExp("^("+q+")(?!px)[a-z%]+$","i"),De=function(t){var n=t.ownerDocument.defaultView;return n&&n.opener||(n=e),n.getComputedStyle(t)},Se=function(e,t,n,i){var r,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];for(o in r=n.apply(e,i||[]),t)e.style[o]=a[o];return r},Ee=i.documentElement;function Oe(e,t,n){var i,r,o,a,s=e.style;return""!==(a=(n=n||De(e))?n.getPropertyValue(t)||n[t]:void 0)&&void 0!==a||f.contains(e.ownerDocument,e)||(a=f.style(e,t)),n&&!p.pixelMarginRight()&&Te.test(a)&&Ce.test(t)&&(i=s.width,r=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=i,s.minWidth=r,s.maxWidth=o),void 0!==a?a+"":a}function Ae(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}!function(){var t,n,r,o,a=i.createElement("div"),s=i.createElement("div");if(s.style){function l(){s.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",s.innerHTML="",Ee.appendChild(a);var i=e.getComputedStyle(s);t="1%"!==i.top,o="2px"===i.marginLeft,n="4px"===i.width,s.style.marginRight="50%",r="4px"===i.marginRight,Ee.removeChild(a)}s.style.backgroundClip="content-box",s.cloneNode(!0).style.backgroundClip="",p.clearCloneStyle="content-box"===s.style.backgroundClip,a.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",a.appendChild(s),f.extend(p,{pixelPosition:function(){return l(),t},boxSizingReliable:function(){return null==n&&l(),n},pixelMarginRight:function(){return null==n&&l(),r},reliableMarginLeft:function(){return null==n&&l(),o},reliableMarginRight:function(){var t,n=s.appendChild(i.createElement("div"));return n.style.cssText=s.style.cssText="-webkit-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",n.style.marginRight=n.style.width="0",s.style.width="1px",Ee.appendChild(a),t=!parseFloat(e.getComputedStyle(n).marginRight),Ee.removeChild(a),s.removeChild(n),t}})}}();var Me=/^(none|table(?!-c[ea]).+)/,Ne={position:"absolute",visibility:"hidden",display:"block"},Ie={letterSpacing:"0",fontWeight:"400"},Pe=["Webkit","O","Moz","ms"],je=i.createElement("div").style;function Ve(e){if(e in je)return e;for(var t=e[0].toUpperCase()+e.slice(1),n=Pe.length;n--;)if((e=Pe[n]+t)in je)return e}function Re(e,t,n){var i=B.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function Fe(e,t,n,i,r){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,a=0;4>o;o+=2)"margin"===n&&(a+=f.css(e,n+_[o],!0,r)),i?("content"===n&&(a-=f.css(e,"padding"+_[o],!0,r)),"margin"!==n&&(a-=f.css(e,"border"+_[o]+"Width",!0,r))):(a+=f.css(e,"padding"+_[o],!0,r),"padding"!==n&&(a+=f.css(e,"border"+_[o]+"Width",!0,r)));return a}function Ue(e,t,n){var i=!0,r="width"===t?e.offsetWidth:e.offsetHeight,o=De(e),a="border-box"===f.css(e,"boxSizing",!1,o);if(0>=r||null==r){if((0>(r=Oe(e,t,o))||null==r)&&(r=e.style[t]),Te.test(r))return r;i=a&&(p.boxSizingReliable()||r===e.style[t]),r=parseFloat(r)||0}return r+Fe(e,t,n||(a?"border":"content"),i,o)+"px"}function Le(e,t){for(var n,i,r,o=[],a=0,s=e.length;s>a;a++)(i=e[a]).style&&(o[a]=R.get(i,"olddisplay"),n=i.style.display,t?(o[a]||"none"!==n||(i.style.display=""),""===i.style.display&&W(i)&&(o[a]=R.access(i,"olddisplay",ke(i.nodeName)))):(r=W(i),"none"===n&&r||R.set(i,"olddisplay",r?n:f.css(i,"display"))));for(a=0;s>a;a++)(i=e[a]).style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[a]||"":"none"));return e}function He(e,t,n,i,r){return new He.prototype.init(e,t,n,i,r)}f.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Oe(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,a,s=f.camelCase(t),l=e.style;return t=f.cssProps[s]||(f.cssProps[s]=Ve(s)||s),a=f.cssHooks[t]||f.cssHooks[s],void 0===n?a&&"get"in a&&void 0!==(r=a.get(e,!1,i))?r:l[t]:("string"===(o=typeof n)&&(r=B.exec(n))&&r[1]&&(n=z(e,t,r),o="number"),void(null!=n&&n==n&&("number"===o&&(n+=r&&r[3]||(f.cssNumber[s]?"":"px")),p.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,i))||(l[t]=n))))}},css:function(e,t,n,i){var r,o,a,s=f.camelCase(t);return t=f.cssProps[s]||(f.cssProps[s]=Ve(s)||s),(a=f.cssHooks[t]||f.cssHooks[s])&&"get"in a&&(r=a.get(e,!0,n)),void 0===r&&(r=Oe(e,t,i)),"normal"===r&&t in Ie&&(r=Ie[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),f.each(["height","width"],function(e,t){f.cssHooks[t]={get:function(e,n,i){return n?Me.test(f.css(e,"display"))&&0===e.offsetWidth?Se(e,Ne,function(){return Ue(e,t,i)}):Ue(e,t,i):void 0},set:function(e,n,i){var r,o=i&&De(e),a=i&&Fe(e,t,i,"border-box"===f.css(e,"boxSizing",!1,o),o);return a&&(r=B.exec(n))&&"px"!==(r[3]||"px")&&(e.style[t]=n,n=f.css(e,t)),Re(0,n,a)}}}),f.cssHooks.marginLeft=Ae(p.reliableMarginLeft,function(e,t){return t?(parseFloat(Oe(e,"marginLeft"))||e.getBoundingClientRect().left-Se(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px":void 0}),f.cssHooks.marginRight=Ae(p.reliableMarginRight,function(e,t){return t?Se(e,{display:"inline-block"},Oe,[e,"marginRight"]):void 0}),f.each({margin:"",padding:"",border:"Width"},function(e,t){f.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];4>i;i++)r[e+_[i]+t]=o[i]||o[i-2]||o[0];return r}},Ce.test(e)||(f.cssHooks[e+t].set=Re)}),f.fn.extend({css:function(e,t){return P(this,function(e,t,n){var i,r,o={},a=0;if(f.isArray(t)){for(i=De(e),r=t.length;r>a;a++)o[t[a]]=f.css(e,t[a],!1,i);return o}return void 0!==n?f.style(e,t,n):f.css(e,t)},e,t,arguments.length>1)},show:function(){return Le(this,!0)},hide:function(){return Le(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){W(this)?f(this).show():f(this).hide()})}}),f.Tween=He,He.prototype={constructor:He,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||f.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(f.cssNumber[n]?"":"px")},cur:function(){var e=He.propHooks[this.prop];return e&&e.get?e.get(this):He.propHooks._default.get(this)},run:function(e){var t,n=He.propHooks[this.prop];return this.options.duration?this.pos=t=f.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):He.propHooks._default.set(this),this}},He.prototype.init.prototype=He.prototype,He.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=f.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){f.fx.step[e.prop]?f.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[f.cssProps[e.prop]]&&!f.cssHooks[e.prop]?e.elem[e.prop]=e.now:f.style(e.elem,e.prop,e.now+e.unit)}}},He.propHooks.scrollTop=He.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},f.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},f.fx=He.prototype.init,f.fx.step={};var qe,Be,_e=/^(?:toggle|show|hide)$/,We=/queueHooks$/;function ze(){return e.setTimeout(function(){qe=void 0}),qe=f.now()}function Ye(e,t){var n,i=0,r={height:e};for(t=t?1:0;4>i;i+=2-t)r["margin"+(n=_[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function Ge(e,t,n){for(var i,r=(Ke.tweeners[t]||[]).concat(Ke.tweeners["*"]),o=0,a=r.length;a>o;o++)if(i=r[o].call(n,t,e))return i}function Ke(e,t,n){var i,r,o=0,a=Ke.prefilters.length,s=f.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=qe||ze(),n=Math.max(0,u.startTime+u.duration-t),i=1-(n/u.duration||0),o=0,a=u.tweens.length;a>o;o++)u.tweens[o].run(i);return s.notifyWith(e,[u,i,n]),1>i&&a?n:(s.resolveWith(e,[u]),!1)},u=s.promise({elem:e,props:f.extend({},t),opts:f.extend(!0,{specialEasing:{},easing:f.easing._default},n),originalProperties:t,originalOptions:n,startTime:qe||ze(),duration:n.duration,tweens:[],createTween:function(t,n){var i=f.Tween(e,u.opts,t,n,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(i),i},stop:function(t){var n=0,i=t?u.tweens.length:0;if(r)return this;for(r=!0;i>n;n++)u.tweens[n].run(1);return t?(s.notifyWith(e,[u,1,0]),s.resolveWith(e,[u,t])):s.rejectWith(e,[u,t]),this}}),c=u.props;for(function(e,t){var n,i,r,o,a;for(n in e)if(r=t[i=f.camelCase(n)],o=e[n],f.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(a=f.cssHooks[i])&&"expand"in a)for(n in o=a.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(c,u.opts.specialEasing);a>o;o++)if(i=Ke.prefilters[o].call(u,e,c,u.opts))return f.isFunction(i.stop)&&(f._queueHooks(u.elem,u.opts.queue).stop=f.proxy(i.stop,i)),i;return f.map(c,Ge,u),f.isFunction(u.opts.start)&&u.opts.start.call(e,u),f.fx.timer(f.extend(l,{elem:e,anim:u,queue:u.opts.queue})),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always)}f.Animation=f.extend(Ke,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return z(n.elem,e,B.exec(t),n),n}]},tweener:function(e,t){f.isFunction(e)?(t=e,e=["*"]):e=e.match(N);for(var n,i=0,r=e.length;r>i;i++)n=e[i],Ke.tweeners[n]=Ke.tweeners[n]||[],Ke.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,a,s,l,u,c=this,p={},d=e.style,h=e.nodeType&&W(e),m=R.get(e,"fxshow");for(i in n.queue||(null==(s=f._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,c.always(function(){c.always(function(){s.unqueued--,f.queue(e,"fx").length||s.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],"inline"===("none"===(u=f.css(e,"display"))?R.get(e,"olddisplay")||ke(e.nodeName):u)&&"none"===f.css(e,"float")&&(d.display="inline-block")),n.overflow&&(d.overflow="hidden",c.always(function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]})),t)if(r=t[i],_e.exec(r)){if(delete t[i],o=o||"toggle"===r,r===(h?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;h=!0}p[i]=m&&m[i]||f.style(e,i)}else u=void 0;if(f.isEmptyObject(p))"inline"===("none"===u?ke(e.nodeName):u)&&(d.display=u);else for(i in m?"hidden"in m&&(h=m.hidden):m=R.access(e,"fxshow",{}),o&&(m.hidden=!h),h?f(e).show():c.done(function(){f(e).hide()}),c.done(function(){var t;for(t in R.remove(e,"fxshow"),p)f.style(e,t,p[t])}),p)a=Ge(h?m[i]:0,i,c),i in m||(m[i]=a.start,h&&(a.end=a.start,a.start="width"===i||"height"===i?1:0))}],prefilter:function(e,t){t?Ke.prefilters.unshift(e):Ke.prefilters.push(e)}}),f.speed=function(e,t,n){var i=e&&"object"==typeof e?f.extend({},e):{complete:n||!n&&t||f.isFunction(e)&&e,duration:e,easing:n&&t||t&&!f.isFunction(t)&&t};return i.duration=f.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in f.fx.speeds?f.fx.speeds[i.duration]:f.fx.speeds._default,null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){f.isFunction(i.old)&&i.old.call(this),i.queue&&f.dequeue(this,i.queue)},i},f.fn.extend({fadeTo:function(e,t,n,i){return this.filter(W).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=f.isEmptyObject(e),o=f.speed(t,n,i),a=function(){var t=Ke(this,f.extend({},e),o);(r||R.get(this,"finish"))&&t.stop(!0)};return a.finish=a,r||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=f.timers,a=R.get(this);if(r)a[r]&&a[r].stop&&i(a[r]);else for(r in a)a[r]&&a[r].stop&&We.test(r)&&i(a[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));!t&&n||f.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,n=R.get(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=f.timers,a=i?i.length:0;for(n.finish=!0,f.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;a>t;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),f.each(["toggle","show","hide"],function(e,t){var n=f.fn[t];f.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(Ye(t,!0),e,i,r)}}),f.each({slideDown:Ye("show"),slideUp:Ye("hide"),slideToggle:Ye("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){f.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),f.timers=[],f.fx.tick=function(){var e,t=0,n=f.timers;for(qe=f.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||f.fx.stop(),qe=void 0},f.fx.timer=function(e){f.timers.push(e),e()?f.fx.start():f.timers.pop()},f.fx.interval=13,f.fx.start=function(){Be||(Be=e.setInterval(f.fx.tick,f.fx.interval))},f.fx.stop=function(){e.clearInterval(Be),Be=null},f.fx.speeds={slow:600,fast:200,_default:400},f.fn.delay=function(t,n){return t=f.fx&&f.fx.speeds[t]||t,n=n||"fx",this.queue(n,function(n,i){var r=e.setTimeout(n,t);i.stop=function(){e.clearTimeout(r)}})},function(){var e=i.createElement("input"),t=i.createElement("select"),n=t.appendChild(i.createElement("option"));e.type="checkbox",p.checkOn=""!==e.value,p.optSelected=n.selected,t.disabled=!0,p.optDisabled=!n.disabled,(e=i.createElement("input")).value="t",e.type="radio",p.radioValue="t"===e.value}();var Je,Xe=f.expr.attrHandle;f.fn.extend({attr:function(e,t){return P(this,f.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){f.removeAttr(this,e)})}}),f.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?f.prop(e,t,n):(1===o&&f.isXMLDoc(e)||(t=t.toLowerCase(),r=f.attrHooks[t]||(f.expr.match.bool.test(t)?Je:void 0)),void 0!==n?null===n?void f.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):r&&"get"in r&&null!==(i=r.get(e,t))?i:null==(i=f.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!p.radioValue&&"radio"===t&&f.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i,r=0,o=t&&t.match(N);if(o&&1===e.nodeType)for(;n=o[r++];)i=f.propFix[n]||n,f.expr.match.bool.test(n)&&(e[i]=!1),e.removeAttribute(n)}}),Je={set:function(e,t,n){return!1===t?f.removeAttr(e,n):e.setAttribute(n,n),n}},f.each(f.expr.match.bool.source.match(/\w+/g),function(e,t){var n=Xe[t]||f.find.attr;Xe[t]=function(e,t,i){var r,o;return i||(o=Xe[t],Xe[t]=r,r=null!=n(e,t,i)?t.toLowerCase():null,Xe[t]=o),r}});var Qe=/^(?:input|select|textarea|button)$/i,Ze=/^(?:a|area)$/i;f.fn.extend({prop:function(e,t){return P(this,f.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[f.propFix[e]||e]})}}),f.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&f.isXMLDoc(e)||(t=f.propFix[t]||t,r=f.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=f.find.attr(e,"tabindex");return t?parseInt(t,10):Qe.test(e.nodeName)||Ze.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),p.optSelected||(f.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),f.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){f.propFix[this.toLowerCase()]=this});var et=/[\t\r\n\f]/g;function tt(e){return e.getAttribute&&e.getAttribute("class")||""}f.fn.extend({addClass:function(e){var t,n,i,r,o,a,s,l=0;if(f.isFunction(e))return this.each(function(t){f(this).addClass(e.call(this,t,tt(this)))});if("string"==typeof e&&e)for(t=e.match(N)||[];n=this[l++];)if(r=tt(n),i=1===n.nodeType&&(" "+r+" ").replace(et," ")){for(a=0;o=t[a++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");r!==(s=f.trim(i))&&n.setAttribute("class",s)}return this},removeClass:function(e){var t,n,i,r,o,a,s,l=0;if(f.isFunction(e))return this.each(function(t){f(this).removeClass(e.call(this,t,tt(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(N)||[];n=this[l++];)if(r=tt(n),i=1===n.nodeType&&(" "+r+" ").replace(et," ")){for(a=0;o=t[a++];)for(;i.indexOf(" "+o+" ")>-1;)i=i.replace(" "+o+" "," ");r!==(s=f.trim(i))&&n.setAttribute("class",s)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):f.isFunction(e)?this.each(function(n){f(this).toggleClass(e.call(this,n,tt(this),t),t)}):this.each(function(){var t,i,r,o;if("string"===n)for(i=0,r=f(this),o=e.match(N)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else void 0!==e&&"boolean"!==n||((t=tt(this))&&R.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":R.get(this,"__className__")||""))})},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&(" "+tt(n)+" ").replace(et," ").indexOf(t)>-1)return!0;return!1}});var nt=/\r/g,it=/[\x20\t\r\n\f]+/g;f.fn.extend({val:function(e){var t,n,i,r=this[0];return arguments.length?(i=f.isFunction(e),this.each(function(n){var r;1===this.nodeType&&(null==(r=i?e.call(this,n,f(this).val()):e)?r="":"number"==typeof r?r+="":f.isArray(r)&&(r=f.map(r,function(e){return null==e?"":e+""})),(t=f.valHooks[this.type]||f.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))})):r?(t=f.valHooks[r.type]||f.valHooks[r.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:"string"==typeof(n=r.value)?n.replace(nt,""):null==n?"":n:void 0}}),f.extend({valHooks:{option:{get:function(e){var t=f.find.attr(e,"value");return null!=t?t:f.trim(f.text(e)).replace(it," ")}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type||0>r,a=o?null:[],s=o?r+1:i.length,l=0>r?s:o?r:0;s>l;l++)if(((n=i[l]).selected||l===r)&&(p.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!f.nodeName(n.parentNode,"optgroup"))){if(t=f(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=f.makeArray(t),a=r.length;a--;)((i=r[a]).selected=f.inArray(f.valHooks.option.get(i),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),f.each(["radio","checkbox"],function(){f.valHooks[this]={set:function(e,t){return f.isArray(t)?e.checked=f.inArray(f(e).val(),t)>-1:void 0}},p.checkOn||(f.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var rt=/^(?:focusinfocus|focusoutblur)$/;f.extend(f.event,{trigger:function(t,n,r,o){var a,s,l,u,p,d,h,m=[r||i],g=c.call(t,"type")?t.type:t,v=c.call(t,"namespace")?t.namespace.split("."):[];if(s=l=r=r||i,3!==r.nodeType&&8!==r.nodeType&&!rt.test(g+f.event.triggered)&&(g.indexOf(".")>-1&&(v=g.split("."),g=v.shift(),v.sort()),p=g.indexOf(":")<0&&"on"+g,(t=t[f.expando]?t:new f.Event(g,"object"==typeof t&&t)).isTrigger=o?2:3,t.namespace=v.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),n=null==n?[t]:f.makeArray(n,[t]),h=f.event.special[g]||{},o||!h.trigger||!1!==h.trigger.apply(r,n))){if(!o&&!h.noBubble&&!f.isWindow(r)){for(u=h.delegateType||g,rt.test(u+g)||(s=s.parentNode);s;s=s.parentNode)m.push(s),l=s;l===(r.ownerDocument||i)&&m.push(l.defaultView||l.parentWindow||e)}for(a=0;(s=m[a++])&&!t.isPropagationStopped();)t.type=a>1?u:h.bindType||g,(d=(R.get(s,"events")||{})[t.type]&&R.get(s,"handle"))&&d.apply(s,n),(d=p&&s[p])&&d.apply&&j(s)&&(t.result=d.apply(s,n),!1===t.result&&t.preventDefault());return t.type=g,o||t.isDefaultPrevented()||h._default&&!1!==h._default.apply(m.pop(),n)||!j(r)||p&&f.isFunction(r[g])&&!f.isWindow(r)&&((l=r[p])&&(r[p]=null),f.event.triggered=g,r[g](),f.event.triggered=void 0,l&&(r[p]=l)),t.result}},simulate:function(e,t,n){var i=f.extend(new f.Event,n,{type:e,isSimulated:!0});f.event.trigger(i,null,t)}}),f.fn.extend({trigger:function(e,t){return this.each(function(){f.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?f.event.trigger(e,t,n,!0):void 0}}),f.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){f.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),f.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),p.focusin="onfocusin"in e,p.focusin||f.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){f.event.simulate(t,e.target,f.event.fix(e))};f.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=R.access(i,t);r||i.addEventListener(e,n,!0),R.access(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=R.access(i,t)-1;r?R.access(i,t,r):(i.removeEventListener(e,n,!0),R.remove(i,t))}}});var ot=e.location,at=f.now(),st=/\?/;f.parseJSON=function(e){return JSON.parse(e+"")},f.parseXML=function(t){var n;if(!t||"string"!=typeof t)return null;try{n=(new e.DOMParser).parseFromString(t,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||f.error("Invalid XML: "+t),n};var lt=/#.*$/,ut=/([?&])_=[^&]*/,ct=/^(.*?):[ \t]*([^\r\n]*)$/gm,pt=/^(?:GET|HEAD)$/,dt=/^\/\//,ft={},ht={},mt="*/".concat("*"),gt=i.createElement("a");function vt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(N)||[];if(f.isFunction(n))for(;i=o[r++];)"+"===i[0]?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function $t(e,t,n,i){var r={},o=e===ht;function a(s){var l;return r[s]=!0,f.each(e[s]||[],function(e,s){var u=s(t,n,i);return"string"!=typeof u||o||r[u]?o?!(l=u):void 0:(t.dataTypes.unshift(u),a(u),!1)}),l}return a(t.dataTypes[0])||!r["*"]&&a("*")}function bt(e,t){var n,i,r=f.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i||(i={}))[n]=t[n]);return i&&f.extend(!0,e,i),e}gt.href=ot.href,f.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ot.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(ot.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":mt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":f.parseJSON,"text xml":f.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?bt(bt(e,f.ajaxSettings),t):bt(f.ajaxSettings,e)},ajaxPrefilter:vt(ft),ajaxTransport:vt(ht),ajax:function(t,n){"object"==typeof t&&(n=t,t=void 0),n=n||{};var r,o,a,s,l,u,c,p,d=f.ajaxSetup({},n),h=d.context||d,m=d.context&&(h.nodeType||h.jquery)?f(h):f.event,g=f.Deferred(),v=f.Callbacks("once memory"),$=d.statusCode||{},b={},y={},w=0,x="canceled",k={readyState:0,getResponseHeader:function(e){var t;if(2===w){if(!s)for(s={};t=ct.exec(a);)s[t[1].toLowerCase()]=t[2];t=s[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===w?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return w||(e=y[n]=y[n]||e,b[e]=t),this},overrideMimeType:function(e){return w||(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>w)for(t in e)$[t]=[$[t],e[t]];else k.always(e[k.status]);return this},abort:function(e){var t=e||x;return r&&r.abort(t),C(0,t),this}};if(g.promise(k).complete=v.add,k.success=k.done,k.error=k.fail,d.url=((t||d.url||ot.href)+"").replace(lt,"").replace(dt,ot.protocol+"//"),d.type=n.method||n.type||d.method||d.type,d.dataTypes=f.trim(d.dataType||"*").toLowerCase().match(N)||[""],null==d.crossDomain){u=i.createElement("a");try{u.href=d.url,u.href=u.href,d.crossDomain=gt.protocol+"//"+gt.host!=u.protocol+"//"+u.host}catch(e){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=f.param(d.data,d.traditional)),$t(ft,d,n,k),2===w)return k;for(p in(c=f.event&&d.global)&&0==f.active++&&f.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!pt.test(d.type),o=d.url,d.hasContent||(d.data&&(o=d.url+=(st.test(o)?"&":"?")+d.data,delete d.data),!1===d.cache&&(d.url=ut.test(o)?o.replace(ut,"$1_="+at++):o+(st.test(o)?"&":"?")+"_="+at++)),d.ifModified&&(f.lastModified[o]&&k.setRequestHeader("If-Modified-Since",f.lastModified[o]),f.etag[o]&&k.setRequestHeader("If-None-Match",f.etag[o])),(d.data&&d.hasContent&&!1!==d.contentType||n.contentType)&&k.setRequestHeader("Content-Type",d.contentType),k.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+mt+"; q=0.01":""):d.accepts["*"]),d.headers)k.setRequestHeader(p,d.headers[p]);if(d.beforeSend&&(!1===d.beforeSend.call(h,k,d)||2===w))return k.abort();for(p in x="abort",{success:1,error:1,complete:1})k[p](d[p]);if(r=$t(ht,d,n,k)){if(k.readyState=1,c&&m.trigger("ajaxSend",[k,d]),2===w)return k;d.async&&d.timeout>0&&(l=e.setTimeout(function(){k.abort("timeout")},d.timeout));try{w=1,r.send(b,C)}catch(e){if(!(2>w))throw e;C(-1,e)}}else C(-1,"No Transport");function C(t,n,i,s){var u,p,b,y,x,C=n;2!==w&&(w=2,l&&e.clearTimeout(l),r=void 0,a=s||"",k.readyState=t>0?4:0,u=t>=200&&300>t||304===t,i&&(y=function(e,t,n){for(var i,r,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in s)if(s[r]&&s[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}a||(a=r)}o=o||a}return o?(o!==l[0]&&l.unshift(o),n[o]):void 0}(d,k,i)),y=function(e,t,n,i){var r,o,a,s,l,u={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=u[l+" "+o]||u["* "+o]))for(r in u)if((s=r.split(" "))[1]===o&&(a=u[l+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[r]:!0!==u[r]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(d,y,k,u),u?(d.ifModified&&((x=k.getResponseHeader("Last-Modified"))&&(f.lastModified[o]=x),(x=k.getResponseHeader("etag"))&&(f.etag[o]=x)),204===t||"HEAD"===d.type?C="nocontent":304===t?C="notmodified":(C=y.state,p=y.data,u=!(b=y.error))):(b=C,!t&&C||(C="error",0>t&&(t=0))),k.status=t,k.statusText=(n||C)+"",u?g.resolveWith(h,[p,C,k]):g.rejectWith(h,[k,C,b]),k.statusCode($),$=void 0,c&&m.trigger(u?"ajaxSuccess":"ajaxError",[k,d,u?p:b]),v.fireWith(h,[k,C]),c&&(m.trigger("ajaxComplete",[k,d]),--f.active||f.event.trigger("ajaxStop")))}return k},getJSON:function(e,t,n){return f.get(e,t,n,"json")},getScript:function(e,t){return f.get(e,void 0,t,"script")}}),f.each(["get","post"],function(e,t){f[t]=function(e,n,i,r){return f.isFunction(n)&&(r=r||i,i=n,n=void 0),f.ajax(f.extend({url:e,type:t,dataType:r,data:n,success:i},f.isPlainObject(e)&&e))}}),f._evalUrl=function(e){return f.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},f.fn.extend({wrapAll:function(e){var t;return f.isFunction(e)?this.each(function(t){f(this).wrapAll(e.call(this,t))}):(this[0]&&(t=f(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this)},wrapInner:function(e){return f.isFunction(e)?this.each(function(t){f(this).wrapInner(e.call(this,t))}):this.each(function(){var t=f(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=f.isFunction(e);return this.each(function(n){f(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){f.nodeName(this,"body")||f(this).replaceWith(this.childNodes)}).end()}}),f.expr.filters.hidden=function(e){return!f.expr.filters.visible(e)},f.expr.filters.visible=function(e){return e.offsetWidth>0||e.offsetHeight>0||e.getClientRects().length>0};var yt=/%20/g,wt=/\[\]$/,xt=/\r?\n/g,kt=/^(?:submit|button|image|reset|file)$/i,Ct=/^(?:input|select|textarea|keygen)/i;function Tt(e,t,n,i){var r;if(f.isArray(t))f.each(t,function(t,r){n||wt.test(e)?i(e,r):Tt(e+"["+("object"==typeof r&&null!=r?t:"")+"]",r,n,i)});else if(n||"object"!==f.type(t))i(e,t);else for(r in t)Tt(e+"["+r+"]",t[r],n,i)}f.param=function(e,t){var n,i=[],r=function(e,t){t=f.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=f.ajaxSettings&&f.ajaxSettings.traditional),f.isArray(e)||e.jquery&&!f.isPlainObject(e))f.each(e,function(){r(this.name,this.value)});else for(n in e)Tt(n,e[n],t,r);return i.join("&").replace(yt,"+")},f.fn.extend({serialize:function(){return f.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=f.prop(this,"elements");return e?f.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!f(this).is(":disabled")&&Ct.test(this.nodeName)&&!kt.test(e)&&(this.checked||!Y.test(e))}).map(function(e,t){var n=f(this).val();return null==n?null:f.isArray(n)?f.map(n,function(e){return{name:t.name,value:e.replace(xt,"\r\n")}}):{name:t.name,value:n.replace(xt,"\r\n")}}).get()}}),f.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(e){}};var Dt={0:200,1223:204},St=f.ajaxSettings.xhr();p.cors=!!St&&"withCredentials"in St,p.ajax=St=!!St,f.ajaxTransport(function(t){var n,i;return p.cors||St&&!t.crossDomain?{send:function(r,o){var a,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(a in t.xhrFields)s[a]=t.xhrFields[a];for(a in t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||r["X-Requested-With"]||(r["X-Requested-With"]="XMLHttpRequest"),r)s.setRequestHeader(a,r[a]);n=function(e){return function(){n&&(n=i=s.onload=s.onerror=s.onabort=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!=typeof s.status?o(0,"error"):o(s.status,s.statusText):o(Dt[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=n(),i=s.onerror=n("error"),void 0!==s.onabort?s.onabort=i:s.onreadystatechange=function(){4===s.readyState&&e.setTimeout(function(){n&&i()})},n=n("abort");try{s.send(t.hasContent&&t.data||null)}catch(e){if(n)throw e}},abort:function(){n&&n()}}:void 0}),f.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return f.globalEval(e),e}}}),f.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),f.ajaxTransport("script",function(e){var t,n;if(e.crossDomain)return{send:function(r,o){t=f("<script>").prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&o("error"===e.type?404:200,e.type)}),i.head.appendChild(t[0])},abort:function(){n&&n()}}});var Et=[],Ot=/(=)\?(?=&|$)|\?\?/;f.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Et.pop()||f.expando+"_"+at++;return this[e]=!0,e}}),f.ajaxPrefilter("json jsonp",function(t,n,i){var r,o,a,s=!1!==t.jsonp&&(Ot.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ot.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(r=t.jsonpCallback=f.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(Ot,"$1"+r):!1!==t.jsonp&&(t.url+=(st.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return a||f.error(r+" was not called"),a[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){a=arguments},i.always(function(){void 0===o?f(e).removeProp(r):e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,Et.push(r)),a&&f.isFunction(o)&&o(a[0]),a=o=void 0}),"script"):void 0}),f.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||i;var r=k.exec(e),o=!n&&[];return r?[t.createElement(r[1])]:(r=ee([e],t,o),o&&o.length&&f(o).remove(),f.merge([],r.childNodes))};var At=f.fn.load;function Mt(e){return f.isWindow(e)?e:9===e.nodeType&&e.defaultView}f.fn.load=function(e,t,n){if("string"!=typeof e&&At)return At.apply(this,arguments);var i,r,o,a=this,s=e.indexOf(" ");return s>-1&&(i=f.trim(e.slice(s)),e=e.slice(0,s)),f.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),a.length>0&&f.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,a.html(i?f("<div>").append(f.parseHTML(e)).find(i):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},f.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){f.fn[t]=function(e){return this.on(t,e)}}),f.expr.filters.animated=function(e){return f.grep(f.timers,function(t){return e===t.elem}).length},f.offset={setOffset:function(e,t,n){var i,r,o,a,s,l,u=f.css(e,"position"),c=f(e),p={};"static"===u&&(e.style.position="relative"),s=c.offset(),o=f.css(e,"top"),l=f.css(e,"left"),("absolute"===u||"fixed"===u)&&(o+l).indexOf("auto")>-1?(a=(i=c.position()).top,r=i.left):(a=parseFloat(o)||0,r=parseFloat(l)||0),f.isFunction(t)&&(t=t.call(e,n,f.extend({},s))),null!=t.top&&(p.top=t.top-s.top+a),null!=t.left&&(p.left=t.left-s.left+r),"using"in t?t.using.call(e,p):c.css(p)}},f.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){f.offset.setOffset(this,e,t)});var t,n,i=this[0],r={top:0,left:0},o=i&&i.ownerDocument;return o?(t=o.documentElement,f.contains(t,i)?(r=i.getBoundingClientRect(),n=Mt(o),{top:r.top+n.pageYOffset-t.clientTop,left:r.left+n.pageXOffset-t.clientLeft}):r):void 0},position:function(){if(this[0]){var e,t,n=this[0],i={top:0,left:0};return"fixed"===f.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),f.nodeName(e[0],"html")||(i=e.offset()),i.top+=f.css(e[0],"borderTopWidth",!0),i.left+=f.css(e[0],"borderLeftWidth",!0)),{top:t.top-i.top-f.css(n,"marginTop",!0),left:t.left-i.left-f.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===f.css(e,"position");)e=e.offsetParent;return e||Ee})}}),f.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n="pageYOffset"===t;f.fn[e]=function(i){return P(this,function(e,i,r){var o=Mt(e);return void 0===r?o?o[t]:e[i]:void(o?o.scrollTo(n?o.pageXOffset:r,n?r:o.pageYOffset):e[i]=r)},e,i,arguments.length)}}),f.each(["top","left"],function(e,t){f.cssHooks[t]=Ae(p.pixelPosition,function(e,n){return n?(n=Oe(e,t),Te.test(n)?f(e).position()[t]+"px":n):void 0})}),f.each({Height:"height",Width:"width"},function(e,t){f.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){f.fn[i]=function(i,r){var o=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===r?"margin":"border");return P(this,function(t,n,i){var r;return f.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===i?f.css(t,n,a):f.style(t,n,i,a)},t,o?i:void 0,o,null)}})}),f.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},size:function(){return this.length}}),f.fn.andSelf=f.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return f});var Nt=e.jQuery,It=e.$;return f.noConflict=function(t){return e.$===f&&(e.$=It),t&&e.jQuery===f&&(e.jQuery=Nt),f},t||(e.jQuery=e.$=f),f}),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(e){"use strict";var t=jQuery.fn.jquery.split(" ")[0].split(".");if(t[0]<2&&t[1]<9||1==t[0]&&9==t[1]&&t[2]<1||t[0]>3)throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4")}(),function(e){"use strict";e.fn.emulateTransitionEnd=function(t){var n=!1,i=this;e(this).one("bsTransitionEnd",function(){n=!0});return setTimeout(function(){n||e(i).trigger(e.support.transition.end)},t),this},e(function(){e.support.transition=function(){var e=document.createElement("bootstrap"),t={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var n in t)if(void 0!==e.style[n])return{end:t[n]};return!1}(),e.support.transition&&(e.event.special.bsTransitionEnd={bindType:e.support.transition.end,delegateType:e.support.transition.end,handle:function(t){if(e(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}})})}(jQuery),function(e){"use strict";var t='[data-dismiss="alert"]',n=function(n){e(n).on("click",t,this.close)};n.VERSION="3.3.7",n.TRANSITION_DURATION=150,n.prototype.close=function(t){function i(){a.detach().trigger("closed.bs.alert").remove()}var r=e(this),o=r.attr("data-target");o||(o=(o=r.attr("href"))&&o.replace(/.*(?=#[^\s]*$)/,""));var a=e("#"===o?[]:o);t&&t.preventDefault(),a.length||(a=r.closest(".alert")),a.trigger(t=e.Event("close.bs.alert")),t.isDefaultPrevented()||(a.removeClass("in"),e.support.transition&&a.hasClass("fade")?a.one("bsTransitionEnd",i).emulateTransitionEnd(n.TRANSITION_DURATION):i())};var i=e.fn.alert;e.fn.alert=function(t){return this.each(function(){var i=e(this),r=i.data("bs.alert");r||i.data("bs.alert",r=new n(this)),"string"==typeof t&&r[t].call(i)})},e.fn.alert.Constructor=n,e.fn.alert.noConflict=function(){return e.fn.alert=i,this},e(document).on("click.bs.alert.data-api",t,n.prototype.close)}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var i=e(this),r=i.data("bs.button"),o="object"==typeof t&&t;r||i.data("bs.button",r=new n(this,o)),"toggle"==t?r.toggle():t&&r.setState(t)})}var n=function(t,i){this.$element=e(t),this.options=e.extend({},n.DEFAULTS,i),this.isLoading=!1};n.VERSION="3.3.7",n.DEFAULTS={loadingText:"loading..."},n.prototype.setState=function(t){var n="disabled",i=this.$element,r=i.is("input")?"val":"html",o=i.data();t+="Text",null==o.resetText&&i.data("resetText",i[r]()),setTimeout(e.proxy(function(){i[r](null==o[t]?this.options[t]:o[t]),"loadingText"==t?(this.isLoading=!0,i.addClass(n).attr(n,n).prop(n,!0)):this.isLoading&&(this.isLoading=!1,i.removeClass(n).removeAttr(n).prop(n,!1))},this),0)},n.prototype.toggle=function(){var e=!0,t=this.$element.closest('[data-toggle="buttons"]');if(t.length){var n=this.$element.find("input");"radio"==n.prop("type")?(n.prop("checked")&&(e=!1),t.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==n.prop("type")&&(n.prop("checked")!==this.$element.hasClass("active")&&(e=!1),this.$element.toggleClass("active")),n.prop("checked",this.$element.hasClass("active")),e&&n.trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")};var i=e.fn.button;e.fn.button=t,e.fn.button.Constructor=n,e.fn.button.noConflict=function(){return e.fn.button=i,this},e(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(n){var i=e(n.target).closest(".btn");t.call(i,"toggle"),e(n.target).is('input[type="radio"], input[type="checkbox"]')||(n.preventDefault(),i.is("input,button")?i.trigger("focus"):i.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(t){e(t.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(t.type))})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var i=e(this),r=i.data("bs.carousel"),o=e.extend({},n.DEFAULTS,i.data(),"object"==typeof t&&t),a="string"==typeof t?t:o.slide;r||i.data("bs.carousel",r=new n(this,o)),"number"==typeof t?r.to(t):a?r[a]():o.interval&&r.pause().cycle()})}var n=function(t,n){this.$element=e(t),this.$indicators=this.$element.find(".carousel-indicators"),this.options=n,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",e.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",e.proxy(this.pause,this)).on("mouseleave.bs.carousel",e.proxy(this.cycle,this))};n.VERSION="3.3.7",n.TRANSITION_DURATION=600,n.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},n.prototype.keydown=function(e){if(!/input|textarea/i.test(e.target.tagName)){switch(e.which){case 37:this.prev();break;case 39:this.next();break;default:return}e.preventDefault()}},n.prototype.cycle=function(t){return t||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(e.proxy(this.next,this),this.options.interval)),this},n.prototype.getItemIndex=function(e){return this.$items=e.parent().children(".item"),this.$items.index(e||this.$active)},n.prototype.getItemForDirection=function(e,t){var n=this.getItemIndex(t);if(("prev"==e&&0===n||"next"==e&&n==this.$items.length-1)&&!this.options.wrap)return t;var i=(n+("prev"==e?-1:1))%this.$items.length;return this.$items.eq(i)},n.prototype.to=function(e){var t=this,n=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(e>this.$items.length-1||e<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){t.to(e)}):n==e?this.pause().cycle():this.slide(e>n?"next":"prev",this.$items.eq(e))},n.prototype.pause=function(t){return t||(this.paused=!0),this.$element.find(".next, .prev").length&&e.support.transition&&(this.$element.trigger(e.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},n.prototype.next=function(){if(!this.sliding)return this.slide("next")},n.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},n.prototype.slide=function(t,i){var r=this.$element.find(".item.active"),o=i||this.getItemForDirection(t,r),a=this.interval,s="next"==t?"left":"right",l=this;if(o.hasClass("active"))return this.sliding=!1;var u=o[0],c=e.Event("slide.bs.carousel",{relatedTarget:u,direction:s});if(this.$element.trigger(c),!c.isDefaultPrevented()){if(this.sliding=!0,a&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var p=e(this.$indicators.children()[this.getItemIndex(o)]);p&&p.addClass("active")}var d=e.Event("slid.bs.carousel",{relatedTarget:u,direction:s});return e.support.transition&&this.$element.hasClass("slide")?(o.addClass(t),o[0].offsetWidth,r.addClass(s),o.addClass(s),r.one("bsTransitionEnd",function(){o.removeClass([t,s].join(" ")).addClass("active"),r.removeClass(["active",s].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(d)},0)}).emulateTransitionEnd(n.TRANSITION_DURATION)):(r.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger(d)),a&&this.cycle(),this}};var i=e.fn.carousel;e.fn.carousel=t,e.fn.carousel.Constructor=n,e.fn.carousel.noConflict=function(){return e.fn.carousel=i,this};var r=function(n){var i,r=e(this),o=e(r.attr("data-target")||(i=r.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,""));if(o.hasClass("carousel")){var a=e.extend({},o.data(),r.data()),s=r.attr("data-slide-to");s&&(a.interval=!1),t.call(o,a),s&&o.data("bs.carousel").to(s),n.preventDefault()}};e(document).on("click.bs.carousel.data-api","[data-slide]",r).on("click.bs.carousel.data-api","[data-slide-to]",r),e(window).on("load",function(){e('[data-ride="carousel"]').each(function(){var n=e(this);t.call(n,n.data())})})}(jQuery),function(e){"use strict";function t(t){var n,i=t.attr("data-target")||(n=t.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,"");return e(i)}function n(t){return this.each(function(){var n=e(this),r=n.data("bs.collapse"),o=e.extend({},i.DEFAULTS,n.data(),"object"==typeof t&&t);!r&&o.toggle&&/show|hide/.test(t)&&(o.toggle=!1),r||n.data("bs.collapse",r=new i(this,o)),"string"==typeof t&&r[t]()})}var i=function(t,n){this.$element=e(t),this.options=e.extend({},i.DEFAULTS,n),this.$trigger=e('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'),this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};i.VERSION="3.3.7",i.TRANSITION_DURATION=350,i.DEFAULTS={toggle:!0},i.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},i.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var t,r=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(r&&r.length&&(t=r.data("bs.collapse"),t&&t.transitioning))){var o=e.Event("show.bs.collapse");if(this.$element.trigger(o),!o.isDefaultPrevented()){r&&r.length&&(n.call(r,"hide"),t||r.data("bs.collapse",null));var a=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[a](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("collapse in")[a](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!e.support.transition)return s.call(this);var l=e.camelCase(["scroll",a].join("-"));this.$element.one("bsTransitionEnd",e.proxy(s,this)).emulateTransitionEnd(i.TRANSITION_DURATION)[a](this.$element[0][l])}}}},i.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var t=e.Event("hide.bs.collapse");if(this.$element.trigger(t),!t.isDefaultPrevented()){var n=this.dimension();this.$element[n](this.$element[n]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var r=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};return e.support.transition?void this.$element[n](0).one("bsTransitionEnd",e.proxy(r,this)).emulateTransitionEnd(i.TRANSITION_DURATION):r.call(this)}}},i.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},i.prototype.getParent=function(){return e(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(e.proxy(function(n,i){var r=e(i);this.addAriaAndCollapsedClass(t(r),r)},this)).end()},i.prototype.addAriaAndCollapsedClass=function(e,t){var n=e.hasClass("in");e.attr("aria-expanded",n),t.toggleClass("collapsed",!n).attr("aria-expanded",n)};var r=e.fn.collapse;e.fn.collapse=n,e.fn.collapse.Constructor=i,e.fn.collapse.noConflict=function(){return e.fn.collapse=r,this},e(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(i){var r=e(this);r.attr("data-target")||i.preventDefault();var o=t(r),a=o.data("bs.collapse")?"toggle":r.data();n.call(o,a)})}(jQuery),function(e){"use strict";function t(t){var n=t.attr("data-target");n||(n=(n=t.attr("href"))&&/#[A-Za-z]/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,""));var i=n&&e(n);return i&&i.length?i:t.parent()}function n(n){n&&3===n.which||(e(i).remove(),e(r).each(function(){var i=e(this),r=t(i),o={relatedTarget:this};r.hasClass("open")&&(n&&"click"==n.type&&/input|textarea/i.test(n.target.tagName)&&e.contains(r[0],n.target)||(r.trigger(n=e.Event("hide.bs.dropdown",o)),n.isDefaultPrevented()||(i.attr("aria-expanded","false"),r.removeClass("open").trigger(e.Event("hidden.bs.dropdown",o)))))}))}var i=".dropdown-backdrop",r='[data-toggle="dropdown"]',o=function(t){e(t).on("click.bs.dropdown",this.toggle)};o.VERSION="3.3.7",o.prototype.toggle=function(i){var r=e(this);if(!r.is(".disabled, :disabled")){var o=t(r),a=o.hasClass("open");if(n(),!a){"ontouchstart"in document.documentElement&&!o.closest(".navbar-nav").length&&e(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(e(this)).on("click",n);var s={relatedTarget:this};if(o.trigger(i=e.Event("show.bs.dropdown",s)),i.isDefaultPrevented())return;r.trigger("focus").attr("aria-expanded","true"),o.toggleClass("open").trigger(e.Event("shown.bs.dropdown",s))}return!1}},o.prototype.keydown=function(n){if(/(38|40|27|32)/.test(n.which)&&!/input|textarea/i.test(n.target.tagName)){var i=e(this);if(n.preventDefault(),n.stopPropagation(),!i.is(".disabled, :disabled")){var o=t(i),a=o.hasClass("open");if(!a&&27!=n.which||a&&27==n.which)return 27==n.which&&o.find(r).trigger("focus"),i.trigger("click");var s=o.find(".dropdown-menu li:not(.disabled):visible a");if(s.length){var l=s.index(n.target);38==n.which&&l>0&&l--,40==n.which&&l<s.length-1&&l++,~l||(l=0),s.eq(l).trigger("focus")}}}};var a=e.fn.dropdown;e.fn.dropdown=function(t){return this.each(function(){var n=e(this),i=n.data("bs.dropdown");i||n.data("bs.dropdown",i=new o(this)),"string"==typeof t&&i[t].call(n)})},e.fn.dropdown.Constructor=o,e.fn.dropdown.noConflict=function(){return e.fn.dropdown=a,this},e(document).on("click.bs.dropdown.data-api",n).on("click.bs.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}).on("click.bs.dropdown.data-api",r,o.prototype.toggle).on("keydown.bs.dropdown.data-api",r,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(e){"use strict";function t(t,i){return this.each(function(){var r=e(this),o=r.data("bs.modal"),a=e.extend({},n.DEFAULTS,r.data(),"object"==typeof t&&t);o||r.data("bs.modal",o=new n(this,a)),"string"==typeof t?o[t](i):a.show&&o.show(i)})}var n=function(t,n){this.options=n,this.$body=e(document.body),this.$element=e(t),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,e.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};n.VERSION="3.3.7",n.TRANSITION_DURATION=300,n.BACKDROP_TRANSITION_DURATION=150,n.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},n.prototype.toggle=function(e){return this.isShown?this.hide():this.show(e)},n.prototype.show=function(t){var i=this,r=e.Event("show.bs.modal",{relatedTarget:t});this.$element.trigger(r),this.isShown||r.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',e.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){i.$element.one("mouseup.dismiss.bs.modal",function(t){e(t.target).is(i.$element)&&(i.ignoreBackdropClick=!0)})}),this.backdrop(function(){var r=e.support.transition&&i.$element.hasClass("fade");i.$element.parent().length||i.$element.appendTo(i.$body),i.$element.show().scrollTop(0),i.adjustDialog(),r&&i.$element[0].offsetWidth,i.$element.addClass("in"),i.enforceFocus();var o=e.Event("shown.bs.modal",{relatedTarget:t});r?i.$dialog.one("bsTransitionEnd",function(){i.$element.trigger("focus").trigger(o)}).emulateTransitionEnd(n.TRANSITION_DURATION):i.$element.trigger("focus").trigger(o)}))},n.prototype.hide=function(t){t&&t.preventDefault(),t=e.Event("hide.bs.modal"),this.$element.trigger(t),this.isShown&&!t.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),e(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),e.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",e.proxy(this.hideModal,this)).emulateTransitionEnd(n.TRANSITION_DURATION):this.hideModal())},n.prototype.enforceFocus=function(){e(document).off("focusin.bs.modal").on("focusin.bs.modal",e.proxy(function(e){document===e.target||this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.trigger("focus")},this))},n.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",e.proxy(function(e){27==e.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},n.prototype.resize=function(){this.isShown?e(window).on("resize.bs.modal",e.proxy(this.handleUpdate,this)):e(window).off("resize.bs.modal")},n.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop(function(){e.$body.removeClass("modal-open"),e.resetAdjustments(),e.resetScrollbar(),e.$element.trigger("hidden.bs.modal")})},n.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},n.prototype.backdrop=function(t){var i=this,r=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var o=e.support.transition&&r;if(this.$backdrop=e(document.createElement("div")).addClass("modal-backdrop "+r).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",e.proxy(function(e){return this.ignoreBackdropClick?void(this.ignoreBackdropClick=!1):void(e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide()))},this)),o&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!t)return;o?this.$backdrop.one("bsTransitionEnd",t).emulateTransitionEnd(n.BACKDROP_TRANSITION_DURATION):t()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var a=function(){i.removeBackdrop(),t&&t()};e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",a).emulateTransitionEnd(n.BACKDROP_TRANSITION_DURATION):a()}else t&&t()},n.prototype.handleUpdate=function(){this.adjustDialog()},n.prototype.adjustDialog=function(){var e=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&e?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!e?this.scrollbarWidth:""})},n.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},n.prototype.checkScrollbar=function(){var e=window.innerWidth;if(!e){var t=document.documentElement.getBoundingClientRect();e=t.right-Math.abs(t.left)}this.bodyIsOverflowing=document.body.clientWidth<e,this.scrollbarWidth=this.measureScrollbar()},n.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"",this.bodyIsOverflowing&&this.$body.css("padding-right",e+this.scrollbarWidth)},n.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad)},n.prototype.measureScrollbar=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",this.$body.append(e);var t=e.offsetWidth-e.clientWidth;return this.$body[0].removeChild(e),t};var i=e.fn.modal;e.fn.modal=t,e.fn.modal.Constructor=n,e.fn.modal.noConflict=function(){return e.fn.modal=i,this},e(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(n){var i=e(this),r=i.attr("href"),o=e(i.attr("data-target")||r&&r.replace(/.*(?=#[^\s]+$)/,"")),a=o.data("bs.modal")?"toggle":e.extend({remote:!/#/.test(r)&&r},o.data(),i.data());i.is("a")&&n.preventDefault(),o.one("show.bs.modal",function(e){e.isDefaultPrevented()||o.one("hidden.bs.modal",function(){i.is(":visible")&&i.trigger("focus")})}),t.call(o,a,this)})}(jQuery),function(e){"use strict";var t=function(e,t){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",e,t)};t.VERSION="3.3.7",t.TRANSITION_DURATION=150,t.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},t.prototype.init=function(t,n,i){if(this.enabled=!0,this.type=t,this.$element=e(n),this.options=this.getOptions(i),this.$viewport=this.options.viewport&&e(e.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var r=this.options.trigger.split(" "),o=r.length;o--;){var a=r[o];if("click"==a)this.$element.on("click."+this.type,this.options.selector,e.proxy(this.toggle,this));else if("manual"!=a){var s="hover"==a?"mouseenter":"focusin",l="hover"==a?"mouseleave":"focusout";this.$element.on(s+"."+this.type,this.options.selector,e.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,e.proxy(this.leave,this))}}this.options.selector?this._options=e.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},t.prototype.getDefaults=function(){return t.DEFAULTS},t.prototype.getOptions=function(t){return(t=e.extend({},this.getDefaults(),this.$element.data(),t)).delay&&"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),t},t.prototype.getDelegateOptions=function(){var t={},n=this.getDefaults();return this._options&&e.each(this._options,function(e,i){n[e]!=i&&(t[e]=i)}),t},t.prototype.enter=function(t){var n=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);return n||(n=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,n)),t instanceof e.Event&&(n.inState["focusin"==t.type?"focus":"hover"]=!0),n.tip().hasClass("in")||"in"==n.hoverState?void(n.hoverState="in"):(clearTimeout(n.timeout),n.hoverState="in",n.options.delay&&n.options.delay.show?void(n.timeout=setTimeout(function(){"in"==n.hoverState&&n.show()},n.options.delay.show)):n.show())},t.prototype.isInStateTrue=function(){for(var e in this.inState)if(this.inState[e])return!0;return!1},t.prototype.leave=function(t){var n=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);if(n||(n=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,n)),t instanceof e.Event&&(n.inState["focusout"==t.type?"focus":"hover"]=!1),!n.isInStateTrue())return clearTimeout(n.timeout),n.hoverState="out",n.options.delay&&n.options.delay.hide?void(n.timeout=setTimeout(function(){"out"==n.hoverState&&n.hide()},n.options.delay.hide)):n.hide()},t.prototype.show=function(){var n=e.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(n);var i=e.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(n.isDefaultPrevented()||!i)return;var r=this,o=this.tip(),a=this.getUID(this.type);this.setContent(),o.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&o.addClass("fade");var s="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,l=/\s?auto?\s?/i,u=l.test(s);u&&(s=s.replace(l,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(s).data("bs."+this.type,this),this.options.container?o.appendTo(this.options.container):o.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var c=this.getPosition(),p=o[0].offsetWidth,d=o[0].offsetHeight;if(u){var f=s,h=this.getPosition(this.$viewport);s="bottom"==s&&c.bottom+d>h.bottom?"top":"top"==s&&c.top-d<h.top?"bottom":"right"==s&&c.right+p>h.width?"left":"left"==s&&c.left-p<h.left?"right":s,o.removeClass(f).addClass(s)}var m=this.getCalculatedOffset(s,c,p,d);this.applyPlacement(m,s);var g=function(){var e=r.hoverState;r.$element.trigger("shown.bs."+r.type),r.hoverState=null,"out"==e&&r.leave(r)};e.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",g).emulateTransitionEnd(t.TRANSITION_DURATION):g()}},t.prototype.applyPlacement=function(t,n){var i=this.tip(),r=i[0].offsetWidth,o=i[0].offsetHeight,a=parseInt(i.css("margin-top"),10),s=parseInt(i.css("margin-left"),10);isNaN(a)&&(a=0),isNaN(s)&&(s=0),t.top+=a,t.left+=s,e.offset.setOffset(i[0],e.extend({using:function(e){i.css({top:Math.round(e.top),left:Math.round(e.left)})}},t),0),i.addClass("in");var l=i[0].offsetWidth,u=i[0].offsetHeight;"top"==n&&u!=o&&(t.top=t.top+o-u);var c=this.getViewportAdjustedDelta(n,t,l,u);c.left?t.left+=c.left:t.top+=c.top;var p=/top|bottom/.test(n),d=p?2*c.left-r+l:2*c.top-o+u,f=p?"offsetWidth":"offsetHeight";i.offset(t),this.replaceArrow(d,i[0][f],p)},t.prototype.replaceArrow=function(e,t,n){this.arrow().css(n?"left":"top",50*(1-e/t)+"%").css(n?"top":"left","")},t.prototype.setContent=function(){var e=this.tip(),t=this.getTitle();e.find(".tooltip-inner")[this.options.html?"html":"text"](t),e.removeClass("fade in top bottom left right")},t.prototype.hide=function(n){function i(){"in"!=r.hoverState&&o.detach(),r.$element&&r.$element.removeAttr("aria-describedby").trigger("hidden.bs."+r.type),n&&n()}var r=this,o=e(this.$tip),a=e.Event("hide.bs."+this.type);if(this.$element.trigger(a),!a.isDefaultPrevented())return o.removeClass("in"),e.support.transition&&o.hasClass("fade")?o.one("bsTransitionEnd",i).emulateTransitionEnd(t.TRANSITION_DURATION):i(),this.hoverState=null,this},t.prototype.fixTitle=function(){var e=this.$element;(e.attr("title")||"string"!=typeof e.attr("data-original-title"))&&e.attr("data-original-title",e.attr("title")||"").attr("title","")},t.prototype.hasContent=function(){return this.getTitle()},t.prototype.getPosition=function(t){var n=(t=t||this.$element)[0],i="BODY"==n.tagName,r=n.getBoundingClientRect();null==r.width&&(r=e.extend({},r,{width:r.right-r.left,height:r.bottom-r.top}));var o=window.SVGElement&&n instanceof window.SVGElement,a=i?{top:0,left:0}:o?null:t.offset(),s={scroll:i?document.documentElement.scrollTop||document.body.scrollTop:t.scrollTop()},l=i?{width:e(window).width(),height:e(window).height()}:null;return e.extend({},r,s,l,a)},t.prototype.getCalculatedOffset=function(e,t,n,i){return"bottom"==e?{top:t.top+t.height,left:t.left+t.width/2-n/2}:"top"==e?{top:t.top-i,left:t.left+t.width/2-n/2}:"left"==e?{top:t.top+t.height/2-i/2,left:t.left-n}:{top:t.top+t.height/2-i/2,left:t.left+t.width}},t.prototype.getViewportAdjustedDelta=function(e,t,n,i){var r={top:0,left:0};if(!this.$viewport)return r;var o=this.options.viewport&&this.options.viewport.padding||0,a=this.getPosition(this.$viewport);if(/right|left/.test(e)){var s=t.top-o-a.scroll,l=t.top+o-a.scroll+i;s<a.top?r.top=a.top-s:l>a.top+a.height&&(r.top=a.top+a.height-l)}else{var u=t.left-o,c=t.left+o+n;u<a.left?r.left=a.left-u:c>a.right&&(r.left=a.left+a.width-c)}return r},t.prototype.getTitle=function(){var e=this.$element,t=this.options;return e.attr("data-original-title")||("function"==typeof t.title?t.title.call(e[0]):t.title)},t.prototype.getUID=function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},t.prototype.tip=function(){if(!this.$tip&&(this.$tip=e(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},t.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},t.prototype.enable=function(){this.enabled=!0},t.prototype.disable=function(){this.enabled=!1},t.prototype.toggleEnabled=function(){this.enabled=!this.enabled},t.prototype.toggle=function(t){var n=this;t&&((n=e(t.currentTarget).data("bs."+this.type))||(n=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,n))),t?(n.inState.click=!n.inState.click,n.isInStateTrue()?n.enter(n):n.leave(n)):n.tip().hasClass("in")?n.leave(n):n.enter(n)},t.prototype.destroy=function(){var e=this;clearTimeout(this.timeout),this.hide(function(){e.$element.off("."+e.type).removeData("bs."+e.type),e.$tip&&e.$tip.detach(),e.$tip=null,e.$arrow=null,e.$viewport=null,e.$element=null})};var n=e.fn.tooltip;e.fn.tooltip=function(n){return this.each(function(){var i=e(this),r=i.data("bs.tooltip"),o="object"==typeof n&&n;!r&&/destroy|hide/.test(n)||(r||i.data("bs.tooltip",r=new t(this,o)),"string"==typeof n&&r[n]())})},e.fn.tooltip.Constructor=t,e.fn.tooltip.noConflict=function(){return e.fn.tooltip=n,this}}(jQuery),function(e){"use strict";var t=function(e,t){this.init("popover",e,t)};if(!e.fn.tooltip)throw new Error("Popover requires tooltip.js");t.VERSION="3.3.7",t.DEFAULTS=e.extend({},e.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),t.prototype=e.extend({},e.fn.tooltip.Constructor.prototype),t.prototype.constructor=t,t.prototype.getDefaults=function(){return t.DEFAULTS},t.prototype.setContent=function(){var e=this.tip(),t=this.getTitle(),n=this.getContent();e.find(".popover-title")[this.options.html?"html":"text"](t),e.find(".popover-content").children().detach().end()[this.options.html?"string"==typeof n?"html":"append":"text"](n),e.removeClass("fade top bottom left right in"),e.find(".popover-title").html()||e.find(".popover-title").hide()},t.prototype.hasContent=function(){return this.getTitle()||this.getContent()},t.prototype.getContent=function(){var e=this.$element,t=this.options;return e.attr("data-content")||("function"==typeof t.content?t.content.call(e[0]):t.content)},t.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var n=e.fn.popover;e.fn.popover=function(n){return this.each(function(){var i=e(this),r=i.data("bs.popover"),o="object"==typeof n&&n;!r&&/destroy|hide/.test(n)||(r||i.data("bs.popover",r=new t(this,o)),"string"==typeof n&&r[n]())})},e.fn.popover.Constructor=t,e.fn.popover.noConflict=function(){return e.fn.popover=n,this}}(jQuery),function(e){"use strict";function t(n,i){this.$body=e(document.body),this.$scrollElement=e(e(n).is(document.body)?window:n),this.options=e.extend({},t.DEFAULTS,i),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",e.proxy(this.process,this)),this.refresh(),this.process()}function n(n){return this.each(function(){var i=e(this),r=i.data("bs.scrollspy"),o="object"==typeof n&&n;r||i.data("bs.scrollspy",r=new t(this,o)),"string"==typeof n&&r[n]()})}t.VERSION="3.3.7",t.DEFAULTS={offset:10},t.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},t.prototype.refresh=function(){var t=this,n="offset",i=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),e.isWindow(this.$scrollElement[0])||(n="position",i=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var t=e(this),r=t.data("target")||t.attr("href"),o=/^#./.test(r)&&e(r);return o&&o.length&&o.is(":visible")&&[[o[n]().top+i,r]]||null}).sort(function(e,t){return e[0]-t[0]}).each(function(){t.offsets.push(this[0]),t.targets.push(this[1])})},t.prototype.process=function(){var e,t=this.$scrollElement.scrollTop()+this.options.offset,n=this.getScrollHeight(),i=this.options.offset+n-this.$scrollElement.height(),r=this.offsets,o=this.targets,a=this.activeTarget;if(this.scrollHeight!=n&&this.refresh(),t>=i)return a!=(e=o[o.length-1])&&this.activate(e);if(a&&t<r[0])return this.activeTarget=null,this.clear();for(e=r.length;e--;)a!=o[e]&&t>=r[e]&&(void 0===r[e+1]||t<r[e+1])&&this.activate(o[e])},t.prototype.activate=function(t){this.activeTarget=t,this.clear();var n=this.selector+'[data-target="'+t+'"],'+this.selector+'[href="'+t+'"]',i=e(n).parents("li").addClass("active");i.parent(".dropdown-menu").length&&(i=i.closest("li.dropdown").addClass("active")),i.trigger("activate.bs.scrollspy")},t.prototype.clear=function(){e(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var i=e.fn.scrollspy;e.fn.scrollspy=n,e.fn.scrollspy.Constructor=t,e.fn.scrollspy.noConflict=function(){return e.fn.scrollspy=i,this},e(window).on("load.bs.scrollspy.data-api",function(){e('[data-spy="scroll"]').each(function(){var t=e(this);n.call(t,t.data())})})}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var i=e(this),r=i.data("bs.tab");r||i.data("bs.tab",r=new n(this)),"string"==typeof t&&r[t]()})}var n=function(t){this.element=e(t)};n.VERSION="3.3.7",n.TRANSITION_DURATION=150,n.prototype.show=function(){var t=this.element,n=t.closest("ul:not(.dropdown-menu)"),i=t.data("target");if(i||(i=(i=t.attr("href"))&&i.replace(/.*(?=#[^\s]*$)/,"")),!t.parent("li").hasClass("active")){var r=n.find(".active:last a"),o=e.Event("hide.bs.tab",{relatedTarget:t[0]}),a=e.Event("show.bs.tab",{relatedTarget:r[0]});if(r.trigger(o),t.trigger(a),!a.isDefaultPrevented()&&!o.isDefaultPrevented()){var s=e(i);this.activate(t.closest("li"),n),this.activate(s,s.parent(),function(){r.trigger({type:"hidden.bs.tab",relatedTarget:t[0]}),t.trigger({type:"shown.bs.tab",relatedTarget:r[0]})})}}},n.prototype.activate=function(t,i,r){function o(){a.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),t.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),s?(t[0].offsetWidth,t.addClass("in")):t.removeClass("fade"),t.parent(".dropdown-menu").length&&t.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),r&&r()}var a=i.find("> .active"),s=r&&e.support.transition&&(a.length&&a.hasClass("fade")||!!i.find("> .fade").length);a.length&&s?a.one("bsTransitionEnd",o).emulateTransitionEnd(n.TRANSITION_DURATION):o(),a.removeClass("in")};var i=e.fn.tab;e.fn.tab=t,e.fn.tab.Constructor=n,e.fn.tab.noConflict=function(){return e.fn.tab=i,this};var r=function(n){n.preventDefault(),t.call(e(this),"show")};e(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',r).on("click.bs.tab.data-api",'[data-toggle="pill"]',r)}(jQuery),function(e){"use strict";function t(t){return this.each(function(){var i=e(this),r=i.data("bs.affix"),o="object"==typeof t&&t;r||i.data("bs.affix",r=new n(this,o)),"string"==typeof t&&r[t]()})}var n=function(t,i){this.options=e.extend({},n.DEFAULTS,i),this.$target=e(this.options.target).on("scroll.bs.affix.data-api",e.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",e.proxy(this.checkPositionWithEventLoop,this)),this.$element=e(t),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};n.VERSION="3.3.7",n.RESET="affix affix-top affix-bottom",n.DEFAULTS={offset:0,target:window},n.prototype.getState=function(e,t,n,i){var r=this.$target.scrollTop(),o=this.$element.offset(),a=this.$target.height();if(null!=n&&"top"==this.affixed)return r<n&&"top";if("bottom"==this.affixed)return null!=n?!(r+this.unpin<=o.top)&&"bottom":!(r+a<=e-i)&&"bottom";var s=null==this.affixed,l=s?r:o.top;return null!=n&&r<=n?"top":null!=i&&l+(s?a:t)>=e-i&&"bottom"},n.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(n.RESET).addClass("affix");var e=this.$target.scrollTop(),t=this.$element.offset();return this.pinnedOffset=t.top-e},n.prototype.checkPositionWithEventLoop=function(){setTimeout(e.proxy(this.checkPosition,this),1)},n.prototype.checkPosition=function(){if(this.$element.is(":visible")){var t=this.$element.height(),i=this.options.offset,r=i.top,o=i.bottom,a=Math.max(e(document).height(),e(document.body).height());"object"!=typeof i&&(o=r=i),"function"==typeof r&&(r=i.top(this.$element)),"function"==typeof o&&(o=i.bottom(this.$element));var s=this.getState(a,t,r,o);if(this.affixed!=s){null!=this.unpin&&this.$element.css("top","");var l="affix"+(s?"-"+s:""),u=e.Event(l+".bs.affix");if(this.$element.trigger(u),u.isDefaultPrevented())return;this.affixed=s,this.unpin="bottom"==s?this.getPinnedOffset():null,this.$element.removeClass(n.RESET).addClass(l).trigger(l.replace("affix","affixed")+".bs.affix")}"bottom"==s&&this.$element.offset({top:a-t-o})}};var i=e.fn.affix;e.fn.affix=t,e.fn.affix.Constructor=n,e.fn.affix.noConflict=function(){return e.fn.affix=i,this},e(window).on("load",function(){e('[data-spy="affix"]').each(function(){var n=e(this),i=n.data();i.offset=i.offset||{},null!=i.offsetBottom&&(i.offset.bottom=i.offsetBottom),null!=i.offsetTop&&(i.offset.top=i.offsetTop),t.call(n,i)})})}(jQuery),function(e){"use strict";function t(e){if(!b(e))return zn;$(e.objectMaxDepth)&&(zn.objectMaxDepth=n(e.objectMaxDepth)?e.objectMaxDepth:NaN)}function n(e){return x(e)&&0<e}function i(e,t){return t=t||Error,function(){var n,i=arguments[0];for(n="["+(e?e+":":"")+i+"] http://errors.angularjs.org/1.6.10/"+(e?e+"/":"")+i,i=1;i<arguments.length;i++){var r;n=n+(1==i?"?":"&")+"p"+(i-1)+"=",n+=encodeURIComponent(r="function"==typeof(r=arguments[i])?r.toString().replace(/ \{[\s\S]*$/,""):void 0===r?"undefined":"string"!=typeof r?JSON.stringify(r):r)}return new t(n)}}function r(e){if(null==e||S(e))return!1;if(li(e)||w(e)||Bn&&e instanceof Bn)return!0;var t="length"in Object(e)&&e.length;return x(t)&&(0<=t&&(t-1 in e||e instanceof Array)||"function"==typeof e.item)}function o(e,t,n){var i,a;if(e)if(T(e))for(i in e)"prototype"!==i&&"length"!==i&&"name"!==i&&e.hasOwnProperty(i)&&t.call(n,e[i],i,e);else if(li(e)||r(e)){var s="object"!=typeof e;for(i=0,a=e.length;i<a;i++)(s||i in e)&&t.call(n,e[i],i,e)}else if(e.forEach&&e.forEach!==o)e.forEach(t,n,e);else if(y(e))for(i in e)t.call(n,e[i],i,e);else if("function"==typeof e.hasOwnProperty)for(i in e)e.hasOwnProperty(i)&&t.call(n,e[i],i,e);else for(i in e)Gn.call(e,i)&&t.call(n,e[i],i,e);return e}function a(e,t,n){for(var i=Object.keys(e).sort(),r=0;r<i.length;r++)t.call(n,e[i[r]],i[r]);return i}function s(e){return function(t,n){e(n,t)}}function l(e,t,n){for(var i=e.$$hashKey,r=0,o=t.length;r<o;++r){var a=t[r];if(b(a)||T(a))for(var s=Object.keys(a),u=0,c=s.length;u<c;u++){var p=s[u],d=a[p];n&&b(d)?k(d)?e[p]=new Date(d.valueOf()):D(d)?e[p]=new RegExp(d):d.nodeName?e[p]=d.cloneNode(!0):A(d)?e[p]=d.clone():(b(e[p])||(e[p]=li(d)?[]:{}),l(e[p],[d],!0)):e[p]=d}}return i?e.$$hashKey=i:delete e.$$hashKey,e}function u(e){return l(e,Xn.call(arguments,1),!1)}function c(e){return l(e,Xn.call(arguments,1),!0)}function p(e){return parseInt(e,10)}function d(e,t){return u(Object.create(e),t)}function f(){}function h(e){return e}function m(e){return function(){return e}}function g(e){return T(e.toString)&&e.toString!==ei}function v(e){return void 0===e}function $(e){return void 0!==e}function b(e){return null!==e&&"object"==typeof e}function y(e){return null!==e&&"object"==typeof e&&!ti(e)}function w(e){return"string"==typeof e}function x(e){return"number"==typeof e}function k(e){return"[object Date]"===ei.call(e)}function C(e){switch(ei.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return e instanceof Error}}function T(e){return"function"==typeof e}function D(e){return"[object RegExp]"===ei.call(e)}function S(e){return e&&e.window===e}function E(e){return e&&e.$evalAsync&&e.$watch}function O(e){return"boolean"==typeof e}function A(e){return!(!e||!(e.nodeName||e.prop&&e.attr&&e.find))}function M(e){return Kn(e.nodeName||e[0]&&e[0].nodeName)}function N(e,t){var n=e.indexOf(t);return 0<=n&&e.splice(n,1),n}function I(e,t,i){function r(e,t,n){if(0>--n)return"...";var i,r=t.$$hashKey;if(li(e)){i=0;for(var o=e.length;i<o;i++)t.push(a(e[i],n))}else if(y(e))for(i in e)t[i]=a(e[i],n);else if(e&&"function"==typeof e.hasOwnProperty)for(i in e)e.hasOwnProperty(i)&&(t[i]=a(e[i],n));else for(i in e)Gn.call(e,i)&&(t[i]=a(e[i],n));return r?t.$$hashKey=r:delete t.$$hashKey,t}function a(e,t){if(!b(e))return e;if(-1!==(n=l.indexOf(e)))return u[n];if(S(e)||E(e))throw ni("cpws");var n=!1,i=s(e);return void 0===i&&(i=li(e)?[]:Object.create(ti(e)),n=!0),l.push(e),u.push(i),n?r(e,i,t):i}function s(e){switch(ei.call(e)){case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Float32Array]":case"[object Float64Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return new e.constructor(a(e.buffer),e.byteOffset,e.length);case"[object ArrayBuffer]":if(!e.slice){var t=new ArrayBuffer(e.byteLength);return new Uint8Array(t).set(new Uint8Array(e)),t}return e.slice(0);case"[object Boolean]":case"[object Number]":case"[object String]":case"[object Date]":return new e.constructor(e.valueOf());case"[object RegExp]":return(t=new RegExp(e.source,e.toString().match(/[^/]*$/)[0])).lastIndex=e.lastIndex,t;case"[object Blob]":return new e.constructor([e],{type:e.type})}if(T(e.cloneNode))return e.cloneNode(!0)}var l=[],u=[];if(i=n(i)?i:NaN,t){if(function(e){return e&&x(e.length)&&ui.test(ei.call(e))}(t)||"[object ArrayBuffer]"===ei.call(t))throw ni("cpta");if(e===t)throw ni("cpi");return li(t)?t.length=0:o(t,function(e,n){"$$hashKey"!==n&&delete t[n]}),l.push(e),u.push(t),r(e,t,i)}return a(e,i)}function P(e,t){return e===t||e!=e&&t!=t}function j(e,t){if(e===t)return!0;if(null===e||null===t)return!1;if(e!=e&&t!=t)return!0;var n,i=typeof e;if(i===typeof t&&"object"===i){if(!li(e)){if(k(e))return!!k(t)&&P(e.getTime(),t.getTime());if(D(e))return!!D(t)&&e.toString()===t.toString();if(E(e)||E(t)||S(e)||S(t)||li(t)||k(t)||D(t))return!1;for(n in i=ae(),e)if("$"!==n.charAt(0)&&!T(e[n])){if(!j(e[n],t[n]))return!1;i[n]=!0}for(n in t)if(!(n in i)&&"$"!==n.charAt(0)&&$(t[n])&&!T(t[n]))return!1;return!0}if(!li(t))return!1;if((i=e.length)===t.length){for(n=0;n<i;n++)if(!j(e[n],t[n]))return!1;return!0}}return!1}function V(e,t,n){return e.concat(Xn.call(t,n))}function R(e,t){var n=2<arguments.length?Xn.call(arguments,2):[];return!T(t)||t instanceof RegExp?t:n.length?function(){return arguments.length?t.apply(e,V(n,arguments,0)):t.apply(e,n)}:function(){return arguments.length?t.apply(e,arguments):t.call(e)}}function F(t,n){var i=n;return"string"==typeof t&&"$"===t.charAt(0)&&"$"===t.charAt(1)?i=void 0:S(n)?i="$WINDOW":n&&e.document===n?i="$DOCUMENT":E(n)&&(i="$SCOPE"),i}function U(e,t){if(!v(e))return x(t)||(t=t?2:null),JSON.stringify(e,F,t)}function L(e){return w(e)?JSON.parse(e):e}function H(e,t){e=e.replace(hi,"");var n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return oi(n)?t:n}function q(e,t){return(e=new Date(e.getTime())).setMinutes(e.getMinutes()+t),e}function B(e,t,n){n=n?-1:1;var i=e.getTimezoneOffset();return q(e,n*((t=H(t,i))-i))}function _(e){e=Bn(e).clone().empty();var t=Bn("<div></div>").append(e).html();try{return e[0].nodeType===bi?Kn(t):t.match(/^(<[^>]+>)/)[1].replace(/^<([\w-]+)/,function(e,t){return"<"+Kn(t)})}catch(e){return Kn(t)}}function W(e){try{return decodeURIComponent(e)}catch(e){}}function z(e){var t={};return o((e||"").split("&"),function(e){var n,i,r;e&&(i=e=e.replace(/\+/g,"%20"),-1!==(n=e.indexOf("="))&&(i=e.substring(0,n),r=e.substring(n+1)),$(i=W(i))&&(r=!$(r)||W(r),Gn.call(t,i)?li(t[i])?t[i].push(r):t[i]=[t[i],r]:t[i]=r))}),t}function Y(e){var t=[];return o(e,function(e,n){li(e)?o(e,function(e){t.push(K(n,!0)+(!0===e?"":"="+K(e,!0)))}):t.push(K(n,!0)+(!0===e?"":"="+K(e,!0)))}),t.length?t.join("&"):""}function G(e){return K(e,!0).replace(/%26/gi,"&").replace(/%3D/gi,"=").replace(/%2B/gi,"+")}function K(e,t){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%20/g,t?"%20":"+")}function J(t,n){var i,r,a={};o(mi,function(e){e+="app",!i&&t.hasAttribute&&t.hasAttribute(e)&&(i=t,r=t.getAttribute(e))}),o(mi,function(e){var n;e+="app",!i&&(n=t.querySelector("["+e.replace(":","\\:")+"]"))&&(i=n,r=n.getAttribute(e))}),i&&(gi?(a.strictDi=null!==function(e,t){var n,i,r=mi.length;for(i=0;i<r;++i)if(n=mi[i]+t,w(n=e.getAttribute(n)))return n;return null}(i,"strict-di"),n(i,r?[r]:[],a)):e.console.error("AngularJS: disabling automatic bootstrap. <script> protocol indicates an extension, document.location.href does not match."))}function X(t,n,i){b(i)||(i={}),i=u({strictDi:!1},i);var r=function(){if((t=Bn(t)).injector()){var r=t[0]===e.document?"document":_(t);throw ni("btstrpd",r.replace(/</,"&lt;").replace(/>/,"&gt;"))}return(n=n||[]).unshift(["$provide",function(e){e.value("$rootElement",t)}]),i.debugInfoEnabled&&n.push(["$compileProvider",function(e){e.debugInfoEnabled(!0)}]),n.unshift("ng"),(r=Re(n,i.strictDi)).invoke(["$rootScope","$rootElement","$compile","$injector",function(e,t,n,i){e.$apply(function(){t.data("$injector",i),n(t)(e)})}]),r},a=/^NG_ENABLE_DEBUG_INFO!/,s=/^NG_DEFER_BOOTSTRAP!/;if(e&&a.test(e.name)&&(i.debugInfoEnabled=!0,e.name=e.name.replace(a,"")),e&&!s.test(e.name))return r();e.name=e.name.replace(s,""),ii.resumeBootstrap=function(e){return o(e,function(e){n.push(e)}),r()},T(ii.resumeDeferredBootstrap)&&ii.resumeDeferredBootstrap()}function Q(){e.name="NG_ENABLE_DEBUG_INFO!"+e.name,e.location.reload()}function Z(e){if(!(e=ii.element(e).injector()))throw ni("test");return e.get("$$testability")}function ee(e,t){return t=t||"_",e.replace(vi,function(e,n){return(n?t:"")+e.toLowerCase()})}function te(e,t,n){if(!e)throw ni("areq",t||"?",n||"required");return e}function ne(e,t,n){return n&&li(e)&&(e=e[e.length-1]),te(T(e),t,"not a function, got "+(e&&"object"==typeof e?e.constructor.name||"Object":typeof e)),e}function ie(e,t){if("hasOwnProperty"===e)throw ni("badname",t)}function re(e,t,n){if(!t)return e;for(var i,r=e,o=(t=t.split(".")).length,a=0;a<o;a++)i=t[a],e&&(e=(r=e)[i]);return!n&&T(e)?R(r,e):e}function oe(e){for(var t,n=e[0],i=e[e.length-1],r=1;n!==i&&(n=n.nextSibling);r++)(t||e[r]!==n)&&(t||(t=Bn(Xn.call(e,0,r))),t.push(n));return t||e}function ae(){return Object.create(null)}function se(e){if(null==e)return"";switch(typeof e){case"string":break;case"number":e=""+e;break;default:e=!g(e)||li(e)||k(e)?U(e):e.toString()}return e}function le(e,t){if(li(e)){t=t||[];for(var n=0,i=e.length;n<i;n++)t[n]=e[n]}else if(b(e))for(n in t=t||{},e)"$"===n.charAt(0)&&"$"===n.charAt(1)||(t[n]=e[n]);return t||e}function ue(e,t){var i=[];return n(t)&&(e=ii.copy(e,null,t)),JSON.stringify(e,function(e,t){if(b(t=F(e,t))){if(0<=i.indexOf(t))return"...";i.push(t)}return t})}function ce(e,t){return t.toUpperCase()}function pe(e){return e.replace(ki,ce)}function de(e){return 1===(e=e.nodeType)||!e||9===e}function fe(e,t){var n,i,r=t.createDocumentFragment(),a=[];if(Ei.test(e)){for(n=r.appendChild(t.createElement("div")),i=(Oi.exec(e)||["",""])[1].toLowerCase(),i=Mi[i]||Mi._default,n.innerHTML=i[1]+e.replace(Ai,"<$1></$2>")+i[2],i=i[0];i--;)n=n.lastChild;a=V(a,n.childNodes),(n=r.firstChild).textContent=""}else a.push(t.createTextNode(e));return r.textContent="",r.innerHTML="",o(a,function(e){r.appendChild(e)}),r}function he(t){if(t instanceof he)return t;var n,i;if(w(t)&&(t=ci(t),n=!0),!(this instanceof he)){if(n&&"<"!==t.charAt(0))throw Di("nosel");return new he(t)}n?(n=e.document,Ce(this,t=(i=Si.exec(t))?[n.createElement(i[1])]:(i=fe(t,n))?i.childNodes:[])):T(t)?Oe(t):Ce(this,t)}function me(e){return e.cloneNode(!0)}function ge(e,t){!t&&de(e)&&Bn.cleanData([e]),e.querySelectorAll&&Bn.cleanData(e.querySelectorAll("*"))}function ve(e,t,n,i){if($(i))throw Di("offargs");var r=(i=be(e))&&i.events,a=i&&i.handle;if(a)if(t){var s=function(t){var i=r[t];$(n)&&N(i||[],n),$(n)&&i&&0<i.length||(e.removeEventListener(t,a),delete r[t])};o(t.split(" "),function(e){s(e),Ti[e]&&s(Ti[e])})}else for(t in r)"$destroy"!==t&&e.removeEventListener(t,a),delete r[t]}function $e(e,t){var n=e.ng339,i=n&&wi[n];i&&(t?delete i.data[t]:(i.handle&&(i.events.$destroy&&i.handle({},"$destroy"),ve(e)),delete wi[n],e.ng339=void 0))}function be(e,t){var n=(n=e.ng339)&&wi[n];return t&&!n&&(e.ng339=n=++xi,n=wi[n]={events:{},data:{},handle:void 0}),n}function ye(e,t,n){if(de(e)){var i,r=$(n),o=!r&&t&&!b(t),a=!t;if(e=(e=be(e,!o))&&e.data,r)e[pe(t)]=n;else{if(a)return e;if(o)return e&&e[pe(t)];for(i in t)e[pe(i)]=t[i]}}}function we(e,t){return!!e.getAttribute&&-1<(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," ").indexOf(" "+t+" ")}function xe(e,t){if(t&&e.setAttribute){var n=(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," "),i=n;o(t.split(" "),function(e){e=ci(e),i=i.replace(" "+e+" "," ")}),i!==n&&e.setAttribute("class",ci(i))}}function ke(e,t){if(t&&e.setAttribute){var n=(" "+(e.getAttribute("class")||"")+" ").replace(/[\n\t]/g," "),i=n;o(t.split(" "),function(e){e=ci(e),-1===i.indexOf(" "+e+" ")&&(i+=e+" ")}),i!==n&&e.setAttribute("class",ci(i))}}function Ce(e,t){if(t)if(t.nodeType)e[e.length++]=t;else{var n=t.length;if("number"==typeof n&&t.window!==t){if(n)for(var i=0;i<n;i++)e[e.length++]=t[i]}else e[e.length++]=t}}function Te(e,t){return De(e,"$"+(t||"ngController")+"Controller")}function De(e,t,n){for(9===e.nodeType&&(e=e.documentElement),t=li(t)?t:[t];e;){for(var i=0,r=t.length;i<r;i++)if($(n=Bn.data(e,t[i])))return n;e=e.parentNode||11===e.nodeType&&e.host}}function Se(e){for(ge(e,!0);e.firstChild;)e.removeChild(e.firstChild)}function Ee(e,t){t||ge(e);var n=e.parentNode;n&&n.removeChild(e)}function Oe(t){function n(){e.document.removeEventListener("DOMContentLoaded",n),e.removeEventListener("load",n),t()}"complete"===e.document.readyState?e.setTimeout(t):(e.document.addEventListener("DOMContentLoaded",n),e.addEventListener("load",n))}function Ae(e,t){var n=Pi[t.toLowerCase()];return n&&ji[M(e)]&&n}function Me(e,t,n){n.call(e,t)}function Ne(e,t,n){var i=t.relatedTarget;i&&(i===e||Ni.call(e,i))||n.call(e,t)}function Ie(){this.$get=function(){return u(he,{hasClass:function(e,t){return e.attr&&(e=e[0]),we(e,t)},addClass:function(e,t){return e.attr&&(e=e[0]),ke(e,t)},removeClass:function(e,t){return e.attr&&(e=e[0]),xe(e,t)}})}}function Pe(e,t){var n=e&&e.$$hashKey;return n?("function"==typeof n&&(n=e.$$hashKey()),n):"function"===(n=typeof e)||"object"===n&&null!==e?e.$$hashKey=n+":"+(t||function(){return++ri})():n+":"+e}function je(){this._keys=[],this._values=[],this._lastKey=NaN,this._lastIndex=-1}function Ve(e){return(e=Function.prototype.toString.call(e).replace(_i,"")).match(Li)||e.match(Hi)}function Re(e,t){function n(e){return function(t,n){if(!b(t))return e(t,n);o(t,s(e))}}function i(e,t){if(ie(e,"service"),(T(t)||li(t))&&(t=h.instantiate(t)),!t.$get)throw Wi("pget",e);return f[e+"Provider"]=t}function r(e,t){return function(){var n=y.invoke(t,this);if(v(n))throw Wi("undef",e);return n}}function a(e,t,n){return i(e,{$get:!1!==n?r(e,t):t})}function l(e){te(v(e)||li(e),"modulesToLoad","not an array");var t,n=[];return o(e,function(e){function i(e){var t,n;for(t=0,n=e.length;t<n;t++){var i=e[t],r=h.get(i[0]);r[i[1]].apply(r,i[2])}}if(!d.get(e)){d.set(e,!0);try{w(e)?(t=Wn(e),y.modules[e]=t,n=n.concat(l(t.requires)).concat(t._runBlocks),i(t._invokeQueue),i(t._configBlocks)):T(e)?n.push(h.invoke(e)):li(e)?n.push(h.invoke(e)):ne(e,"module")}catch(t){throw li(e)&&(e=e[e.length-1]),t.message&&t.stack&&-1===t.stack.indexOf(t.message)&&(t=t.message+"\n"+t.stack),Wi("modulerr",e,t.stack||t.message||t)}}}),n}function u(e,n){function i(t,i){if(e.hasOwnProperty(t)){if(e[t]===c)throw Wi("cdep",t+" <- "+p.join(" <- "));return e[t]}try{return p.unshift(t),e[t]=c,e[t]=n(t,i),e[t]}catch(n){throw e[t]===c&&delete e[t],n}finally{p.shift()}}function r(e,n,r){for(var o=[],a=0,s=(e=Re.$$annotate(e,t,r)).length;a<s;a++){var l=e[a];if("string"!=typeof l)throw Wi("itkn",l);o.push(n&&n.hasOwnProperty(l)?n[l]:i(l,r))}return o}return{invoke:function(e,t,n,i){if("string"==typeof n&&(i=n,n=null),n=r(e,n,i),li(e)&&(e=e[e.length-1]),i=e,qn||"function"!=typeof i)i=!1;else{var o=i.$$ngIsClass;O(o)||(o=i.$$ngIsClass=/^(?:class\b|constructor\()/.test(Function.prototype.toString.call(i))),i=o}return i?(n.unshift(null),new(Function.prototype.bind.apply(e,n))):e.apply(t,n)},instantiate:function(e,t,n){var i=li(e)?e[e.length-1]:e;return(e=r(e,t,n)).unshift(null),new(Function.prototype.bind.apply(i,e))},get:i,annotate:Re.$$annotate,has:function(t){return f.hasOwnProperty(t+"Provider")||e.hasOwnProperty(t)}}}t=!0===t;var c={},p=[],d=new Fi,f={$provide:{provider:n(i),factory:n(a),service:n(function(e,t){return a(e,["$injector",function(e){return e.instantiate(t)}])}),value:n(function(e,t){return a(e,m(t),!1)}),constant:n(function(e,t){ie(e,"constant"),f[e]=t,g[e]=t}),decorator:function(e,t){var n=h.get(e+"Provider"),i=n.$get;n.$get=function(){var e=y.invoke(i,n);return y.invoke(t,null,{$delegate:e})}}}},h=f.$injector=u(f,function(e,t){throw ii.isString(t)&&p.push(t),Wi("unpr",p.join(" <- "))}),g={},$=u(g,function(e,t){var n=h.get(e+"Provider",t);return y.invoke(n.$get,n,void 0,e)}),y=$;f.$injectorProvider={$get:m($)},y.modules=h.modules=ae();var x=l(e);return(y=$.get("$injector")).strictDi=t,o(x,function(e){e&&y.invoke(e)}),y.loadNewModules=function(e){o(l(e),function(e){e&&y.invoke(e)})},y}function Fe(){var t=!0;this.disableAutoScrolling=function(){t=!1},this.$get=["$window","$location","$rootScope",function(n,i,r){function o(e){var t;e?(e.scrollIntoView(),T(t=a.yOffset)?t=t():A(t)?(t=t[0],t="fixed"!==n.getComputedStyle(t).position?0:t.getBoundingClientRect().bottom):x(t)||(t=0),t&&(e=e.getBoundingClientRect().top,n.scrollBy(0,e-t))):n.scrollTo(0,0)}function a(e){var t;(e=w(e)?e:x(e)?e.toString():i.hash())?(t=s.getElementById(e))?o(t):(t=function(e){var t=null;return Array.prototype.some.call(e,function(e){if("a"===M(e))return t=e,!0}),t}(s.getElementsByName(e)))?o(t):"top"===e&&o(null):o(null)}var s=n.document;return t&&r.$watch(function(){return i.hash()},function(t,n){t===n&&""===t||function(t,n){"complete"===(n=n||e).document.readyState?n.setTimeout(t):Bn(n).on("load",t)}(function(){r.$evalAsync(a)})}),a}]}function Ue(e,t){return e||t?e?t?(li(e)&&(e=e.join(" ")),li(t)&&(t=t.join(" ")),e+" "+t):e:t:""}function Le(e){return b(e)?e:{}}function He(e,t,n,i){function r(e){try{e.apply(null,Xn.call(arguments,1))}finally{if(0===--g)for(;$.length;)try{$.pop()()}catch(e){n.error(e)}}}function a(){k=null,l()}function s(){j(b=v(b=C())?null:b,S)&&(b=S),y=S=b}function l(){var e=y;s(),w===u.url()&&e===b||(w=u.url(),y=b,o(T,function(e){e(u.url(),b)}))}var u=this,c=e.location,p=e.history,d=e.setTimeout,h=e.clearTimeout,m={};u.isMock=!1;var g=0,$=[];u.$$completeOutstandingRequest=r,u.$$incOutstandingRequestCount=function(){g++},u.notifyWhenNoOutstandingRequests=function(e){0===g?e():$.push(e)};var b,y,w=c.href,x=t.find("base"),k=null,C=i.history?function(){try{return p.state}catch(e){}}:f;s(),u.url=function(t,n,r){if(v(r)&&(r=null),c!==e.location&&(c=e.location),p!==e.history&&(p=e.history),t){var o=y===r;if(w===t&&(!i.history||o))return u;var a=w&&$t(w)===$t(t);return w=t,y=r,!i.history||a&&o?(a||(k=t),n?c.replace(t):a?(n=c,r=-1===(r=t.indexOf("#"))?"":t.substr(r),n.hash=r):c.href=t,c.href!==t&&(k=t)):(p[n?"replaceState":"pushState"](r,"",t),s()),k&&(k=t),u}return k||c.href.replace(/%27/g,"'")},u.state=function(){return b};var T=[],D=!1,S=null;u.onUrlChange=function(t){return D||(i.history&&Bn(e).on("popstate",a),Bn(e).on("hashchange",a),D=!0),T.push(t),t},u.$$applicationDestroyed=function(){Bn(e).off("hashchange popstate",a)},u.$$checkUrlChange=l,u.baseHref=function(){var e=x.attr("href");return e?e.replace(/^(https?:)?\/\/[^/]*/,""):""},u.defer=function(e,t){var n;return g++,n=d(function(){delete m[n],r(e)},t||0),m[n]=!0,n},u.defer.cancel=function(e){return!!m[e]&&(delete m[e],h(e),r(f),!0)}}function qe(){this.$get=["$window","$log","$sniffer","$document",function(e,t,n,i){return new He(e,i,t,n)}]}function Be(){this.$get=function(){function e(e,n){function r(e){e!==d&&(f?f===e&&(f=e.n):f=e,o(e.n,e.p),o(e,d),(d=e).n=null)}function o(e,t){e!==t&&(e&&(e.p=t),t&&(t.n=e))}if(e in t)throw i("$cacheFactory")("iid",e);var a=0,s=u({},n,{id:e}),l=ae(),c=n&&n.capacity||Number.MAX_VALUE,p=ae(),d=null,f=null;return t[e]={put:function(e,t){if(!v(t)){if(c<Number.MAX_VALUE)r(p[e]||(p[e]={key:e}));return e in l||a++,l[e]=t,a>c&&this.remove(f.key),t}},get:function(e){if(c<Number.MAX_VALUE){var t=p[e];if(!t)return;r(t)}return l[e]},remove:function(e){if(c<Number.MAX_VALUE){var t=p[e];if(!t)return;t===d&&(d=t.p),t===f&&(f=t.n),o(t.n,t.p),delete p[e]}e in l&&(delete l[e],a--)},removeAll:function(){l=ae(),a=0,p=ae(),d=f=null},destroy:function(){p=s=l=null,delete t[e]},info:function(){return u({},s,{size:a})}}}var t={};return e.info=function(){var e={};return o(t,function(t,n){e[n]=t.info()}),e},e.get=function(e){return t[e]},e}}function _e(){this.$get=["$cacheFactory",function(e){return e("templates")}]}function We(t,n){function i(e,t,n){var i=/^([@&<]|=(\*?))(\??)\s*([\w$]*)$/,r=ae();return o(e,function(e,o){if((e=e.trim())in x)r[o]=x[e];else{var a=e.match(i);if(!a)throw Zi("iscp",t,o,e,n?"controller bindings definition":"isolate scope definition");r[o]={mode:a[1][0],collection:"*"===a[2],optional:"?"===a[3],attrName:a[4]||o},a[4]&&(x[e]=r[o])}}),r}function r(e){var t=e.require||e.controller&&e.name;return!li(t)&&b(t)&&o(t,function(e,n){var i=e.match(g);e.substring(i[0].length)||(t[n]=i[0]+n)}),t}var a={},l=/^\s*directive:\s*([\w-]+)\s+(.*)$/,c=/(([\w-]+)(?::([^;]+))?;?)/,p=function(e){var t,n={};for(e=e.split(","),t=0;t<e.length;t++)n[e[t]]=!0;return n}("ngSrc,ngSrcset,src,srcset"),g=/^(?:(\^\^?)?(\?)?(\^\^?)?)?/,y=/^(on[a-z]+|formaction)$/,x=ae();this.directive=function e(n,i){return te(n,"name"),ie(n,"directive"),w(n)?(function(e){var t=e.charAt(0);if(!t||t!==Kn(t))throw Zi("baddir",e);if(e!==e.trim())throw Zi("baddir",e)}(n),te(i,"directiveFactory"),a.hasOwnProperty(n)||(a[n]=[],t.factory(n+"Directive",["$injector","$exceptionHandler",function(e,t){var i=[];return o(a[n],function(o,a){try{var s=e.invoke(o);T(s)?s={compile:m(s)}:!s.compile&&s.link&&(s.compile=m(s.link)),s.priority=s.priority||0,s.index=a,s.name=s.name||n,s.require=r(s);var l=s,u=s.restrict;if(u&&(!w(u)||!/[EACM]/.test(u)))throw Zi("badrestrict",u,n);l.restrict=u||"EA",s.$$moduleName=o.$$moduleName,i.push(s)}catch(e){t(e)}}),i}])),a[n].push(i)):o(n,s(e)),this},this.component=function e(t,n){function i(e){function t(t){return T(t)||li(t)?function(n,i){return e.invoke(t,this,{$element:n,$attrs:i})}:t}var i=n.template||n.templateUrl?n.template:"",a={controller:r,controllerAs:Je(n.controller)||n.controllerAs||"$ctrl",template:t(i),templateUrl:t(n.templateUrl),transclude:n.transclude,scope:{},bindToController:n.bindings||{},restrict:"E",require:n.require};return o(n,function(e,t){"$"===t.charAt(0)&&(a[t]=e)}),a}if(!w(t))return o(t,s(R(this,e))),this;var r=n.controller||function(){};return o(n,function(e,t){"$"===t.charAt(0)&&(i[t]=e,T(r)&&(r[t]=e))}),i.$inject=["$injector"],this.directive(t,i)},this.aHrefSanitizationWhitelist=function(e){return $(e)?(n.aHrefSanitizationWhitelist(e),this):n.aHrefSanitizationWhitelist()},this.imgSrcSanitizationWhitelist=function(e){return $(e)?(n.imgSrcSanitizationWhitelist(e),this):n.imgSrcSanitizationWhitelist()};var k=!0;this.debugInfoEnabled=function(e){return $(e)?(k=e,this):k};var D=!1;this.preAssignBindingsEnabled=function(e){return $(e)?(D=e,this):D};var S=!1;this.strictComponentBindingsEnabled=function(e){return $(e)?(S=e,this):S};var A=10;this.onChangesTtl=function(e){return arguments.length?(A=e,this):A};var I=!0;this.commentDirectivesEnabled=function(e){return arguments.length?(I=e,this):I};var V=!0;this.cssClassDirectivesEnabled=function(e){return arguments.length?(V=e,this):V},this.$get=["$injector","$interpolate","$exceptionHandler","$templateRequest","$parse","$controller","$rootScope","$sce","$animate","$$sanitizeUri",function(t,n,r,s,m,x,F,U,L,H){function q(){try{if(!--Ee)throw ke=void 0,Zi("infchng",A);F.$apply(function(){for(var e=0,t=ke.length;e<t;++e)try{ke[e]()}catch(e){r(e)}ke=void 0})}finally{Ee++}}function B(e,t){if(t){var n,i,r,o=Object.keys(t);for(n=0,i=o.length;n<i;n++)this[r=o[n]]=t[r]}else this.$attr={};this.$$element=e}function W(e,t){try{e.addClass(t)}catch(e){}}function z(e,t,n,i,r){e instanceof Bn||(e=Bn(e));var o=Y(e,t,e,n,i,r);z.$$addScopeClass(e);var a=null;return function(t,n,i){if(!e)throw Zi("multilink");te(t,"scope"),r&&r.needsNewScope&&(t=t.$parent.$new());var s=(i=i||{}).parentBoundTranscludeFn,l=i.transcludeControllers;if(i=i.futureParentElement,s&&s.$$boundTransclude&&(s=s.$$boundTransclude),a||(a=(i=i&&i[0])&&"foreignobject"!==M(i)&&ei.call(i).match(/SVG/)?"svg":"html"),i="html"!==a?Bn(ge(a,Bn("<div></div>").append(e).html())):n?Ii.clone.call(e):e,l)for(var u in l)i.data("$"+u+"Controller",l[u].instance);return z.$$addScopeInfo(i,t),n&&n(i,t),o&&o(t,i,i,s),n||(e=o=null),i}}function Y(e,t,n,i,r,o){for(var a,s,l,u,c,p=[],d=li(e)||e instanceof Bn,f=0;f<e.length;f++)a=new B,11===qn&&G(e,f,d),(o=(s=J(e[f],[],a,0===f?i:void 0,r)).length?ie(s,e[f],a,t,n,null,[],[],o):null)&&o.scope&&z.$$addScopeClass(a.$$element),a=o&&o.terminal||!(l=e[f].childNodes)||!l.length?null:Y(l,o?(o.transcludeOnThisElement||!o.templateOnThisElement)&&o.transclude:t),(o||a)&&(p.push(f,o,a),u=!0,c=c||o),o=null;return u?function(e,n,i,r){var o,a,s,l,u,d;if(c)for(d=Array(n.length),l=0;l<p.length;l+=3)d[o=p[l]]=n[o];else d=n;for(l=0,u=p.length;l<u;)a=d[p[l++]],n=p[l++],o=p[l++],n?(n.scope?(s=e.$new(),z.$$addScopeInfo(Bn(a),s)):s=e,n(o,s,a,i,n.transcludeOnThisElement?K(e,n.transclude,r):!n.templateOnThisElement&&r?r:!r&&t?K(e,t):null)):o&&o(e,a.childNodes,void 0,r)}:null}function G(e,t,n){var i,r=e[t],o=r.parentNode;if(r.nodeType===bi)for(;(i=o?r.nextSibling:e[t+1])&&i.nodeType===bi;)r.nodeValue+=i.nodeValue,i.parentNode&&i.parentNode.removeChild(i),n&&i===e[t+1]&&e.splice(t+1,1)}function K(e,t,n){function i(i,r,o,a,s){return i||((i=e.$new(!1,s)).$$transcluded=!0),t(i,r,{parentBoundTranscludeFn:n,transcludeControllers:o,futureParentElement:a})}var r,o=i.$$slots=ae();for(r in t.$$slots)o[r]=t.$$slots[r]?K(e,t.$$slots[r],n):null;return i}function J(e,t,n,i,r){var o,a=n.$attr;switch(e.nodeType){case 1:le(t,Ye(o=M(e)),"E",i,r);for(var s,l,u,p,d=e.attributes,f=0,h=d&&d.length;f<h;f++){var m=!1,g=!1;l=(s=d[f]).name,u=s.value,s=Ye(l),(p=Ie.test(s))&&(l=l.replace(tr,"").substr(8).replace(/_(.)/g,function(e,t){return t.toUpperCase()})),(s=s.match(Pe))&&ue(s[1])&&(m=l,g=l.substr(0,l.length-5)+"end",l=l.substr(0,l.length-6)),a[s=Ye(l.toLowerCase())]=l,!p&&n.hasOwnProperty(s)||(n[s]=u,Ae(e,s)&&(n[s]=!0)),ve(e,t,u,s,p),le(t,s,"A",i,r,m,g)}if("input"===o&&"hidden"===e.getAttribute("type")&&e.setAttribute("autocomplete","off"),!Se)break;if(b(a=e.className)&&(a=a.animVal),w(a)&&""!==a)for(;e=c.exec(a);)le(t,s=Ye(e[2]),"C",i,r)&&(n[s]=ci(e[3])),a=a.substr(e.index+e[0].length);break;case bi:he(t,e.nodeValue);break;case 8:if(!De)break;X(e,t,n,i,r)}return t.sort(de),t}function X(e,t,n,i,r){try{var o=l.exec(e.nodeValue);if(o){var a=Ye(o[1]);le(t,a,"M",i,r)&&(n[a]=ci(o[2]))}}catch(e){}}function Q(e,t,n){var i=[],r=0;if(t&&e.hasAttribute&&e.hasAttribute(t))do{if(!e)throw Zi("uterdir",t,n);1===e.nodeType&&(e.hasAttribute(t)&&r++,e.hasAttribute(n)&&r--),i.push(e),e=e.nextSibling}while(0<r);else i.push(e);return Bn(i)}function Z(e,t,n){return function(i,r,o,a,s){return r=Q(r[0],t,n),e(i,r,o,a,s)}}function ne(e,t,n,i,r,o){var a;return e?z(t,n,i,r,o):function(){return a||(a=z(t,n,i,r,o),t=n=o=null),a.apply(this,arguments)}}function ie(e,t,n,i,a,s,l,c,p){function d(e,t,n,i){e&&(n&&(e=Z(e,n,i)),e.require=h.require,e.directiveName=m,(C===h||h.$$isolateScope)&&(e=be(e,{isolateScope:!0})),l.push(e)),t&&(n&&(t=Z(t,n,i)),t.require=h.require,t.directiveName=m,(C===h||h.$$isolateScope)&&(t=be(t,{isolateScope:!0})),c.push(t))}function f(e,i,a,s,p){var d,f,h,m,g,$,y,w;for(d in t===a?(s=n,w=n.$$element):s=new B(w=Bn(a),n),g=i,C?m=i.$new(!0):x&&(g=i.$parent),p&&((y=function(e,t,n,i){var r;if(E(e)||(i=n,n=t,t=e,e=void 0),I&&(r=$),n||(n=I?w.parent():w),!i)return p(e,t,r,n,M);var o=p.$$slots[i];if(o)return o(e,t,r,n,M);if(v(o))throw Zi("noslot",i,_(w))}).$$boundTransclude=p,y.isSlotFilled=function(e){return!!p.$$slots[e]}),k&&($=oe(w,s,y,k,m,i,C)),C&&(z.$$addScopeInfo(w,m,!0,!(S&&(S===C||S===C.$$originalDirective))),z.$$addScopeClass(w,!0),m.$$isolateBindings=C.$$isolateBindings,(f=xe(i,s,m,m.$$isolateBindings,C)).removeWatches&&m.$on("$destroy",f.removeWatches)),$){f=k[d],h=$[d];var O=f.$$bindings.bindToController;if(D){h.bindingInfo=O?xe(g,s,h.instance,O,f):{};var A=h();A!==h.instance&&(h.instance=A,w.data("$"+f.name+"Controller",A),h.bindingInfo.removeWatches&&h.bindingInfo.removeWatches(),h.bindingInfo=xe(g,s,h.instance,O,f))}else h.instance=h(),w.data("$"+f.name+"Controller",h.instance),h.bindingInfo=xe(g,s,h.instance,O,f)}for(o(k,function(e,t){var n=e.require;e.bindToController&&!li(n)&&b(n)&&u($[t].instance,re(t,n,w,$))}),o($,function(e){var t=e.instance;if(T(t.$onChanges))try{t.$onChanges(e.bindingInfo.initialChanges)}catch(e){r(e)}if(T(t.$onInit))try{t.$onInit()}catch(e){r(e)}T(t.$doCheck)&&(g.$watch(function(){t.$doCheck()}),t.$doCheck()),T(t.$onDestroy)&&g.$on("$destroy",function(){t.$onDestroy()})}),d=0,f=l.length;d<f;d++)ye(h=l[d],h.isolateScope?m:i,w,s,h.require&&re(h.directiveName,h.require,w,$),y);var M=i;for(C&&(C.template||null===C.templateUrl)&&(M=m),e&&e(M,a.childNodes,void 0,p),d=c.length-1;0<=d;d--)ye(h=c[d],h.isolateScope?m:i,w,s,h.require&&re(h.directiveName,h.require,w,$),y);o($,function(e){T((e=e.instance).$postLink)&&e.$postLink()})}p=p||{};for(var h,m,g,$,y,w=-Number.MAX_VALUE,x=p.newScopeDirective,k=p.controllerDirectives,C=p.newIsolateScopeDirective,S=p.templateDirective,O=p.nonTlbTranscludeDirective,A=!1,N=!1,I=p.hasElementTranscludeDirective,P=n.$$element=Bn(t),j=i,V=!1,F=!1,U=0,L=e.length;U<L;U++){var H=(h=e[U]).$$start,q=h.$$end;if(H&&(P=Q(t,H,q)),g=void 0,w>h.priority)break;if((y=h.scope)&&(h.templateUrl||(b(y)?(fe("new/isolated scope",C||x,h,P),C=h):fe("new/isolated scope",C,h,P)),x=x||h),m=h.name,!V&&(h.replace&&(h.templateUrl||h.template)||h.transclude&&!h.$$tlb)){for(y=U+1;V=e[y++];)if(V.transclude&&!V.$$tlb||V.replace&&(V.templateUrl||V.template)){F=!0;break}V=!0}if(!h.templateUrl&&h.controller&&(k=k||ae(),fe("'"+m+"' controller",k[m],h,P),k[m]=h),y=h.transclude)if(A=!0,h.$$tlb||(fe("transclusion",O,h,P),O=h),"element"===y)I=!0,w=h.priority,g=P,P=n.$$element=Bn(z.$$createComment(m,n[m])),t=P[0],$e(a,Xn.call(g,0),t),g[0].$$parentNode=g[0].parentNode,j=ne(F,g,i,w,s&&s.name,{nonTlbTranscludeDirective:O});else{var W=ae();if(b(y)){g=[];var Y=ae(),G=ae();for(var K in o(y,function(e,t){var n="?"===e.charAt(0);e=n?e.substring(1):e,Y[e]=t,W[t]=null,G[t]=n}),o(P.contents(),function(e){var t=Y[Ye(M(e))];t?(G[t]=!0,W[t]=W[t]||[],W[t].push(e)):g.push(e)}),o(G,function(e,t){if(!e)throw Zi("reqslot",t)}),W)W[K]&&(W[K]=ne(F,W[K],i))}else g=Bn(me(t)).contents();P.empty(),(j=ne(F,g,i,void 0,void 0,{needsNewScope:h.$$isolateScope||h.$$newScope})).$$slots=W}if(h.template)if(N=!0,fe("template",S,h,P),S=h,y=T(h.template)?h.template(P,n):h.template,y=Ne(y),h.replace){if(s=h,g=Ei.test(y)?Ke(ge(h.templateNamespace,ci(y))):[],t=g[0],1!==g.length||1!==t.nodeType)throw Zi("tplrt",m,"");$e(a,P,t),y=J(t,[],L={$attr:{}});var X=e.splice(U+1,e.length-(U+1));(C||x)&&se(y,C,x),e=e.concat(y).concat(X),ce(n,L),L=e.length}else P.html(y);if(h.templateUrl)N=!0,fe("template",S,h,P),S=h,h.replace&&(s=h),f=pe(e.splice(U,e.length-U),P,n,a,A&&j,l,c,{controllerDirectives:k,newScopeDirective:x!==h&&x,newIsolateScopeDirective:C,templateDirective:S,nonTlbTranscludeDirective:O}),L=e.length;else if(h.compile)try{$=h.compile(P,n,j);var ee=h.$$originalDirective||h;T($)?d(null,R(ee,$),H,q):$&&d(R(ee,$.pre),R(ee,$.post),H,q)}catch(e){r(e,_(P))}h.terminal&&(f.terminal=!0,w=Math.max(w,h.priority))}return f.scope=x&&!0===x.scope,f.transcludeOnThisElement=A,f.templateOnThisElement=N,f.transclude=j,p.hasElementTranscludeDirective=I,f}function re(e,t,n,i){var r;if(w(t)){var a=t.match(g);t=t.substring(a[0].length);var s=a[1]||a[3];a="?"===a[2];if("^^"===s?n=n.parent():r=(r=i&&i[t])&&r.instance,!r){var l="$"+t+"Controller";r=s?n.inheritedData(l):n.data(l)}if(!r&&!a)throw Zi("ctreq",t,e)}else if(li(t))for(r=[],s=0,a=t.length;s<a;s++)r[s]=re(e,t[s],n,i);else b(t)&&(r={},o(t,function(t,o){r[o]=re(e,t,n,i)}));return r||null}function oe(e,t,n,i,r,o,a){var s,l=ae();for(s in i){var u=i[s],c={$scope:u===a||u.$$isolateScope?r:o,$element:e,$attrs:t,$transclude:n},p=u.controller;"@"===p&&(p=t[u.name]),c=x(p,c,!0,u.controllerAs),l[u.name]=c,e.data("$"+u.name+"Controller",c.instance)}return l}function se(e,t,n){for(var i=0,r=e.length;i<r;i++)e[i]=d(e[i],{$$isolateScope:t,$$newScope:n})}function le(e,n,r,o,s,l,u){if(n===s)return null;var c=null;if(a.hasOwnProperty(n))for(var p=0,f=(s=t.get(n+"Directive")).length;p<f;p++)if(n=s[p],(v(o)||o>n.priority)&&-1!==n.restrict.indexOf(r)){if(l&&(n=d(n,{$$start:l,$$end:u})),!n.$$bindings){var h=c=n,m=n.name,g={isolateScope:null,bindToController:null};if(b(h.scope)&&(!0===h.bindToController?(g.bindToController=i(h.scope,m,!0),g.isolateScope={}):g.isolateScope=i(h.scope,m,!1)),b(h.bindToController)&&(g.bindToController=i(h.bindToController,m,!0)),g.bindToController&&!h.controller)throw Zi("noctrl",m);b((c=c.$$bindings=g).isolateScope)&&(n.$$isolateBindings=c.isolateScope)}e.push(n),c=n}return c}function ue(e){if(a.hasOwnProperty(e))for(var n=t.get(e+"Directive"),i=0,r=n.length;i<r;i++)if((e=n[i]).multiElement)return!0;return!1}function ce(e,t){var n=t.$attr,i=e.$attr;o(e,function(i,r){"$"!==r.charAt(0)&&(t[r]&&t[r]!==i&&(i=i.length?i+("style"===r?";":" ")+t[r]:t[r]),e.$set(r,i,!0,n[r]))}),o(t,function(t,r){e.hasOwnProperty(r)||"$"===r.charAt(0)||(e[r]=t,"class"!==r&&"style"!==r&&(i[r]=n[r]))})}function pe(e,t,n,i,a,l,u,c){var p,f,h=[],m=t[0],g=e.shift(),v=d(g,{templateUrl:null,transclude:null,replace:null,$$originalDirective:g}),$=T(g.templateUrl)?g.templateUrl(t,n):g.templateUrl,y=g.templateNamespace;return t.empty(),s($).then(function(r){var s,d;if(r=Ne(r),g.replace){if(r=Ei.test(r)?Ke(ge(y,ci(r))):[],s=r[0],1!==r.length||1!==s.nodeType)throw Zi("tplrt",g.name,$);r={$attr:{}},$e(i,t,s);var w=J(s,[],r);b(g.scope)&&se(w,!0),e=w.concat(e),ce(n,r)}else s=m,t.html(r);for(e.unshift(v),p=ie(e,s,n,a,t,g,l,u,c),o(i,function(e,n){e===s&&(i[n]=t[0])}),f=Y(t[0].childNodes,a);h.length;){r=h.shift(),d=h.shift();var x=h.shift(),k=h.shift();w=t[0];if(!r.$$destroyed){if(d!==m){var C=d.className;c.hasElementTranscludeDirective&&g.replace||(w=me(s)),$e(x,Bn(d),w),W(Bn(w),C)}d=p.transcludeOnThisElement?K(r,p.transclude,k):k,p(f,r,w,i,d)}}h=null}).catch(function(e){C(e)&&r(e)}),function(e,t,n,i,r){e=r,t.$$destroyed||(h?h.push(t,n,i,e):(p.transcludeOnThisElement&&(e=K(t,p.transclude,r)),p(f,t,n,i,e)))}}function de(e,t){var n=t.priority-e.priority;return 0!==n?n:e.name!==t.name?e.name<t.name?-1:1:e.index-t.index}function fe(e,t,n,i){function r(e){return e?" (module: "+e+")":""}if(t)throw Zi("multidir",t.name,r(t.$$moduleName),n.name,r(n.$$moduleName),e,_(i))}function he(e,t){var i=n(t,!0);i&&e.push({priority:0,compile:function(e){var t=!!(e=e.parent()).length;return t&&z.$$addBindingClass(e),function(e,n){var r=n.parent();t||z.$$addBindingClass(r),z.$$addBindingInfo(r,i.expressions),e.$watch(i,function(e){n[0].nodeValue=e})}}})}function ge(t,n){switch(t=Kn(t||"html")){case"svg":case"math":var i=e.document.createElement("div");return i.innerHTML="<"+t+">"+n+"</"+t+">",i.childNodes[0].childNodes;default:return n}}function ve(e,t,i,r,o){var a=function(e,t){if("srcdoc"===t)return U.HTML;var n=M(e);if("src"===t||"ngSrc"===t){if(-1===["img","video","audio","source","track"].indexOf(n))return U.RESOURCE_URL}else if("xlinkHref"===t||"form"===n&&"action"===t||"link"===n&&"href"===t)return U.RESOURCE_URL}(e,r),s=p[r]||o,l=n(i,!o,a,s);if(l){if("multiple"===r&&"select"===M(e))throw Zi("selmulti",_(e));if(y.test(r))throw Zi("nodomevents");t.push({priority:100,compile:function(){return{pre:function(e,t,o){t=o.$$observers||(o.$$observers=ae());var u=o[r];u!==i&&(l=u&&n(u,!0,a,s),i=u),l&&(o[r]=l(e),(t[r]||(t[r]=[])).$$inter=!0,(o.$$observers&&o.$$observers[r].$$scope||e).$watch(l,function(e,t){"class"===r&&e!==t?o.$updateClass(e,t):o.$set(r,e)}))}}}})}}function $e(t,n,i){var r,o,a=n[0],s=n.length,l=a.parentNode;if(t)for(r=0,o=t.length;r<o;r++)if(t[r]===a){t[r++]=i,o=r+s-1;for(var u=t.length;r<u;r++,o++)o<u?t[r]=t[o]:delete t[r];t.length-=s-1,t.context===a&&(t.context=i);break}for(l&&l.replaceChild(i,a),t=e.document.createDocumentFragment(),r=0;r<s;r++)t.appendChild(n[r]);for(Bn.hasData(a)&&(Bn.data(i,Bn.data(a)),Bn(a).off("$destroy")),Bn.cleanData(t.querySelectorAll("*")),r=1;r<s;r++)delete n[r];n[0]=i,n.length=1}function be(e,t){return u(function(){return e.apply(null,arguments)},e,t)}function ye(e,t,n,i,o,a){try{e(t,n,i,o,a)}catch(e){r(e,_(n))}}function we(e,t){if(S)throw Zi("missingattr",e,t)}function xe(e,t,i,r,a){function s(t,n,r){T(i.$onChanges)&&!P(n,r)&&(ke||(e.$$postDigest(q),ke=[]),u||(u={},ke.push(l)),u[t]&&(r=u[t].previousValue),u[t]=new ze(r,n))}function l(){i.$onChanges(u),u=void 0}var u,c=[],p={};return o(r,function(r,o){var l,u,d,h,g=r.attrName,v=r.optional;switch(r.mode){case"@":v||Gn.call(t,g)||(we(g,a.name),i[o]=t[g]=void 0),v=t.$observe(g,function(e){(w(e)||O(e))&&(s(o,e,i[o]),i[o]=e)}),t.$$observers[g].$$scope=e,w(l=t[g])?i[o]=n(l)(e):O(l)&&(i[o]=l),p[o]=new ze(er,i[o]),c.push(v);break;case"=":if(!Gn.call(t,g)){if(v)break;we(g,a.name),t[g]=void 0}if(v&&!t[g])break;u=m(t[g]),h=u.literal?j:P,d=u.assign||function(){throw l=i[o]=u(e),Zi("nonassign",t[g],g,a.name)},l=i[o]=u(e),(v=function(t){return h(t,i[o])||(h(t,l)?d(e,t=i[o]):i[o]=t),l=t}).$stateful=!0,v=r.collection?e.$watchCollection(t[g],v):e.$watch(m(t[g],v),null,u.literal),c.push(v);break;case"<":if(!Gn.call(t,g)){if(v)break;we(g,a.name),t[g]=void 0}if(v&&!t[g])break;var $=(u=m(t[g])).literal,b=i[o]=u(e);p[o]=new ze(er,i[o]),v=e.$watch(u,function(e,t){if(t===e){if(t===b||$&&j(t,b))return;t=b}s(o,e,t),i[o]=e},$),c.push(v);break;case"&":if(v||Gn.call(t,g)||we(g,a.name),(u=t.hasOwnProperty(g)?m(t[g]):f)===f&&v)break;i[o]=function(t){return u(e,t)}}}),{initialChanges:p,removeWatches:c.length&&function(){for(var e=0,t=c.length;e<t;++e)c[e]()}}}var ke,Ce=/^\w/,Te=e.document.createElement("div"),De=I,Se=V,Ee=A;B.prototype={$normalize:Ye,$addClass:function(e){e&&0<e.length&&L.addClass(this.$$element,e)},$removeClass:function(e){e&&0<e.length&&L.removeClass(this.$$element,e)},$updateClass:function(e,t){var n=Ge(e,t);n&&n.length&&L.addClass(this.$$element,n),(n=Ge(t,e))&&n.length&&L.removeClass(this.$$element,n)},$set:function(e,t,n,i){var a=Ae(this.$$element[0],e),s=Vi[e],l=e;if(a?(this.$$element.prop(e,t),i=a):s&&(this[s]=t,l=s),this[e]=t,i?this.$attr[e]=i:(i=this.$attr[e])||(this.$attr[e]=i=ee(e,"-")),"a"===(a=M(this.$$element))&&("href"===e||"xlinkHref"===e)||"img"===a&&"src"===e)this[e]=t=null==t?t:H(t,"src"===e);else if("img"===a&&"srcset"===e&&$(t)){a="",s=ci(t);for(var u=/(\s+\d+x\s*,|\s+\d+w\s*,|\s+,|,\s+)/,c=(u=/\s/.test(s)?u:/(,)/,s=s.split(u),u=Math.floor(s.length/2),0);c<u;c++){var p=2*c;a=(a=a+H(ci(s[p]),!0))+" "+ci(s[p+1])}s=ci(s[2*c]).split(/\s/),a+=H(ci(s[0]),!0),2===s.length&&(a+=" "+ci(s[1])),this[e]=t=a}!1!==n&&(null==t?this.$$element.removeAttr(i):Ce.test(i)?this.$$element.attr(i,t):function(e,t,n){Te.innerHTML="<span "+t+">";var i=(t=Te.firstChild.attributes)[0];t.removeNamedItem(i.name),i.value=n,e.attributes.setNamedItem(i)}(this.$$element[0],i,t)),(e=this.$$observers)&&o(e[l],function(e){try{e(t)}catch(e){r(e)}})},$observe:function(e,t){var n=this,i=n.$$observers||(n.$$observers=ae()),r=i[e]||(i[e]=[]);return r.push(t),F.$evalAsync(function(){r.$$inter||!n.hasOwnProperty(e)||v(n[e])||t(n[e])}),function(){N(r,t)}}};var Oe=n.startSymbol(),Me=n.endSymbol(),Ne="{{"===Oe&&"}}"===Me?h:function(e){return e.replace(/\{\{/g,Oe).replace(/}}/g,Me)},Ie=/^ngAttr[A-Z]/,Pe=/^(.+)Start$/;return z.$$addBindingInfo=k?function(e,t){var n=e.data("$binding")||[];li(t)?n=n.concat(t):n.push(t),e.data("$binding",n)}:f,z.$$addBindingClass=k?function(e){W(e,"ng-binding")}:f,z.$$addScopeInfo=k?function(e,t,n,i){e.data(n?i?"$isolateScopeNoTemplate":"$isolateScope":"$scope",t)}:f,z.$$addScopeClass=k?function(e,t){W(e,t?"ng-isolate-scope":"ng-scope")}:f,z.$$createComment=function(t,n){var i="";return k&&(i=" "+(t||"")+": ",n&&(i+=n+" ")),e.document.createComment(i)},z}]}function ze(e,t){this.previousValue=e,this.currentValue=t}function Ye(e){return e.replace(tr,"").replace(nr,function(e,t,n){return n?t.toUpperCase():t})}function Ge(e,t){var n="",i=e.split(/\s+/),r=t.split(/\s+/),o=0;e:for(;o<i.length;o++){for(var a=i[o],s=0;s<r.length;s++)if(a===r[s])continue e;n+=(0<n.length?" ":"")+a}return n}function Ke(e){var t=(e=Bn(e)).length;if(1>=t)return e;for(;t--;){var n=e[t];(8===n.nodeType||n.nodeType===bi&&""===n.nodeValue.trim())&&Qn.call(e,t,1)}return e}function Je(e,t){if(t&&w(t))return t;if(w(e)){var n=rr.exec(e);if(n)return n[3]}}function Xe(){var e={},t=!1;this.has=function(t){return e.hasOwnProperty(t)},this.register=function(t,n){ie(t,"controller"),b(t)?u(e,t):e[t]=n},this.allowGlobals=function(){t=!0},this.$get=["$injector","$window",function(n,r){function o(e,t,n,r){if(!e||!b(e.$scope))throw i("$controller")("noscp",r,t);e.$scope[t]=n}return function(i,a,s,l){var c,p,d;if(s=!0===s,l&&w(l)&&(d=l),w(i)){if(!(l=i.match(rr)))throw ir("ctrlfmt",i);if(p=l[1],d=d||l[3],!(i=e.hasOwnProperty(p)?e[p]:re(a.$scope,p,!0)||(t?re(r,p,!0):void 0)))throw ir("ctrlreg",p);ne(i,p,!0)}return s?(s=(li(i)?i[i.length-1]:i).prototype,c=Object.create(s||null),d&&o(a,d,c,p||i.name),u(function(){var e=n.invoke(i,c,a,p);return e!==c&&(b(e)||T(e))&&(c=e,d&&o(a,d,c,p||i.name)),c},{instance:c,identifier:d})):(c=n.instantiate(i,a,p),d&&o(a,d,c,p||i.name),c)}}]}function Qe(){this.$get=["$window",function(e){return Bn(e.document)}]}function Ze(){this.$get=["$document","$rootScope",function(e,t){function n(){r=i.hidden}var i=e[0],r=i&&i.hidden;return e.on("visibilitychange",n),t.$on("$destroy",function(){e.off("visibilitychange",n)}),function(){return r}}]}function et(){this.$get=["$log",function(e){return function(t,n){e.error.apply(e,arguments)}}]}function tt(e){return b(e)?k(e)?e.toISOString():U(e):e}function nt(){this.$get=function(){return function(e){if(!e)return"";var t=[];return a(e,function(e,n){null===e||v(e)||T(e)||(li(e)?o(e,function(e){t.push(K(n)+"="+K(tt(e)))}):t.push(K(n)+"="+K(tt(e))))}),t.join("&")}}}function it(){this.$get=function(){return function(e){if(!e)return"";var t=[];return function e(n,i,r){null===n||v(n)||(li(n)?o(n,function(t,n){e(t,i+"["+(b(t)?n:"")+"]")}):b(n)&&!k(n)?a(n,function(t,n){e(t,i+(r?"":"[")+n+(r?"":"]"))}):t.push(K(i)+"="+K(tt(n))))}(e,"",!0),t.join("&")}}}function rt(e,t){if(w(e)){var n,i,r=e.replace(cr,"").trim();if(r)if((i=n=(n=t("Content-Type"))&&0===n.indexOf(ar))||(i=(i=r.match(lr))&&ur[i[0]].test(r)),i)try{e=L(r)}catch(t){if(!n)return e;throw pr("baddata",e,t)}}return e}function ot(e){var t,n=ae();return w(e)?o(e.split("\n"),function(e){t=e.indexOf(":");var i=Kn(ci(e.substr(0,t)));e=ci(e.substr(t+1)),i&&(n[i]=n[i]?n[i]+", "+e:e)}):b(e)&&o(e,function(e,t){var i=Kn(t),r=ci(e);i&&(n[i]=n[i]?n[i]+", "+r:r)}),n}function at(e){var t;return function(n){return t||(t=ot(e)),n?(void 0===(n=t[Kn(n)])&&(n=null),n):t}}function st(e,t,n,i){return T(i)?i(e,t,n):(o(i,function(i){e=i(e,t,n)}),e)}function lt(){var e=this.defaults={transformResponse:[rt],transformRequest:[function(e){return b(e)&&"[object File]"!==ei.call(e)&&"[object Blob]"!==ei.call(e)&&"[object FormData]"!==ei.call(e)?U(e):e}],headers:{common:{Accept:"application/json, text/plain, */*"},post:le(sr),put:le(sr),patch:le(sr)},xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",paramSerializer:"$httpParamSerializer",jsonpCallbackParam:"callback"},t=!1;this.useApplyAsync=function(e){return $(e)?(t=!!e,this):t};var n=this.interceptors=[],r=this.xsrfWhitelistedOrigins=[];this.$get=["$browser","$httpBackend","$$cookieReader","$cacheFactory","$rootScope","$q","$injector","$sce",function(a,s,l,c,p,d,h,m){function g(t){function n(e,t){for(var n=0,i=t.length;n<i;){var r=t[n++],o=t[n++];e=e.then(r,o)}return t.length=0,e}function r(e){var t=u({},e);return t.data=st(e.data,e.headers,e.status,s.transformResponse),200<=(e=e.status)&&300>e?t:d.reject(t)}if(!b(t))throw i("$http")("badreq",t);if(!w(m.valueOf(t.url)))throw i("$http")("badreq",t.url);var s=u({method:"get",transformRequest:e.transformRequest,transformResponse:e.transformResponse,paramSerializer:e.paramSerializer,jsonpCallbackParam:e.jsonpCallbackParam},t);s.headers=function(t){var n,i,r,a=e.headers,s=u({},t.headers);a=u({},a.common,a[Kn(t.method)]);e:for(n in a){for(r in i=Kn(n),s)if(Kn(r)===i)continue e;s[n]=a[n]}return function(e,t){var n,i={};return o(e,function(e,r){T(e)?null!=(n=e(t))&&(i[r]=n):i[r]=e}),i}(s,le(t))}(t),s.method=Jn(s.method),s.paramSerializer=w(s.paramSerializer)?h.get(s.paramSerializer):s.paramSerializer,a.$$incOutstandingRequestCount();var l=[],c=[];return t=d.resolve(s),o(k,function(e){(e.request||e.requestError)&&l.unshift(e.request,e.requestError),(e.response||e.responseError)&&c.push(e.response,e.responseError)}),t=(t=n(t,l)).then(function(t){var n=t.headers,i=st(t.data,at(n),void 0,t.transformRequest);return v(i)&&o(n,function(e,t){"content-type"===Kn(t)&&delete n[t]}),v(t.withCredentials)&&!v(e.withCredentials)&&(t.withCredentials=e.withCredentials),y(t,i).then(r,r)}),(t=n(t,c)).finally(function(){a.$$completeOutstandingRequest(f)})}function y(n,i){function r(e){if(e){var n={};return o(e,function(e,i){n[i]=function(n){function i(){e(n)}t?p.$applyAsync(i):p.$$phase?i():p.$apply(i)}}),n}}function a(e,t,i,r,o){(200<=(t=-1<=t?t:0)&&300>t?y.resolve:y.reject)({data:e,status:t,headers:at(i),config:n,statusText:r,xhrStatus:o})}function u(e){a(e.data,e.status,le(e.headers()),e.statusText,e.xhrStatus)}function c(){var e=g.pendingRequests.indexOf(n);-1!==e&&g.pendingRequests.splice(e,1)}var f,h,y=d.defer(),k=y.promise,D=n.headers,S="jsonp"===Kn(n.method),E=n.url;return S?E=m.getTrustedResourceUrl(E):w(E)||(E=m.valueOf(E)),E=function(e,t){return 0<t.length&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}(E,n.paramSerializer(n.params)),S&&(E=function(e,t){var n=e.split("?");if(2<n.length)throw pr("badjsonp",e);return o(n=z(n[1]),function(n,i){if("JSON_CALLBACK"===n)throw pr("badjsonp",e);if(i===t)throw pr("badjsonp",t,e)}),e+=(-1===e.indexOf("?")?"?":"&")+t+"=JSON_CALLBACK"}(E,n.jsonpCallbackParam)),g.pendingRequests.push(n),k.then(c,c),!n.cache&&!e.cache||!1===n.cache||"GET"!==n.method&&"JSONP"!==n.method||(f=b(n.cache)?n.cache:b(e.cache)?e.cache:x),f&&($(h=f.get(E))?h&&T(h.then)?h.then(u,u):li(h)?a(h[1],h[0],le(h[2]),h[3],h[4]):a(h,200,{},"OK","complete"):f.put(E,k)),v(h)&&((h=C(n.url)?l()[n.xsrfCookieName||e.xsrfCookieName]:void 0)&&(D[n.xsrfHeaderName||e.xsrfHeaderName]=h),s(n.method,E,i,function(e,n,i,r,o){function s(){a(n,e,i,r,o)}f&&(200<=e&&300>e?f.put(E,[e,n,ot(i),r,o]):f.remove(E)),t?p.$applyAsync(s):(s(),p.$$phase||p.$apply())},D,n.timeout,n.withCredentials,n.responseType,r(n.eventHandlers),r(n.uploadEventHandlers))),k}var x=c("$http");e.paramSerializer=w(e.paramSerializer)?h.get(e.paramSerializer):e.paramSerializer;var k=[];o(n,function(e){k.unshift(w(e)?h.get(e):h.invoke(e))});var C=function(e){var t=[Mr].concat(e.map(Qt));return function(e){return e=Qt(e),t.some(Zt.bind(null,e))}}(r);return g.pendingRequests=[],function(e){o(arguments,function(e){g[e]=function(t,n){return g(u({},n||{},{method:e,url:t}))}})}("get","delete","head","jsonp"),function(e){o(arguments,function(e){g[e]=function(t,n,i){return g(u({},i||{},{method:e,url:t,data:n}))}})}("post","put","patch"),g.defaults=e,g}]}function ut(){this.$get=function(){return function(){return new e.XMLHttpRequest}}}function ct(){this.$get=["$browser","$jsonpCallbacks","$document","$xhrFactory",function(e,t,n,i){return function(e,t,n,i,r){function a(e,t,n){e=e.replace("JSON_CALLBACK",t);var o=r.createElement("script"),a=null;return o.type="text/javascript",o.src=e,o.async=!0,a=function(e){o.removeEventListener("load",a),o.removeEventListener("error",a),r.body.removeChild(o),o=null;var s=-1,l="unknown";e&&("load"!==e.type||i.wasCalled(t)||(e={type:"error"}),l=e.type,s="error"===e.type?404:200),n&&n(s,l)},o.addEventListener("load",a),o.addEventListener("error",a),r.body.appendChild(o),a}return function(r,s,l,u,c,p,d,f,h,m){function g(e){k="timeout"===e,w&&w(),x&&x.abort()}function b(e,t,i,r,o,a){$(C)&&n.cancel(C),w=x=null,e(t,i,r,o,a)}if(s=s||e.url(),"jsonp"===Kn(r))var y=i.createCallback(s),w=a(s,y,function(e,t){var n=200===e&&i.getResponse(y);b(u,e,n,"",t,"complete"),i.removeCallback(y)});else{var x=t(r,s),k=!1;if(x.open(r,s,!0),o(c,function(e,t){$(e)&&x.setRequestHeader(t,e)}),x.onload=function(){var e=x.statusText||"",t="response"in x?x.response:x.responseText,n=1223===x.status?204:x.status;0===n&&(n=t?200:"file"===Qt(s).protocol?404:0),b(u,n,t,x.getAllResponseHeaders(),e,"complete")},x.onerror=function(){b(u,-1,null,null,"","error")},x.ontimeout=function(){b(u,-1,null,null,"","timeout")},x.onabort=function(){b(u,-1,null,null,"",k?"timeout":"abort")},o(h,function(e,t){x.addEventListener(t,e)}),o(m,function(e,t){x.upload.addEventListener(t,e)}),d&&(x.withCredentials=!0),f)try{x.responseType=f}catch(e){if("json"!==f)throw e}x.send(v(l)?null:l)}if(0<p)var C=n(function(){g("timeout")},p);else p&&T(p.then)&&p.then(function(){g($(p.$$timeoutId)?"timeout":"abort")})}}(e,i,e.defer,t,n[0])}]}function pt(){var e="{{",t="}}";this.startSymbol=function(t){return t?(e=t,this):e},this.endSymbol=function(e){return e?(t=e,this):t},this.$get=["$parse","$exceptionHandler","$sce",function(n,i,r){function o(e){return"\\\\\\"+e}function a(n){return n.replace(d,e).replace(f,t)}function s(e,t,n,i){var r=e.$watch(function(e){return r(),i(e)},t,n);return r}function l(o,l,d,f){function h(e){try{var t=e;return e=d?r.getTrusted(d,t):r.valueOf(t),f&&!$(e)?e:se(e)}catch(e){i(dr.interr(o,e))}}var g;if(!o.length||-1===o.indexOf(e))return l||((g=m(l=a(o))).exp=o,g.expressions=[],g.$$watchDelegate=s),g;f=!!f;var b,y,w=0,x=[],k=[];g=o.length;for(var C=[],T=[];w<g;){if(-1===(b=o.indexOf(e,w))||-1===(y=o.indexOf(t,b+c))){w!==g&&C.push(a(o.substring(w)));break}w!==b&&C.push(a(o.substring(w,b))),w=o.substring(b+c,y),x.push(w),k.push(n(w,h)),w=y+p,T.push(C.length),C.push("")}if(d&&1<C.length&&dr.throwNoconcat(o),!l||x.length){var D=function(e){for(var t=0,n=x.length;t<n;t++){if(f&&v(e[t]))return;C[T[t]]=e[t]}return C.join("")};return u(function(e){var t=0,n=x.length,r=Array(n);try{for(;t<n;t++)r[t]=k[t](e);return D(r)}catch(e){i(dr.interr(o,e))}},{exp:o,expressions:x,$$watchDelegate:function(e,t){var n;return e.$watchGroup(k,function(i,r){var o=D(i);t.call(this,o,i!==r?n:o,e),n=o})}})}}var c=e.length,p=t.length,d=new RegExp(e.replace(/./g,o),"g"),f=new RegExp(t.replace(/./g,o),"g");return l.startSymbol=function(){return e},l.endSymbol=function(){return t},l}]}function dt(){this.$get=["$rootScope","$window","$q","$$q","$browser",function(e,t,n,i,r){function o(o,s,l,u){function c(){p?o.apply(null,d):o(m)}var p=4<arguments.length,d=p?Xn.call(arguments,4):[],f=t.setInterval,h=t.clearInterval,m=0,g=$(u)&&!u,v=(g?i:n).defer(),b=v.promise;return l=$(l)?l:0,b.$$intervalId=f(function(){g?r.defer(c):e.$evalAsync(c),v.notify(m++),0<l&&m>=l&&(v.resolve(m),h(b.$$intervalId),delete a[b.$$intervalId]),g||e.$apply()},s),a[b.$$intervalId]=v,b}var a={};return o.cancel=function(e){return!!(e&&e.$$intervalId in a)&&(a[e.$$intervalId].promise.$$state.pur=!0,a[e.$$intervalId].reject("canceled"),t.clearInterval(e.$$intervalId),delete a[e.$$intervalId],!0)},o}]}function ft(e){for(var t=(e=e.split("/")).length;t--;)e[t]=G(e[t].replace(/%2F/g,"/"));return e.join("/")}function ht(e,t){var n=Qt(e);t.$$protocol=n.protocol,t.$$host=n.hostname,t.$$port=p(n.port)||mr[n.protocol]||null}function mt(e,t,n){if(vr.test(e))throw gr("badpath",e);(i="/"!==e.charAt(0))&&(e="/"+e),e=Qt(e);for(var i,r=(i=(i&&"/"===e.pathname.charAt(0)?e.pathname.substring(1):e.pathname).split("/")).length;r--;)i[r]=decodeURIComponent(i[r]),n&&(i[r]=i[r].replace(/\//g,"%2F"));n=i.join("/"),t.$$path=n,t.$$search=z(e.search),t.$$hash=decodeURIComponent(e.hash),t.$$path&&"/"!==t.$$path.charAt(0)&&(t.$$path="/"+t.$$path)}function gt(e,t){return e.slice(0,t.length)===t}function vt(e,t){if(gt(t,e))return t.substr(e.length)}function $t(e){var t=e.indexOf("#");return-1===t?e:e.substr(0,t)}function bt(e){return e.replace(/(#.+)|#$/,"$1")}function yt(e,t,n){this.$$html5=!0,n=n||"",ht(e,this),this.$$parse=function(e){var n=vt(t,e);if(!w(n))throw gr("ipthprfx",e,t);mt(n,this,!0),this.$$path||(this.$$path="/"),this.$$compose()},this.$$compose=function(){var e=Y(this.$$search),n=this.$$hash?"#"+G(this.$$hash):"";this.$$url=ft(this.$$path)+(e?"?"+e:"")+n,this.$$absUrl=t+this.$$url.substr(1),this.$$urlUpdatedByLocation=!0},this.$$parseLinkUrl=function(i,r){return r&&"#"===r[0]?(this.hash(r.slice(1)),!0):($(o=vt(e,i))?(a=o,a=n&&$(o=vt(n,o))?t+(vt("/",o)||o):e+a):$(o=vt(t,i))?a=t+o:t===i+"/"&&(a=t),a&&this.$$parse(a),!!a);var o,a}}function wt(e,t,n){ht(e,this),this.$$parse=function(i){var r;v(o=vt(e,i)||vt(t,i))||"#"!==o.charAt(0)?this.$$html5?r=o:(r="",v(o)&&(e=i,this.replace())):v(r=vt(n,o))&&(r=o),mt(r,this,!1),i=this.$$path;var o,a=/^\/[A-Z]:(\/.*)/;gt(r,o=e)&&(r=r.replace(o,"")),a.exec(r)||(i=(r=a.exec(i))?r[1]:i),this.$$path=i,this.$$compose()},this.$$compose=function(){var t=Y(this.$$search),i=this.$$hash?"#"+G(this.$$hash):"";this.$$url=ft(this.$$path)+(t?"?"+t:"")+i,this.$$absUrl=e+(this.$$url?n+this.$$url:""),this.$$urlUpdatedByLocation=!0},this.$$parseLinkUrl=function(t,n){return $t(e)===$t(t)&&(this.$$parse(t),!0)}}function xt(e,t,n){this.$$html5=!0,wt.apply(this,arguments),this.$$parseLinkUrl=function(i,r){return r&&"#"===r[0]?(this.hash(r.slice(1)),!0):(e===$t(i)?o=i:(a=vt(t,i))?o=e+n+a:t===i+"/"&&(o=t),o&&this.$$parse(o),!!o);var o,a},this.$$compose=function(){var t=Y(this.$$search),i=this.$$hash?"#"+G(this.$$hash):"";this.$$url=ft(this.$$path)+(t?"?"+t:"")+i,this.$$absUrl=e+n+this.$$url,this.$$urlUpdatedByLocation=!0}}function kt(e){return function(){return this[e]}}function Ct(e,t){return function(n){return v(n)?this[e]:(this[e]=t(n),this.$$compose(),this)}}function Tt(){var e="!",t={enabled:!1,requireBase:!0,rewriteLinks:!0};this.hashPrefix=function(t){return $(t)?(e=t,this):e},this.html5Mode=function(e){return O(e)?(t.enabled=e,this):b(e)?(O(e.enabled)&&(t.enabled=e.enabled),O(e.requireBase)&&(t.requireBase=e.requireBase),(O(e.rewriteLinks)||w(e.rewriteLinks))&&(t.rewriteLinks=e.rewriteLinks),this):t},this.$get=["$rootScope","$browser","$sniffer","$rootElement","$window",function(n,i,r,o,a){function s(e,t,n){var r=u.url(),o=u.$$state;try{i.url(e,t,n),u.$$state=i.state()}catch(e){throw u.url(r),u.$$state=o,e}}function l(e,t){n.$broadcast("$locationChangeSuccess",u.absUrl(),e,u.$$state,t)}var u,c;c=i.baseHref();var p,d=i.url();if(t.enabled){if(!c&&t.requireBase)throw gr("nobase");p=d.substring(0,d.indexOf("/",d.indexOf("//")+2))+(c||"/"),c=r.history?yt:xt}else p=$t(d),c=wt;var f=p.substr(0,$t(p).lastIndexOf("/")+1);(u=new c(p,f,"#"+e)).$$parseLinkUrl(d,d),u.$$state=i.state();var h=/^\s*(javascript|mailto):/i;o.on("click",function(e){if((s=t.rewriteLinks)&&!e.ctrlKey&&!e.metaKey&&!e.shiftKey&&2!==e.which&&2!==e.button){for(var r=Bn(e.target);"a"!==M(r[0]);)if(r[0]===o[0]||!(r=r.parent())[0])return;if(!w(s)||!v(r.attr(s))){var s=r.prop("href"),l=r.attr("href")||r.attr("xlink:href");b(s)&&"[object SVGAnimatedString]"===s.toString()&&(s=Qt(s.animVal).href),h.test(s)||!s||r.attr("target")||e.isDefaultPrevented()||!u.$$parseLinkUrl(s,l)||(e.preventDefault(),u.absUrl()!==i.url()&&(n.$apply(),a.angular["ff-684208-preventDefault"]=!0))}}}),bt(u.absUrl())!==bt(d)&&i.url(u.absUrl(),!0);var m=!0;return i.onUrlChange(function(e,t){gt(e,f)?(n.$evalAsync(function(){var i,r=u.absUrl(),o=u.$$state;e=bt(e),u.$$parse(e),u.$$state=t,i=n.$broadcast("$locationChangeStart",e,r,t,o).defaultPrevented,u.absUrl()===e&&(i?(u.$$parse(r),u.$$state=o,s(r,!1,o)):(m=!1,l(r,o)))}),n.$$phase||n.$digest()):a.location.href=e}),n.$watch(function(){if(m||u.$$urlUpdatedByLocation){u.$$urlUpdatedByLocation=!1;var e=bt(i.url()),t=bt(u.absUrl()),o=i.state(),a=u.$$replace,c=e!==t||u.$$html5&&r.history&&o!==u.$$state;(m||c)&&(m=!1,n.$evalAsync(function(){var t=u.absUrl(),i=n.$broadcast("$locationChangeStart",t,e,u.$$state,o).defaultPrevented;u.absUrl()===t&&(i?(u.$$parse(e),u.$$state=o):(c&&s(t,a,o===u.$$state?null:u.$$state),l(e,o)))}))}u.$$replace=!1}),u}]}function Dt(){var e=!0,t=this;this.debugEnabled=function(t){return $(t)?(e=t,this):e},this.$get=["$window",function(n){function i(e){var t=n.console||{},i=t[e]||t.log||f;return function(){var e=[];return o(arguments,function(t){e.push(function(e){return C(e)&&(e.stack&&r?e=e.message&&-1===e.stack.indexOf(e.message)?"Error: "+e.message+"\n"+e.stack:e.stack:e.sourceURL&&(e=e.message+"\n"+e.sourceURL+":"+e.line)),e}(t))}),Function.prototype.apply.call(i,t,e)}}var r=qn||/\bEdge\//.test(n.navigator&&n.navigator.userAgent);return{log:i("log"),info:i("info"),warn:i("warn"),error:i("error"),debug:function(){var n=i("debug");return function(){e&&n.apply(t,arguments)}}()}}]}function St(e){return e+""}function Et(e,t){return void 0!==e?e:t}function Ot(e,t){return void 0===e?t:void 0===t?e:e+t}function At(e,t,n){var i,r,a=e.isPure=function(e,t){switch(e.type){case Cr.MemberExpression:if(e.computed)return!1;break;case Cr.UnaryExpression:return 1;case Cr.BinaryExpression:return"+"!==e.operator&&1;case Cr.CallExpression:return!1}return void 0===t?Tr:t}(e,n);switch(e.type){case Cr.Program:i=!0,o(e.body,function(e){At(e.expression,t,a),i=i&&e.expression.constant}),e.constant=i;break;case Cr.Literal:e.constant=!0,e.toWatch=[];break;case Cr.UnaryExpression:At(e.argument,t,a),e.constant=e.argument.constant,e.toWatch=e.argument.toWatch;break;case Cr.BinaryExpression:At(e.left,t,a),At(e.right,t,a),e.constant=e.left.constant&&e.right.constant,e.toWatch=e.left.toWatch.concat(e.right.toWatch);break;case Cr.LogicalExpression:At(e.left,t,a),At(e.right,t,a),e.constant=e.left.constant&&e.right.constant,e.toWatch=e.constant?[]:[e];break;case Cr.ConditionalExpression:At(e.test,t,a),At(e.alternate,t,a),At(e.consequent,t,a),e.constant=e.test.constant&&e.alternate.constant&&e.consequent.constant,e.toWatch=e.constant?[]:[e];break;case Cr.Identifier:e.constant=!1,e.toWatch=[e];break;case Cr.MemberExpression:At(e.object,t,a),e.computed&&At(e.property,t,a),e.constant=e.object.constant&&(!e.computed||e.property.constant),e.toWatch=e.constant?[]:[e];break;case Cr.CallExpression:i=n=!!e.filter&&!t(e.callee.name).$stateful,r=[],o(e.arguments,function(e){At(e,t,a),i=i&&e.constant,r.push.apply(r,e.toWatch)}),e.constant=i,e.toWatch=n?r:[e];break;case Cr.AssignmentExpression:At(e.left,t,a),At(e.right,t,a),e.constant=e.left.constant&&e.right.constant,e.toWatch=[e];break;case Cr.ArrayExpression:i=!0,r=[],o(e.elements,function(e){At(e,t,a),i=i&&e.constant,r.push.apply(r,e.toWatch)}),e.constant=i,e.toWatch=r;break;case Cr.ObjectExpression:i=!0,r=[],o(e.properties,function(e){At(e.value,t,a),i=i&&e.value.constant,r.push.apply(r,e.value.toWatch),e.computed&&(At(e.key,t,!1),i=i&&e.key.constant,r.push.apply(r,e.key.toWatch))}),e.constant=i,e.toWatch=r;break;case Cr.ThisExpression:e.constant=!1,e.toWatch=[];break;case Cr.LocalsExpression:e.constant=!1,e.toWatch=[]}}function Mt(e){if(1===e.length){var t=(e=e[0].expression).toWatch;return 1!==t.length?t:t[0]!==e?t:void 0}}function Nt(e){return e.type===Cr.Identifier||e.type===Cr.MemberExpression}function It(e){if(1===e.body.length&&Nt(e.body[0].expression))return{type:Cr.AssignmentExpression,left:e.body[0].expression,right:{type:Cr.NGValueParameter},operator:"="}}function Pt(e){this.$filter=e}function jt(e){this.$filter=e}function Vt(e,t,n){this.ast=new Cr(e,n),this.astCompiler=n.csp?new jt(t):new Pt(t)}function Rt(e){return T(e.valueOf)?e.valueOf():yr.call(e)}function Ft(){var e,t,n=ae(),i={true:!0,false:!1,null:null,undefined:void 0};this.addLiteral=function(e,t){i[e]=t},this.setIdentifierFns=function(n,i){return e=n,t=i,this},this.$get=["$filter",function(r){function a(e,t){var i,o;switch(typeof e){case"string":return o=e=e.trim(),(i=n[o])||((i=new Vt(i=new kr(h),r,h).parse(e)).constant?i.$$watchDelegate=p:i.oneTime?i.$$watchDelegate=i.literal?c:u:i.inputs&&(i.$$watchDelegate=l),n[o]=i),d(i,t);case"function":return d(e,t);default:return d(f,t)}}function s(e,t,n){return null==e||null==t?e===t:!("object"==typeof e&&(e=Rt(e),"object"==typeof e&&!n))&&(e===t||e!=e&&t!=t)}function l(e,t,n,i,r){var o;if(1===(l=i.inputs).length){var a=s,l=l[0];return e.$watch(function(e){var t=l(e);return s(t,a,l.isPure)||(o=i(e,void 0,void 0,[t]),a=t&&Rt(t)),o},t,n,r)}for(var u=[],c=[],p=0,d=l.length;p<d;p++)u[p]=s,c[p]=null;return e.$watch(function(e){for(var t=!1,n=0,r=l.length;n<r;n++){var a=l[n](e);(t||(t=!s(a,u[n],l[n].isPure)))&&(c[n]=a,u[n]=a&&Rt(a))}return t&&(o=i(e,void 0,void 0,c)),o},t,n,r)}function u(e,t,n,i,r){function o(e,n,i){s=e,T(t)&&t(e,n,i),$(e)&&i.$$postDigest(function(){$(s)&&a()})}var a,s;return a=i.inputs?l(e,o,n,i,r):e.$watch(function(e){return i(e)},o,n)}function c(e,t,n,i){function r(e){var t=!0;return o(e,function(e){$(e)||(t=!1)}),t}var a,s;return a=e.$watch(function(e){return i(e)},function(e,n,i){s=e,T(t)&&t(e,n,i),r(e)&&i.$$postDigest(function(){r(s)&&a()})},n)}function p(e,t,n,i){var r=e.$watch(function(e){return r(),i(e)},t,n);return r}function d(e,t){if(!t)return e;var n=e.$$watchDelegate,i=!1,r=n!==c&&n!==u?function(n,r,o,a){return o=i&&a?a[0]:e(n,r,o,a),t(o,n,r)}:function(n,i,r,o){return r=e(n,i,r,o),n=t(r,n,i),$(r)?n:r};i=!e.inputs;return n&&n!==l?(r.$$watchDelegate=n,r.inputs=e.inputs):t.$stateful||(r.$$watchDelegate=l,r.inputs=e.inputs?e.inputs:[e]),r.inputs&&(r.inputs=r.inputs.map(function(e){return e.isPure===Tr?function(t){return e(t)}:e})),r}var h={csp:di().noUnsafeEval,literals:I(i),isIdentifierStart:T(e)&&e,isIdentifierContinue:T(t)&&t};return a.$$getAst=function(e){return new Vt(new kr(h),r,h).getAst(e).ast},a}]}function Ut(){var e=!0;this.$get=["$rootScope","$exceptionHandler",function(t,n){return Ht(function(e){t.$evalAsync(e)},n,e)}],this.errorOnUnhandledRejections=function(t){return $(t)?(e=t,this):e}}function Lt(){var e=!0;this.$get=["$browser","$exceptionHandler",function(t,n){return Ht(function(e){t.defer(e)},n,e)}],this.errorOnUnhandledRejections=function(t){return $(t)?(e=t,this):e}}function Ht(e,t,n){function r(){return new a}function a(){var e=this.promise=new s;this.resolve=function(t){p(e,t)},this.reject=function(t){d(e,t)},this.notify=function(t){h(e,t)}}function s(){this.$$state={status:0}}function l(){for(;!x&&k.length;){var e=k.shift();if(!e.pur){e.pur=!0;var n="Possibly unhandled rejection: "+("function"==typeof(n=e.value)?n.toString().replace(/ \{[\s\S]*$/,""):v(n)?"undefined":"string"!=typeof n?ue(n,void 0):n);C(e.value)?t(e.value,n):t(n)}}}function c(i){!n||i.pending||2!==i.status||i.pur||(0===x&&0===k.length&&e(l),k.push(i)),!i.processScheduled&&i.pending&&(i.processScheduled=!0,++x,e(function(){var r,o,a;a=i.pending,i.processScheduled=!1,i.pending=void 0;try{for(var s=0,u=a.length;s<u;++s){i.pur=!0,o=a[s][0],r=a[s][i.status];try{T(r)?p(o,r(i.value)):1===i.status?p(o,i.value):d(o,i.value)}catch(e){d(o,e),e&&!0===e.$$passToExceptionHandler&&t(e)}}}finally{--x,n&&0===x&&e(l)}}))}function p(e,t){e.$$state.status||(t===e?f(e,w("qcycle",t)):function e(t,n){function i(n){a||(a=!0,e(t,n))}function r(e){a||(a=!0,f(t,e))}var o,a=!1;try{(b(n)||T(n))&&(o=n.then),T(o)?(t.$$state.status=-1,o.call(n,function i(e){a||(a=!0,function e(t,n){function i(n){a||(a=!0,e(t,n))}function r(e){a||(a=!0,f(t,e))}var o,a=!1;try{(b(n)||T(n))&&(o=n.then),T(o)?(t.$$state.status=-1,o.call(n,i,r,function(e){h(t,e)})):(t.$$state.value=n,t.$$state.status=1,c(t.$$state))}catch(e){r(e)}}(t,e))},r,function(e){h(t,e)})):(t.$$state.value=n,t.$$state.status=1,c(t.$$state))}catch(e){r(e)}}(e,t))}function d(e,t){e.$$state.status||f(e,t)}function f(e,t){e.$$state.value=t,e.$$state.status=2,c(e.$$state)}function h(n,i){var r=n.$$state.pending;0>=n.$$state.status&&r&&r.length&&e(function(){for(var e,n,o=0,a=r.length;o<a;o++){n=r[o][0],e=r[o][3];try{h(n,T(e)?e(i):i)}catch(e){t(e)}}})}function m(e){var t=new s;return d(t,e),t}function g(e,t,n){var i=null;try{T(n)&&(i=n())}catch(e){return m(e)}return i&&T(i.then)?i.then(function(){return t(e)},m):t(e)}function $(e,t,n,i){var r=new s;return p(r,e),r.then(t,n,i)}function y(e){if(!T(e))throw w("norslvr",e);var t=new s;return e(function(e){p(t,e)},function(e){d(t,e)}),t}var w=i("$q",TypeError),x=0,k=[];u(s.prototype,{then:function(e,t,n){if(v(e)&&v(t)&&v(n))return this;var i=new s;return this.$$state.pending=this.$$state.pending||[],this.$$state.pending.push([i,e,t,n]),0<this.$$state.status&&c(this.$$state),i},catch:function(e){return this.then(null,e)},finally:function(e,t){return this.then(function(t){return g(t,D,e)},function(t){return g(t,m,e)},t)}});var D=$;return y.prototype=s.prototype,y.defer=r,y.reject=m,y.when=$,y.resolve=D,y.all=function(e){var t=new s,n=0,i=li(e)?[]:{};return o(e,function(e,r){n++,$(e).then(function(e){i[r]=e,--n||p(t,i)},function(e){d(t,e)})}),0===n&&p(t,i),t},y.race=function(e){var t=r();return o(e,function(e){$(e).then(t.resolve,t.reject)}),t.promise},y}function qt(){this.$get=["$window","$timeout",function(e,t){var n=e.requestAnimationFrame||e.webkitRequestAnimationFrame,i=e.cancelAnimationFrame||e.webkitCancelAnimationFrame||e.webkitCancelRequestAnimationFrame,r=!!n,o=r?function(e){var t=n(e);return function(){i(t)}}:function(e){var n=t(e,16.66,!1);return function(){t.cancel(n)}};return o.supported=r,o}]}function Bt(){var e=10,t=i("$rootScope"),n=null,a=null;this.digestTtl=function(t){return arguments.length&&(e=t),e},this.$get=["$exceptionHandler","$parse","$browser",function(i,s,l){function u(e){e.currentScope.$$destroyed=!0}function c(){this.$id=++ri,this.$$phase=this.$parent=this.$$watchers=this.$$nextSibling=this.$$prevSibling=this.$$childHead=this.$$childTail=null,this.$root=this,this.$$suspended=this.$$destroyed=!1,this.$$listeners={},this.$$listenerCount={},this.$$watchersCount=0,this.$$isolateBindings=null}function p(e){if($.$$phase)throw t("inprog",$.$$phase);$.$$phase=e}function d(e,t){do{e.$$watchersCount+=t}while(e=e.$parent)}function h(e,t,n){do{e.$$listenerCount[n]-=t,0===e.$$listenerCount[n]&&delete e.$$listenerCount[n]}while(e=e.$parent)}function m(){}function g(){for(;x.length;)try{x.shift()()}catch(e){i(e)}a=null}c.prototype={constructor:c,$new:function(e,t){var n;return t=t||this,e?(n=new c).$root=this.$root:(this.$$ChildScope||(this.$$ChildScope=function(e){function t(){this.$$watchers=this.$$nextSibling=this.$$childHead=this.$$childTail=null,this.$$listeners={},this.$$listenerCount={},this.$$watchersCount=0,this.$id=++ri,this.$$ChildScope=null,this.$$suspended=!1}return t.prototype=e,t}(this)),n=new this.$$ChildScope),n.$parent=t,n.$$prevSibling=t.$$childTail,t.$$childHead?(t.$$childTail.$$nextSibling=n,t.$$childTail=n):t.$$childHead=t.$$childTail=n,(e||t!==this)&&n.$on("$destroy",u),n},$watch:function(e,t,i,r){var o=s(e);if(t=T(t)?t:f,o.$$watchDelegate)return o.$$watchDelegate(this,t,i,o,e);var a=this,l=a.$$watchers,u={fn:t,last:m,get:o,exp:r||e,eq:!!i};return n=null,l||((l=a.$$watchers=[]).$$digestWatchIndex=-1),l.unshift(u),l.$$digestWatchIndex++,d(this,1),function(){var e=N(l,u);0<=e&&(d(a,-1),e<l.$$digestWatchIndex&&l.$$digestWatchIndex--),n=null}},$watchGroup:function(e,t){function n(){l=!1,u?(u=!1,t(r,r,s)):t(r,i,s)}var i=Array(e.length),r=Array(e.length),a=[],s=this,l=!1,u=!0;if(!e.length){var c=!0;return s.$evalAsync(function(){c&&t(r,r,s)}),function(){c=!1}}return 1===e.length?this.$watch(e[0],function(e,n,o){r[0]=e,i[0]=n,t(r,e===n?r:i,o)}):(o(e,function(e,t){var o=s.$watch(e,function(e,o){r[t]=e,i[t]=o,l||(l=!0,s.$evalAsync(n))});a.push(o)}),function(){for(;a.length;)a.shift()()})},$watchCollection:function(e,t){function n(e){var t,n,a;if(!v(i=e)){if(b(i))if(r(i))for(o!==d&&(m=(o=d).length=0,c++),e=i.length,m!==e&&(c++,o.length=m=e),t=0;t<e;t++)a=o[t],n=i[t],a!=a&&n!=n||a===n||(c++,o[t]=n);else{for(t in o!==f&&(o=f={},m=0,c++),e=0,i)Gn.call(i,t)&&(e++,n=i[t],a=o[t],t in o?a!=a&&n!=n||a===n||(c++,o[t]=n):(m++,o[t]=n,c++));if(m>e)for(t in c++,o)Gn.call(i,t)||(m--,delete o[t])}else o!==i&&(o=i,c++);return c}}n.$stateful=!0;var i,o,a,l=this,u=1<t.length,c=0,p=s(e,n),d=[],f={},h=!0,m=0;return this.$watch(p,function(){if(h?(h=!1,t(i,i,l)):t(i,a,l),u)if(b(i))if(r(i)){a=Array(i.length);for(var e=0;e<i.length;e++)a[e]=i[e]}else for(e in a={},i)Gn.call(i,e)&&(a[e]=i[e]);else a=i})},$digest:function(){var r,o,s,u,c,d,f,h,v=e,b=[];p("$digest"),l.$$checkUrlChange(),this===$&&null!==a&&(l.defer.cancel(a),g()),n=null;do{for(c=!1,d=this,u=0;u<y.length;u++){try{(0,(h=y[u]).fn)(h.scope,h.locals)}catch(e){i(e)}n=null}y.length=0;e:do{if(u=!d.$$suspended&&d.$$watchers)for(u.$$digestWatchIndex=u.length;u.$$digestWatchIndex--;)try{if(r=u[u.$$digestWatchIndex])if((o=(0,r.get)(d))===(s=r.last)||(r.eq?j(o,s):oi(o)&&oi(s))){if(r===n){c=!1;break e}}else c=!0,n=r,r.last=r.eq?I(o,null):o,(0,r.fn)(o,s===m?o:s,d),5>v&&(b[f=4-v]||(b[f]=[]),b[f].push({msg:T(r.exp)?"fn: "+(r.exp.name||r.exp.toString()):r.exp,newVal:o,oldVal:s}))}catch(e){i(e)}if(!(u=!d.$$suspended&&d.$$watchersCount&&d.$$childHead||d!==this&&d.$$nextSibling))for(;d!==this&&!(u=d.$$nextSibling);)d=d.$parent}while(d=u);if((c||y.length)&&!v--)throw $.$$phase=null,t("infdig",e,b)}while(c||y.length);for($.$$phase=null;k<w.length;)try{w[k++]()}catch(e){i(e)}w.length=k=0,l.$$checkUrlChange()},$suspend:function(){this.$$suspended=!0},$isSuspended:function(){return this.$$suspended},$resume:function(){this.$$suspended=!1},$destroy:function(){if(!this.$$destroyed){var e=this.$parent;for(var t in this.$broadcast("$destroy"),this.$$destroyed=!0,this===$&&l.$$applicationDestroyed(),d(this,-this.$$watchersCount),this.$$listenerCount)h(this,this.$$listenerCount[t],t);e&&e.$$childHead===this&&(e.$$childHead=this.$$nextSibling),e&&e.$$childTail===this&&(e.$$childTail=this.$$prevSibling),this.$$prevSibling&&(this.$$prevSibling.$$nextSibling=this.$$nextSibling),this.$$nextSibling&&(this.$$nextSibling.$$prevSibling=this.$$prevSibling),this.$destroy=this.$digest=this.$apply=this.$evalAsync=this.$applyAsync=f,this.$on=this.$watch=this.$watchGroup=function(){return f},this.$$listeners={},this.$$nextSibling=null,function e(t){9===qn&&(t.$$childHead&&e(t.$$childHead),t.$$nextSibling&&e(t.$$nextSibling)),t.$parent=t.$$nextSibling=t.$$prevSibling=t.$$childHead=t.$$childTail=t.$root=t.$$watchers=null}(this)}},$eval:function(e,t){return s(e)(this,t)},$evalAsync:function(e,t){$.$$phase||y.length||l.defer(function(){y.length&&$.$digest()}),y.push({scope:this,fn:s(e),locals:t})},$$postDigest:function(e){w.push(e)},$apply:function(e){try{p("$apply");try{return this.$eval(e)}finally{$.$$phase=null}}catch(e){i(e)}finally{try{$.$digest()}catch(e){throw i(e),e}}},$applyAsync:function(e){var t=this;e&&x.push(function(){t.$eval(e)}),e=s(e),null===a&&(a=l.defer(function(){$.$apply(g)}))},$on:function(e,t){var n=this.$$listeners[e];n||(this.$$listeners[e]=n=[]),n.push(t);var i=this;do{i.$$listenerCount[e]||(i.$$listenerCount[e]=0),i.$$listenerCount[e]++}while(i=i.$parent);var r=this;return function(){var i=n.indexOf(t);-1!==i&&(delete n[i],h(r,1,e))}},$emit:function(e,t){var n,r,o,a=[],s=this,l=!1,u={name:e,targetScope:s,stopPropagation:function(){l=!0},preventDefault:function(){u.defaultPrevented=!0},defaultPrevented:!1},c=V([u],arguments,1);do{for(n=s.$$listeners[e]||a,u.currentScope=s,r=0,o=n.length;r<o;r++)if(n[r])try{n[r].apply(null,c)}catch(e){i(e)}else n.splice(r,1),r--,o--;if(l)break;s=s.$parent}while(s);return u.currentScope=null,u},$broadcast:function(e,t){var n=this,r=this,o={name:e,targetScope:this,preventDefault:function(){o.defaultPrevented=!0},defaultPrevented:!1};if(!this.$$listenerCount[e])return o;for(var a,s,l=V([o],arguments,1);n=r;){for(o.currentScope=n,a=0,s=(r=n.$$listeners[e]||[]).length;a<s;a++)if(r[a])try{r[a].apply(null,l)}catch(e){i(e)}else r.splice(a,1),a--,s--;if(!(r=n.$$listenerCount[e]&&n.$$childHead||n!==this&&n.$$nextSibling))for(;n!==this&&!(r=n.$$nextSibling);)n=n.$parent}return o.currentScope=null,o}};var $=new c,y=$.$$asyncQueue=[],w=$.$$postDigestQueue=[],x=$.$$applyAsyncQueue=[],k=0;return $}]}function _t(){var e=/^\s*(https?|s?ftp|mailto|tel|file):/,t=/^\s*((https?|ftp|file|blob):|data:image\/)/;this.aHrefSanitizationWhitelist=function(t){return $(t)?(e=t,this):e},this.imgSrcSanitizationWhitelist=function(e){return $(e)?(t=e,this):t},this.$get=function(){return function(n,i){var r,o=i?t:e;return""===(r=Qt(n&&n.trim()).href)||r.match(o)?n:"unsafe:"+r}}}function Wt(e){var t=[];return $(e)&&o(e,function(e){t.push(function(e){if("self"===e)return e;if(w(e)){if(-1<e.indexOf("***"))throw Dr("iwcard",e);return e=pi(e).replace(/\\\*\\\*/g,".*").replace(/\\\*/g,"[^:/.?&;]*"),new RegExp("^"+e+"$")}if(D(e))return new RegExp("^"+e.source+"$");throw Dr("imatcher")}(e))}),t}function zt(){this.SCE_CONTEXTS=Sr;var e=["self"],t=[];this.resourceUrlWhitelist=function(t){return arguments.length&&(e=Wt(t)),e},this.resourceUrlBlacklist=function(e){return arguments.length&&(t=Wt(e)),t},this.$get=["$injector",function(n){function i(e,t){return"self"===e?Zt(t,Mr):!!e.exec(t.href)}function r(e){var t=function(e){this.$$unwrapTrustedValue=function(){return e}};return e&&(t.prototype=new e),t.prototype.valueOf=function(){return this.$$unwrapTrustedValue()},t.prototype.toString=function(){return this.$$unwrapTrustedValue().toString()},t}var o=function(e){throw Dr("unsafe")};n.has("$sanitize")&&(o=n.get("$sanitize"));var a=r(),s={};return s[Sr.HTML]=r(a),s[Sr.CSS]=r(a),s[Sr.URL]=r(a),s[Sr.JS]=r(a),s[Sr.RESOURCE_URL]=r(s[Sr.URL]),{trustAs:function(e,t){var n=s.hasOwnProperty(e)?s[e]:null;if(!n)throw Dr("icontext",e,t);if(null===t||v(t)||""===t)return t;if("string"!=typeof t)throw Dr("itype",e);return new n(t)},getTrusted:function(n,r){if(null===r||v(r)||""===r)return r;if((u=s.hasOwnProperty(n)?s[n]:null)&&r instanceof u)return r.$$unwrapTrustedValue();if(n===Sr.RESOURCE_URL){var a,l,u=Qt(r.toString()),c=!1;for(a=0,l=e.length;a<l;a++)if(i(e[a],u)){c=!0;break}if(c)for(a=0,l=t.length;a<l;a++)if(i(t[a],u)){c=!1;break}if(c)return r;throw Dr("insecurl",r.toString())}if(n===Sr.HTML)return o(r);throw Dr("unsafe")},valueOf:function(e){return e instanceof a?e.$$unwrapTrustedValue():e}}}]}function Yt(){var e=!0;this.enabled=function(t){return arguments.length&&(e=!!t),e},this.$get=["$parse","$sceDelegate",function(t,n){if(e&&8>qn)throw Dr("iequirks");var i=le(Sr);i.isEnabled=function(){return e},i.trustAs=n.trustAs,i.getTrusted=n.getTrusted,i.valueOf=n.valueOf,e||(i.trustAs=i.getTrusted=function(e,t){return t},i.valueOf=h),i.parseAs=function(e,n){var r=t(n);return r.literal&&r.constant?r:t(n,function(t){return i.getTrusted(e,t)})};var r=i.parseAs,a=i.getTrusted,s=i.trustAs;return o(Sr,function(e,t){var n=Kn(t);i[("parse_as_"+n).replace(Er,ce)]=function(t){return r(e,t)},i[("get_trusted_"+n).replace(Er,ce)]=function(t){return a(e,t)},i[("trust_as_"+n).replace(Er,ce)]=function(t){return s(e,t)}}),i}]}function Gt(){this.$get=["$window","$document",function(e,t){var n={},i=!((!e.nw||!e.nw.process)&&e.chrome&&(e.chrome.app&&e.chrome.app.runtime||!e.chrome.app&&e.chrome.runtime&&e.chrome.runtime.id))&&e.history&&e.history.pushState,r=p((/android (\d+)/.exec(Kn((e.navigator||{}).userAgent))||[])[1]),o=/Boxee/i.test((e.navigator||{}).userAgent),a=t[0]||{},s=a.body&&a.body.style,l=!1,u=!1;return s&&(l=!!("transition"in s||"webkitTransition"in s),u=!!("animation"in s||"webkitAnimation"in s)),{history:!(!i||4>r||o),hasEvent:function(e){if("input"===e&&qn)return!1;if(v(n[e])){var t=a.createElement("div");n[e]="on"+e in t}return n[e]},csp:di(),transitions:l,animations:u,android:r}}]}function Kt(){var e;this.httpOptions=function(t){return t?(e=t,this):e},this.$get=["$exceptionHandler","$templateCache","$http","$q","$sce",function(t,n,i,r,o){function a(s,l){a.totalPendingRequests++,w(s)&&!v(n.get(s))||(s=o.getTrustedResourceUrl(s));var c=i.defaults&&i.defaults.transformResponse;return li(c)?c=c.filter(function(e){return e!==rt}):c===rt&&(c=null),i.get(s,u({cache:n,transformResponse:c},e)).finally(function(){a.totalPendingRequests--}).then(function(e){return n.put(s,e.data),e.data},function(e){return l||(e=Or("tpload",s,e.status,e.statusText),t(e)),r.reject(e)})}return a.totalPendingRequests=0,a}]}function Jt(){this.$get=["$rootScope","$browser","$location",function(e,t,n){return{findBindings:function(e,t,n){e=e.getElementsByClassName("ng-binding");var i=[];return o(e,function(e){var r=ii.element(e).data("$binding");r&&o(r,function(r){n?new RegExp("(^|\\s)"+pi(t)+"(\\s|\\||$)").test(r)&&i.push(e):-1!==r.indexOf(t)&&i.push(e)})}),i},findModels:function(e,t,n){for(var i=["ng-","data-ng-","ng\\:"],r=0;r<i.length;++r){var o=e.querySelectorAll("["+i[r]+"model"+(n?"=":"*=")+'"'+t+'"]');if(o.length)return o}},getLocation:function(){return n.url()},setLocation:function(t){t!==n.url()&&(n.url(t),e.$digest())},whenStable:function(e){t.notifyWhenNoOutstandingRequests(e)}}}]}function Xt(){this.$get=["$rootScope","$browser","$q","$$q","$exceptionHandler",function(e,t,n,i,r){function o(o,s,l){T(o)||(l=s,s=o,o=f);var u,c=Xn.call(arguments,3),p=$(l)&&!l,d=(p?i:n).defer(),h=d.promise;return u=t.defer(function(){try{d.resolve(o.apply(null,c))}catch(e){d.reject(e),r(e)}finally{delete a[h.$$timeoutId]}p||e.$apply()},s),h.$$timeoutId=u,a[u]=d,h}var a={};return o.cancel=function(e){return!!(e&&e.$$timeoutId in a)&&(a[e.$$timeoutId].promise.$$state.pur=!0,a[e.$$timeoutId].reject("canceled"),delete a[e.$$timeoutId],t.defer.cancel(e.$$timeoutId))},o}]}function Qt(e){return w(e)?(qn&&(Ar.setAttribute("href",e),e=Ar.href),Ar.setAttribute("href",e),{href:Ar.href,protocol:Ar.protocol?Ar.protocol.replace(/:$/,""):"",host:Ar.host,search:Ar.search?Ar.search.replace(/^\?/,""):"",hash:Ar.hash?Ar.hash.replace(/^#/,""):"",hostname:Ar.hostname,port:Ar.port,pathname:"/"===Ar.pathname.charAt(0)?Ar.pathname:"/"+Ar.pathname}):e}function Zt(e,t){return e=Qt(e),t=Qt(t),e.protocol===t.protocol&&e.host===t.host}function en(){this.$get=m(e)}function tn(e){function t(e){try{return decodeURIComponent(e)}catch(t){return e}}var n=e[0]||{},i={},r="";return function(){var e,o,a,s,l;try{e=n.cookie||""}catch(t){e=""}if(e!==r)for(e=(r=e).split("; "),i={},a=0;a<e.length;a++)0<(s=(o=e[a]).indexOf("="))&&(l=t(o.substring(0,s)),v(i[l])&&(i[l]=t(o.substring(s+1))));return i}}function nn(){this.$get=tn}function rn(e){function t(n,i){if(b(n)){var r={};return o(n,function(e,n){r[n]=t(n,e)}),r}return e.factory(n+"Filter",i)}this.register=t,this.$get=["$injector",function(e){return function(t){return e.get(t+"Filter")}}],t("currency",ln),t("date",vn),t("filter",on),t("json",$n),t("limitTo",bn),t("lowercase",Fr),t("number",un),t("orderBy",wn),t("uppercase",Ur)}function on(){return function(e,t,n,o){if(!r(e)){if(null==e)return e;throw i("filter")("notarray",e)}var a;switch(o=o||"$",sn(t)){case"function":break;case"boolean":case"null":case"number":case"string":a=!0;case"object":t=function(e,t,n,i){var r=b(e)&&n in e;return!0===t?t=j:T(t)||(t=function(e,t){return!(v(e)||(null===e||null===t?e!==t:b(t)||b(e)&&!g(e)||(e=Kn(""+e),t=Kn(""+t),-1===e.indexOf(t))))}),function(o){return r&&!b(o)?an(o,e[n],t,n,!1):an(o,e,t,n,i)}}(t,n,o,a);break;default:return e}return Array.prototype.filter.call(e,t)}}function an(e,t,n,i,r,o){var a=sn(e),s=sn(t);if("string"===s&&"!"===t.charAt(0))return!an(e,t.substring(1),n,i,r);if(li(e))return e.some(function(e){return an(e,t,n,i,r)});switch(a){case"object":var l;if(r){for(l in e)if(l.charAt&&"$"!==l.charAt(0)&&an(e[l],t,n,i,!0))return!0;return!o&&an(e,t,n,i,!1)}if("object"===s){for(l in t)if(!T(o=t[l])&&!v(o)&&!an((a=l===i)?e:e[l],o,n,i,a,a))return!1;return!0}return n(e,t);case"function":return!1;default:return n(e,t)}}function sn(e){return null===e?"null":typeof e}function ln(e){var t=e.NUMBER_FORMATS;return function(e,n,i){v(n)&&(n=t.CURRENCY_SYM),v(i)&&(i=t.PATTERNS[1].maxFrac);var r=n?/\u00A4/g:/\s*\u00A4\s*/g;return null==e?e:cn(e,t.PATTERNS[1],t.GROUP_SEP,t.DECIMAL_SEP,i).replace(r,n)}}function un(e){var t=e.NUMBER_FORMATS;return function(e,n){return null==e?e:cn(e,t.PATTERNS[0],t.GROUP_SEP,t.DECIMAL_SEP,n)}}function cn(e,t,n,i,r){if(!w(e)&&!x(e)||isNaN(e))return"";var o=!isFinite(e),a=!1,s=Math.abs(e)+"",l="";if(o)l="∞";else{for(function(e,t,n,i){var r=e.d,o=r.length-e.i;if(i=r[n=(t=v(t)?Math.min(Math.max(n,o),i):+t)+e.i],0<n){r.splice(Math.max(e.i,n));for(var a=n;a<r.length;a++)r[a]=0}else for(o=Math.max(0,o),e.i=1,r.length=Math.max(1,n=t+1),r[0]=0,a=1;a<n;a++)r[a]=0;if(5<=i)if(0>n-1){for(i=0;i>n;i--)r.unshift(0),e.i++;r.unshift(1),e.i++}else r[n-1]++;for(;o<Math.max(0,t);o++)r.push(0);(t=r.reduceRight(function(e,t,n,i){return t+=e,i[n]=t%10,Math.floor(t/10)},0))&&(r.unshift(t),e.i++)}(a=function(e){var t,n,i,r,o,a=0;for(-1<(n=e.indexOf(Ir))&&(e=e.replace(Ir,"")),0<(i=e.search(/e/i))?(0>n&&(n=i),n+=+e.slice(i+1),e=e.substring(0,i)):0>n&&(n=e.length),i=0;e.charAt(i)===Pr;i++);if(i===(o=e.length))t=[0],n=1;else{for(o--;e.charAt(o)===Pr;)o--;for(n-=i,t=[],r=0;i<=o;i++,r++)t[r]=+e.charAt(i)}return n>Nr&&(t=t.splice(0,Nr-1),a=n-1,n=1),{d:t,e:a,i:n}}(s),r,t.minFrac,t.maxFrac),l=a.d,s=a.i,r=a.e,o=[],a=l.reduce(function(e,t){return e&&!t},!0);0>s;)l.unshift(0),s++;for(0<s?o=l.splice(s,l.length):(o=l,l=[0]),s=[],l.length>=t.lgSize&&s.unshift(l.splice(-t.lgSize,l.length).join(""));l.length>t.gSize;)s.unshift(l.splice(-t.gSize,l.length).join(""));l.length&&s.unshift(l.join("")),l=s.join(n),o.length&&(l+=i+o.join("")),r&&(l+="e+"+r)}return 0>e&&!a?t.negPre+l+t.negSuf:t.posPre+l+t.posSuf}function pn(e,t,n,i){var r="";for((0>e||i&&0>=e)&&(i?e=1-e:(e=-e,r="-")),e=""+e;e.length<t;)e=Pr+e;return n&&(e=e.substr(e.length-t)),r+e}function dn(e,t,n,i,r){return n=n||0,function(o){return o=o["get"+e](),(0<n||o>-n)&&(o+=n),0===o&&-12===n&&(o=12),pn(o,t,i,r)}}function fn(e,t,n){return function(i,r){var o=i["get"+e]();return r[Jn((n?"STANDALONE":"")+(t?"SHORT":"")+e)][o]}}function hn(e){var t=new Date(e,0,1).getDay();return new Date(e,0,(4>=t?5:12)-t)}function mn(e){return function(t){var n=hn(t.getFullYear());return t=+new Date(t.getFullYear(),t.getMonth(),t.getDate()+(4-t.getDay()))-+n,pn(t=1+Math.round(t/6048e5),e)}}function gn(e,t){return 0>=e.getFullYear()?t.ERAS[0]:t.ERAS[1]}function vn(e){function t(e){var t;if(t=e.match(n)){e=new Date(0);var i=0,r=0,o=t[8]?e.setUTCFullYear:e.setFullYear,a=t[8]?e.setUTCHours:e.setHours;t[9]&&(i=p(t[9]+t[10]),r=p(t[9]+t[11])),o.call(e,p(t[1]),p(t[2])-1,p(t[3])),i=p(t[4]||0)-i,r=p(t[5]||0)-r,o=p(t[6]||0),t=Math.round(1e3*parseFloat("0."+(t[7]||0))),a.call(e,i,r,o,t)}return e}var n=/^(\d{4})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/;return function(n,i,r){var a,s,l="",u=[];if(i=i||"mediumDate",i=e.DATETIME_FORMATS[i]||i,w(n)&&(n=Rr.test(n)?p(n):t(n)),x(n)&&(n=new Date(n)),!k(n)||!isFinite(n.getTime()))return n;for(;i;)(s=Vr.exec(i))?i=(u=V(u,s,1)).pop():(u.push(i),i=null);var c=n.getTimezoneOffset();return r&&(c=H(r,c),n=B(n,r,!0)),o(u,function(t){a=jr[t],l+=a?a(n,e.DATETIME_FORMATS,c):"''"===t?"'":t.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}}function $n(){return function(e,t){return v(t)&&(t=2),U(e,t)}}function bn(){return function(e,t,n){return t=1/0===Math.abs(Number(t))?Number(t):p(t),oi(t)?e:(x(e)&&(e=e.toString()),r(e)?(n=0>(n=!n||isNaN(n)?0:p(n))?Math.max(0,e.length+n):n,0<=t?yn(e,n,n+t):0===n?yn(e,t,e.length):yn(e,Math.max(0,n+t),n)):e)}}function yn(e,t,n){return w(e)?e.slice(t,n):Xn.call(e,t,n)}function wn(e){function t(t){return t.map(function(t){var n=1,i=h;if(T(t))i=t;else if(w(t)&&("+"!==t.charAt(0)&&"-"!==t.charAt(0)||(n="-"===t.charAt(0)?-1:1,t=t.substring(1)),""!==t&&(i=e(t)).constant)){var r=i();i=function(e){return e[r]}}return{get:i,descending:n}})}function n(e){switch(typeof e){case"number":case"boolean":case"string":return!0;default:return!1}}function o(e,t){var n=0,i=e.type;if(i===(r=t.type)){var r=e.value,o=t.value;"string"===i?(r=r.toLowerCase(),o=o.toLowerCase()):"object"===i&&(b(r)&&(r=e.index),b(o)&&(o=t.index)),r!==o&&(n=r<o?-1:1)}else n=i<r?-1:1;return n}return function(e,a,s,l){if(null==e)return e;if(!r(e))throw i("orderBy")("notarray",e);li(a)||(a=[a]),0===a.length&&(a=["+"]);var u=t(a),c=s?-1:1,p=T(l)?l:o;return(e=Array.prototype.map.call(e,function(e,t){return{value:e,tieBreaker:{value:t,type:"number",index:t},predicateValues:u.map(function(i){var r=i.get(e);return i=typeof r,null===r?(i="string",r="null"):"object"===i&&(T(r.valueOf)&&n(r=r.valueOf())||g(r)&&n(r=r.toString())),{value:r,type:i,index:t}})}})).sort(function(e,t){for(var n=0,i=u.length;n<i;n++){var r=p(e.predicateValues[n],t.predicateValues[n]);if(r)return r*u[n].descending*c}return(p(e.tieBreaker,t.tieBreaker)||o(e.tieBreaker,t.tieBreaker))*c}),e.map(function(e){return e.value})}}function xn(e){return T(e)&&(e={link:e}),e.restrict=e.restrict||"AC",m(e)}function kn(e,t,n,i,r){this.$$controls=[],this.$error={},this.$$success={},this.$pending=void 0,this.$name=r(t.name||t.ngForm||"")(n),this.$dirty=!1,this.$valid=this.$pristine=!0,this.$submitted=this.$invalid=!1,this.$$parentForm=qr,this.$$element=e,this.$$animate=i,Cn(this)}function Cn(e){e.$$classCache={},e.$$classCache[Co]=!(e.$$classCache[ko]=e.$$element.hasClass(ko))}function Tn(e){function t(e,t,n){n&&!e.$$classCache[t]?(e.$$animate.addClass(e.$$element,t),e.$$classCache[t]=!0):!n&&e.$$classCache[t]&&(e.$$animate.removeClass(e.$$element,t),e.$$classCache[t]=!1)}function n(e,n,i){n=n?"-"+ee(n,"-"):"",t(e,ko+n,!0===i),t(e,Co+n,!1===i)}var i=e.set,r=e.unset;e.clazz.prototype.$setValidity=function(e,o,a){v(o)?(this.$pending||(this.$pending={}),i(this.$pending,e,a)):(this.$pending&&r(this.$pending,e,a),Dn(this.$pending)&&(this.$pending=void 0)),O(o)?o?(r(this.$error,e,a),i(this.$$success,e,a)):(i(this.$error,e,a),r(this.$$success,e,a)):(r(this.$error,e,a),r(this.$$success,e,a)),this.$pending?(t(this,"ng-pending",!0),this.$valid=this.$invalid=void 0,n(this,"",null)):(t(this,"ng-pending",!1),this.$valid=Dn(this.$error),this.$invalid=!this.$valid,n(this,"",this.$valid)),n(this,e,o=this.$pending&&this.$pending[e]?void 0:!this.$error[e]&&(!!this.$$success[e]||null)),this.$$parentForm.$setValidity(e,o,this)}}function Dn(e){if(e)for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}function Sn(e){e.$formatters.push(function(t){return e.$isEmpty(t)?t:t.toString()})}function En(e,t,n,i,r,o){var a=Kn(t[0].type);if(!r.android){var s=!1;t.on("compositionstart",function(){s=!0}),t.on("compositionupdate",function(e){(v(e.data)||""===e.data)&&(s=!1)}),t.on("compositionend",function(){s=!1,u()})}var l,u=function(e){if(l&&(o.defer.cancel(l),l=null),!s){var r=t.val();e=e&&e.type,"password"===a||n.ngTrim&&"false"===n.ngTrim||(r=ci(r)),(i.$viewValue!==r||""===r&&i.$$hasNativeValidators)&&i.$setViewValue(r,e)}};if(r.hasEvent("input"))t.on("input",u);else{var c=function(e,t,n){l||(l=o.defer(function(){l=null,t&&t.value===n||u(e)}))};t.on("keydown",function(e){var t=e.keyCode;91===t||15<t&&19>t||37<=t&&40>=t||c(e,this,this.value)}),r.hasEvent("paste")&&t.on("paste cut drop",c)}t.on("change",u),to[a]&&i.$$hasNativeValidators&&a===n.type&&t.on("keydown wheel mousedown",function(e){if(!l){var t=this.validity,n=t.badInput,i=t.typeMismatch;l=o.defer(function(){l=null,t.badInput===n&&t.typeMismatch===i||u(e)})}}),i.$render=function(){var e=i.$isEmpty(i.$viewValue)?"":i.$viewValue;t.val()!==e&&t.val(e)}}function On(e,t){return function(n,i){var r,a;if(k(n))return n;if(w(n)){if('"'===n.charAt(0)&&'"'===n.charAt(n.length-1)&&(n=n.substring(1,n.length-1)),zr.test(n))return new Date(n);if(e.lastIndex=0,r=e.exec(n))return r.shift(),a=i?{yyyy:i.getFullYear(),MM:i.getMonth()+1,dd:i.getDate(),HH:i.getHours(),mm:i.getMinutes(),ss:i.getSeconds(),sss:i.getMilliseconds()/1e3}:{yyyy:1970,MM:1,dd:1,HH:0,mm:0,ss:0,sss:0},o(r,function(e,n){n<t.length&&(a[t[n]]=+e)}),new Date(a.yyyy,a.MM-1,a.dd,a.HH,a.mm,a.ss||0,1e3*a.sss||0)}return NaN}}function An(e,t,n,i){return function(r,o,a,s,l,u,c){function p(e){return e&&!(e.getTime&&e.getTime()!=e.getTime())}function d(e){return $(e)&&!k(e)?f(e)||void 0:e}function f(e,t){var i=s.$options.getOption("timezone");m&&m!==i&&(t=q(t,H(m)));var r=n(e,t);return!isNaN(r)&&i&&(r=B(r,i)),r}var h,m,g,b;(Mn(r,o,a,s),En(0,o,a,s,l,u),s.$$parserName=e,s.$parsers.push(function(e){return s.$isEmpty(e)?null:t.test(e)?f(e,h):void 0}),s.$formatters.push(function(e){if(e&&!k(e))throw So("datefmt",e);if(p(e)){h=e;var t=s.$options.getOption("timezone");return t&&(m=t,h=B(h,t,!0)),c("date")(e,i,t)}return m=h=null,""}),$(a.min)||a.ngMin)&&(s.$validators.min=function(e){return!p(e)||v(g)||n(e)>=g},a.$observe("min",function(e){g=d(e),s.$validate()}));($(a.max)||a.ngMax)&&(s.$validators.max=function(e){return!p(e)||v(b)||n(e)<=b},a.$observe("max",function(e){b=d(e),s.$validate()}))}}function Mn(e,t,n,i){(i.$$hasNativeValidators=b(t[0].validity))&&i.$parsers.push(function(e){var n=t.prop("validity")||{};return n.badInput||n.typeMismatch?void 0:e})}function Nn(e){e.$$parserName="number",e.$parsers.push(function(t){return e.$isEmpty(t)?null:Kr.test(t)?parseFloat(t):void 0}),e.$formatters.push(function(t){if(!e.$isEmpty(t)){if(!x(t))throw So("numfmt",t);t=t.toString()}return t})}function In(e){return $(e)&&!x(e)&&(e=parseFloat(e)),oi(e)?void 0:e}function Pn(e){var t=e.toString(),n=t.indexOf(".");return-1===n?-1<e&&1>e&&(e=/e-(\d+)$/.exec(t))?Number(e[1]):0:t.length-n-1}function jn(e,t,n){var i=(0|(e=Number(e)))!==e,r=(0|t)!==t,o=(0|n)!==n;if(i||r||o){var a=i?Pn(e):0,s=r?Pn(t):0,l=o?Pn(n):0;a=Math.max(a,s,l);e*=a=Math.pow(10,a),t*=a,n*=a,i&&(e=Math.round(e)),r&&(t=Math.round(t)),o&&(n=Math.round(n))}return 0==(e-t)%n}function Vn(e,t,n,i,r){if($(i)){if(!(e=e(i)).constant)throw So("constexpr",n,i);return e(t)}return r}function Rn(e,t){function n(e,t){if(!e||!e.length)return[];if(!t||!t.length)return e;var n=[],i=0;e:for(;i<e.length;i++){for(var r=e[i],o=0;o<t.length;o++)if(r===t[o])continue e;n.push(r)}return n}function i(e){var t=e;return li(e)?t=e.map(i).join(" "):b(e)&&(t=Object.keys(e).filter(function(t){return e[t]}).join(" ")),t}function r(e){var t=e;if(li(e))t=e.map(r);else if(b(e)){var n=!1;t=Object.keys(e).filter(function(t){return t=e[t],!n&&v(t)&&(n=!0),t});n&&t.push(void 0)}return t}var a;return e="ngClass"+e,["$parse",function(s){return{restrict:"AC",link:function(l,u,c){function p(e,t){var n=[];return o(e,function(e){(0<t||v[e])&&(v[e]=(v[e]||0)+t,v[e]===+(0<t)&&n.push(e))}),n.join(" ")}function d(e){if($===t){var i=f&&f.split(" "),r=e&&e.split(" "),o=n(i,r);i=n(r,i),o=p(o,-1),i=p(i,1);c.$addClass(i),c.$removeClass(o)}f=e}var f,h=":"===(m=c[e].trim()).charAt(0)&&":"===m.charAt(1),m=s(m,h?r:i),g=h?function(e){(e=i(e))!==f&&d(e)}:d,v=u.data("$classCounts"),$=!0;v||(v=ae(),u.data("$classCounts",v)),"ngClass"!==e&&(a||(a=s("$index",function(e){return 1&e})),l.$watch(a,function(e){if(e===t){var n=p((n=f)&&n.split(" "),1);c.$addClass(n)}else n=p((n=f)&&n.split(" "),-1),c.$removeClass(n);$=e})),l.$watch(m,g,h)}}}]}function Fn(e,t,n,i,r,o,a,s,l){this.$modelValue=this.$viewValue=Number.NaN,this.$$rawModelValue=void 0,this.$validators={},this.$asyncValidators={},this.$parsers=[],this.$formatters=[],this.$viewChangeListeners=[],this.$untouched=!0,this.$touched=!1,this.$pristine=!0,this.$dirty=!1,this.$valid=!0,this.$invalid=!1,this.$error={},this.$$success={},this.$pending=void 0,this.$name=l(n.name||"",!1)(e),this.$$parentForm=qr,this.$options=Eo,this.$$updateEvents="",this.$$updateEventHandler=this.$$updateEventHandler.bind(this),this.$$parsedNgModel=r(n.ngModel),this.$$parsedNgModelAssign=this.$$parsedNgModel.assign,this.$$ngModelGet=this.$$parsedNgModel,this.$$ngModelSet=this.$$parsedNgModelAssign,this.$$pendingDebounce=null,this.$$parserValid=void 0,this.$$currentValidationRunId=0,Object.defineProperty(this,"$$scope",{value:e}),this.$$attr=n,this.$$element=i,this.$$animate=o,this.$$timeout=a,this.$$parse=r,this.$$q=s,this.$$exceptionHandler=t,Cn(this),function(e){e.$$scope.$watch(function(t){return(t=e.$$ngModelGet(t))===e.$modelValue||e.$modelValue!=e.$modelValue&&t!=t||e.$$setModelValue(t),t})}(this)}function Un(e){this.$$options=e}function Ln(e,t){o(t,function(t,n){$(e[n])||(e[n]=t)})}function Hn(e,t){e.prop("selected",t),e.attr("selected",t)}var qn,Bn,_n,Wn,zn={objectMaxDepth:5},Yn=/^\/(.+)\/([a-z]*)$/,Gn=Object.prototype.hasOwnProperty,Kn=function(e){return w(e)?e.toLowerCase():e},Jn=function(e){return w(e)?e.toUpperCase():e},Xn=[].slice,Qn=[].splice,Zn=[].push,ei=Object.prototype.toString,ti=Object.getPrototypeOf,ni=i("ng"),ii=e.angular||(e.angular={}),ri=0;qn=e.document.documentMode;var oi=Number.isNaN||function(e){return e!=e};f.$inject=[],h.$inject=[];var ai,si,li=Array.isArray,ui=/^\[object (?:Uint8|Uint8Clamped|Uint16|Uint32|Int8|Int16|Int32|Float32|Float64)Array]$/,ci=function(e){return w(e)?e.trim():e},pi=function(e){return e.replace(/([-()[\]{}+?*.$^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")},di=function(){if(!$(di.rules)){var t=e.document.querySelector("[ng-csp]")||e.document.querySelector("[data-ng-csp]");if(t){var n=t.getAttribute("ng-csp")||t.getAttribute("data-ng-csp");di.rules={noUnsafeEval:!n||-1!==n.indexOf("no-unsafe-eval"),noInlineStyle:!n||-1!==n.indexOf("no-inline-style")}}else{t=di;try{new Function(""),n=!1}catch(e){n=!0}t.rules={noUnsafeEval:n,noInlineStyle:!1}}}return di.rules},fi=function(){if($(fi.name_))return fi.name_;var t,n,i,r,o=mi.length;for(n=0;n<o;++n)if(i=mi[n],t=e.document.querySelector("["+i.replace(":","\\:")+"jq]")){r=t.getAttribute(i+"jq");break}return fi.name_=r},hi=/:/g,mi=["ng-","data-ng-","ng:","x-ng-"],gi=(ai=e.document,!(si=ai.currentScript)||(si instanceof e.HTMLScriptElement||si instanceof e.SVGScriptElement)&&[(si=si.attributes).getNamedItem("src"),si.getNamedItem("href"),si.getNamedItem("xlink:href")].every(function(e){if(!e)return!0;if(!e.value)return!1;var t=ai.createElement("a");if(t.href=e.value,ai.location.origin===t.origin)return!0;switch(t.protocol){case"http:":case"https:":case"ftp:":case"blob:":case"file:":case"data:":return!0;default:return!1}})),vi=/[A-Z]/g,$i=!1,bi=3,yi={full:"1.6.10",major:1,minor:6,dot:10,codeName:"crystalline-persuasion"};he.expando="ng339";var wi=he.cache={},xi=1;he._data=function(e){return this.cache[e[this.expando]]||{}};var ki=/-([a-z])/g,Ci=/^-ms-/,Ti={mouseleave:"mouseout",mouseenter:"mouseover"},Di=i("jqLite"),Si=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,Ei=/<|&#?\w+;/,Oi=/<([\w:-]+)/,Ai=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,Mi={option:[1,'<select multiple="multiple">',"</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Mi.optgroup=Mi.option,Mi.tbody=Mi.tfoot=Mi.colgroup=Mi.caption=Mi.thead,Mi.th=Mi.td;var Ni=e.Node.prototype.contains||function(e){return!!(16&this.compareDocumentPosition(e))},Ii=he.prototype={ready:Oe,toString:function(){var e=[];return o(this,function(t){e.push(""+t)}),"["+e.join(", ")+"]"},eq:function(e){return Bn(0<=e?this[e]:this[this.length+e])},length:0,push:Zn,sort:[].sort,splice:[].splice},Pi={};o("multiple selected checked disabled readOnly required open".split(" "),function(e){Pi[Kn(e)]=e});var ji={};o("input select option textarea button form details".split(" "),function(e){ji[e]=!0});var Vi={ngMinlength:"minlength",ngMaxlength:"maxlength",ngMin:"min",ngMax:"max",ngPattern:"pattern",ngStep:"step"};o({data:ye,removeData:$e,hasData:function(e){for(var t in wi[e.ng339])return!0;return!1},cleanData:function(e){for(var t=0,n=e.length;t<n;t++)$e(e[t])}},function(e,t){he[t]=e}),o({data:ye,inheritedData:De,scope:function(e){return Bn.data(e,"$scope")||De(e.parentNode||e,["$isolateScope","$scope"])},isolateScope:function(e){return Bn.data(e,"$isolateScope")||Bn.data(e,"$isolateScopeNoTemplate")},controller:Te,injector:function(e){return De(e,"$injector")},removeAttr:function(e,t){e.removeAttribute(t)},hasClass:we,css:function(e,t,n){if(t=pe(t.replace(Ci,"ms-")),!$(n))return e.style[t];e.style[t]=n},attr:function(e,t,n){if((i=e.nodeType)!==bi&&2!==i&&8!==i&&e.getAttribute){var i=Kn(t),r=Pi[i];if(!$(n))return e=e.getAttribute(t),r&&null!==e&&(e=i),null===e?void 0:e;null===n||!1===n&&r?e.removeAttribute(t):e.setAttribute(t,r?i:n)}},prop:function(e,t,n){if(!$(n))return e[t];e[t]=n},text:function(){function e(e,t){if(v(t)){var n=e.nodeType;return 1===n||n===bi?e.textContent:""}e.textContent=t}return e.$dv="",e}(),val:function(e,t){if(v(t)){if(e.multiple&&"select"===M(e)){var n=[];return o(e.options,function(e){e.selected&&n.push(e.value||e.text)}),n}return e.value}e.value=t},html:function(e,t){if(v(t))return e.innerHTML;ge(e,!0),e.innerHTML=t},empty:Se},function(e,t){he.prototype[t]=function(t,n){var i,r,o=this.length;if(e!==Se&&v(2===e.length&&e!==we&&e!==Te?t:n)){if(b(t)){for(i=0;i<o;i++)if(e===ye)e(this[i],t);else for(r in t)e(this[i],r,t[r]);return this}for(o=v(i=e.$dv)?Math.min(o,1):o,r=0;r<o;r++){var a=e(this[r],t,n);i=i?i+a:a}return i}for(i=0;i<o;i++)e(this[i],t,n);return this}}),o({removeData:$e,on:function(e,t,n,i){if($(i))throw Di("onargs");if(de(e)){var r=(i=be(e,!0)).events,o=i.handle;o||(o=i.handle=function(e,t){var n=function(n,i){n.isDefaultPrevented=function(){return n.defaultPrevented};var r=t[i||n.type],o=r?r.length:0;if(o){if(v(n.immediatePropagationStopped)){var a=n.stopImmediatePropagation;n.stopImmediatePropagation=function(){n.immediatePropagationStopped=!0,n.stopPropagation&&n.stopPropagation(),a&&a.call(n)}}n.isImmediatePropagationStopped=function(){return!0===n.immediatePropagationStopped};var s=r.specialHandlerWrapper||Me;1<o&&(r=le(r));for(var l=0;l<o;l++)n.isImmediatePropagationStopped()||s(e,n,r[l])}};return n.elem=e,n}(e,r));for(var a=(i=0<=t.indexOf(" ")?t.split(" "):[t]).length,s=function(t,i,a){var s=r[t];s||((s=r[t]=[]).specialHandlerWrapper=i,"$destroy"===t||a||e.addEventListener(t,o)),s.push(n)};a--;)t=i[a],Ti[t]?(s(Ti[t],Ne),s(t,void 0,!0)):s(t)}},off:ve,one:function(e,t,n){(e=Bn(e)).on(t,function i(){e.off(t,n),e.off(t,i)}),e.on(t,n)},replaceWith:function(e,t){var n,i=e.parentNode;ge(e),o(new he(t),function(t){n?i.insertBefore(t,n.nextSibling):i.replaceChild(t,e),n=t})},children:function(e){var t=[];return o(e.childNodes,function(e){1===e.nodeType&&t.push(e)}),t},contents:function(e){return e.contentDocument||e.childNodes||[]},append:function(e,t){if(1===(n=e.nodeType)||11===n)for(var n=0,i=(t=new he(t)).length;n<i;n++)e.appendChild(t[n])},prepend:function(e,t){if(1===e.nodeType){var n=e.firstChild;o(new he(t),function(t){e.insertBefore(t,n)})}},wrap:function(e,t){var n=Bn(t).eq(0).clone()[0],i=e.parentNode;i&&i.replaceChild(n,e),n.appendChild(e)},remove:Ee,detach:function(e){Ee(e,!0)},after:function(e,t){var n=e,i=e.parentNode;if(i)for(var r=0,o=(t=new he(t)).length;r<o;r++){var a=t[r];i.insertBefore(a,n.nextSibling),n=a}},addClass:ke,removeClass:xe,toggleClass:function(e,t,n){t&&o(t.split(" "),function(t){var i=n;v(i)&&(i=!we(e,t)),(i?ke:xe)(e,t)})},parent:function(e){return(e=e.parentNode)&&11!==e.nodeType?e:null},next:function(e){return e.nextElementSibling},find:function(e,t){return e.getElementsByTagName?e.getElementsByTagName(t):[]},clone:me,triggerHandler:function(e,t,n){var i,r,a=t.type||t,s=be(e);(s=(s=s&&s.events)&&s[a])&&(i={preventDefault:function(){this.defaultPrevented=!0},isDefaultPrevented:function(){return!0===this.defaultPrevented},stopImmediatePropagation:function(){this.immediatePropagationStopped=!0},isImmediatePropagationStopped:function(){return!0===this.immediatePropagationStopped},stopPropagation:f,type:a,target:e},t.type&&(i=u(i,t)),t=le(s),r=n?[i].concat(n):[i],o(t,function(t){i.isImmediatePropagationStopped()||t.apply(e,r)}))}},function(e,t){he.prototype[t]=function(t,n,i){for(var r,o=0,a=this.length;o<a;o++)v(r)?$(r=e(this[o],t,n,i))&&(r=Bn(r)):Ce(r,e(this[o],t,n,i));return $(r)?r:this}}),he.prototype.bind=he.prototype.on,he.prototype.unbind=he.prototype.off;var Ri=Object.create(null);je.prototype={_idx:function(e){return e===this._lastKey?this._lastIndex:(this._lastKey=e,this._lastIndex=this._keys.indexOf(e))},_transformKey:function(e){return oi(e)?Ri:e},get:function(e){if(e=this._transformKey(e),-1!==(e=this._idx(e)))return this._values[e]},set:function(e,t){e=this._transformKey(e);var n=this._idx(e);-1===n&&(n=this._lastIndex=this._keys.length),this._keys[n]=e,this._values[n]=t},delete:function(e){return e=this._transformKey(e),-1!==(e=this._idx(e))&&(this._keys.splice(e,1),this._values.splice(e,1),this._lastKey=NaN,this._lastIndex=-1,!0)}};var Fi=je,Ui=[function(){this.$get=[function(){return Fi}]}],Li=/^([^(]+?)=>/,Hi=/^[^(]*\(\s*([^)]*)\)/m,qi=/,/,Bi=/^\s*(_?)(\S+?)\1\s*$/,_i=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm,Wi=i("$injector");Re.$$annotate=function(e,t,n){var i;if("function"==typeof e){if(!(i=e.$inject)){if(i=[],e.length){if(t)throw w(n)&&n||(n=e.name||function(e){return(e=Ve(e))?"function("+(e[1]||"").replace(/[\s\r\n]+/," ")+")":"fn"}(e)),Wi("strictdi",n);o((t=Ve(e))[1].split(qi),function(e){e.replace(Bi,function(e,t,n){i.push(n)})})}e.$inject=i}}else li(e)?(ne(e[t=e.length-1],"fn"),i=e.slice(0,t)):ne(e,"fn",!0);return i};var zi=i("$animate"),Yi=function(){this.$get=f},Gi=function(){var e=new Fi,t=[];this.$get=["$$AnimateRunner","$rootScope",function(n,i){function r(e,t,n){var i=!1;return t&&o(t=w(t)?t.split(" "):li(t)?t:[],function(t){t&&(i=!0,e[t]=n)}),i}function a(){o(t,function(t){var n=e.get(t);if(n){var i=function(e){w(e)&&(e=e.split(" "));var t=ae();return o(e,function(e){e.length&&(t[e]=!0)}),t}(t.attr("class")),r="",a="";o(n,function(e,t){e!==!!i[t]&&(e?r+=(r.length?" ":"")+t:a+=(a.length?" ":"")+t)}),o(t,function(e){r&&ke(e,r),a&&xe(e,a)}),e.delete(t)}}),t.length=0}return{enabled:f,on:f,off:f,pin:f,push:function(o,s,l,u){return u&&u(),(l=l||{}).from&&o.css(l.from),l.to&&o.css(l.to),(l.addClass||l.removeClass)&&(s=l.addClass,u=l.removeClass,s=r(l=e.get(o)||{},s,!0),u=r(l,u,!1),(s||u)&&(e.set(o,l),t.push(o),1===t.length&&i.$$postDigest(a))),(o=new n).complete(),o}}}]},Ki=["$provide",function(e){var t=this,n=null,i=null;this.$$registeredAnimations=Object.create(null),this.register=function(n,i){if(n&&"."!==n.charAt(0))throw zi("notcsel",n);var r=n+"-animation";t.$$registeredAnimations[n.substr(1)]=r,e.factory(r,i)},this.customFilter=function(e){return 1===arguments.length&&(i=T(e)?e:null),i},this.classNameFilter=function(e){if(1===arguments.length&&(n=e instanceof RegExp?e:null)&&/[(\s|\/)]ng-animate[(\s|\/)]/.test(n.toString()))throw n=null,zi("nongcls","ng-animate");return n},this.$get=["$$animateQueue",function(e){function t(e,t,n){if(n){var i;e:{for(i=0;i<n.length;i++){var r=n[i];if(1===r.nodeType){i=r;break e}}i=void 0}!i||i.parentNode||i.previousElementSibling||(n=null)}n?n.after(e):t.prepend(e)}return{on:e.on,off:e.off,pin:e.pin,enabled:e.enabled,cancel:function(e){e.end&&e.end()},enter:function(n,i,r,o){return i=i&&Bn(i),r=r&&Bn(r),t(n,i=i||r.parent(),r),e.push(n,"enter",Le(o))},move:function(n,i,r,o){return i=i&&Bn(i),r=r&&Bn(r),t(n,i=i||r.parent(),r),e.push(n,"move",Le(o))},leave:function(t,n){return e.push(t,"leave",Le(n),function(){t.remove()})},addClass:function(t,n,i){return(i=Le(i)).addClass=Ue(i.addclass,n),e.push(t,"addClass",i)},removeClass:function(t,n,i){return(i=Le(i)).removeClass=Ue(i.removeClass,n),e.push(t,"removeClass",i)},setClass:function(t,n,i,r){return(r=Le(r)).addClass=Ue(r.addClass,n),r.removeClass=Ue(r.removeClass,i),e.push(t,"setClass",r)},animate:function(t,n,i,r,o){return(o=Le(o)).from=o.from?u(o.from,n):n,o.to=o.to?u(o.to,i):i,o.tempClasses=Ue(o.tempClasses,r||"ng-inline-animate"),e.push(t,"animate",o)}}}]}],Ji=function(){this.$get=["$$rAF",function(e){function t(t){n.push(t),1<n.length||e(function(){for(var e=0;e<n.length;e++)n[e]();n=[]})}var n=[];return function(){var e=!1;return t(function(){e=!0}),function(n){e?n():t(n)}}}]},Xi=function(){this.$get=["$q","$sniffer","$$animateAsyncRun","$$isDocumentHidden","$timeout",function(e,t,n,i,r){function a(e){this.setHost(e);var t=n();this._doneCallbacks=[],this._tick=function(e){i()?r(e,0,!1):t(e)},this._state=0}return a.chain=function(e,t){var n=0;!function i(){n===e.length?t(!0):e[n](function(e){!1===e?t(!1):(n++,i())})}()},a.all=function(e,t){function n(n){r=r&&n,++i===e.length&&t(r)}var i=0,r=!0;o(e,function(e){e.done(n)})},a.prototype={setHost:function(e){this.host=e||{}},done:function(e){2===this._state?e():this._doneCallbacks.push(e)},progress:f,getPromise:function(){if(!this.promise){var t=this;this.promise=e(function(e,n){t.done(function(t){!1===t?n():e()})})}return this.promise},then:function(e,t){return this.getPromise().then(e,t)},catch:function(e){return this.getPromise().catch(e)},finally:function(e){return this.getPromise().finally(e)},pause:function(){this.host.pause&&this.host.pause()},resume:function(){this.host.resume&&this.host.resume()},end:function(){this.host.end&&this.host.end(),this._resolve(!0)},cancel:function(){this.host.cancel&&this.host.cancel(),this._resolve(!1)},complete:function(e){var t=this;0===t._state&&(t._state=1,t._tick(function(){t._resolve(e)}))},_resolve:function(e){2!==this._state&&(o(this._doneCallbacks,function(t){t(e)}),this._doneCallbacks.length=0,this._state=2)}},a}]},Qi=function(){this.$get=["$$rAF","$q","$$AnimateRunner",function(e,t,n){return function(t,i){function r(){return e(function(){o.addClass&&(t.addClass(o.addClass),o.addClass=null),o.removeClass&&(t.removeClass(o.removeClass),o.removeClass=null),o.to&&(t.css(o.to),o.to=null),a||s.complete(),a=!0}),s}var o=i||{};o.$$prepared||(o=I(o)),o.cleanupStyles&&(o.from=o.to=null),o.from&&(t.css(o.from),o.from=null);var a,s=new n;return{start:r,end:r}}}]},Zi=i("$compile"),er=new function(){};We.$inject=["$provide","$$sanitizeUriProvider"],ze.prototype.isFirstChange=function(){return this.previousValue===er};var tr=/^((?:x|data)[:\-_])/i,nr=/[:\-_]+(.)/g,ir=i("$controller"),rr=/^(\S+)(\s+as\s+([\w$]+))?$/,or=function(){this.$get=["$document",function(e){return function(t){return t?!t.nodeType&&t instanceof Bn&&(t=t[0]):t=e[0].body,t.offsetWidth+1}}]},ar="application/json",sr={"Content-Type":ar+";charset=utf-8"},lr=/^\[|^\{(?!\{)/,ur={"[":/]$/,"{":/}$/},cr=/^\)]\}',?\n/,pr=i("$http"),dr=ii.$interpolateMinErr=i("$interpolate");dr.throwNoconcat=function(e){throw dr("noconcat",e)},dr.interr=function(e,t){return dr("interr",e,t.toString())};var fr=function(){this.$get=function(){var e=ii.callbacks,t={};return{createCallback:function(n){var i="angular.callbacks."+(n="_"+(e.$$counter++).toString(36)),r=function(e){var t=function(e){t.data=e,t.called=!0};return t.id=e,t}(n);return t[i]=e[n]=r,i},wasCalled:function(e){return t[e].called},getResponse:function(e){return t[e].data},removeCallback:function(n){delete e[t[n].id],delete t[n]}}}},hr=/^([^?#]*)(\?([^#]*))?(#(.*))?$/,mr={http:80,https:443,ftp:21},gr=i("$location"),vr=/^\s*[\\/]{2,}/,$r={$$absUrl:"",$$html5:!1,$$replace:!1,absUrl:kt("$$absUrl"),url:function(e){if(v(e))return this.$$url;var t=hr.exec(e);return(t[1]||""===e)&&this.path(decodeURIComponent(t[1])),(t[2]||t[1]||""===e)&&this.search(t[3]||""),this.hash(t[5]||""),this},protocol:kt("$$protocol"),host:kt("$$host"),port:kt("$$port"),path:Ct("$$path",function(e){return"/"===(e=null!==e?e.toString():"").charAt(0)?e:"/"+e}),search:function(e,t){switch(arguments.length){case 0:return this.$$search;case 1:if(w(e)||x(e))e=e.toString(),this.$$search=z(e);else{if(!b(e))throw gr("isrcharg");o(e=I(e,{}),function(t,n){null==t&&delete e[n]}),this.$$search=e}break;default:v(t)||null===t?delete this.$$search[e]:this.$$search[e]=t}return this.$$compose(),this},hash:Ct("$$hash",function(e){return null!==e?e.toString():""}),replace:function(){return this.$$replace=!0,this}};o([xt,wt,yt],function(e){e.prototype=Object.create($r),e.prototype.state=function(t){if(!arguments.length)return this.$$state;if(e!==yt||!this.$$html5)throw gr("nostate");return this.$$state=v(t)?null:t,this.$$urlUpdatedByLocation=!0,this}});var br=i("$parse"),yr={}.constructor.prototype.valueOf,wr=ae();o("+ - * / % === !== == != < > <= >= && || ! = |".split(" "),function(e){wr[e]=!0});var xr={n:"\n",f:"\f",r:"\r",t:"\t",v:"\v","'":"'",'"':'"'},kr=function(e){this.options=e};kr.prototype={constructor:kr,lex:function(e){for(this.text=e,this.index=0,this.tokens=[];this.index<this.text.length;)if('"'===(e=this.text.charAt(this.index))||"'"===e)this.readString(e);else if(this.isNumber(e)||"."===e&&this.isNumber(this.peek()))this.readNumber();else if(this.isIdentifierStart(this.peekMultichar()))this.readIdent();else if(this.is(e,"(){}[].,;:?"))this.tokens.push({index:this.index,text:e}),this.index++;else if(this.isWhitespace(e))this.index++;else{var t=e+this.peek(),n=t+this.peek(2),i=wr[t],r=wr[n];wr[e]||i||r?(e=r?n:i?t:e,this.tokens.push({index:this.index,text:e,operator:!0}),this.index+=e.length):this.throwError("Unexpected next character ",this.index,this.index+1)}return this.tokens},is:function(e,t){return-1!==t.indexOf(e)},peek:function(e){return e=e||1,this.index+e<this.text.length&&this.text.charAt(this.index+e)},isNumber:function(e){return"0"<=e&&"9">=e&&"string"==typeof e},isWhitespace:function(e){return" "===e||"\r"===e||"\t"===e||"\n"===e||"\v"===e||" "===e},isIdentifierStart:function(e){return this.options.isIdentifierStart?this.options.isIdentifierStart(e,this.codePointAt(e)):this.isValidIdentifierStart(e)},isValidIdentifierStart:function(e){return"a"<=e&&"z">=e||"A"<=e&&"Z">=e||"_"===e||"$"===e},isIdentifierContinue:function(e){return this.options.isIdentifierContinue?this.options.isIdentifierContinue(e,this.codePointAt(e)):this.isValidIdentifierContinue(e)},isValidIdentifierContinue:function(e,t){return this.isValidIdentifierStart(e,t)||this.isNumber(e)},codePointAt:function(e){return 1===e.length?e.charCodeAt(0):(e.charCodeAt(0)<<10)+e.charCodeAt(1)-56613888},peekMultichar:function(){var e=this.text.charAt(this.index),t=this.peek();if(!t)return e;var n=e.charCodeAt(0),i=t.charCodeAt(0);return 55296<=n&&56319>=n&&56320<=i&&57343>=i?e+t:e},isExpOperator:function(e){return"-"===e||"+"===e||this.isNumber(e)},throwError:function(e,t,n){throw n=n||this.index,t=$(t)?"s "+t+"-"+this.index+" ["+this.text.substring(t,n)+"]":" "+n,br("lexerr",e,t,this.text)},readNumber:function(){for(var e="",t=this.index;this.index<this.text.length;){var n=Kn(this.text.charAt(this.index));if("."===n||this.isNumber(n))e+=n;else{var i=this.peek();if("e"===n&&this.isExpOperator(i))e+=n;else if(this.isExpOperator(n)&&i&&this.isNumber(i)&&"e"===e.charAt(e.length-1))e+=n;else{if(!this.isExpOperator(n)||i&&this.isNumber(i)||"e"!==e.charAt(e.length-1))break;this.throwError("Invalid exponent")}}this.index++}this.tokens.push({index:t,text:e,constant:!0,value:Number(e)})},readIdent:function(){var e=this.index;for(this.index+=this.peekMultichar().length;this.index<this.text.length;){var t=this.peekMultichar();if(!this.isIdentifierContinue(t))break;this.index+=t.length}this.tokens.push({index:e,text:this.text.slice(e,this.index),identifier:!0})},readString:function(e){var t=this.index;this.index++;for(var n="",i=e,r=!1;this.index<this.text.length;){var o=this.text.charAt(this.index);i=i+o;if(r)"u"===o?((r=this.text.substring(this.index+1,this.index+5)).match(/[\da-f]{4}/i)||this.throwError("Invalid unicode escape [\\u"+r+"]"),this.index+=4,n+=String.fromCharCode(parseInt(r,16))):n+=xr[o]||o,r=!1;else if("\\"===o)r=!0;else{if(o===e)return this.index++,void this.tokens.push({index:t,text:i,constant:!0,value:n});n+=o}this.index++}this.throwError("Unterminated quote",t)}};var Cr=function(e,t){this.lexer=e,this.options=t};Cr.Program="Program",Cr.ExpressionStatement="ExpressionStatement",Cr.AssignmentExpression="AssignmentExpression",Cr.ConditionalExpression="ConditionalExpression",Cr.LogicalExpression="LogicalExpression",Cr.BinaryExpression="BinaryExpression",Cr.UnaryExpression="UnaryExpression",Cr.CallExpression="CallExpression",Cr.MemberExpression="MemberExpression",Cr.Identifier="Identifier",Cr.Literal="Literal",Cr.ArrayExpression="ArrayExpression",Cr.Property="Property",Cr.ObjectExpression="ObjectExpression",Cr.ThisExpression="ThisExpression",Cr.LocalsExpression="LocalsExpression",Cr.NGValueParameter="NGValueParameter",Cr.prototype={ast:function(e){return this.text=e,this.tokens=this.lexer.lex(e),e=this.program(),0!==this.tokens.length&&this.throwError("is an unexpected token",this.tokens[0]),e},program:function(){for(var e=[];;)if(0<this.tokens.length&&!this.peek("}",")",";","]")&&e.push(this.expressionStatement()),!this.expect(";"))return{type:Cr.Program,body:e}},expressionStatement:function(){return{type:Cr.ExpressionStatement,expression:this.filterChain()}},filterChain:function(){for(var e=this.expression();this.expect("|");)e=this.filter(e);return e},expression:function(){return this.assignment()},assignment:function(){var e=this.ternary();if(this.expect("=")){if(!Nt(e))throw br("lval");e={type:Cr.AssignmentExpression,left:e,right:this.assignment(),operator:"="}}return e},ternary:function(){var e,t,n=this.logicalOR();return this.expect("?")&&(e=this.expression(),this.consume(":"))?(t=this.expression(),{type:Cr.ConditionalExpression,test:n,alternate:e,consequent:t}):n},logicalOR:function(){for(var e=this.logicalAND();this.expect("||");)e={type:Cr.LogicalExpression,operator:"||",left:e,right:this.logicalAND()};return e},logicalAND:function(){for(var e=this.equality();this.expect("&&");)e={type:Cr.LogicalExpression,operator:"&&",left:e,right:this.equality()};return e},equality:function(){for(var e,t=this.relational();e=this.expect("==","!=","===","!==");)t={type:Cr.BinaryExpression,operator:e.text,left:t,right:this.relational()};return t},relational:function(){for(var e,t=this.additive();e=this.expect("<",">","<=",">=");)t={type:Cr.BinaryExpression,operator:e.text,left:t,right:this.additive()};return t},additive:function(){for(var e,t=this.multiplicative();e=this.expect("+","-");)t={type:Cr.BinaryExpression,operator:e.text,left:t,right:this.multiplicative()};return t},multiplicative:function(){for(var e,t=this.unary();e=this.expect("*","/","%");)t={type:Cr.BinaryExpression,operator:e.text,left:t,right:this.unary()};return t},unary:function(){var e;return(e=this.expect("+","-","!"))?{type:Cr.UnaryExpression,operator:e.text,prefix:!0,argument:this.unary()}:this.primary()},primary:function(){var e,t;for(this.expect("(")?(e=this.filterChain(),this.consume(")")):this.expect("[")?e=this.arrayDeclaration():this.expect("{")?e=this.object():this.selfReferential.hasOwnProperty(this.peek().text)?e=I(this.selfReferential[this.consume().text]):this.options.literals.hasOwnProperty(this.peek().text)?e={type:Cr.Literal,value:this.options.literals[this.consume().text]}:this.peek().identifier?e=this.identifier():this.peek().constant?e=this.constant():this.throwError("not a primary expression",this.peek());t=this.expect("(","[",".");)"("===t.text?(e={type:Cr.CallExpression,callee:e,arguments:this.parseArguments()},this.consume(")")):"["===t.text?(e={type:Cr.MemberExpression,object:e,property:this.expression(),computed:!0},this.consume("]")):"."===t.text?e={type:Cr.MemberExpression,object:e,property:this.identifier(),computed:!1}:this.throwError("IMPOSSIBLE");return e},filter:function(e){e=[e];for(var t={type:Cr.CallExpression,callee:this.identifier(),arguments:e,filter:!0};this.expect(":");)e.push(this.expression());return t},parseArguments:function(){var e=[];if(")"!==this.peekToken().text)do{e.push(this.filterChain())}while(this.expect(","));return e},identifier:function(){var e=this.consume();return e.identifier||this.throwError("is not a valid identifier",e),{type:Cr.Identifier,name:e.text}},constant:function(){return{type:Cr.Literal,value:this.consume().value}},arrayDeclaration:function(){var e=[];if("]"!==this.peekToken().text)do{if(this.peek("]"))break;e.push(this.expression())}while(this.expect(","));return this.consume("]"),{type:Cr.ArrayExpression,elements:e}},object:function(){var e,t=[];if("}"!==this.peekToken().text)do{if(this.peek("}"))break;e={type:Cr.Property,kind:"init"},this.peek().constant?(e.key=this.constant(),e.computed=!1,this.consume(":"),e.value=this.expression()):this.peek().identifier?(e.key=this.identifier(),e.computed=!1,this.peek(":")?(this.consume(":"),e.value=this.expression()):e.value=e.key):this.peek("[")?(this.consume("["),e.key=this.expression(),this.consume("]"),e.computed=!0,this.consume(":"),e.value=this.expression()):this.throwError("invalid key",this.peek()),t.push(e)}while(this.expect(","));return this.consume("}"),{type:Cr.ObjectExpression,properties:t}},throwError:function(e,t){throw br("syntax",t.text,e,t.index+1,this.text,this.text.substring(t.index))},consume:function(e){if(0===this.tokens.length)throw br("ueoe",this.text);var t=this.expect(e);return t||this.throwError("is unexpected, expecting ["+e+"]",this.peek()),t},peekToken:function(){if(0===this.tokens.length)throw br("ueoe",this.text);return this.tokens[0]},peek:function(e,t,n,i){return this.peekAhead(0,e,t,n,i)},peekAhead:function(e,t,n,i,r){if(this.tokens.length>e){var o=(e=this.tokens[e]).text;if(o===t||o===n||o===i||o===r||!(t||n||i||r))return e}return!1},expect:function(e,t,n,i){return!!(e=this.peek(e,t,n,i))&&(this.tokens.shift(),e)},selfReferential:{this:{type:Cr.ThisExpression},$locals:{type:Cr.LocalsExpression}}};var Tr=2;Pt.prototype={compile:function(e){var t=this;this.state={nextId:0,filters:{},fn:{vars:[],body:[],own:{}},assign:{vars:[],body:[],own:{}},inputs:[]},At(e,t.$filter);var n,i="";return this.stage="assign",(n=It(e))&&(this.state.computing="assign",i=this.nextId(),this.recurse(n,i),this.return_(i),i="fn.assign="+this.generateFunction("assign","s,v,l")),n=Mt(e.body),t.stage="inputs",o(n,function(e,n){var i="fn"+n;t.state[i]={vars:[],body:[],own:{}},t.state.computing=i;var r=t.nextId();t.recurse(e,r),t.return_(r),t.state.inputs.push({name:i,isPure:e.isPure}),e.watchId=n}),this.state.computing="fn",this.stage="main",this.recurse(e),e='"'+this.USE+" "+this.STRICT+'";\n'+this.filterPrefix()+"var fn="+this.generateFunction("fn","s,l,a,i")+i+this.watchFns()+"return fn;",e=new Function("$filter","getStringValue","ifDefined","plus",e)(this.$filter,St,Et,Ot),this.state=this.stage=void 0,e},USE:"use",STRICT:"strict",watchFns:function(){var e=[],t=this.state.inputs,n=this;return o(t,function(t){e.push("var "+t.name+"="+n.generateFunction(t.name,"s")),t.isPure&&e.push(t.name,".isPure="+JSON.stringify(t.isPure)+";")}),t.length&&e.push("fn.inputs=["+t.map(function(e){return e.name}).join(",")+"];"),e.join("")},generateFunction:function(e,t){return"function("+t+"){"+this.varsPrefix(e)+this.body(e)+"};"},filterPrefix:function(){var e=[],t=this;return o(this.state.filters,function(n,i){e.push(n+"=$filter("+t.escape(i)+")")}),e.length?"var "+e.join(",")+";":""},varsPrefix:function(e){return this.state[e].vars.length?"var "+this.state[e].vars.join(",")+";":""},body:function(e){return this.state[e].body.join("")},recurse:function(e,t,n,i,r,a){var s,l,u,c,p,d=this;if(i=i||f,!a&&$(e.watchId))t=t||this.nextId(),this.if_("i",this.lazyAssign(t,this.computedMember("i",e.watchId)),this.lazyRecurse(e,t,n,i,r,!0));else switch(e.type){case Cr.Program:o(e.body,function(t,n){d.recurse(t.expression,void 0,void 0,function(e){l=e}),n!==e.body.length-1?d.current().body.push(l,";"):d.return_(l)});break;case Cr.Literal:c=this.escape(e.value),this.assign(t,c),i(t||c);break;case Cr.UnaryExpression:this.recurse(e.argument,void 0,void 0,function(e){l=e}),c=e.operator+"("+this.ifDefined(l,0)+")",this.assign(t,c),i(c);break;case Cr.BinaryExpression:this.recurse(e.left,void 0,void 0,function(e){s=e}),this.recurse(e.right,void 0,void 0,function(e){l=e}),c="+"===e.operator?this.plus(s,l):"-"===e.operator?this.ifDefined(s,0)+e.operator+this.ifDefined(l,0):"("+s+")"+e.operator+"("+l+")",this.assign(t,c),i(c);break;case Cr.LogicalExpression:t=t||this.nextId(),d.recurse(e.left,t),d.if_("&&"===e.operator?t:d.not(t),d.lazyRecurse(e.right,t)),i(t);break;case Cr.ConditionalExpression:t=t||this.nextId(),d.recurse(e.test,t),d.if_(t,d.lazyRecurse(e.alternate,t),d.lazyRecurse(e.consequent,t)),i(t);break;case Cr.Identifier:t=t||this.nextId(),n&&(n.context="inputs"===d.stage?"s":this.assign(this.nextId(),this.getHasOwnProperty("l",e.name)+"?l:s"),n.computed=!1,n.name=e.name),d.if_("inputs"===d.stage||d.not(d.getHasOwnProperty("l",e.name)),function(){d.if_("inputs"===d.stage||"s",function(){r&&1!==r&&d.if_(d.isNull(d.nonComputedMember("s",e.name)),d.lazyAssign(d.nonComputedMember("s",e.name),"{}")),d.assign(t,d.nonComputedMember("s",e.name))})},t&&d.lazyAssign(t,d.nonComputedMember("l",e.name))),i(t);break;case Cr.MemberExpression:s=n&&(n.context=this.nextId())||this.nextId(),t=t||this.nextId(),d.recurse(e.object,s,void 0,function(){d.if_(d.notNull(s),function(){e.computed?(l=d.nextId(),d.recurse(e.property,l),d.getStringValue(l),r&&1!==r&&d.if_(d.not(d.computedMember(s,l)),d.lazyAssign(d.computedMember(s,l),"{}")),c=d.computedMember(s,l),d.assign(t,c),n&&(n.computed=!0,n.name=l)):(r&&1!==r&&d.if_(d.isNull(d.nonComputedMember(s,e.property.name)),d.lazyAssign(d.nonComputedMember(s,e.property.name),"{}")),c=d.nonComputedMember(s,e.property.name),d.assign(t,c),n&&(n.computed=!1,n.name=e.property.name))},function(){d.assign(t,"undefined")}),i(t)},!!r);break;case Cr.CallExpression:t=t||this.nextId(),e.filter?(l=d.filter(e.callee.name),u=[],o(e.arguments,function(e){var t=d.nextId();d.recurse(e,t),u.push(t)}),c=l+"("+u.join(",")+")",d.assign(t,c),i(t)):(l=d.nextId(),s={},u=[],d.recurse(e.callee,l,s,function(){d.if_(d.notNull(l),function(){o(e.arguments,function(t){d.recurse(t,e.constant?void 0:d.nextId(),void 0,function(e){u.push(e)})}),c=s.name?d.member(s.context,s.name,s.computed)+"("+u.join(",")+")":l+"("+u.join(",")+")",d.assign(t,c)},function(){d.assign(t,"undefined")}),i(t)}));break;case Cr.AssignmentExpression:l=this.nextId(),s={},this.recurse(e.left,void 0,s,function(){d.if_(d.notNull(s.context),function(){d.recurse(e.right,l),c=d.member(s.context,s.name,s.computed)+e.operator+l,d.assign(t,c),i(t||c)})},1);break;case Cr.ArrayExpression:u=[],o(e.elements,function(t){d.recurse(t,e.constant?void 0:d.nextId(),void 0,function(e){u.push(e)})}),c="["+u.join(",")+"]",this.assign(t,c),i(t||c);break;case Cr.ObjectExpression:u=[],p=!1,o(e.properties,function(e){e.computed&&(p=!0)}),p?(t=t||this.nextId(),this.assign(t,"{}"),o(e.properties,function(e){e.computed?(s=d.nextId(),d.recurse(e.key,s)):s=e.key.type===Cr.Identifier?e.key.name:""+e.key.value,l=d.nextId(),d.recurse(e.value,l),d.assign(d.member(t,s,e.computed),l)})):(o(e.properties,function(t){d.recurse(t.value,e.constant?void 0:d.nextId(),void 0,function(e){u.push(d.escape(t.key.type===Cr.Identifier?t.key.name:""+t.key.value)+":"+e)})}),c="{"+u.join(",")+"}",this.assign(t,c)),i(t||c);break;case Cr.ThisExpression:this.assign(t,"s"),i(t||"s");break;case Cr.LocalsExpression:this.assign(t,"l"),i(t||"l");break;case Cr.NGValueParameter:this.assign(t,"v"),i(t||"v")}},getHasOwnProperty:function(e,t){var n=e+"."+t,i=this.current().own;return i.hasOwnProperty(n)||(i[n]=this.nextId(!1,e+"&&("+this.escape(t)+" in "+e+")")),i[n]},assign:function(e,t){if(e)return this.current().body.push(e,"=",t,";"),e},filter:function(e){return this.state.filters.hasOwnProperty(e)||(this.state.filters[e]=this.nextId(!0)),this.state.filters[e]},ifDefined:function(e,t){return"ifDefined("+e+","+this.escape(t)+")"},plus:function(e,t){return"plus("+e+","+t+")"},return_:function(e){this.current().body.push("return ",e,";")},if_:function(e,t,n){if(!0===e)t();else{var i=this.current().body;i.push("if(",e,"){"),t(),i.push("}"),n&&(i.push("else{"),n(),i.push("}"))}},not:function(e){return"!("+e+")"},isNull:function(e){return e+"==null"},notNull:function(e){return e+"!=null"},nonComputedMember:function(e,t){return/^[$_a-zA-Z][$_a-zA-Z0-9]*$/.test(t)?e+"."+t:e+'["'+t.replace(/[^$_a-zA-Z0-9]/g,this.stringEscapeFn)+'"]'},computedMember:function(e,t){return e+"["+t+"]"},member:function(e,t,n){return n?this.computedMember(e,t):this.nonComputedMember(e,t)},getStringValue:function(e){this.assign(e,"getStringValue("+e+")")},lazyRecurse:function(e,t,n,i,r,o){var a=this;return function(){a.recurse(e,t,n,i,r,o)}},lazyAssign:function(e,t){var n=this;return function(){n.assign(e,t)}},stringEscapeRegex:/[^ a-zA-Z0-9]/g,stringEscapeFn:function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)},escape:function(e){if(w(e))return"'"+e.replace(this.stringEscapeRegex,this.stringEscapeFn)+"'";if(x(e))return e.toString();if(!0===e)return"true";if(!1===e)return"false";if(null===e)return"null";if(void 0===e)return"undefined";throw br("esc")},nextId:function(e,t){var n="v"+this.state.nextId++;return e||this.current().vars.push(n+(t?"="+t:"")),n},current:function(){return this.state[this.state.computing]}},jt.prototype={compile:function(e){var t,n,i,r=this;At(e,r.$filter),(t=It(e))&&(n=this.recurse(t)),(t=Mt(e.body))&&(i=[],o(t,function(e,t){var n=r.recurse(e);n.isPure=e.isPure,e.input=n,i.push(n),e.watchId=t}));var a=[];return o(e.body,function(e){a.push(r.recurse(e.expression))}),e=0===e.body.length?f:1===e.body.length?a[0]:function(e,t){var n;return o(a,function(i){n=i(e,t)}),n},n&&(e.assign=function(e,t,i){return n(e,i,t)}),i&&(e.inputs=i),e},recurse:function(e,t,n){var i,r,a,s=this;if(e.input)return this.inputs(e.input,e.watchId);switch(e.type){case Cr.Literal:return this.value(e.value,t);case Cr.UnaryExpression:return r=this.recurse(e.argument),this["unary"+e.operator](r,t);case Cr.BinaryExpression:case Cr.LogicalExpression:return i=this.recurse(e.left),r=this.recurse(e.right),this["binary"+e.operator](i,r,t);case Cr.ConditionalExpression:return this["ternary?:"](this.recurse(e.test),this.recurse(e.alternate),this.recurse(e.consequent),t);case Cr.Identifier:return s.identifier(e.name,t,n);case Cr.MemberExpression:return i=this.recurse(e.object,!1,!!n),e.computed||(r=e.property.name),e.computed&&(r=this.recurse(e.property)),e.computed?this.computedMember(i,r,t,n):this.nonComputedMember(i,r,t,n);case Cr.CallExpression:return a=[],o(e.arguments,function(e){a.push(s.recurse(e))}),e.filter&&(r=this.$filter(e.callee.name)),e.filter||(r=this.recurse(e.callee,!0)),e.filter?function(e,n,i,o){for(var s=[],l=0;l<a.length;++l)s.push(a[l](e,n,i,o));return e=r.apply(void 0,s,o),t?{context:void 0,name:void 0,value:e}:e}:function(e,n,i,o){var s,l=r(e,n,i,o);if(null!=l.value){s=[];for(var u=0;u<a.length;++u)s.push(a[u](e,n,i,o));s=l.value.apply(l.context,s)}return t?{value:s}:s};case Cr.AssignmentExpression:return i=this.recurse(e.left,!0,1),r=this.recurse(e.right),function(e,n,o,a){var s=i(e,n,o,a);return e=r(e,n,o,a),s.context[s.name]=e,t?{value:e}:e};case Cr.ArrayExpression:return a=[],o(e.elements,function(e){a.push(s.recurse(e))}),function(e,n,i,r){for(var o=[],s=0;s<a.length;++s)o.push(a[s](e,n,i,r));return t?{value:o}:o};case Cr.ObjectExpression:return a=[],o(e.properties,function(e){e.computed?a.push({key:s.recurse(e.key),computed:!0,value:s.recurse(e.value)}):a.push({key:e.key.type===Cr.Identifier?e.key.name:""+e.key.value,computed:!1,value:s.recurse(e.value)})}),function(e,n,i,r){for(var o={},s=0;s<a.length;++s)a[s].computed?o[a[s].key(e,n,i,r)]=a[s].value(e,n,i,r):o[a[s].key]=a[s].value(e,n,i,r);return t?{value:o}:o};case Cr.ThisExpression:return function(e){return t?{value:e}:e};case Cr.LocalsExpression:return function(e,n){return t?{value:n}:n};case Cr.NGValueParameter:return function(e,n,i){return t?{value:i}:i}}},"unary+":function(e,t){return function(n,i,r,o){return n=$(n=e(n,i,r,o))?+n:0,t?{value:n}:n}},"unary-":function(e,t){return function(n,i,r,o){return n=$(n=e(n,i,r,o))?-n:-0,t?{value:n}:n}},"unary!":function(e,t){return function(n,i,r,o){return n=!e(n,i,r,o),t?{value:n}:n}},"binary+":function(e,t,n){return function(i,r,o,a){var s=e(i,r,o,a);return s=Ot(s,i=t(i,r,o,a)),n?{value:s}:s}},"binary-":function(e,t,n){return function(i,r,o,a){var s=e(i,r,o,a);return i=t(i,r,o,a),s=($(s)?s:0)-($(i)?i:0),n?{value:s}:s}},"binary*":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)*t(i,r,o,a),n?{value:i}:i}},"binary/":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)/t(i,r,o,a),n?{value:i}:i}},"binary%":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)%t(i,r,o,a),n?{value:i}:i}},"binary===":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)===t(i,r,o,a),n?{value:i}:i}},"binary!==":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)!==t(i,r,o,a),n?{value:i}:i}},"binary==":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)==t(i,r,o,a),n?{value:i}:i}},"binary!=":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)!=t(i,r,o,a),n?{value:i}:i}},"binary<":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)<t(i,r,o,a),n?{value:i}:i}},"binary>":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)>t(i,r,o,a),n?{value:i}:i}},"binary<=":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)<=t(i,r,o,a),n?{value:i}:i}},"binary>=":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)>=t(i,r,o,a),n?{value:i}:i}},"binary&&":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)&&t(i,r,o,a),n?{value:i}:i}},"binary||":function(e,t,n){return function(i,r,o,a){return i=e(i,r,o,a)||t(i,r,o,a),n?{value:i}:i}},"ternary?:":function(e,t,n,i){return function(r,o,a,s){return r=e(r,o,a,s)?t(r,o,a,s):n(r,o,a,s),i?{value:r}:r}},value:function(e,t){return function(){return t?{context:void 0,name:void 0,value:e}:e}},identifier:function(e,t,n){return function(i,r,o,a){return i=r&&e in r?r:i,n&&1!==n&&i&&null==i[e]&&(i[e]={}),r=i?i[e]:void 0,t?{context:i,name:e,value:r}:r}},computedMember:function(e,t,n,i){return function(r,o,a,s){var l,u,c=e(r,o,a,s);return null!=c&&(l=t(r,o,a,s),l+="",i&&1!==i&&c&&!c[l]&&(c[l]={}),u=c[l]),n?{context:c,name:l,value:u}:u}},nonComputedMember:function(e,t,n,i){return function(r,o,a,s){return r=e(r,o,a,s),i&&1!==i&&r&&null==r[t]&&(r[t]={}),o=null!=r?r[t]:void 0,n?{context:r,name:t,value:o}:o}},inputs:function(e,t){return function(n,i,r,o){return o?o[t]:e(n,i,r)}}},Vt.prototype={constructor:Vt,parse:function(e){e=this.getAst(e);var t=this.astCompiler.compile(e.ast),n=e.ast;return t.literal=0===n.body.length||1===n.body.length&&(n.body[0].expression.type===Cr.Literal||n.body[0].expression.type===Cr.ArrayExpression||n.body[0].expression.type===Cr.ObjectExpression),t.constant=e.ast.constant,t.oneTime=e.oneTime,t},getAst:function(e){var t=!1;return":"===(e=e.trim()).charAt(0)&&":"===e.charAt(1)&&(t=!0,e=e.substring(2)),{ast:this.ast.ast(e),oneTime:t}}};var Dr=i("$sce"),Sr={HTML:"html",CSS:"css",URL:"url",RESOURCE_URL:"resourceUrl",JS:"js"},Er=/_([a-z])/g,Or=i("$compile"),Ar=e.document.createElement("a"),Mr=Qt(e.location.href);tn.$inject=["$document"],rn.$inject=["$provide"];var Nr=22,Ir=".",Pr="0";ln.$inject=["$locale"],un.$inject=["$locale"];var jr={yyyy:dn("FullYear",4,0,!1,!0),yy:dn("FullYear",2,0,!0,!0),y:dn("FullYear",1,0,!1,!0),MMMM:fn("Month"),MMM:fn("Month",!0),MM:dn("Month",2,1),M:dn("Month",1,1),LLLL:fn("Month",!1,!0),dd:dn("Date",2),d:dn("Date",1),HH:dn("Hours",2),H:dn("Hours",1),hh:dn("Hours",2,-12),h:dn("Hours",1,-12),mm:dn("Minutes",2),m:dn("Minutes",1),ss:dn("Seconds",2),s:dn("Seconds",1),sss:dn("Milliseconds",3),EEEE:fn("Day"),EEE:fn("Day",!0),a:function(e,t){return 12>e.getHours()?t.AMPMS[0]:t.AMPMS[1]},Z:function(e,t,n){return(0<=(e=-1*n)?"+":"")+(pn(Math[0<e?"floor":"ceil"](e/60),2)+pn(Math.abs(e%60),2))},ww:mn(2),w:mn(1),G:gn,GG:gn,GGG:gn,GGGG:function(e,t){return 0>=e.getFullYear()?t.ERANAMES[0]:t.ERANAMES[1]}},Vr=/((?:[^yMLdHhmsaZEwG']+)|(?:'(?:[^']|'')*')|(?:E+|y+|M+|L+|d+|H+|h+|m+|s+|a|Z|G+|w+))([\s\S]*)/,Rr=/^-?\d+$/;vn.$inject=["$locale"];var Fr=m(Kn),Ur=m(Jn);wn.$inject=["$parse"];var Lr=m({restrict:"E",compile:function(e,t){if(!t.href&&!t.xlinkHref)return function(e,t){if("a"===t[0].nodeName.toLowerCase()){var n="[object SVGAnimatedString]"===ei.call(t.prop("href"))?"xlink:href":"href";t.on("click",function(e){t.attr(n)||e.preventDefault()})}}}}),Hr={};o(Pi,function(e,t){function n(e,n,r){e.$watch(r[i],function(e){r.$set(t,!!e)})}if("multiple"!==e){var i=Ye("ng-"+t),r=n;"checked"===e&&(r=function(e,t,r){r.ngModel!==r[i]&&n(e,0,r)}),Hr[i]=function(){return{restrict:"A",priority:100,link:r}}}}),o(Vi,function(e,t){Hr[t]=function(){return{priority:100,link:function(e,n,i){"ngPattern"===t&&"/"===i.ngPattern.charAt(0)&&(n=i.ngPattern.match(Yn))?i.$set("ngPattern",new RegExp(n[1],n[2])):e.$watch(i[t],function(e){i.$set(t,e)})}}}}),o(["src","srcset","href"],function(e){var t=Ye("ng-"+e);Hr[t]=function(){return{priority:99,link:function(n,i,r){var o=e,a=e;"href"===e&&"[object SVGAnimatedString]"===ei.call(i.prop("href"))&&(a="xlinkHref",r.$attr[a]="xlink:href",o=null),r.$observe(t,function(t){t?(r.$set(a,t),qn&&o&&i.prop(o,r[a])):"href"===e&&r.$set(a,null)})}}}});var qr={$addControl:f,$$renameControl:function(e,t){e.$name=t},$removeControl:f,$setValidity:f,$setDirty:f,$setPristine:f,$setSubmitted:f};kn.$inject=["$element","$attrs","$scope","$animate","$interpolate"],kn.prototype={$rollbackViewValue:function(){o(this.$$controls,function(e){e.$rollbackViewValue()})},$commitViewValue:function(){o(this.$$controls,function(e){e.$commitViewValue()})},$addControl:function(e){ie(e.$name,"input"),this.$$controls.push(e),e.$name&&(this[e.$name]=e),e.$$parentForm=this},$$renameControl:function(e,t){var n=e.$name;this[n]===e&&delete this[n],this[t]=e,e.$name=t},$removeControl:function(e){e.$name&&this[e.$name]===e&&delete this[e.$name],o(this.$pending,function(t,n){this.$setValidity(n,null,e)},this),o(this.$error,function(t,n){this.$setValidity(n,null,e)},this),o(this.$$success,function(t,n){this.$setValidity(n,null,e)},this),N(this.$$controls,e),e.$$parentForm=qr},$setDirty:function(){this.$$animate.removeClass(this.$$element,To),this.$$animate.addClass(this.$$element,Do),this.$dirty=!0,this.$pristine=!1,this.$$parentForm.$setDirty()},$setPristine:function(){this.$$animate.setClass(this.$$element,To,Do+" ng-submitted"),this.$dirty=!1,this.$pristine=!0,this.$submitted=!1,o(this.$$controls,function(e){e.$setPristine()})},$setUntouched:function(){o(this.$$controls,function(e){e.$setUntouched()})},$setSubmitted:function(){this.$$animate.addClass(this.$$element,"ng-submitted"),this.$submitted=!0,this.$$parentForm.$setSubmitted()}},Tn({clazz:kn,set:function(e,t,n){var i=e[t];i?-1===i.indexOf(n)&&i.push(n):e[t]=[n]},unset:function(e,t,n){var i=e[t];i&&(N(i,n),0===i.length&&delete e[t])}});var Br=function(e){return["$timeout","$parse",function(t,n){function i(e){return""===e?n('this[""]').assign:n(e).assign||f}return{name:"form",restrict:e?"EAC":"E",require:["form","^^?form"],controller:kn,compile:function(n,r){n.addClass(To).addClass(ko);var o=r.name?"name":!(!e||!r.ngForm)&&"ngForm";return{pre:function(e,n,r,a){var s=a[0];if(!("action"in r)){var l=function(t){e.$apply(function(){s.$commitViewValue(),s.$setSubmitted()}),t.preventDefault()};n[0].addEventListener("submit",l),n.on("$destroy",function(){t(function(){n[0].removeEventListener("submit",l)},0,!1)})}(a[1]||s.$$parentForm).$addControl(s);var c=o?i(s.$name):f;o&&(c(e,s),r.$observe(o,function(t){s.$name!==t&&(c(e,void 0),s.$$parentForm.$$renameControl(s,t),(c=i(s.$name))(e,s))})),n.on("$destroy",function(){s.$$parentForm.$removeControl(s),c(e,void 0),u(s,qr)})}}}}}]},_r=Br(),Wr=Br(!0),zr=/^\d{4,}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+(?:[+-][0-2]\d:[0-5]\d|Z)$/,Yr=/^[a-z][a-z\d.+-]*:\/*(?:[^:@]+(?::[^@]+)?@)?(?:[^\s:/?#]+|\[[a-f\d:]+])(?::\d+)?(?:\/[^?#]*)?(?:\?[^#]*)?(?:#.*)?$/i,Gr=/^(?=.{1,254}$)(?=.{1,64}@)[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+(\.[-!#$%&'*+/0-9=?A-Z^_`a-z{|}~]+)*@[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?(\.[A-Za-z0-9]([A-Za-z0-9-]{0,61}[A-Za-z0-9])?)*$/,Kr=/^\s*(-|\+)?(\d+|(\d*(\.\d*)))([eE][+-]?\d+)?\s*$/,Jr=/^(\d{4,})-(\d{2})-(\d{2})$/,Xr=/^(\d{4,})-(\d\d)-(\d\d)T(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,Qr=/^(\d{4,})-W(\d\d)$/,Zr=/^(\d{4,})-(\d\d)$/,eo=/^(\d\d):(\d\d)(?::(\d\d)(\.\d{1,3})?)?$/,to=ae();o(["date","datetime-local","month","time","week"],function(e){to[e]=!0});var no={text:function(e,t,n,i,r,o){En(0,t,n,i,r,o),Sn(i)},date:An("date",Jr,On(Jr,["yyyy","MM","dd"]),"yyyy-MM-dd"),"datetime-local":An("datetimelocal",Xr,On(Xr,"yyyy MM dd HH mm ss sss".split(" ")),"yyyy-MM-ddTHH:mm:ss.sss"),time:An("time",eo,On(eo,["HH","mm","ss","sss"]),"HH:mm:ss.sss"),week:An("week",Qr,function(e,t){if(k(e))return e;if(w(e)){Qr.lastIndex=0;var n=Qr.exec(e);if(n){var i=+n[1],r=+n[2],o=n=0,a=0,s=0,l=hn(i);r=7*(r-1);return t&&(n=t.getHours(),o=t.getMinutes(),a=t.getSeconds(),s=t.getMilliseconds()),new Date(i,0,l.getDate()+r,n,o,a,s)}}return NaN},"yyyy-Www"),month:An("month",Zr,On(Zr,["yyyy","MM"]),"yyyy-MM"),number:function(e,t,n,i,r,o){var a,s,l;(Mn(0,t,0,i),Nn(i),En(0,t,n,i,r,o),($(n.min)||n.ngMin)&&(i.$validators.min=function(e){return i.$isEmpty(e)||v(a)||e>=a},n.$observe("min",function(e){a=In(e),i.$validate()})),($(n.max)||n.ngMax)&&(i.$validators.max=function(e){return i.$isEmpty(e)||v(s)||e<=s},n.$observe("max",function(e){s=In(e),i.$validate()})),$(n.step)||n.ngStep)&&(i.$validators.step=function(e,t){return i.$isEmpty(t)||v(l)||jn(t,a||0,l)},n.$observe("step",function(e){l=In(e),i.$validate()}))},url:function(e,t,n,i,r,o){En(0,t,n,i,r,o),Sn(i),i.$$parserName="url",i.$validators.url=function(e,t){var n=e||t;return i.$isEmpty(n)||Yr.test(n)}},email:function(e,t,n,i,r,o){En(0,t,n,i,r,o),Sn(i),i.$$parserName="email",i.$validators.email=function(e,t){var n=e||t;return i.$isEmpty(n)||Gr.test(n)}},radio:function(e,t,n,i){var r=!n.ngTrim||"false"!==ci(n.ngTrim);v(n.name)&&t.attr("name",++ri),t.on("click",function(e){var o;t[0].checked&&(o=n.value,r&&(o=ci(o)),i.$setViewValue(o,e&&e.type))}),i.$render=function(){var e=n.value;r&&(e=ci(e)),t[0].checked=e===i.$viewValue},n.$observe("value",i.$render)},range:function(e,t,n,i,r,o){function a(e,i){t.attr(e,n[e]),n.$observe(e,i)}Mn(0,t,0,i),Nn(i),En(0,t,n,i,r,o);var s=i.$$hasNativeValidators&&"range"===t[0].type,l=s?0:void 0,u=s?100:void 0,c=s?1:void 0,p=t[0].validity;e=$(n.min),r=$(n.max),o=$(n.step);var d=i.$render;i.$render=s&&$(p.rangeUnderflow)&&$(p.rangeOverflow)?function(){d(),i.$setViewValue(t.val())}:d,e&&(i.$validators.min=s?function(){return!0}:function(e,t){return i.$isEmpty(t)||v(l)||t>=l},a("min",function(e){l=In(e),oi(i.$modelValue)||(s?(e=t.val(),l>e&&(e=l,t.val(e)),i.$setViewValue(e)):i.$validate())})),r&&(i.$validators.max=s?function(){return!0}:function(e,t){return i.$isEmpty(t)||v(u)||t<=u},a("max",function(e){u=In(e),oi(i.$modelValue)||(s?(e=t.val(),u<e&&(t.val(u),e=u<l?l:u),i.$setViewValue(e)):i.$validate())})),o&&(i.$validators.step=s?function(){return!p.stepMismatch}:function(e,t){return i.$isEmpty(t)||v(c)||jn(t,l||0,c)},a("step",function(e){c=In(e),oi(i.$modelValue)||(s&&i.$viewValue!==t.val()?i.$setViewValue(t.val()):i.$validate())}))},checkbox:function(e,t,n,i,r,o,a,s){var l=Vn(s,e,"ngTrueValue",n.ngTrueValue,!0),u=Vn(s,e,"ngFalseValue",n.ngFalseValue,!1);t.on("click",function(e){i.$setViewValue(t[0].checked,e&&e.type)}),i.$render=function(){t[0].checked=i.$viewValue},i.$isEmpty=function(e){return!1===e},i.$formatters.push(function(e){return j(e,l)}),i.$parsers.push(function(e){return e?l:u})},hidden:f,button:f,submit:f,reset:f,file:f},io=["$browser","$sniffer","$filter","$parse",function(e,t,n,i){return{restrict:"E",require:["?ngModel"],link:{pre:function(r,o,a,s){s[0]&&(no[Kn(a.type)]||no.text)(r,o,a,s[0],t,e,n,i)}}}}],ro=/^(true|false|\d+)$/,oo=function(){function e(e,t,n){var i=$(n)?n:9===qn?"":null;e.prop("value",i),t.$set("value",n)}return{restrict:"A",priority:100,compile:function(t,n){return ro.test(n.ngValue)?function(t,n,i){e(n,i,t=t.$eval(i.ngValue))}:function(t,n,i){t.$watch(i.ngValue,function(t){e(n,i,t)})}}}},ao=["$compile",function(e){return{restrict:"AC",compile:function(t){return e.$$addBindingClass(t),function(t,n,i){e.$$addBindingInfo(n,i.ngBind),n=n[0],t.$watch(i.ngBind,function(e){n.textContent=se(e)})}}}}],so=["$interpolate","$compile",function(e,t){return{compile:function(n){return t.$$addBindingClass(n),function(n,i,r){n=e(i.attr(r.$attr.ngBindTemplate)),t.$$addBindingInfo(i,n.expressions),i=i[0],r.$observe("ngBindTemplate",function(e){i.textContent=v(e)?"":e})}}}}],lo=["$sce","$parse","$compile",function(e,t,n){return{restrict:"A",compile:function(i,r){var o=t(r.ngBindHtml),a=t(r.ngBindHtml,function(t){return e.valueOf(t)});return n.$$addBindingClass(i),function(t,i,r){n.$$addBindingInfo(i,r.ngBindHtml),t.$watch(a,function(){var n=o(t);i.html(e.getTrustedHtml(n)||"")})}}}}],uo=m({restrict:"A",require:"ngModel",link:function(e,t,n,i){i.$viewChangeListeners.push(function(){e.$eval(n.ngChange)})}}),co=Rn("",!0),po=Rn("Odd",0),fo=Rn("Even",1),ho=xn({compile:function(e,t){t.$set("ngCloak",void 0),e.removeClass("ng-cloak")}}),mo=[function(){return{restrict:"A",scope:!0,controller:"@",priority:500}}],go={},vo={blur:!0,focus:!0};o("click dblclick mousedown mouseup mouseover mouseout mousemove mouseenter mouseleave keydown keyup keypress submit focus blur copy cut paste".split(" "),function(e){var t=Ye("ng-"+e);go[t]=["$parse","$rootScope",function(n,i){return{restrict:"A",compile:function(r,o){var a=n(o[t]);return function(t,n){n.on(e,function(n){var r=function(){a(t,{$event:n})};vo[e]&&i.$$phase?t.$evalAsync(r):t.$apply(r)})}}}}]});var $o=["$animate","$compile",function(e,t){return{multiElement:!0,transclude:"element",priority:600,terminal:!0,restrict:"A",$$tlb:!0,link:function(n,i,r,o,a){var s,l,u;n.$watch(r.ngIf,function(n){n?l||a(function(n,o){l=o,n[n.length++]=t.$$createComment("end ngIf",r.ngIf),s={clone:n},e.enter(n,i.parent(),i)}):(u&&(u.remove(),u=null),l&&(l.$destroy(),l=null),s&&(u=oe(s.clone),e.leave(u).done(function(e){!1!==e&&(u=null)}),s=null))})}}}],bo=["$templateRequest","$anchorScroll","$animate",function(e,t,n){return{restrict:"ECA",priority:400,terminal:!0,transclude:"element",controller:ii.noop,compile:function(i,r){var o=r.ngInclude||r.src,a=r.onload||"",s=r.autoscroll;return function(i,r,l,u,c){var p,d,f,h=0,m=function(){d&&(d.remove(),d=null),p&&(p.$destroy(),p=null),f&&(n.leave(f).done(function(e){!1!==e&&(d=null)}),d=f,f=null)};i.$watch(o,function(o){var l=function(e){!1===e||!$(s)||s&&!i.$eval(s)||t()},d=++h;o?(e(o,!0).then(function(e){if(!i.$$destroyed&&d===h){var t=i.$new();u.template=e,e=c(t,function(e){m(),n.enter(e,null,r).done(l)}),f=e,(p=t).$emit("$includeContentLoaded",o),i.$eval(a)}},function(){i.$$destroyed||d!==h||(m(),i.$emit("$includeContentError",o))}),i.$emit("$includeContentRequested",o)):(m(),u.template=null)})}}}}],yo=["$compile",function(t){return{restrict:"ECA",priority:-400,require:"ngInclude",link:function(n,i,r,o){ei.call(i[0]).match(/SVG/)?(i.empty(),t(fe(o.template,e.document).childNodes)(n,function(e){i.append(e)},{futureParentElement:i})):(i.html(o.template),t(i.contents())(n))}}}],wo=xn({priority:450,compile:function(){return{pre:function(e,t,n){e.$eval(n.ngInit)}}}}),xo=function(){return{restrict:"A",priority:100,require:"ngModel",link:function(e,t,n,i){var r=n.ngList||", ",a="false"!==n.ngTrim,s=a?ci(r):r;i.$parsers.push(function(e){if(!v(e)){var t=[];return e&&o(e.split(s),function(e){e&&t.push(a?ci(e):e)}),t}}),i.$formatters.push(function(e){if(li(e))return e.join(r)}),i.$isEmpty=function(e){return!e||!e.length}}}},ko="ng-valid",Co="ng-invalid",To="ng-pristine",Do="ng-dirty",So=i("ngModel");Fn.$inject="$scope $exceptionHandler $attrs $element $parse $animate $timeout $q $interpolate".split(" "),Fn.prototype={$$initGetterSetters:function(){if(this.$options.getOption("getterSetter")){var e=this.$$parse(this.$$attr.ngModel+"()"),t=this.$$parse(this.$$attr.ngModel+"($$$p)");this.$$ngModelGet=function(t){var n=this.$$parsedNgModel(t);return T(n)&&(n=e(t)),n},this.$$ngModelSet=function(e,n){T(this.$$parsedNgModel(e))?t(e,{$$$p:n}):this.$$parsedNgModelAssign(e,n)}}else if(!this.$$parsedNgModel.assign)throw So("nonassign",this.$$attr.ngModel,_(this.$$element))},$render:f,$isEmpty:function(e){return v(e)||""===e||null===e||e!=e},$$updateEmptyClasses:function(e){this.$isEmpty(e)?(this.$$animate.removeClass(this.$$element,"ng-not-empty"),this.$$animate.addClass(this.$$element,"ng-empty")):(this.$$animate.removeClass(this.$$element,"ng-empty"),this.$$animate.addClass(this.$$element,"ng-not-empty"))},$setPristine:function(){this.$dirty=!1,this.$pristine=!0,this.$$animate.removeClass(this.$$element,Do),this.$$animate.addClass(this.$$element,To)},$setDirty:function(){this.$dirty=!0,this.$pristine=!1,this.$$animate.removeClass(this.$$element,To),this.$$animate.addClass(this.$$element,Do),this.$$parentForm.$setDirty()},$setUntouched:function(){this.$touched=!1,this.$untouched=!0,this.$$animate.setClass(this.$$element,"ng-untouched","ng-touched")},$setTouched:function(){this.$touched=!0,this.$untouched=!1,this.$$animate.setClass(this.$$element,"ng-touched","ng-untouched")},$rollbackViewValue:function(){this.$$timeout.cancel(this.$$pendingDebounce),this.$viewValue=this.$$lastCommittedViewValue,this.$render()},$validate:function(){if(!oi(this.$modelValue)){var e=this.$$lastCommittedViewValue,t=this.$$rawModelValue,n=this.$valid,i=this.$modelValue,r=this.$options.getOption("allowInvalid"),o=this;this.$$runValidators(t,e,function(e){r||n===e||(o.$modelValue=e?t:void 0,o.$modelValue!==i&&o.$$writeModelToScope())})}},$$runValidators:function(e,t,n){function i(e,t){a===s.$$currentValidationRunId&&s.$setValidity(e,t)}function r(e){a===s.$$currentValidationRunId&&n(e)}this.$$currentValidationRunId++;var a=this.$$currentValidationRunId,s=this;!function(){var e=s.$$parserName||"parse";return v(s.$$parserValid)?(i(e,null),!0):(s.$$parserValid||(o(s.$validators,function(e,t){i(t,null)}),o(s.$asyncValidators,function(e,t){i(t,null)})),i(e,s.$$parserValid),s.$$parserValid)}()?r(!1):function(){var n=!0;return o(s.$validators,function(r,o){var a=Boolean(r(e,t));n=n&&a,i(o,a)}),!!n||(o(s.$asyncValidators,function(e,t){i(t,null)}),!1)}()?function(){var n=[],a=!0;o(s.$asyncValidators,function(r,o){var s=r(e,t);if(!s||!T(s.then))throw So("nopromise",s);i(o,void 0),n.push(s.then(function(){i(o,!0)},function(){a=!1,i(o,!1)}))}),n.length?s.$$q.all(n).then(function(){r(a)},f):r(!0)}():r(!1)},$commitViewValue:function(){var e=this.$viewValue;this.$$timeout.cancel(this.$$pendingDebounce),(this.$$lastCommittedViewValue!==e||""===e&&this.$$hasNativeValidators)&&(this.$$updateEmptyClasses(e),this.$$lastCommittedViewValue=e,this.$pristine&&this.$setDirty(),this.$$parseAndValidate())},$$parseAndValidate:function(){var e=this.$$lastCommittedViewValue,t=this;if(this.$$parserValid=!v(e)||void 0)for(var n=0;n<this.$parsers.length;n++)if(v(e=this.$parsers[n](e))){this.$$parserValid=!1;break}oi(this.$modelValue)&&(this.$modelValue=this.$$ngModelGet(this.$$scope));var i=this.$modelValue,r=this.$options.getOption("allowInvalid");this.$$rawModelValue=e,r&&(this.$modelValue=e,t.$modelValue!==i&&t.$$writeModelToScope()),this.$$runValidators(e,this.$$lastCommittedViewValue,function(n){r||(t.$modelValue=n?e:void 0,t.$modelValue!==i&&t.$$writeModelToScope())})},$$writeModelToScope:function(){this.$$ngModelSet(this.$$scope,this.$modelValue),o(this.$viewChangeListeners,function(e){try{e()}catch(e){this.$$exceptionHandler(e)}},this)},$setViewValue:function(e,t){this.$viewValue=e,this.$options.getOption("updateOnDefault")&&this.$$debounceViewValueCommit(t)},$$debounceViewValueCommit:function(e){var t=this.$options.getOption("debounce");x(t[e])?t=t[e]:x(t.default)&&(t=t.default),this.$$timeout.cancel(this.$$pendingDebounce);var n=this;0<t?this.$$pendingDebounce=this.$$timeout(function(){n.$commitViewValue()},t):this.$$scope.$root.$$phase?this.$commitViewValue():this.$$scope.$apply(function(){n.$commitViewValue()})},$overrideModelOptions:function(e){this.$options=this.$options.createChild(e),this.$$setUpdateOnEvents()},$processModelValue:function(){var e=this.$$format();this.$viewValue!==e&&(this.$$updateEmptyClasses(e),this.$viewValue=this.$$lastCommittedViewValue=e,this.$render(),this.$$runValidators(this.$modelValue,this.$viewValue,f))},$$format:function(){for(var e=this.$formatters,t=e.length,n=this.$modelValue;t--;)n=e[t](n);return n},$$setModelValue:function(e){this.$modelValue=this.$$rawModelValue=e,this.$$parserValid=void 0,this.$processModelValue()},$$setUpdateOnEvents:function(){this.$$updateEvents&&this.$$element.off(this.$$updateEvents,this.$$updateEventHandler),(this.$$updateEvents=this.$options.getOption("updateOn"))&&this.$$element.on(this.$$updateEvents,this.$$updateEventHandler)},$$updateEventHandler:function(e){this.$$debounceViewValueCommit(e&&e.type)}},Tn({clazz:Fn,set:function(e,t){e[t]=!0},unset:function(e,t){delete e[t]}});var Eo,Oo=["$rootScope",function(e){return{restrict:"A",require:["ngModel","^?form","^?ngModelOptions"],controller:Fn,priority:1,compile:function(t){return t.addClass(To).addClass("ng-untouched").addClass(ko),{pre:function(e,t,n,i){var r=i[0];t=i[1]||r.$$parentForm,(i=i[2])&&(r.$options=i.$options),r.$$initGetterSetters(),t.$addControl(r),n.$observe("name",function(e){r.$name!==e&&r.$$parentForm.$$renameControl(r,e)}),e.$on("$destroy",function(){r.$$parentForm.$removeControl(r)})},post:function(t,n,i,r){function o(){a.$setTouched()}var a=r[0];a.$$setUpdateOnEvents(),n.on("blur",function(){a.$touched||(e.$$phase?t.$evalAsync(o):t.$apply(o))})}}}}}],Ao=/(\s+|^)default(\s+|$)/;Un.prototype={getOption:function(e){return this.$$options[e]},createChild:function(e){var t=!1;return o(e=u({},e),function(n,i){"$inherit"===n?"*"===i?t=!0:(e[i]=this.$$options[i],"updateOn"===i&&(e.updateOnDefault=this.$$options.updateOnDefault)):"updateOn"===i&&(e.updateOnDefault=!1,e[i]=ci(n.replace(Ao,function(){return e.updateOnDefault=!0," "})))},this),t&&(delete e["*"],Ln(e,this.$$options)),Ln(e,Eo.$$options),new Un(e)}},Eo=new Un({updateOn:"",updateOnDefault:!0,debounce:0,getterSetter:!1,allowInvalid:!1,timezone:null});var Mo=function(){function e(e,t){this.$$attrs=e,this.$$scope=t}return e.$inject=["$attrs","$scope"],e.prototype={$onInit:function(){var e=this.parentCtrl?this.parentCtrl.$options:Eo,t=this.$$scope.$eval(this.$$attrs.ngModelOptions);this.$options=e.createChild(t)}},{restrict:"A",priority:10,require:{parentCtrl:"?^^ngModelOptions"},bindToController:!0,controller:e}},No=xn({terminal:!0,priority:1e3}),Io=i("ngOptions"),Po=/^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+group\s+by\s+([\s\S]+?))?(?:\s+disable\s+when\s+([\s\S]+?))?\s+for\s+(?:([$\w][$\w]*)|(?:\(\s*([$\w][$\w]*)\s*,\s*([$\w][$\w]*)\s*\)))\s+in\s+([\s\S]+?)(?:\s+track\s+by\s+([\s\S]+?))?$/,jo=["$compile","$document","$parse",function(t,n,i){function a(e,t,n){function o(e,t,n,i,r){this.selectValue=e,this.viewValue=t,this.label=n,this.group=i,this.disabled=r}function a(e){var t;if(!u&&r(e))t=e;else for(var n in t=[],e)e.hasOwnProperty(n)&&"$"!==n.charAt(0)&&t.push(n);return t}var s=e.match(Po);if(!s)throw Io("iexp",e,_(t));var l=s[5]||s[7],u=s[6];e=/ as /.test(s[0])&&s[1];var c=s[9];t=i(s[2]?s[1]:l);var p=e&&i(e)||t,d=c&&i(c),f=c?function(e,t){return d(n,t)}:function(e){return Pe(e)},h=function(e,t){return f(e,y(e,t))},m=i(s[2]||s[1]),g=i(s[3]||""),v=i(s[4]||""),$=i(s[8]),b={},y=u?function(e,t){return b[u]=t,b[l]=e,b}:function(e){return b[l]=e,b};return{trackBy:c,getTrackByValue:h,getWatchables:i($,function(e){for(var t=[],i=a(e=e||[]),r=i.length,o=0;o<r;o++){var l=e[u=e===i?o:i[o]],u=y(l,u);l=f(l,u);t.push(l),(s[2]||s[1])&&(l=m(n,u),t.push(l)),s[4]&&(u=v(n,u),t.push(u))}return t}),getOptions:function(){for(var e=[],t={},i=$(n)||[],r=a(i),s=r.length,l=0;l<s;l++){var u=i===r?l:r[l],d=y(i[u],u),b=p(n,d);b=new o(u=f(b,d),b,m(n,d),g(n,d),d=v(n,d));e.push(b),t[u]=b}return{items:e,selectValueMap:t,getOptionFromViewValue:function(e){return t[h(e)]},getViewValueFromOption:function(e){return c?I(e.viewValue):e.viewValue}}}}}var s=e.document.createElement("option"),l=e.document.createElement("optgroup");return{restrict:"A",terminal:!0,require:["select","ngModel"],link:{pre:function(e,t,n,i){i[0].registerOption=f},post:function(e,i,r,u){function c(e){var t=(e=v.getOptionFromViewValue(e))&&e.element;return t&&!t.selected&&(t.selected=!0),e}function p(e,t){e.element=t,t.disabled=e.disabled,e.label!==t.label&&(t.label=e.label,t.textContent=e.label),t.value=e.selectValue}var d=u[0],f=u[1],h=r.multiple;u=0;for(var m=i.children(),g=m.length;u<g;u++)if(""===m[u].value){d.hasEmptyOption=!0,d.emptyOption=m.eq(u);break}i.empty(),u=!!d.emptyOption,Bn(s.cloneNode(!1)).val("?");var v,b=a(r.ngOptions,i,e),y=n[0].createDocumentFragment();d.generateUnknownOptionValue=function(e){return"?"},h?(d.writeValue=function(e){if(v){var t=e&&e.map(c)||[];v.items.forEach(function(e){e.element.selected&&-1===Array.prototype.indexOf.call(t,e)&&(e.element.selected=!1)})}},d.readValue=function(){var e=i.val()||[],t=[];return o(e,function(e){(e=v.selectValueMap[e])&&!e.disabled&&t.push(v.getViewValueFromOption(e))}),t},b.trackBy&&e.$watchCollection(function(){if(li(f.$viewValue))return f.$viewValue.map(function(e){return b.getTrackByValue(e)})},function(){f.$render()})):(d.writeValue=function(e){if(v){var t=i[0].options[i[0].selectedIndex],n=v.getOptionFromViewValue(e);t&&t.removeAttribute("selected"),n?(i[0].value!==n.selectValue&&(d.removeUnknownOption(),i[0].value=n.selectValue,n.element.selected=!0),n.element.setAttribute("selected","selected")):d.selectUnknownOrEmptyOption(e)}},d.readValue=function(){var e=v.selectValueMap[i.val()];return e&&!e.disabled?(d.unselectEmptyOption(),d.removeUnknownOption(),v.getViewValueFromOption(e)):null},b.trackBy&&e.$watch(function(){return b.getTrackByValue(f.$viewValue)},function(){f.$render()})),u&&(t(d.emptyOption)(e),i.prepend(d.emptyOption),8===d.emptyOption[0].nodeType?(d.hasEmptyOption=!1,d.registerOption=function(e,t){""===t.val()&&(d.hasEmptyOption=!0,d.emptyOption=t,d.emptyOption.removeClass("ng-scope"),f.$render(),t.on("$destroy",function(){var e=d.$isEmptyOptionSelected();d.hasEmptyOption=!1,d.emptyOption=void 0,e&&f.$render()}))}):d.emptyOption.removeClass("ng-scope")),e.$watchCollection(b.getWatchables,function(){var e=v&&d.readValue();if(v)for(var t=v.items.length-1;0<=t;t--){var n=v.items[t];$(n.group)?Ee(n.element.parentNode):Ee(n.element)}v=b.getOptions();var r={};v.items.forEach(function(e){var t;if($(e.group)){(t=r[e.group])||(t=l.cloneNode(!1),y.appendChild(t),t.label=null===e.group?"null":e.group,r[e.group]=t);var n=s.cloneNode(!1);t.appendChild(n),p(e,n)}else t=s.cloneNode(!1),y.appendChild(t),p(e,t)}),i[0].appendChild(y),f.$render(),f.$isEmpty(e)||(t=d.readValue(),(b.trackBy||h?j(e,t):e===t)||(f.$setViewValue(t),f.$render()))})}}}}],Vo=["$locale","$interpolate","$log",function(e,t,n){var i=/{}/g,r=/^when(Minus)?(.+)$/;return{link:function(a,s,l){function u(e){s.text(e||"")}var c,p=l.count,d=l.$attr.when&&s.attr(l.$attr.when),h=l.offset||0,m=a.$eval(d)||{},g={},$=t.startSymbol(),b=t.endSymbol(),y=$+p+"-"+h+b,w=ii.noop;o(l,function(e,t){var n=r.exec(t);n&&(n=(n[1]?"-":"")+Kn(n[2]),m[n]=s.attr(l.$attr[t]))}),o(m,function(e,n){g[n]=t(e.replace(i,y))}),a.$watch(p,function(t){var i=parseFloat(t),r=oi(i);r||i in m||(i=e.pluralCat(i-h)),i===c||r&&oi(c)||(w(),v(r=g[i])?(null!=t&&n.debug("ngPluralize: no rule defined for '"+i+"' in "+d),w=f,u()):w=a.$watch(r,u),c=i)})}}}],Ro=["$parse","$animate","$compile",function(e,t,n){var a=i("ngRepeat"),s=function(e,t,n,i,r,o,a){e[n]=i,r&&(e[r]=o),e.$index=t,e.$first=0===t,e.$last=t===a-1,e.$middle=!(e.$first||e.$last),e.$odd=!(e.$even=0==(1&t))};return{restrict:"A",multiElement:!0,transclude:"element",priority:1e3,terminal:!0,$$tlb:!0,compile:function(i,l){var u=l.ngRepeat,c=n.$$createComment("end ngRepeat",u);if(!(p=u.match(/^\s*([\s\S]+?)\s+in\s+([\s\S]+?)(?:\s+as\s+([\s\S]+?))?(?:\s+track\s+by\s+([\s\S]+?))?\s*$/)))throw a("iexp",u);var p,d=p[1],f=p[2],h=p[3],m=p[4];if(!(p=d.match(/^(?:(\s*[$\w]+)|\(\s*([$\w]+)\s*,\s*([$\w]+)\s*\))$/)))throw a("iidexp",d);var g=p[3]||p[1],v=p[2];if(h&&(!/^[$a-zA-Z_][$a-zA-Z0-9_]*$/.test(h)||/^(null|undefined|this|\$index|\$first|\$middle|\$last|\$even|\$odd|\$parent|\$root|\$id)$/.test(h)))throw a("badident",h);var $,b,y,w,x={$id:Pe};return m?$=e(m):(y=function(e,t){return Pe(t)},w=function(e){return e}),function(e,n,i,l,p){$&&(b=function(t,n,i){return v&&(x[v]=t),x[g]=n,x.$index=i,$(e,x)});var d=ae();e.$watchCollection(f,function(i){var l,f,m,$,x,k,C,T,D,S,E=n[0],O=ae();if(h&&(e[h]=i),r(i))T=i,f=b||y;else for(S in f=b||w,T=[],i)Gn.call(i,S)&&"$"!==S.charAt(0)&&T.push(S);for($=T.length,S=Array($),l=0;l<$;l++)if(x=i===T?l:T[l],k=i[x],C=f(x,k,l),d[C])D=d[C],delete d[C],O[C]=D,S[l]=D;else{if(O[C])throw o(S,function(e){e&&e.scope&&(d[e.id]=e)}),a("dupes",u,C,k);S[l]={id:C,scope:void 0,clone:void 0},O[C]=!0}for(m in d){if(C=oe((D=d[m]).clone),t.leave(C),C[0].parentNode)for(l=0,f=C.length;l<f;l++)C[l].$$NG_REMOVED=!0;D.scope.$destroy()}for(l=0;l<$;l++)if(x=i===T?l:T[l],k=i[x],(D=S[l]).scope){m=E;do{m=m.nextSibling}while(m&&m.$$NG_REMOVED);D.clone[0]!==m&&t.move(oe(D.clone),null,E),E=D.clone[D.clone.length-1],s(D.scope,l,g,k,v,x,$)}else p(function(e,n){D.scope=n;var i=c.cloneNode(!1);e[e.length++]=i,t.enter(e,null,E),E=i,D.clone=e,O[D.id]=D,s(D.scope,l,g,k,v,x,$)});d=O})}}}}],Fo=["$animate",function(e){return{restrict:"A",multiElement:!0,link:function(t,n,i){t.$watch(i.ngShow,function(t){e[t?"removeClass":"addClass"](n,"ng-hide",{tempClasses:"ng-hide-animate"})})}}}],Uo=["$animate",function(e){return{restrict:"A",multiElement:!0,link:function(t,n,i){t.$watch(i.ngHide,function(t){e[t?"addClass":"removeClass"](n,"ng-hide",{tempClasses:"ng-hide-animate"})})}}}],Lo=xn(function(e,t,n){e.$watch(n.ngStyle,function(e,n){n&&e!==n&&o(n,function(e,n){t.css(n,"")}),e&&t.css(e)},!0)}),Ho=["$animate","$compile",function(e,t){return{require:"ngSwitch",controller:["$scope",function(){this.cases={}}],link:function(n,i,r,a){var s=[],l=[],u=[],c=[],p=function(e,t){return function(n){!1!==n&&e.splice(t,1)}};n.$watch(r.ngSwitch||r.on,function(n){for(var i,r;u.length;)e.cancel(u.pop());for(i=0,r=c.length;i<r;++i){var d=oe(l[i].clone);c[i].$destroy(),(u[i]=e.leave(d)).done(p(u,i))}l.length=0,c.length=0,(s=a.cases["!"+n]||a.cases["?"])&&o(s,function(n){n.transclude(function(i,r){c.push(r);var o=n.element;i[i.length++]=t.$$createComment("end ngSwitchWhen"),l.push({clone:i}),e.enter(i,o.parent(),o)})})})}}}],qo=xn({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(e,t,n,i,r){o(n.ngSwitchWhen.split(n.ngSwitchWhenSeparator).sort().filter(function(e,t,n){return n[t-1]!==e}),function(e){i.cases["!"+e]=i.cases["!"+e]||[],i.cases["!"+e].push({transclude:r,element:t})})}}),Bo=xn({transclude:"element",priority:1200,require:"^ngSwitch",multiElement:!0,link:function(e,t,n,i,r){i.cases["?"]=i.cases["?"]||[],i.cases["?"].push({transclude:r,element:t})}}),_o=i("ngTransclude"),Wo=["$compile",function(e){return{restrict:"EAC",compile:function(t){var n=e(t.contents());return t.empty(),function(e,t,i,r,o){function a(){n(e,function(e){t.append(e)})}if(!o)throw _o("orphan",_(t));i.ngTransclude===i.$attr.ngTransclude&&(i.ngTransclude=""),o(function(e,n){var i;if(i=e.length)e:{i=0;for(var r=e.length;i<r;i++){var o=e[i];if(o.nodeType!==bi||o.nodeValue.trim()){i=!0;break e}}i=void 0}i?t.append(e):(a(),n.$destroy())},null,i=i.ngTransclude||i.ngTranscludeSlot),i&&!o.isSlotFilled(i)&&a()}}}}],zo=["$templateCache",function(e){return{restrict:"E",terminal:!0,compile:function(t,n){"text/ng-template"===n.type&&e.put(n.id,t[0].text)}}}],Yo={$setViewValue:f,$render:f},Go=["$element","$scope",function(t,n){function i(){s||(s=!0,n.$$postDigest(function(){s=!1,o.ngModelCtrl.$render()}))}function r(e){l||(l=!0,n.$$postDigest(function(){n.$$destroyed||(l=!1,o.ngModelCtrl.$setViewValue(o.readValue()),e&&o.ngModelCtrl.$render())}))}var o=this,a=new Fi;o.selectValueMap={},o.ngModelCtrl=Yo,o.multiple=!1,o.unknownOption=Bn(e.document.createElement("option")),o.hasEmptyOption=!1,o.emptyOption=void 0,o.renderUnknownOption=function(e){e=o.generateUnknownOptionValue(e),o.unknownOption.val(e),t.prepend(o.unknownOption),Hn(o.unknownOption,!0),t.val(e)},o.updateUnknownOption=function(e){e=o.generateUnknownOptionValue(e),o.unknownOption.val(e),Hn(o.unknownOption,!0),t.val(e)},o.generateUnknownOptionValue=function(e){return"? "+Pe(e)+" ?"},o.removeUnknownOption=function(){o.unknownOption.parent()&&o.unknownOption.remove()},o.selectEmptyOption=function(){o.emptyOption&&(t.val(""),Hn(o.emptyOption,!0))},o.unselectEmptyOption=function(){o.hasEmptyOption&&Hn(o.emptyOption,!1)},n.$on("$destroy",function(){o.renderUnknownOption=f}),o.readValue=function(){var e=(e=t.val())in o.selectValueMap?o.selectValueMap[e]:e;return o.hasOption(e)?e:null},o.writeValue=function(e){var n=t[0].options[t[0].selectedIndex];n&&Hn(Bn(n),!1),o.hasOption(e)?(o.removeUnknownOption(),n=Pe(e),t.val(n in o.selectValueMap?n:e),Hn(Bn(t[0].options[t[0].selectedIndex]),!0)):o.selectUnknownOrEmptyOption(e)},o.addOption=function(e,t){if(8!==t[0].nodeType){ie(e,'"option value"'),""===e&&(o.hasEmptyOption=!0,o.emptyOption=t);var n=a.get(e)||0;a.set(e,n+1),i()}},o.removeOption=function(e){var t=a.get(e);t&&(1===t?(a.delete(e),""===e&&(o.hasEmptyOption=!1,o.emptyOption=void 0)):a.set(e,t-1))},o.hasOption=function(e){return!!a.get(e)},o.$hasEmptyOption=function(){return o.hasEmptyOption},o.$isUnknownOptionSelected=function(){return t[0].options[0]===o.unknownOption[0]},o.$isEmptyOptionSelected=function(){return o.hasEmptyOption&&t[0].options[t[0].selectedIndex]===o.emptyOption[0]},o.selectUnknownOrEmptyOption=function(e){null==e&&o.emptyOption?(o.removeUnknownOption(),o.selectEmptyOption()):o.unknownOption.parent().length?o.updateUnknownOption(e):o.renderUnknownOption(e)};var s=!1,l=!1;o.registerOption=function(e,t,n,a,s){if(n.$attr.ngValue){var l,u=NaN;n.$observe("value",function(e){var n,i=t.prop("selected");$(u)&&(o.removeOption(l),delete o.selectValueMap[u],n=!0),u=Pe(e),l=e,o.selectValueMap[u]=e,o.addOption(e,t),t.attr("value",u),n&&i&&r()})}else a?n.$observe("value",function(e){o.readValue();var n,i=t.prop("selected");$(l)&&(o.removeOption(l),n=!0),l=e,o.addOption(e,t),n&&i&&r()}):s?e.$watch(s,function(e,i){n.$set("value",e);var a=t.prop("selected");i!==e&&o.removeOption(i),o.addOption(e,t),i&&a&&r()}):o.addOption(n.value,t);n.$observe("disabled",function(e){("true"===e||e&&t.prop("selected"))&&(o.multiple?r(!0):(o.ngModelCtrl.$setViewValue(null),o.ngModelCtrl.$render()))}),t.on("$destroy",function(){var e=o.readValue(),t=n.value;o.removeOption(t),i(),(o.multiple&&e&&-1!==e.indexOf(t)||e===t)&&r(!0)})}}],Ko=function(){return{restrict:"E",require:["select","?ngModel"],controller:Go,priority:1,link:{pre:function(e,t,n,i){var r=i[0],a=i[1];if(a){if(r.ngModelCtrl=a,t.on("change",function(){r.removeUnknownOption(),e.$apply(function(){a.$setViewValue(r.readValue())})}),n.multiple){r.multiple=!0,r.readValue=function(){var e=[];return o(t.find("option"),function(t){t.selected&&!t.disabled&&(t=t.value,e.push(t in r.selectValueMap?r.selectValueMap[t]:t))}),e},r.writeValue=function(e){o(t.find("option"),function(t){var n=!!e&&(-1!==Array.prototype.indexOf.call(e,t.value)||-1!==Array.prototype.indexOf.call(e,r.selectValueMap[t.value]));n!==t.selected&&Hn(Bn(t),n)})};var s,l=NaN;e.$watch(function(){l!==a.$viewValue||j(s,a.$viewValue)||(s=le(a.$viewValue),a.$render()),l=a.$viewValue}),a.$isEmpty=function(e){return!e||0===e.length}}}else r.registerOption=f},post:function(e,t,n,i){var r=i[1];if(r){var o=i[0];r.$render=function(){o.writeValue(r.$viewValue)}}}}}},Jo=["$interpolate",function(e){return{restrict:"E",priority:100,compile:function(t,n){var i,r;return $(n.ngValue)||($(n.value)?i=e(n.value,!0):(r=e(t.text(),!0))||n.$set("value",t.text())),function(e,t,n){var o=t.parent();(o=o.data("$selectController")||o.parent().data("$selectController"))&&o.registerOption(e,t,n,i,r)}}}}],Xo=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,i){i&&(n.required=!0,i.$validators.required=function(e,t){return!n.required||!i.$isEmpty(t)},n.$observe("required",function(){i.$validate()}))}}},Qo=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,r){if(r){var o,a=n.ngPattern||n.pattern;n.$observe("pattern",function(e){if(w(e)&&0<e.length&&(e=new RegExp("^"+e+"$")),e&&!e.test)throw i("ngPattern")("noregexp",a,e,_(t));o=e||void 0,r.$validate()}),r.$validators.pattern=function(e,t){return r.$isEmpty(t)||v(o)||o.test(t)}}}}},Zo=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,i){if(i){var r=-1;n.$observe("maxlength",function(e){e=p(e),r=oi(e)?-1:e,i.$validate()}),i.$validators.maxlength=function(e,t){return 0>r||i.$isEmpty(t)||t.length<=r}}}}},ea=function(){return{restrict:"A",require:"?ngModel",link:function(e,t,n,i){if(i){var r=0;n.$observe("minlength",function(e){r=p(e)||0,i.$validate()}),i.$validators.minlength=function(e,t){return i.$isEmpty(t)||t.length>=r}}}}};e.angular.bootstrap?e.console&&console.log("WARNING: Tried to load AngularJS more than once."):(function(){var t;if(!$i){var n=fi();(_n=v(n)?e.jQuery:n?e[n]:void 0)&&_n.fn.on?(Bn=_n,u(_n.fn,{scope:Ii.scope,isolateScope:Ii.isolateScope,controller:Ii.controller,injector:Ii.injector,inheritedData:Ii.inheritedData}),t=_n.cleanData,_n.cleanData=function(e){for(var n,i,r=0;null!=(i=e[r]);r++)(n=_n._data(i,"events"))&&n.$destroy&&_n(i).triggerHandler("$destroy");t(e)}):Bn=he,ii.element=Bn,$i=!0}}(),function(n){u(n,{errorHandlingConfig:t,bootstrap:X,copy:I,extend:u,merge:c,equals:j,element:Bn,forEach:o,injector:Re,noop:f,bind:R,toJson:U,fromJson:L,identity:h,isUndefined:v,isDefined:$,isString:w,isFunction:T,isObject:b,isNumber:x,isElement:A,isArray:li,version:yi,isDate:k,lowercase:Kn,uppercase:Jn,callbacks:{$$counter:0},getTestability:Z,reloadWithDebugInfo:Q,$$minErr:i,$$csp:di,$$encodeUriSegment:G,$$encodeUriQuery:K,$$stringify:se}),(Wn=function(e){function t(e,t,n){return e[t]||(e[t]=n())}var n=i("$injector"),r=i("ng");return(e=t(e,"angular",Object)).$$minErr=e.$$minErr||i,t(e,"module",function(){var e={};return function(i,o,a){var s={};if("hasOwnProperty"===i)throw r("badname","module");return o&&e.hasOwnProperty(i)&&(e[i]=null),t(e,i,function(){function e(e,t,n,i){return i||(i=l),function(){return i[n||"push"]([e,t,arguments]),d}}function t(e,t,n){return n||(n=l),function(r,o){return o&&T(o)&&(o.$$moduleName=i),n.push([e,t,arguments]),d}}if(!o)throw n("nomod",i);var l=[],u=[],c=[],p=e("$injector","invoke","push",u),d={_invokeQueue:l,_configBlocks:u,_runBlocks:c,info:function(e){if($(e)){if(!b(e))throw r("aobj","value");return s=e,this}return s},requires:o,name:i,provider:t("$provide","provider"),factory:t("$provide","factory"),service:t("$provide","service"),value:e("$provide","value"),constant:e("$provide","constant","unshift"),decorator:t("$provide","decorator",u),animation:t("$animateProvider","register"),filter:t("$filterProvider","register"),controller:t("$controllerProvider","register"),directive:t("$compileProvider","directive"),component:t("$compileProvider","component"),config:p,run:function(e){return c.push(e),this}};return a&&p(a),d})}})}(e))("ng",["ngLocale"],["$provide",function(e){e.provider({$$sanitizeUri:_t}),e.provider("$compile",We).directive({a:Lr,input:io,textarea:io,form:_r,script:zo,select:Ko,option:Jo,ngBind:ao,ngBindHtml:lo,ngBindTemplate:so,ngClass:co,ngClassEven:fo,ngClassOdd:po,ngCloak:ho,ngController:mo,ngForm:Wr,ngHide:Uo,ngIf:$o,ngInclude:bo,ngInit:wo,ngNonBindable:No,ngPluralize:Vo,ngRepeat:Ro,ngShow:Fo,ngStyle:Lo,ngSwitch:Ho,ngSwitchWhen:qo,ngSwitchDefault:Bo,ngOptions:jo,ngTransclude:Wo,ngModel:Oo,ngList:xo,ngChange:uo,pattern:Qo,ngPattern:Qo,required:Xo,ngRequired:Xo,minlength:ea,ngMinlength:ea,maxlength:Zo,ngMaxlength:Zo,ngValue:oo,ngModelOptions:Mo}).directive({ngInclude:yo}).directive(Hr).directive(go),e.provider({$anchorScroll:Fe,$animate:Ki,$animateCss:Qi,$$animateJs:Yi,$$animateQueue:Gi,$$AnimateRunner:Xi,$$animateAsyncRun:Ji,$browser:qe,$cacheFactory:Be,$controller:Xe,$document:Qe,$$isDocumentHidden:Ze,$exceptionHandler:et,$filter:rn,$$forceReflow:or,$interpolate:pt,$interval:dt,$http:lt,$httpParamSerializer:nt,$httpParamSerializerJQLike:it,$httpBackend:ct,$xhrFactory:ut,$jsonpCallbacks:fr,$location:Tt,$log:Dt,$parse:Ft,$rootScope:Bt,$q:Ut,$$q:Lt,$sce:Yt,$sceDelegate:zt,$sniffer:Gt,$templateCache:_e,$templateRequest:Kt,$$testability:Jt,$timeout:Xt,$window:en,$$rAF:qt,$$jqLite:Ie,$$Map:Ui,$$cookieReader:nn})}]).info({angularVersion:"1.6.10"})}(ii),ii.module("ngLocale",[],["$provide",function(e){e.value("$locale",{DATETIME_FORMATS:{AMPMS:["AM","PM"],DAY:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),ERANAMES:["Before Christ","Anno Domini"],ERAS:["BC","AD"],FIRSTDAYOFWEEK:6,MONTH:"January February March April May June July August September October November December".split(" "),SHORTDAY:"Sun Mon Tue Wed Thu Fri Sat".split(" "),SHORTMONTH:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),STANDALONEMONTH:"January February March April May June July August September October November December".split(" "),WEEKENDRANGE:[5,6],fullDate:"EEEE, MMMM d, y",longDate:"MMMM d, y",medium:"MMM d, y h:mm:ss a",mediumDate:"MMM d, y",mediumTime:"h:mm:ss a",short:"M/d/yy h:mm a",shortDate:"M/d/yy",shortTime:"h:mm a"},NUMBER_FORMATS:{CURRENCY_SYM:"$",DECIMAL_SEP:".",GROUP_SEP:",",PATTERNS:[{gSize:3,lgSize:3,maxFrac:3,minFrac:0,minInt:1,negPre:"-",negSuf:"",posPre:"",posSuf:""},{gSize:3,lgSize:3,maxFrac:2,minFrac:2,minInt:1,negPre:"-¤",negSuf:"",posPre:"¤",posSuf:""}]},id:"en-us",localeID:"en_US",pluralCat:function(e,t){var n=0|e,i=t;return void 0===i&&(i=Math.min(function(e){var t=(e+="").indexOf(".");return-1==t?0:e.length-t-1}(e),3)),Math.pow(10,i),1==n&&0==i?"one":"other"}})}]),Bn(function(){J(e.document,X)}))}(window),!window.angular.$$csp().noInlineStyle&&window.angular.element(document.head).prepend('<style type="text/css">@charset "UTF-8";[ng\\:cloak],[ng-cloak],[data-ng-cloak],[x-ng-cloak],.ng-cloak,.x-ng-cloak,.ng-hide:not(.ng-hide-animate){display:none !important;}ng\\:form{display:block;}.ng-animate-shim{visibility:hidden;}.ng-anchor{position:absolute;}</style>'),function(e,t){"use strict";var n,i,r,o,a,s,l,u,c,p,d=t.$$minErr("$sanitize");t.module("ngSanitize",[]).provider("$sanitize",function(){function f(e,t){return h(e.split(","),t)}function h(e,t){var n,i={};for(n=0;n<e.length;n++)i[t?s(e[n]):e[n]]=!0;return i}function m(e,t){t&&t.length&&i(e,h(t))}function g(e){for(var t={},n=0,i=e.length;n<i;n++){var r=e[n];t[r.name]=r.value}return t}function v(e){return e.replace(/&/g,"&amp;").replace(w,function(e){return"&#"+(1024*(e.charCodeAt(0)-55296)+((e=e.charCodeAt(1))-56320)+65536)+";"}).replace(x,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}function $(e,t){var n=t[e];if(n&&u.call(t,n))throw d("elclob",t.outerHTML||t.outerText);return n}var b=!1,y=!1;this.$get=["$$sanitizeUri",function(e){return b=!0,y&&i(O,S),function(t){var n=[];return c(t,p(n,function(t,n){return!/^unsafe:/.test(e(t,n))})),n.join("")}}],this.enableSvg=function(e){return a(e)?(y=e,this):y},this.addValidElements=function(e){return b||(o(e)&&(e={htmlElements:e}),m(S,e.svgElements),m(k,e.htmlVoidElements),m(O,e.htmlVoidElements),m(O,e.htmlElements)),this},this.addValidAttrs=function(e){return b||i(M,h(e,!0)),this},n=t.bind,i=t.extend,r=t.forEach,o=t.isArray,a=t.isDefined,s=t.lowercase,l=t.noop,c=function(e,t){null==e?e="":"string"!=typeof e&&(e=""+e);var n=N(e);if(!n)return"";var i=5;do{if(0===i)throw d("uinput");i--,e=n.innerHTML,n=N(e)}while(e!==n.innerHTML);for(i=n.firstChild;i;){switch(i.nodeType){case 1:t.start(i.nodeName.toLowerCase(),g(i.attributes));break;case 3:t.chars(i.textContent)}var r;if(!(r=i.firstChild)&&(1===i.nodeType&&t.end(i.nodeName.toLowerCase()),!(r=$("nextSibling",i))))for(;null==r&&(i=$("parentNode",i))!==n;)r=$("nextSibling",i),1===i.nodeType&&t.end(i.nodeName.toLowerCase());i=r}for(;i=n.firstChild;)n.removeChild(i)},p=function(e,t){var i=!1,o=n(e,e.push);return{start:function(e,n){e=s(e),!i&&E[e]&&(i=e),i||!0!==O[e]||(o("<"),o(e),r(n,function(n,i){var r=s(i),a="img"===e&&"src"===r||"background"===r;!0!==M[r]||!0===A[r]&&!t(n,a)||(o(" "),o(i),o('="'),o(v(n)),o('"'))}),o(">"))},end:function(e){e=s(e),i||!0!==O[e]||!0===k[e]||(o("</"),o(e),o(">")),e==i&&(i=!1)},chars:function(e){i||o(v(e))}}},u=e.Node.prototype.contains||function(e){return!!(16&this.compareDocumentPosition(e))};var w=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,x=/([^#-~ |!])/g,k=f("area,br,col,hr,img,wbr"),C=f("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),T=f("rp,rt"),D=i({},T,C),S=(C=i({},C,f("address,article,aside,blockquote,caption,center,del,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,map,menu,nav,ol,pre,section,table,ul")),T=i({},T,f("a,abbr,acronym,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,q,ruby,rp,rt,s,samp,small,span,strike,strong,sub,sup,time,tt,u,var")),f("circle,defs,desc,ellipse,font-face,font-face-name,font-face-src,g,glyph,hkern,image,linearGradient,line,marker,metadata,missing-glyph,mpath,path,polygon,polyline,radialGradient,rect,stop,svg,switch,text,title,tspan")),E=f("script,style"),O=i({},k,C,T,D),A=f("background,cite,href,longdesc,src,xlink:href,xml:base"),M=(D=f("abbr,align,alt,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,coords,dir,face,headers,height,hreflang,hspace,ismap,lang,language,nohref,nowrap,rel,rev,rows,rowspan,rules,scope,scrolling,shape,size,span,start,summary,tabindex,target,title,type,valign,value,vspace,width"),T=f("accent-height,accumulate,additive,alphabetic,arabic-form,ascent,baseProfile,bbox,begin,by,calcMode,cap-height,class,color,color-rendering,content,cx,cy,d,dx,dy,descent,display,dur,end,fill,fill-rule,font-family,font-size,font-stretch,font-style,font-variant,font-weight,from,fx,fy,g1,g2,glyph-name,gradientUnits,hanging,height,horiz-adv-x,horiz-origin-x,ideographic,k,keyPoints,keySplines,keyTimes,lang,marker-end,marker-mid,marker-start,markerHeight,markerUnits,markerWidth,mathematical,max,min,offset,opacity,orient,origin,overline-position,overline-thickness,panose-1,path,pathLength,points,preserveAspectRatio,r,refX,refY,repeatCount,repeatDur,requiredExtensions,requiredFeatures,restart,rotate,rx,ry,slope,stemh,stemv,stop-color,stop-opacity,strikethrough-position,strikethrough-thickness,stroke,stroke-dasharray,stroke-dashoffset,stroke-linecap,stroke-linejoin,stroke-miterlimit,stroke-opacity,stroke-width,systemLanguage,target,text-anchor,to,transform,type,u1,u2,underline-position,underline-thickness,unicode,unicode-range,units-per-em,values,version,viewBox,visibility,width,widths,x,x-height,x1,x2,xlink:actuate,xlink:arcrole,xlink:role,xlink:show,xlink:title,xlink:type,xml:base,xml:lang,xml:space,xmlns,xmlns:xlink,y,y1,y2,zoomAndPan",!0),i({},A,T,D)),N=function(t,n){var i;if(!n||!n.implementation)throw d("noinert");var r=((i=n.implementation.createHTMLDocument("inert")).documentElement||i.getDocumentElement()).querySelector("body");return r.innerHTML='<svg><g onload="this.parentNode.remove()"></g></svg>',r.querySelector("svg")?(r.innerHTML='<svg><p><style><img src="</style><img src=x onerror=alert(1)//">',r.querySelector("svg img")?function(e){e="<remove></remove>"+e;try{var n=(new t.DOMParser).parseFromString(e,"text/html").body;return n.firstChild.remove(),n}catch(e){}}:function(t){return r.innerHTML=t,n.documentMode&&function t(n){for(;n;){if(n.nodeType===e.Node.ELEMENT_NODE)for(var i=n.attributes,r=0,o=i.length;r<o;r++){var a=i[r],s=a.name.toLowerCase();"xmlns:ns1"!==s&&0!==s.lastIndexOf("ns1:",0)||(n.removeAttributeNode(a),r--,o--)}(i=n.firstChild)&&t(i),n=$("nextSibling",n)}}(r),r}):function(e){e="<remove></remove>"+e;try{e=encodeURI(e)}catch(e){return}var n=new t.XMLHttpRequest;return n.responseType="document",n.open("GET","data:text/html;charset=utf-8,"+e,!1),n.send(null),(e=n.response.body).firstChild.remove(),e}}(e,e.document)}).info({angularVersion:"1.6.10"}),t.module("ngSanitize").filter("linky",["$sanitize",function(e){var n=/((s?ftp|https?):\/\/|(www\.)|(mailto:)?[A-Za-z0-9._%+-]+@)\S*[^\s.;,(){}<>"\u201d\u2019]/i,i=/^mailto:/i,r=t.$$minErr("linky"),o=t.isDefined,a=t.isFunction,s=t.isObject,u=t.isString;return function(t,c,d){function f(e){e&&b.push(function(e){var t=[];return p(t,l).chars(e),t.join("")}(e))}function h(e,t){var n,i=v(e);for(n in b.push("<a "),i)b.push(n+'="'+i[n]+'" ');!o(c)||"target"in i||b.push('target="',c,'" '),b.push('href="',e.replace(/"/g,"&quot;"),'">'),f(t),b.push("</a>")}if(null==t||""===t)return t;if(!u(t))throw r("notstring",t);for(var m,g,v=a(d)?d:s(d)?function(){return d}:function(){return{}},$=t,b=[];t=$.match(n);)m=t[0],t[2]||t[4]||(m=(t[3]?"http://":"mailto:")+m),g=t.index,f($.substr(0,g)),h(m,t[0].replace(i,"")),$=$.substring(g+t[0].length);return f($),e(b.join(""))}}])}(window,window.angular),angular.module("ui.bootstrap",["ui.bootstrap.tpls","ui.bootstrap.collapse","ui.bootstrap.tabindex","ui.bootstrap.accordion","ui.bootstrap.alert","ui.bootstrap.buttons","ui.bootstrap.carousel","ui.bootstrap.dateparser","ui.bootstrap.isClass","ui.bootstrap.datepicker","ui.bootstrap.position","ui.bootstrap.datepickerPopup","ui.bootstrap.debounce","ui.bootstrap.multiMap","ui.bootstrap.dropdown","ui.bootstrap.stackedMap","ui.bootstrap.modal","ui.bootstrap.paging","ui.bootstrap.pager","ui.bootstrap.pagination","ui.bootstrap.tooltip","ui.bootstrap.popover","ui.bootstrap.progressbar","ui.bootstrap.rating","ui.bootstrap.tabs","ui.bootstrap.timepicker","ui.bootstrap.typeahead"]),angular.module("ui.bootstrap.tpls",["uib/template/accordion/accordion-group.html","uib/template/accordion/accordion.html","uib/template/alert/alert.html","uib/template/carousel/carousel.html","uib/template/carousel/slide.html","uib/template/datepicker/datepicker.html","uib/template/datepicker/day.html","uib/template/datepicker/month.html","uib/template/datepicker/year.html","uib/template/datepickerPopup/popup.html","uib/template/modal/window.html","uib/template/pager/pager.html","uib/template/pagination/pagination.html","uib/template/tooltip/tooltip-html-popup.html","uib/template/tooltip/tooltip-popup.html","uib/template/tooltip/tooltip-template-popup.html","uib/template/popover/popover-html.html","uib/template/popover/popover-template.html","uib/template/popover/popover.html","uib/template/progressbar/bar.html","uib/template/progressbar/progress.html","uib/template/progressbar/progressbar.html","uib/template/rating/rating.html","uib/template/tabs/tab.html","uib/template/tabs/tabset.html","uib/template/timepicker/timepicker.html","uib/template/typeahead/typeahead-match.html","uib/template/typeahead/typeahead-popup.html"]),angular.module("ui.bootstrap.collapse",[]).directive("uibCollapse",["$animate","$q","$parse","$injector",function(e,t,n,i){var r=i.has("$animateCss")?i.get("$animateCss"):null;return{link:function(i,o,a){function s(e){return g?{width:e.scrollWidth+"px"}:{height:e.scrollHeight+"px"}}function l(){o.hasClass("collapse")&&o.hasClass("in")||t.resolve(d(i)).then(function(){o.removeClass("collapse").addClass("collapsing").attr("aria-expanded",!0).attr("aria-hidden",!1),r?r(o,{addClass:"in",easing:"ease",css:{overflow:"hidden"},to:s(o[0])}).start().finally(u):e.addClass(o,"in",{css:{overflow:"hidden"},to:s(o[0])}).then(u)},angular.noop)}function u(){o.removeClass("collapsing").addClass("collapse").css(v),f(i)}function c(){return o.hasClass("collapse")||o.hasClass("in")?void t.resolve(h(i)).then(function(){o.css(s(o[0])).removeClass("collapse").addClass("collapsing").attr("aria-expanded",!1).attr("aria-hidden",!0),r?r(o,{removeClass:"in",to:$}).start().finally(p):e.removeClass(o,"in",{to:$}).then(p)},angular.noop):p()}function p(){o.css($),o.removeClass("collapsing").addClass("collapse"),m(i)}var d=n(a.expanding),f=n(a.expanded),h=n(a.collapsing),m=n(a.collapsed),g=!1,v={},$={};(g=!!("horizontal"in a))?(v={width:""},$={width:"0"}):(v={height:""},$={height:"0"}),i.$eval(a.uibCollapse)||o.addClass("in").addClass("collapse").attr("aria-expanded",!0).attr("aria-hidden",!1).css(v),i.$watch(a.uibCollapse,function(e){e?c():l()})}}}]),angular.module("ui.bootstrap.tabindex",[]).directive("uibTabindexToggle",function(){return{restrict:"A",link:function(e,t,n){n.$observe("disabled",function(e){n.$set("tabindex",e?-1:null)})}}}),angular.module("ui.bootstrap.accordion",["ui.bootstrap.collapse","ui.bootstrap.tabindex"]).constant("uibAccordionConfig",{closeOthers:!0}).controller("UibAccordionController",["$scope","$attrs","uibAccordionConfig",function(e,t,n){this.groups=[],this.closeOthers=function(i){(angular.isDefined(t.closeOthers)?e.$eval(t.closeOthers):n.closeOthers)&&angular.forEach(this.groups,function(e){e!==i&&(e.isOpen=!1)})},this.addGroup=function(e){var t=this;this.groups.push(e),e.$on("$destroy",function(n){t.removeGroup(e)})},this.removeGroup=function(e){var t=this.groups.indexOf(e);-1!==t&&this.groups.splice(t,1)}}]).directive("uibAccordion",function(){return{controller:"UibAccordionController",controllerAs:"accordion",transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/accordion/accordion.html"}}}).directive("uibAccordionGroup",function(){return{require:"^uibAccordion",transclude:!0,restrict:"A",templateUrl:function(e,t){return t.templateUrl||"uib/template/accordion/accordion-group.html"},scope:{heading:"@",panelClass:"@?",isOpen:"=?",isDisabled:"=?"},controller:function(){this.setHeading=function(e){this.heading=e}},link:function(e,t,n,i){t.addClass("panel"),i.addGroup(e),e.openClass=n.openClass||"panel-open",e.panelClass=n.panelClass||"panel-default",e.$watch("isOpen",function(n){t.toggleClass(e.openClass,!!n),n&&i.closeOthers(e)}),e.toggleOpen=function(t){e.isDisabled||t&&32!==t.which||(e.isOpen=!e.isOpen)};var r="accordiongroup-"+e.$id+"-"+Math.floor(1e4*Math.random());e.headingId=r+"-tab",e.panelId=r+"-panel"}}}).directive("uibAccordionHeading",function(){return{transclude:!0,template:"",replace:!0,require:"^uibAccordionGroup",link:function(e,t,n,i,r){i.setHeading(r(e,angular.noop))}}}).directive("uibAccordionTransclude",function(){return{require:"^uibAccordionGroup",link:function(e,t,n,i){e.$watch(function(){return i[n.uibAccordionTransclude]},function(e){if(e){var n=angular.element(t[0].querySelector("uib-accordion-header,data-uib-accordion-header,x-uib-accordion-header,uib\\:accordion-header,[uib-accordion-header],[data-uib-accordion-header],[x-uib-accordion-header]"));n.html(""),n.append(e)}})}}}),angular.module("ui.bootstrap.alert",[]).controller("UibAlertController",["$scope","$element","$attrs","$interpolate","$timeout",function(e,t,n,i,r){e.closeable=!!n.close,t.addClass("alert"),n.$set("role","alert"),e.closeable&&t.addClass("alert-dismissible");var o=angular.isDefined(n.dismissOnTimeout)?i(n.dismissOnTimeout)(e.$parent):null;o&&r(function(){e.close()},parseInt(o,10))}]).directive("uibAlert",function(){return{controller:"UibAlertController",controllerAs:"alert",restrict:"A",templateUrl:function(e,t){return t.templateUrl||"uib/template/alert/alert.html"},transclude:!0,scope:{close:"&"}}}),angular.module("ui.bootstrap.buttons",[]).constant("uibButtonConfig",{activeClass:"active",toggleEvent:"click"}).controller("UibButtonsController",["uibButtonConfig",function(e){this.activeClass=e.activeClass||"active",this.toggleEvent=e.toggleEvent||"click"}]).directive("uibBtnRadio",["$parse",function(e){return{require:["uibBtnRadio","ngModel"],controller:"UibButtonsController",controllerAs:"buttons",link:function(t,n,i,r){var o=r[0],a=r[1],s=e(i.uibUncheckable);n.find("input").css({display:"none"}),a.$render=function(){n.toggleClass(o.activeClass,angular.equals(a.$modelValue,t.$eval(i.uibBtnRadio)))},n.on(o.toggleEvent,function(){if(!i.disabled){var e=n.hasClass(o.activeClass);e&&!angular.isDefined(i.uncheckable)||t.$apply(function(){a.$setViewValue(e?null:t.$eval(i.uibBtnRadio)),a.$render()})}}),i.uibUncheckable&&t.$watch(s,function(e){i.$set("uncheckable",e?"":void 0)})}}}]).directive("uibBtnCheckbox",function(){return{require:["uibBtnCheckbox","ngModel"],controller:"UibButtonsController",controllerAs:"button",link:function(e,t,n,i){function r(){return o(n.btnCheckboxTrue,!0)}function o(t,n){return angular.isDefined(t)?e.$eval(t):n}var a=i[0],s=i[1];t.find("input").css({display:"none"}),s.$render=function(){t.toggleClass(a.activeClass,angular.equals(s.$modelValue,r()))},t.on(a.toggleEvent,function(){n.disabled||e.$apply(function(){s.$setViewValue(t.hasClass(a.activeClass)?o(n.btnCheckboxFalse,!1):r()),s.$render()})})}}}),angular.module("ui.bootstrap.carousel",[]).controller("UibCarouselController",["$scope","$element","$interval","$timeout","$animate",function(e,t,n,i,r){function o(e){for(var t=0;t<h.length;t++)h[t].slide.active=t===e}function a(n,i,a){if(!v){if(angular.extend(n,{direction:a}),angular.extend(h[g].slide||{},{direction:a}),r.enabled(t)&&!e.$currentTransition&&h[i].element&&f.slides.length>1){h[i].element.data(m,n.direction);var s=f.getCurrentIndex();angular.isNumber(s)&&h[s].element&&h[s].element.data(m,n.direction),e.$currentTransition=!0,r.on("addClass",h[i].element,function(t,n){"close"===n&&(e.$currentTransition=null,r.off("addClass",t))})}e.active=n.index,g=n.index,o(i),u()}}function s(e){for(var t=0;t<h.length;t++)if(h[t].slide===e)return t}function l(){p&&(n.cancel(p),p=null)}function u(){l();var t=+e.interval;!isNaN(t)&&t>0&&(p=n(c,t))}function c(){var t=+e.interval;d&&!isNaN(t)&&t>0&&h.length?e.next():e.pause()}var p,d,f=this,h=f.slides=e.slides=[],m="uib-slideDirection",g=e.active,v=!1;t.addClass("carousel"),f.addSlide=function(t,n){h.push({slide:t,element:n}),h.sort(function(e,t){return+e.slide.index-+t.slide.index}),(t.index===e.active||1===h.length&&!angular.isNumber(e.active))&&(e.$currentTransition&&(e.$currentTransition=null),g=t.index,e.active=t.index,o(g),f.select(h[s(t)]),1===h.length&&e.play())},f.getCurrentIndex=function(){for(var e=0;e<h.length;e++)if(h[e].slide.index===g)return e},f.next=e.next=function(){var t=(f.getCurrentIndex()+1)%h.length;return 0===t&&e.noWrap()?void e.pause():f.select(h[t],"next")},f.prev=e.prev=function(){var t=f.getCurrentIndex()-1<0?h.length-1:f.getCurrentIndex()-1;return e.noWrap()&&t===h.length-1?void e.pause():f.select(h[t],"prev")},f.removeSlide=function(t){var n=s(t);h.splice(n,1),h.length>0&&g===n?n>=h.length?(g=h.length-1,e.active=g,o(g),f.select(h[h.length-1])):(g=n,e.active=g,o(g),f.select(h[n])):g>n&&(g--,e.active=g),0===h.length&&(g=null,e.active=null)},f.select=e.select=function(t,n){var i=s(t.slide);void 0===n&&(n=i>f.getCurrentIndex()?"next":"prev"),t.slide.index===g||e.$currentTransition||a(t.slide,i,n)},e.indexOfSlide=function(e){return+e.slide.index},e.isActive=function(t){return e.active===t.slide.index},e.isPrevDisabled=function(){return 0===e.active&&e.noWrap()},e.isNextDisabled=function(){return e.active===h.length-1&&e.noWrap()},e.pause=function(){e.noPause||(d=!1,l())},e.play=function(){d||(d=!0,u())},t.on("mouseenter",e.pause),t.on("mouseleave",e.play),e.$on("$destroy",function(){v=!0,l()}),e.$watch("noTransition",function(e){r.enabled(t,!e)}),e.$watch("interval",u),e.$watchCollection("slides",function(t){t.length||(e.$currentTransition=null)}),e.$watch("active",function(e){if(angular.isNumber(e)&&g!==e){for(var t=0;t<h.length;t++)if(h[t].slide.index===e){e=t;break}h[e]&&(o(e),f.select(h[e]),g=e)}})}]).directive("uibCarousel",function(){return{transclude:!0,controller:"UibCarouselController",controllerAs:"carousel",restrict:"A",templateUrl:function(e,t){return t.templateUrl||"uib/template/carousel/carousel.html"},scope:{active:"=",interval:"=",noTransition:"=",noPause:"=",noWrap:"&"}}}).directive("uibSlide",["$animate",function(e){return{require:"^uibCarousel",restrict:"A",transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/carousel/slide.html"},scope:{actual:"=?",index:"=?"},link:function(t,n,i,r){n.addClass("item"),r.addSlide(t,n),t.$on("$destroy",function(){r.removeSlide(t)}),t.$watch("active",function(t){e[t?"addClass":"removeClass"](n,"active")})}}}]).animation(".item",["$animateCss",function(e){function t(e,t,n){e.removeClass(t),n&&n()}var n="uib-slideDirection";return{beforeAddClass:function(i,r,o){if("active"===r){var a=i.data(n),s="next"===a?"left":"right",l=t.bind(this,i,s+" "+a,o);return i.addClass(a),e(i,{addClass:s}).start().done(l),function(){!0}}o()},beforeRemoveClass:function(i,r,o){if("active"===r){var a="next"===i.data(n)?"left":"right",s=t.bind(this,i,a,o);return e(i,{addClass:a}).start().done(s),function(){!0}}o()}}}]),angular.module("ui.bootstrap.dateparser",[]).service("uibDateParser",["$log","$locale","dateFilter","orderByFilter","filterFilter",function(e,t,n,i,r){function o(e){return r(h,{key:e},!0)[0]}function a(e){var t=[],n=e.split(""),r=e.indexOf("'");if(r>-1){var o=!1;e=e.split("");for(var a=r;a<e.length;a++)o?("'"===e[a]&&(a+1<e.length&&"'"===e[a+1]?(e[a+1]="$",n[a+1]=""):(n[a]="",o=!1)),e[a]="$"):"'"===e[a]&&(e[a]="$",n[a]="",o=!0);e=e.join("")}return angular.forEach(h,function(i){var r=e.indexOf(i.key);if(r>-1){e=e.split(""),n[r]="("+i.regex+")",e[r]="$";for(var o=r+1,a=r+i.key.length;a>o;o++)n[o]="",e[o]="$";e=e.join(""),t.push({index:r,key:i.key,apply:i.apply,matcher:i.regex})}}),{regex:new RegExp("^"+n.join("")+"$"),map:i(t,"index")}}function s(e,t,n){return function(){return e.substr(t+1,n-t-1)}}function l(e,t){for(var n=e.substr(t),i=0;i<h.length;i++)if(new RegExp("^"+h[i].key).test(n)){var r=h[i];return{endIdx:t+r.key.length,parser:r.formatter}}return{endIdx:t+1,parser:function(){return n.charAt(0)}}}function u(e){return parseInt(e,10)}function c(e,t){e=e.replace(/:/g,"");var n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function p(e,t){return(e=new Date(e.getTime())).setMinutes(e.getMinutes()+t),e}function d(e,t,n){n=n?-1:1;var i=e.getTimezoneOffset();return p(e,n*(c(t,i)-i))}var f,h,m=/[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g;this.init=function(){f=t.id,this.parsers={},this.formatters={},h=[{key:"yyyy",regex:"\\d{4}",apply:function(e){this.year=+e},formatter:function(e){var t=new Date;return t.setFullYear(Math.abs(e.getFullYear())),n(t,"yyyy")}},{key:"yy",regex:"\\d{2}",apply:function(e){e=+e,this.year=69>e?e+2e3:e+1900},formatter:function(e){var t=new Date;return t.setFullYear(Math.abs(e.getFullYear())),n(t,"yy")}},{key:"y",regex:"\\d{1,4}",apply:function(e){this.year=+e},formatter:function(e){var t=new Date;return t.setFullYear(Math.abs(e.getFullYear())),n(t,"y")}},{key:"M!",regex:"0?[1-9]|1[0-2]",apply:function(e){this.month=e-1},formatter:function(e){var t=e.getMonth();return/^[0-9]$/.test(t)?n(e,"MM"):n(e,"M")}},{key:"MMMM",regex:t.DATETIME_FORMATS.MONTH.join("|"),apply:function(e){this.month=t.DATETIME_FORMATS.MONTH.indexOf(e)},formatter:function(e){return n(e,"MMMM")}},{key:"MMM",regex:t.DATETIME_FORMATS.SHORTMONTH.join("|"),apply:function(e){this.month=t.DATETIME_FORMATS.SHORTMONTH.indexOf(e)},formatter:function(e){return n(e,"MMM")}},{key:"MM",regex:"0[1-9]|1[0-2]",apply:function(e){this.month=e-1},formatter:function(e){return n(e,"MM")}},{key:"M",regex:"[1-9]|1[0-2]",apply:function(e){this.month=e-1},formatter:function(e){return n(e,"M")}},{key:"d!",regex:"[0-2]?[0-9]{1}|3[0-1]{1}",apply:function(e){this.date=+e},formatter:function(e){var t=e.getDate();return/^[1-9]$/.test(t)?n(e,"dd"):n(e,"d")}},{key:"dd",regex:"[0-2][0-9]{1}|3[0-1]{1}",apply:function(e){this.date=+e},formatter:function(e){return n(e,"dd")}},{key:"d",regex:"[1-2]?[0-9]{1}|3[0-1]{1}",apply:function(e){this.date=+e},formatter:function(e){return n(e,"d")}},{key:"EEEE",regex:t.DATETIME_FORMATS.DAY.join("|"),formatter:function(e){return n(e,"EEEE")}},{key:"EEE",regex:t.DATETIME_FORMATS.SHORTDAY.join("|"),formatter:function(e){return n(e,"EEE")}},{key:"HH",regex:"(?:0|1)[0-9]|2[0-3]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"HH")}},{key:"hh",regex:"0[0-9]|1[0-2]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"hh")}},{key:"H",regex:"1?[0-9]|2[0-3]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"H")}},{key:"h",regex:"[0-9]|1[0-2]",apply:function(e){this.hours=+e},formatter:function(e){return n(e,"h")}},{key:"mm",regex:"[0-5][0-9]",apply:function(e){this.minutes=+e},formatter:function(e){return n(e,"mm")}},{key:"m",regex:"[0-9]|[1-5][0-9]",apply:function(e){this.minutes=+e},formatter:function(e){return n(e,"m")}},{key:"sss",regex:"[0-9][0-9][0-9]",apply:function(e){this.milliseconds=+e},formatter:function(e){return n(e,"sss")}},{key:"ss",regex:"[0-5][0-9]",apply:function(e){this.seconds=+e},formatter:function(e){return n(e,"ss")}},{key:"s",regex:"[0-9]|[1-5][0-9]",apply:function(e){this.seconds=+e},formatter:function(e){return n(e,"s")}},{key:"a",regex:t.DATETIME_FORMATS.AMPMS.join("|"),apply:function(e){12===this.hours&&(this.hours=0),"PM"===e&&(this.hours+=12)},formatter:function(e){return n(e,"a")}},{key:"Z",regex:"[+-]\\d{4}",apply:function(e){var t=e.match(/([+-])(\d{2})(\d{2})/),n=t[1],i=t[2],r=t[3];this.hours+=u(n+i),this.minutes+=u(n+r)},formatter:function(e){return n(e,"Z")}},{key:"ww",regex:"[0-4][0-9]|5[0-3]",formatter:function(e){return n(e,"ww")}},{key:"w",regex:"[0-9]|[1-4][0-9]|5[0-3]",formatter:function(e){return n(e,"w")}},{key:"GGGG",regex:t.DATETIME_FORMATS.ERANAMES.join("|").replace(/\s/g,"\\s"),formatter:function(e){return n(e,"GGGG")}},{key:"GGG",regex:t.DATETIME_FORMATS.ERAS.join("|"),formatter:function(e){return n(e,"GGG")}},{key:"GG",regex:t.DATETIME_FORMATS.ERAS.join("|"),formatter:function(e){return n(e,"GG")}},{key:"G",regex:t.DATETIME_FORMATS.ERAS.join("|"),formatter:function(e){return n(e,"G")}}],angular.version.major>=1&&angular.version.minor>4&&h.push({key:"LLLL",regex:t.DATETIME_FORMATS.STANDALONEMONTH.join("|"),apply:function(e){this.month=t.DATETIME_FORMATS.STANDALONEMONTH.indexOf(e)},formatter:function(e){return n(e,"LLLL")}})},this.init(),this.getParser=function(e){var t=o(e);return t&&t.apply||null},this.overrideParser=function(e,t){var n=o(e);n&&angular.isFunction(t)&&(this.parsers={},n.apply=t)}.bind(this),this.filter=function(e,n){return angular.isDate(e)&&!isNaN(e)&&n?(n=t.DATETIME_FORMATS[n]||n,t.id!==f&&this.init(),this.formatters[n]||(this.formatters[n]=function(e){for(var t,n,i=[],r=0;r<e.length;)if(angular.isNumber(n)){if("'"===e.charAt(r))(r+1>=e.length||"'"!==e.charAt(r+1))&&(i.push(s(e,n,r)),n=null);else if(r===e.length)for(;n<e.length;)t=l(e,n),i.push(t),n=t.endIdx;r++}else"'"!==e.charAt(r)?(t=l(e,r),i.push(t.parser),r=t.endIdx):(n=r,r++);return i}(n)),this.formatters[n].reduce(function(t,n){return t+n(e)},"")):""},this.parse=function(n,i,r){if(!angular.isString(n)||!i)return n;i=(i=t.DATETIME_FORMATS[i]||i).replace(m,"\\$&"),t.id!==f&&this.init(),this.parsers[i]||(this.parsers[i]=a(i));var o=this.parsers[i],s=o.regex,l=o.map,u=n.match(s),c=!1;if(u&&u.length){var p,d;angular.isDate(r)&&!isNaN(r.getTime())?p={year:r.getFullYear(),month:r.getMonth(),date:r.getDate(),hours:r.getHours(),minutes:r.getMinutes(),seconds:r.getSeconds(),milliseconds:r.getMilliseconds()}:(r&&e.warn("dateparser:","baseDate is not a valid date"),p={year:1900,month:0,date:1,hours:0,minutes:0,seconds:0,milliseconds:0});for(var h=1,g=u.length;g>h;h++){var v=l[h-1];"Z"===v.matcher&&(c=!0),v.apply&&v.apply.call(p,u[h])}var $=c?Date.prototype.setUTCFullYear:Date.prototype.setFullYear,b=c?Date.prototype.setUTCHours:Date.prototype.setHours;return function(e,t,n){return!(1>n)&&(1===t&&n>28?29===n&&(e%4==0&&e%100!=0||e%400==0):3!==t&&5!==t&&8!==t&&10!==t||31>n)}(p.year,p.month,p.date)&&(!angular.isDate(r)||isNaN(r.getTime())||c?(d=new Date(0),$.call(d,p.year,p.month,p.date),b.call(d,p.hours||0,p.minutes||0,p.seconds||0,p.milliseconds||0)):(d=new Date(r),$.call(d,p.year,p.month,p.date),b.call(d,p.hours,p.minutes,p.seconds,p.milliseconds))),d}},this.toTimezone=function(e,t){return e&&t?d(e,t):e},this.fromTimezone=function(e,t){return e&&t?d(e,t,!0):e},this.timezoneToOffset=c,this.addDateMinutes=p,this.convertTimezoneToLocal=d}]),angular.module("ui.bootstrap.isClass",[]).directive("uibIsClass",["$animate",function(e){var t=/^\s*([\s\S]+?)\s+on\s+([\s\S]+?)\s*$/,n=/^\s*([\s\S]+?)\s+for\s+([\s\S]+?)\s*$/;return{restrict:"A",compile:function(i,r){function o(t,i){var r=t.match(n),o=i.$eval(r[1]),a=r[2],s=u[t];if(!s){var c=function(t){var n=null;l.some(function(e){return e.scope.$eval(p)===t?(n=e,!0):void 0}),s.lastActivated!==n&&(s.lastActivated&&e.removeClass(s.lastActivated.element,o),n&&e.addClass(n.element,o),s.lastActivated=n)};u[t]=s={lastActivated:null,scope:i,watchFn:c,compareWithExp:a,watcher:i.$watch(a,c)}}s.watchFn(i.$eval(a))}function a(e){var t=e.targetScope,n=s.indexOf(t);if(s.splice(n,1),l.splice(n,1),s.length){var i=s[0];angular.forEach(u,function(e){e.scope===t&&(e.watcher=i.$watch(e.compareWithExp,e.watchFn),e.scope=i)})}else u={}}var s=[],l=[],u={},c=r.uibIsClass.match(t),p=c[2],d=c[1].split(",");return function(e,t,n){s.push(e),l.push({scope:e,element:t}),d.forEach(function(t,n){o(t,e)}),e.$on("$destroy",a)}}}}]),angular.module("ui.bootstrap.datepicker",["ui.bootstrap.dateparser","ui.bootstrap.isClass"]).value("$datepickerSuppressError",!1).value("$datepickerLiteralWarning",!0).constant("uibDatepickerConfig",{datepickerMode:"day",formatDay:"dd",formatMonth:"MMMM",formatYear:"yyyy",formatDayHeader:"EEE",formatDayTitle:"MMMM yyyy",formatMonthTitle:"yyyy",maxDate:null,maxMode:"year",minDate:null,minMode:"day",monthColumns:3,ngModelOptions:{},shortcutPropagation:!1,showWeeks:!0,yearColumns:5,yearRows:4}).controller("UibDatepickerController",["$scope","$element","$attrs","$parse","$interpolate","$locale","$log","dateFilter","uibDatepickerConfig","$datepickerLiteralWarning","$datepickerSuppressError","uibDateParser",function(e,t,n,i,r,o,a,s,l,u,c,p){function d(t){e.datepickerMode=t,e.datepickerOptions.datepickerMode=t}var f=this,h={$setViewValue:angular.noop},m={},g=[];t.addClass("uib-datepicker"),n.$set("role","application"),e.datepickerOptions||(e.datepickerOptions={}),this.modes=["day","month","year"],["customClass","dateDisabled","datepickerMode","formatDay","formatDayHeader","formatDayTitle","formatMonth","formatMonthTitle","formatYear","maxDate","maxMode","minDate","minMode","monthColumns","showWeeks","shortcutPropagation","startingDay","yearColumns","yearRows"].forEach(function(t){switch(t){case"customClass":case"dateDisabled":e[t]=e.datepickerOptions[t]||angular.noop;break;case"datepickerMode":e.datepickerMode=angular.isDefined(e.datepickerOptions.datepickerMode)?e.datepickerOptions.datepickerMode:l.datepickerMode;break;case"formatDay":case"formatDayHeader":case"formatDayTitle":case"formatMonth":case"formatMonthTitle":case"formatYear":f[t]=angular.isDefined(e.datepickerOptions[t])?r(e.datepickerOptions[t])(e.$parent):l[t];break;case"monthColumns":case"showWeeks":case"shortcutPropagation":case"yearColumns":case"yearRows":f[t]=angular.isDefined(e.datepickerOptions[t])?e.datepickerOptions[t]:l[t];break;case"startingDay":angular.isDefined(e.datepickerOptions.startingDay)?f.startingDay=e.datepickerOptions.startingDay:angular.isNumber(l.startingDay)?f.startingDay=l.startingDay:f.startingDay=(o.DATETIME_FORMATS.FIRSTDAYOFWEEK+8)%7;break;case"maxDate":case"minDate":e.$watch("datepickerOptions."+t,function(e){e?angular.isDate(e)?f[t]=p.fromTimezone(new Date(e),m.getOption("timezone")):(u&&a.warn("Literal date support has been deprecated, please switch to date object usage"),f[t]=new Date(s(e,"medium"))):f[t]=l[t]?p.fromTimezone(new Date(l[t]),m.getOption("timezone")):null,f.refreshView()});break;case"maxMode":case"minMode":e.datepickerOptions[t]?e.$watch(function(){return e.datepickerOptions[t]},function(n){f[t]=e[t]=angular.isDefined(n)?n:e.datepickerOptions[t],("minMode"===t&&f.modes.indexOf(e.datepickerOptions.datepickerMode)<f.modes.indexOf(f[t])||"maxMode"===t&&f.modes.indexOf(e.datepickerOptions.datepickerMode)>f.modes.indexOf(f[t]))&&(e.datepickerMode=f[t],e.datepickerOptions.datepickerMode=f[t])}):f[t]=e[t]=l[t]||null}}),e.uniqueId="datepicker-"+e.$id+"-"+Math.floor(1e4*Math.random()),e.disabled=angular.isDefined(n.disabled)||!1,angular.isDefined(n.ngDisabled)&&g.push(e.$parent.$watch(n.ngDisabled,function(t){e.disabled=t,f.refreshView()})),e.isActive=function(t){return 0===f.compare(t.date,f.activeDate)&&(e.activeDateId=t.uid,!0)},this.init=function(t){m=function(t){var n;if(angular.version.minor<6)(n=t.$options||e.datepickerOptions.ngModelOptions||l.ngModelOptions||{}).getOption=function(e){return n[e]};else{var i=t.$options.getOption("timezone")||(e.datepickerOptions.ngModelOptions?e.datepickerOptions.ngModelOptions.timezone:null)||(l.ngModelOptions?l.ngModelOptions.timezone:null);n=t.$options.createChild(l.ngModelOptions).createChild(e.datepickerOptions.ngModelOptions).createChild(t.$options).createChild({timezone:i})}return n}(h=t),e.datepickerOptions.initDate?(f.activeDate=p.fromTimezone(e.datepickerOptions.initDate,m.getOption("timezone"))||new Date,e.$watch("datepickerOptions.initDate",function(e){e&&(h.$isEmpty(h.$modelValue)||h.$invalid)&&(f.activeDate=p.fromTimezone(e,m.getOption("timezone")),f.refreshView())})):f.activeDate=new Date;var n=h.$modelValue?new Date(h.$modelValue):new Date;this.activeDate=isNaN(n)?p.fromTimezone(new Date,m.getOption("timezone")):p.fromTimezone(n,m.getOption("timezone")),h.$render=function(){f.render()}},this.render=function(){if(h.$viewValue){var e=new Date(h.$viewValue);!isNaN(e)?this.activeDate=p.fromTimezone(e,m.getOption("timezone")):c||a.error('Datepicker directive: "ng-model" value must be a Date object')}this.refreshView()},this.refreshView=function(){if(this.element){e.selectedDt=null,this._refreshView(),e.activeDt&&(e.activeDateId=e.activeDt.uid);var t=h.$viewValue?new Date(h.$viewValue):null;t=p.fromTimezone(t,m.getOption("timezone")),h.$setValidity("dateDisabled",!t||this.element&&!this.isDisabled(t))}},this.createDateObject=function(t,n){var i=h.$viewValue?new Date(h.$viewValue):null;i=p.fromTimezone(i,m.getOption("timezone"));var r=new Date;r=p.fromTimezone(r,m.getOption("timezone"));var o=this.compare(t,r),a={date:t,label:p.filter(t,n),selected:i&&0===this.compare(t,i),disabled:this.isDisabled(t),past:0>o,current:0===o,future:o>0,customClass:this.customClass(t)||null};return i&&0===this.compare(t,i)&&(e.selectedDt=a),f.activeDate&&0===this.compare(a.date,f.activeDate)&&(e.activeDt=a),a},this.isDisabled=function(t){return e.disabled||this.minDate&&this.compare(t,this.minDate)<0||this.maxDate&&this.compare(t,this.maxDate)>0||e.dateDisabled&&e.dateDisabled({date:t,mode:e.datepickerMode})},this.customClass=function(t){return e.customClass({date:t,mode:e.datepickerMode})},this.split=function(e,t){for(var n=[];e.length>0;)n.push(e.splice(0,t));return n},e.select=function(t){if(e.datepickerMode===f.minMode){var n=h.$viewValue?p.fromTimezone(new Date(h.$viewValue),m.getOption("timezone")):new Date(0,0,0,0,0,0,0);n.setFullYear(t.getFullYear(),t.getMonth(),t.getDate()),n=p.toTimezone(n,m.getOption("timezone")),h.$setViewValue(n),h.$render()}else f.activeDate=t,d(f.modes[f.modes.indexOf(e.datepickerMode)-1]),e.$emit("uib:datepicker.mode");e.$broadcast("uib:datepicker.focus")},e.move=function(e){var t=f.activeDate.getFullYear()+e*(f.step.years||0),n=f.activeDate.getMonth()+e*(f.step.months||0);f.activeDate.setFullYear(t,n,1),f.refreshView()},e.toggleMode=function(t){t=t||1,e.datepickerMode===f.maxMode&&1===t||e.datepickerMode===f.minMode&&-1===t||(d(f.modes[f.modes.indexOf(e.datepickerMode)+t]),e.$emit("uib:datepicker.mode"))},e.keys={13:"enter",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down"};e.$on("uib:datepicker.focus",function(){f.element[0].focus()}),e.keydown=function(t){var n=e.keys[t.which];if(n&&!t.shiftKey&&!t.altKey&&!e.disabled)if(t.preventDefault(),f.shortcutPropagation||t.stopPropagation(),"enter"===n||"space"===n){if(f.isDisabled(f.activeDate))return;e.select(f.activeDate)}else!t.ctrlKey||"up"!==n&&"down"!==n?(f.handleKeyDown(n,t),f.refreshView()):e.toggleMode("up"===n?1:-1)},t.on("keydown",function(t){e.$apply(function(){e.keydown(t)})}),e.$on("$destroy",function(){for(;g.length;)g.shift()()})}]).controller("UibDaypickerController",["$scope","$element","dateFilter",function(e,t,n){function i(e,t){return 1!==t||e%4!=0||e%100==0&&e%400!=0?o[t]:29}function r(e){var t=new Date(e);t.setDate(t.getDate()+4-(t.getDay()||7));var n=t.getTime();return t.setMonth(0),t.setDate(1),Math.floor(Math.round((n-t)/864e5)/7)+1}var o=[31,28,31,30,31,30,31,31,30,31,30,31];this.step={months:1},this.element=t,this.init=function(t){angular.extend(t,this),e.showWeeks=t.showWeeks,t.refreshView()},this.getDates=function(e,t){for(var n,i=new Array(t),r=new Date(e),o=0;t>o;)n=new Date(r),i[o++]=n,r.setDate(r.getDate()+1);return i},this._refreshView=function(){var t=this.activeDate.getFullYear(),i=this.activeDate.getMonth(),o=new Date(this.activeDate);o.setFullYear(t,i,1);var a=this.startingDay-o.getDay(),s=a>0?7-a:-a,l=new Date(o);s>0&&l.setDate(1-s);for(var u=this.getDates(l,42),c=0;42>c;c++)u[c]=angular.extend(this.createDateObject(u[c],this.formatDay),{secondary:u[c].getMonth()!==i,uid:e.uniqueId+"-"+c});e.labels=new Array(7);for(var p=0;7>p;p++)e.labels[p]={abbr:n(u[p].date,this.formatDayHeader),full:n(u[p].date,"EEEE")};if(e.title=n(this.activeDate,this.formatDayTitle),e.rows=this.split(u,7),e.showWeeks){e.weekNumbers=[];for(var d=(11-this.startingDay)%7,f=e.rows.length,h=0;f>h;h++)e.weekNumbers.push(r(e.rows[h][d].date))}},this.compare=function(e,t){var n=new Date(e.getFullYear(),e.getMonth(),e.getDate()),i=new Date(t.getFullYear(),t.getMonth(),t.getDate());return n.setFullYear(e.getFullYear()),i.setFullYear(t.getFullYear()),n-i},this.handleKeyDown=function(e,t){var n=this.activeDate.getDate();if("left"===e)n-=1;else if("up"===e)n-=7;else if("right"===e)n+=1;else if("down"===e)n+=7;else if("pageup"===e||"pagedown"===e){var r=this.activeDate.getMonth()+("pageup"===e?-1:1);this.activeDate.setMonth(r,1),n=Math.min(i(this.activeDate.getFullYear(),this.activeDate.getMonth()),n)}else"home"===e?n=1:"end"===e&&(n=i(this.activeDate.getFullYear(),this.activeDate.getMonth()));this.activeDate.setDate(n)}}]).controller("UibMonthpickerController",["$scope","$element","dateFilter",function(e,t,n){this.step={years:1},this.element=t,this.init=function(e){angular.extend(e,this),e.refreshView()},this._refreshView=function(){for(var t,i=new Array(12),r=this.activeDate.getFullYear(),o=0;12>o;o++)(t=new Date(this.activeDate)).setFullYear(r,o,1),i[o]=angular.extend(this.createDateObject(t,this.formatMonth),{uid:e.uniqueId+"-"+o});e.title=n(this.activeDate,this.formatMonthTitle),e.rows=this.split(i,this.monthColumns),e.yearHeaderColspan=this.monthColumns>3?this.monthColumns-2:1},this.compare=function(e,t){var n=new Date(e.getFullYear(),e.getMonth()),i=new Date(t.getFullYear(),t.getMonth());return n.setFullYear(e.getFullYear()),i.setFullYear(t.getFullYear()),n-i},this.handleKeyDown=function(e,t){var n=this.activeDate.getMonth();if("left"===e)n-=1;else if("up"===e)n-=this.monthColumns;else if("right"===e)n+=1;else if("down"===e)n+=this.monthColumns;else if("pageup"===e||"pagedown"===e){var i=this.activeDate.getFullYear()+("pageup"===e?-1:1);this.activeDate.setFullYear(i)}else"home"===e?n=0:"end"===e&&(n=11);this.activeDate.setMonth(n)}}]).controller("UibYearpickerController",["$scope","$element","dateFilter",function(e,t,n){function i(e){return parseInt((e-1)/o,10)*o+1}var r,o;this.element=t,this.yearpickerInit=function(){r=this.yearColumns,o=this.yearRows*r,this.step={years:o}},this._refreshView=function(){for(var t,n=new Array(o),a=0,s=i(this.activeDate.getFullYear());o>a;a++)(t=new Date(this.activeDate)).setFullYear(s+a,0,1),n[a]=angular.extend(this.createDateObject(t,this.formatYear),{uid:e.uniqueId+"-"+a});e.title=[n[0].label,n[o-1].label].join(" - "),e.rows=this.split(n,r),e.columns=r},this.compare=function(e,t){return e.getFullYear()-t.getFullYear()},this.handleKeyDown=function(e,t){var n=this.activeDate.getFullYear();"left"===e?n-=1:"up"===e?n-=r:"right"===e?n+=1:"down"===e?n+=r:"pageup"===e||"pagedown"===e?n+=("pageup"===e?-1:1)*o:"home"===e?n=i(this.activeDate.getFullYear()):"end"===e&&(n=i(this.activeDate.getFullYear())+o-1),this.activeDate.setFullYear(n)}}]).directive("uibDatepicker",function(){return{templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/datepicker.html"},scope:{datepickerOptions:"=?"},require:["uibDatepicker","^ngModel"],restrict:"A",controller:"UibDatepickerController",controllerAs:"datepicker",link:function(e,t,n,i){var r=i[0],o=i[1];r.init(o)}}}).directive("uibDaypicker",function(){return{templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/day.html"},require:["^uibDatepicker","uibDaypicker"],restrict:"A",controller:"UibDaypickerController",link:function(e,t,n,i){var r=i[0];i[1].init(r)}}}).directive("uibMonthpicker",function(){return{templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/month.html"},require:["^uibDatepicker","uibMonthpicker"],restrict:"A",controller:"UibMonthpickerController",link:function(e,t,n,i){var r=i[0];i[1].init(r)}}}).directive("uibYearpicker",function(){return{templateUrl:function(e,t){return t.templateUrl||"uib/template/datepicker/year.html"},require:["^uibDatepicker","uibYearpicker"],restrict:"A",controller:"UibYearpickerController",link:function(e,t,n,i){var r=i[0];angular.extend(r,i[1]),r.yearpickerInit(),r.refreshView()}}}),angular.module("ui.bootstrap.position",[]).factory("$uibPosition",["$document","$window",function(e,t){var n,i,r={normal:/(auto|scroll)/,hidden:/(auto|scroll|hidden)/},o={auto:/\s?auto?\s?/i,primary:/^(top|bottom|left|right)$/,secondary:/^(top|bottom|left|right|center)$/,vertical:/^(top|bottom)$/},a=/(HTML|BODY)/;return{getRawNode:function(e){return e.nodeName?e:e[0]||e},parseStyle:function(e){return e=parseFloat(e),isFinite(e)?e:0},offsetParent:function(n){function i(e){return"static"===(t.getComputedStyle(e).position||"static")}for(var r=(n=this.getRawNode(n)).offsetParent||e[0].documentElement;r&&r!==e[0].documentElement&&i(r);)r=r.offsetParent;return r||e[0].documentElement},scrollbarWidth:function(r){if(r){if(angular.isUndefined(i)){var o=e.find("body");o.addClass("uib-position-body-scrollbar-measure"),i=t.innerWidth-o[0].clientWidth,i=isFinite(i)?i:0,o.removeClass("uib-position-body-scrollbar-measure")}return i}if(angular.isUndefined(n)){var a=angular.element('<div class="uib-position-scrollbar-measure"></div>');e.find("body").append(a),n=a[0].offsetWidth-a[0].clientWidth,n=isFinite(n)?n:0,a.remove()}return n},scrollbarPadding:function(e){e=this.getRawNode(e);var n=t.getComputedStyle(e),i=this.parseStyle(n.paddingRight),r=this.parseStyle(n.paddingBottom),o=this.scrollParent(e,!1,!0),s=this.scrollbarWidth(a.test(o.tagName));return{scrollbarWidth:s,widthOverflow:o.scrollWidth>o.clientWidth,right:i+s,originalRight:i,heightOverflow:o.scrollHeight>o.clientHeight,bottom:r+s,originalBottom:r}},isScrollable:function(e,n){e=this.getRawNode(e);var i=n?r.hidden:r.normal,o=t.getComputedStyle(e);return i.test(o.overflow+o.overflowY+o.overflowX)},scrollParent:function(n,i,o){n=this.getRawNode(n);var a=i?r.hidden:r.normal,s=e[0].documentElement,l=t.getComputedStyle(n);if(o&&a.test(l.overflow+l.overflowY+l.overflowX))return n;var u="absolute"===l.position,c=n.parentElement||s;if(c===s||"fixed"===l.position)return s;for(;c.parentElement&&c!==s;){var p=t.getComputedStyle(c);if(u&&"static"!==p.position&&(u=!1),!u&&a.test(p.overflow+p.overflowY+p.overflowX))break;c=c.parentElement}return c},position:function(n,i){n=this.getRawNode(n);var r=this.offset(n);if(i){var o=t.getComputedStyle(n);r.top-=this.parseStyle(o.marginTop),r.left-=this.parseStyle(o.marginLeft)}var a=this.offsetParent(n),s={top:0,left:0};return a!==e[0].documentElement&&((s=this.offset(a)).top+=a.clientTop-a.scrollTop,s.left+=a.clientLeft-a.scrollLeft),{width:Math.round(angular.isNumber(r.width)?r.width:n.offsetWidth),height:Math.round(angular.isNumber(r.height)?r.height:n.offsetHeight),top:Math.round(r.top-s.top),left:Math.round(r.left-s.left)}},offset:function(n){var i=(n=this.getRawNode(n)).getBoundingClientRect();return{width:Math.round(angular.isNumber(i.width)?i.width:n.offsetWidth),height:Math.round(angular.isNumber(i.height)?i.height:n.offsetHeight),top:Math.round(i.top+(t.pageYOffset||e[0].documentElement.scrollTop)),left:Math.round(i.left+(t.pageXOffset||e[0].documentElement.scrollLeft))}},viewportOffset:function(n,i,r){r=!1!==r;var o=(n=this.getRawNode(n)).getBoundingClientRect(),a={top:0,left:0,bottom:0,right:0},s=i?e[0].documentElement:this.scrollParent(n),l=s.getBoundingClientRect();if(a.top=l.top+s.clientTop,a.left=l.left+s.clientLeft,s===e[0].documentElement&&(a.top+=t.pageYOffset,a.left+=t.pageXOffset),a.bottom=a.top+s.clientHeight,a.right=a.left+s.clientWidth,r){var u=t.getComputedStyle(s);a.top+=this.parseStyle(u.paddingTop),a.bottom-=this.parseStyle(u.paddingBottom),a.left+=this.parseStyle(u.paddingLeft),a.right-=this.parseStyle(u.paddingRight)}return{top:Math.round(o.top-a.top),bottom:Math.round(a.bottom-o.bottom),left:Math.round(o.left-a.left),right:Math.round(a.right-o.right)}},parsePlacement:function(e){var t=o.auto.test(e);return t&&(e=e.replace(o.auto,"")),(e=e.split("-"))[0]=e[0]||"top",o.primary.test(e[0])||(e[0]="top"),e[1]=e[1]||"center",o.secondary.test(e[1])||(e[1]="center"),e[2]=!!t,e},positionElements:function(e,n,i,r){e=this.getRawNode(e),n=this.getRawNode(n);var a=angular.isDefined(n.offsetWidth)?n.offsetWidth:n.prop("offsetWidth"),s=angular.isDefined(n.offsetHeight)?n.offsetHeight:n.prop("offsetHeight");i=this.parsePlacement(i);var l=r?this.offset(e):this.position(e),u={top:0,left:0,placement:""};if(i[2]){var c=this.viewportOffset(e,r),p=t.getComputedStyle(n),d={width:a+Math.round(Math.abs(this.parseStyle(p.marginLeft)+this.parseStyle(p.marginRight))),height:s+Math.round(Math.abs(this.parseStyle(p.marginTop)+this.parseStyle(p.marginBottom)))};if(i[0]="top"===i[0]&&d.height>c.top&&d.height<=c.bottom?"bottom":"bottom"===i[0]&&d.height>c.bottom&&d.height<=c.top?"top":"left"===i[0]&&d.width>c.left&&d.width<=c.right?"right":"right"===i[0]&&d.width>c.right&&d.width<=c.left?"left":i[0],i[1]="top"===i[1]&&d.height-l.height>c.bottom&&d.height-l.height<=c.top?"bottom":"bottom"===i[1]&&d.height-l.height>c.top&&d.height-l.height<=c.bottom?"top":"left"===i[1]&&d.width-l.width>c.right&&d.width-l.width<=c.left?"right":"right"===i[1]&&d.width-l.width>c.left&&d.width-l.width<=c.right?"left":i[1],"center"===i[1])if(o.vertical.test(i[0])){var f=l.width/2-a/2;c.left+f<0&&d.width-l.width<=c.right?i[1]="left":c.right+f<0&&d.width-l.width<=c.left&&(i[1]="right")}else{var h=l.height/2-d.height/2;c.top+h<0&&d.height-l.height<=c.bottom?i[1]="top":c.bottom+h<0&&d.height-l.height<=c.top&&(i[1]="bottom")}}switch(i[0]){case"top":u.top=l.top-s;break;case"bottom":u.top=l.top+l.height;break;case"left":u.left=l.left-a;break;case"right":u.left=l.left+l.width}switch(i[1]){case"top":u.top=l.top;break;case"bottom":u.top=l.top+l.height-s;break;case"left":u.left=l.left;break;case"right":u.left=l.left+l.width-a;break;case"center":o.vertical.test(i[0])?u.left=l.left+l.width/2-a/2:u.top=l.top+l.height/2-s/2}return u.top=Math.round(u.top),u.left=Math.round(u.left),u.placement="center"===i[1]?i[0]:i[0]+"-"+i[1],u},adjustTop:function(e,t,n,i){return-1!==e.indexOf("top")&&n!==i?{top:t.top-i+"px"}:void 0},positionArrow:function(e,n){var i=(e=this.getRawNode(e)).querySelector(".tooltip-inner, .popover-inner");if(i){var r=angular.element(i).hasClass("tooltip-inner"),a=r?e.querySelector(".tooltip-arrow"):e.querySelector(".arrow");if(a){var s={top:"",bottom:"",left:"",right:""};if("center"===(n=this.parsePlacement(n))[1])return void angular.element(a).css(s);var l="border-"+n[0]+"-width",u=t.getComputedStyle(a)[l],c="border-";c+=o.vertical.test(n[0])?n[0]+"-"+n[1]:n[1]+"-"+n[0],c+="-radius";var p=t.getComputedStyle(r?i:e)[c];switch(n[0]){case"top":s.bottom=r?"0":"-"+u;break;case"bottom":s.top=r?"0":"-"+u;break;case"left":s.right=r?"0":"-"+u;break;case"right":s.left=r?"0":"-"+u}s[n[1]]=p,angular.element(a).css(s)}}}}}]),angular.module("ui.bootstrap.datepickerPopup",["ui.bootstrap.datepicker","ui.bootstrap.position"]).value("$datepickerPopupLiteralWarning",!0).constant("uibDatepickerPopupConfig",{altInputFormats:[],appendToBody:!1,clearText:"Clear",closeOnDateSelection:!0,closeText:"Done",currentText:"Today",datepickerPopup:"yyyy-MM-dd",datepickerPopupTemplateUrl:"uib/template/datepickerPopup/popup.html",datepickerTemplateUrl:"uib/template/datepicker/datepicker.html",html5Types:{date:"yyyy-MM-dd","datetime-local":"yyyy-MM-ddTHH:mm:ss.sss",month:"yyyy-MM"},onOpenFocus:!0,showButtonBar:!0,placement:"auto bottom-left"}).controller("UibDatepickerPopupController",["$scope","$element","$attrs","$compile","$log","$parse","$window","$document","$rootScope","$uibPosition","dateFilter","uibDateParser","uibDatepickerPopupConfig","$timeout","uibDatepickerConfig","$datepickerPopupLiteralWarning",function(e,t,n,i,r,o,a,s,l,u,c,p,d,f,h,m){function g(t){var n=p.parse(t,x,e.date);if(isNaN(n))for(var i=0;i<P.length;i++)if(n=p.parse(t,P[i],e.date),!isNaN(n))return n;return n}function v(e){if(angular.isNumber(e)&&(e=new Date(e)),!e)return null;if(angular.isDate(e)&&!isNaN(e))return e;if(angular.isString(e)){var t=g(e);if(!isNaN(t))return p.toTimezone(t,N.getOption("timezone"))}return N.getOption("allowInvalid")?e:void 0}function $(e,t){var i=e||t;return!n.ngRequired&&!i||(angular.isNumber(i)&&(i=new Date(i)),!i||(!(!angular.isDate(i)||isNaN(i))||!!angular.isString(i)&&!isNaN(g(i))))}function b(n){if(e.isOpen||!e.disabled){var i=I[0],r=t[0].contains(n.target),o=void 0!==i.contains&&i.contains(n.target);!e.isOpen||r||o||e.$apply(function(){e.isOpen=!1})}}function y(n){27===n.which&&e.isOpen?(n.preventDefault(),n.stopPropagation(),e.$apply(function(){e.isOpen=!1}),t[0].focus()):40!==n.which||e.isOpen||(n.preventDefault(),n.stopPropagation(),e.$apply(function(){e.isOpen=!0}))}function w(){if(e.isOpen){var i=angular.element(I[0].querySelector(".uib-datepicker-popup")),r=n.popupPlacement?n.popupPlacement:d.placement,o=u.positionElements(t,i,r,C);i.css({top:o.top+"px",left:o.left+"px"}),i.hasClass("uib-position-measure")&&i.removeClass("uib-position-measure")}}var x,k,C,T,D,S,E,O,A,M,N,I,P,j=!1,V=[];this.init=function(r){if(N=function(e){var t;return angular.version.minor<6?(t=angular.isObject(e.$options)?e.$options:{timezone:null}).getOption=function(e){return t[e]}:t=e.$options,t}(M=r),k=angular.isDefined(n.closeOnDateSelection)?e.$parent.$eval(n.closeOnDateSelection):d.closeOnDateSelection,C=angular.isDefined(n.datepickerAppendToBody)?e.$parent.$eval(n.datepickerAppendToBody):d.appendToBody,T=angular.isDefined(n.onOpenFocus)?e.$parent.$eval(n.onOpenFocus):d.onOpenFocus,D=angular.isDefined(n.datepickerPopupTemplateUrl)?n.datepickerPopupTemplateUrl:d.datepickerPopupTemplateUrl,S=angular.isDefined(n.datepickerTemplateUrl)?n.datepickerTemplateUrl:d.datepickerTemplateUrl,P=angular.isDefined(n.altInputFormats)?e.$parent.$eval(n.altInputFormats):d.altInputFormats,e.showButtonBar=angular.isDefined(n.showButtonBar)?e.$parent.$eval(n.showButtonBar):d.showButtonBar,d.html5Types[n.type]?(x=d.html5Types[n.type],j=!0):(x=n.uibDatepickerPopup||d.datepickerPopup,n.$observe("uibDatepickerPopup",function(e,t){var n=e||d.datepickerPopup;if(n!==x&&(x=n,M.$modelValue=null,!x))throw new Error("uibDatepickerPopup must have a date format specified.")})),!x)throw new Error("uibDatepickerPopup must have a date format specified.");if(j&&n.uibDatepickerPopup)throw new Error("HTML5 date input types do not support custom formats.");(E=angular.element("<div uib-datepicker-popup-wrap><div uib-datepicker></div></div>")).attr({"ng-model":"date","ng-change":"dateSelection(date)","template-url":D}),(O=angular.element(E.children()[0])).attr("template-url",S),e.datepickerOptions||(e.datepickerOptions={}),j&&"month"===n.type&&(e.datepickerOptions.datepickerMode="month",e.datepickerOptions.minMode="month"),O.attr("datepicker-options","datepickerOptions"),j?M.$formatters.push(function(t){return e.date=p.fromTimezone(t,N.getOption("timezone")),t}):(M.$$parserName="date",M.$validators.date=$,M.$parsers.unshift(v),M.$formatters.push(function(t){return M.$isEmpty(t)?(e.date=t,t):(angular.isNumber(t)&&(t=new Date(t)),e.date=p.fromTimezone(t,N.getOption("timezone")),p.filter(e.date,x))})),M.$viewChangeListeners.push(function(){e.date=g(M.$viewValue)}),t.on("keydown",y),I=i(E)(e),E.remove(),C?s.find("body").append(I):t.after(I),e.$on("$destroy",function(){for(!0===e.isOpen&&(l.$$phase||e.$apply(function(){e.isOpen=!1})),I.remove(),t.off("keydown",y),s.off("click",b),A&&A.off("scroll",w),angular.element(a).off("resize",w);V.length;)V.shift()()})},e.getText=function(t){return e[t+"Text"]||d[t+"Text"]},e.isDisabled=function(t){"today"===t&&(t=p.fromTimezone(new Date,N.getOption("timezone")));var n={};return angular.forEach(["minDate","maxDate"],function(t){e.datepickerOptions[t]?angular.isDate(e.datepickerOptions[t])?n[t]=new Date(e.datepickerOptions[t]):(m&&r.warn("Literal date support has been deprecated, please switch to date object usage"),n[t]=new Date(c(e.datepickerOptions[t],"medium"))):n[t]=null}),e.datepickerOptions&&n.minDate&&e.compare(t,n.minDate)<0||n.maxDate&&e.compare(t,n.maxDate)>0},e.compare=function(e,t){return new Date(e.getFullYear(),e.getMonth(),e.getDate())-new Date(t.getFullYear(),t.getMonth(),t.getDate())},e.dateSelection=function(n){e.date=n;var i=e.date?p.filter(e.date,x):null;t.val(i),M.$setViewValue(i),k&&(e.isOpen=!1,t[0].focus())},e.keydown=function(n){27===n.which&&(n.stopPropagation(),e.isOpen=!1,t[0].focus())},e.select=function(t,n){if(n.stopPropagation(),"today"===t){var i=new Date;angular.isDate(e.date)?(t=new Date(e.date)).setFullYear(i.getFullYear(),i.getMonth(),i.getDate()):(t=p.fromTimezone(i,N.getOption("timezone"))).setHours(0,0,0,0)}e.dateSelection(t)},e.close=function(n){n.stopPropagation(),e.isOpen=!1,t[0].focus()},e.disabled=angular.isDefined(n.disabled)||!1,n.ngDisabled&&V.push(e.$parent.$watch(o(n.ngDisabled),function(t){e.disabled=t})),e.$watch("isOpen",function(i){i?e.disabled?e.isOpen=!1:f(function(){w(),T&&e.$broadcast("uib:datepicker.focus"),s.on("click",b);var i=n.popupPlacement?n.popupPlacement:d.placement;C||u.parsePlacement(i)[2]?(A=A||angular.element(u.scrollParent(t)))&&A.on("scroll",w):A=null,angular.element(a).on("resize",w)},0,!1):(s.off("click",b),A&&A.off("scroll",w),angular.element(a).off("resize",w))}),e.$on("uib:datepicker.mode",function(){f(w,0,!1)})}]).directive("uibDatepickerPopup",function(){return{require:["ngModel","uibDatepickerPopup"],controller:"UibDatepickerPopupController",scope:{datepickerOptions:"=?",isOpen:"=?",currentText:"@",clearText:"@",closeText:"@"},link:function(e,t,n,i){var r=i[0];i[1].init(r)}}}).directive("uibDatepickerPopupWrap",function(){return{restrict:"A",transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/datepickerPopup/popup.html"}}}),angular.module("ui.bootstrap.debounce",[]).factory("$$debounce",["$timeout",function(e){return function(t,n){var i;return function(){var r=this,o=Array.prototype.slice.call(arguments);i&&e.cancel(i),i=e(function(){t.apply(r,o)},n)}}}]),angular.module("ui.bootstrap.multiMap",[]).factory("$$multiMap",function(){return{createNew:function(){var e={};return{entries:function(){return Object.keys(e).map(function(t){return{key:t,value:e[t]}})},get:function(t){return e[t]},hasKey:function(t){return!!e[t]},keys:function(){return Object.keys(e)},put:function(t,n){e[t]||(e[t]=[]),e[t].push(n)},remove:function(t,n){var i=e[t];if(i){var r=i.indexOf(n);-1!==r&&i.splice(r,1),i.length||delete e[t]}}}}}}),angular.module("ui.bootstrap.dropdown",["ui.bootstrap.multiMap","ui.bootstrap.position"]).constant("uibDropdownConfig",{appendToOpenClass:"uib-dropdown-open",openClass:"open"}).service("uibDropdownService",["$document","$rootScope","$$multiMap",function(e,t,n){var i=null,r=n.createNew();this.isOnlyOpen=function(e,t){var n=r.get(t);if(n&&n.reduce(function(t,n){return n.scope===e?n:t},{}))return 1===n.length;return!1},this.open=function(t,n,a){if(i||e.on("click",o),i&&i!==t&&(i.isOpen=!1),i=t,a){var s=r.get(a);if(s)-1===s.map(function(e){return e.scope}).indexOf(t)&&r.put(a,{scope:t});else r.put(a,{scope:t})}},this.close=function(t,n,a){if(i===t&&(e.off("click",o),e.off("keydown",this.keybindFilter),i=null),a){var s=r.get(a);if(s){var l=s.reduce(function(e,n){return n.scope===t?n:e},{});l&&r.remove(a,l)}}};var o=function(e){if(i&&i.isOpen&&!(e&&"disabled"===i.getAutoClose()||e&&3===e.which)){var n=i.getToggleElement();if(!(e&&n&&n[0].contains(e.target))){var r=i.getDropdownElement();e&&"outsideClick"===i.getAutoClose()&&r&&r[0].contains(e.target)||(i.focusToggleElement(),i.isOpen=!1,t.$$phase||i.$apply())}}};this.keybindFilter=function(e){if(i){var t=i.getDropdownElement(),n=i.getToggleElement(),r=t&&t[0].contains(e.target),a=n&&n[0].contains(e.target);27===e.which?(e.stopPropagation(),i.focusToggleElement(),o()):i.isKeynavEnabled()&&-1!==[38,40].indexOf(e.which)&&i.isOpen&&(r||a)&&(e.preventDefault(),e.stopPropagation(),i.focusDropdownEntry(e.which))}}}]).controller("UibDropdownController",["$scope","$element","$attrs","$parse","uibDropdownConfig","uibDropdownService","$animate","$uibPosition","$document","$compile","$templateRequest",function(e,t,n,i,r,o,a,s,l,u,c){function p(){t.append(h.dropdownMenu)}var d,f,h=this,m=e.$new(),g=r.appendToOpenClass,v=r.openClass,$=angular.noop,b=n.onToggle?i(n.onToggle):angular.noop,y=!1,w=l.find("body");t.addClass("dropdown"),this.init=function(){n.isOpen&&(f=i(n.isOpen),$=f.assign,e.$watch(f,function(e){m.isOpen=!!e})),y=angular.isDefined(n.keyboardNav)},this.toggle=function(e){return m.isOpen=arguments.length?!!e:!m.isOpen,angular.isFunction($)&&$(m,m.isOpen),m.isOpen},this.isOpen=function(){return m.isOpen},m.getToggleElement=function(){return h.toggleElement},m.getAutoClose=function(){return n.autoClose||"always"},m.getElement=function(){return t},m.isKeynavEnabled=function(){return y},m.focusDropdownEntry=function(e){var n=h.dropdownMenu?angular.element(h.dropdownMenu).find("a"):t.find("ul").eq(0).find("a");switch(e){case 40:angular.isNumber(h.selectedOption)?h.selectedOption=h.selectedOption===n.length-1?h.selectedOption:h.selectedOption+1:h.selectedOption=0;break;case 38:angular.isNumber(h.selectedOption)?h.selectedOption=0===h.selectedOption?0:h.selectedOption-1:h.selectedOption=n.length-1}n[h.selectedOption].focus()},m.getDropdownElement=function(){return h.dropdownMenu},m.focusToggleElement=function(){h.toggleElement&&h.toggleElement[0].focus()},m.$watch("isOpen",function(r,f){var y=null,x=!1;if(angular.isDefined(n.dropdownAppendTo)){var k=i(n.dropdownAppendTo)(m);k&&(y=angular.element(k))}angular.isDefined(n.dropdownAppendToBody)&&(!1!==i(n.dropdownAppendToBody)(m)&&(x=!0));if(x&&!y&&(y=w),y&&h.dropdownMenu&&(r?(y.append(h.dropdownMenu),t.on("$destroy",p)):(t.off("$destroy",p),p())),y&&h.dropdownMenu){var C,T,D,S=s.positionElements(t,h.dropdownMenu,"bottom-left",!0),E=0;if(C={top:S.top+"px",display:r?"block":"none"},(T=h.dropdownMenu.hasClass("dropdown-menu-right"))?(C.left="auto",(D=s.scrollbarPadding(y)).heightOverflow&&D.scrollbarWidth&&(E=D.scrollbarWidth),C.right=window.innerWidth-E-(S.left+t.prop("offsetWidth"))+"px"):(C.left=S.left+"px",C.right="auto"),!x){var O=s.offset(y);C.top=S.top-O.top+"px",T?C.right=window.innerWidth-(S.left-O.left+t.prop("offsetWidth"))+"px":C.left=S.left-O.left+"px"}h.dropdownMenu.css(C)}var A=y||t,M=y?g:v,N=A.hasClass(M),I=o.isOnlyOpen(e,y);N===!r&&a[y?I?"removeClass":"addClass":r?"addClass":"removeClass"](A,M).then(function(){angular.isDefined(r)&&r!==f&&b(e,{open:!!r})});if(r)h.dropdownMenuTemplateUrl?c(h.dropdownMenuTemplateUrl).then(function(e){d=m.$new(),u(e.trim())(d,function(e){var t=e;h.dropdownMenu.replaceWith(t),h.dropdownMenu=t,l.on("keydown",o.keybindFilter)})}):l.on("keydown",o.keybindFilter),m.focusToggleElement(),o.open(m,t,y);else{if(o.close(m,t,y),h.dropdownMenuTemplateUrl){d&&d.$destroy();var P=angular.element('<ul class="dropdown-menu"></ul>');h.dropdownMenu.replaceWith(P),h.dropdownMenu=P}h.selectedOption=null}angular.isFunction($)&&$(e,r)})}]).directive("uibDropdown",function(){return{controller:"UibDropdownController",link:function(e,t,n,i){i.init()}}}).directive("uibDropdownMenu",function(){return{restrict:"A",require:"?^uibDropdown",link:function(e,t,n,i){if(i&&!angular.isDefined(n.dropdownNested)){t.addClass("dropdown-menu");var r=n.templateUrl;r&&(i.dropdownMenuTemplateUrl=r),i.dropdownMenu||(i.dropdownMenu=t)}}}}).directive("uibDropdownToggle",function(){return{require:"?^uibDropdown",link:function(e,t,n,i){if(i){t.addClass("dropdown-toggle"),i.toggleElement=t;var r=function(r){r.preventDefault(),t.hasClass("disabled")||n.disabled||e.$apply(function(){i.toggle()})};t.on("click",r),t.attr({"aria-haspopup":!0,"aria-expanded":!1}),e.$watch(i.isOpen,function(e){t.attr("aria-expanded",!!e)}),e.$on("$destroy",function(){t.off("click",r)})}}}}),angular.module("ui.bootstrap.stackedMap",[]).factory("$$stackedMap",function(){return{createNew:function(){var e=[];return{add:function(t,n){e.push({key:t,value:n})},get:function(t){for(var n=0;n<e.length;n++)if(t===e[n].key)return e[n]},keys:function(){for(var t=[],n=0;n<e.length;n++)t.push(e[n].key);return t},top:function(){return e[e.length-1]},remove:function(t){for(var n=-1,i=0;i<e.length;i++)if(t===e[i].key){n=i;break}return e.splice(n,1)[0]},removeTop:function(){return e.pop()},length:function(){return e.length}}}}}),angular.module("ui.bootstrap.modal",["ui.bootstrap.multiMap","ui.bootstrap.stackedMap","ui.bootstrap.position"]).provider("$uibResolve",function(){var e=this;this.resolver=null,this.setResolver=function(e){this.resolver=e},this.$get=["$injector","$q",function(t,n){var i=e.resolver?t.get(e.resolver):null;return{resolve:function(e,r,o,a){if(i)return i.resolve(e,r,o,a);var s=[];return angular.forEach(e,function(e){angular.isFunction(e)||angular.isArray(e)?s.push(n.resolve(t.invoke(e))):angular.isString(e)?s.push(n.resolve(t.get(e))):s.push(n.resolve(e))}),n.all(s).then(function(t){var n={},i=0;return angular.forEach(e,function(e,r){n[r]=t[i++]}),n})}}}]}).directive("uibModalBackdrop",["$animate","$injector","$uibModalStack",function(e,t,n){function i(t,i,r){r.modalInClass&&(e.addClass(i,r.modalInClass),t.$on(n.NOW_CLOSING_EVENT,function(n,o){var a=o();t.modalOptions.animation?e.removeClass(i,r.modalInClass).then(a):a()}))}return{restrict:"A",compile:function(e,t){return e.addClass(t.backdropClass),i}}}]).directive("uibModalWindow",["$uibModalStack","$q","$animateCss","$document",function(e,t,n,i){return{scope:{index:"@"},restrict:"A",transclude:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/modal/window.html"},link:function(r,o,a){o.addClass(a.windowTopClass||""),r.size=a.size,r.close=function(t){var n=e.getTop();n&&n.value.backdrop&&"static"!==n.value.backdrop&&t.target===t.currentTarget&&(t.preventDefault(),t.stopPropagation(),e.dismiss(n.key,"backdrop click"))},o.on("click",r.close),r.$isRendered=!0;var s=t.defer();r.$$postDigest(function(){s.resolve()}),s.promise.then(function(){var s=null;a.modalInClass&&(s=n(o,{addClass:a.modalInClass}).start(),r.$on(e.NOW_CLOSING_EVENT,function(e,t){var i=t();n(o,{removeClass:a.modalInClass}).start().then(i)})),t.when(s).then(function(){var t=e.getTop();if(t&&e.modalRendered(t.key),!i[0].activeElement||!o[0].contains(i[0].activeElement)){var n=o[0].querySelector("[autofocus]");n?n.focus():o[0].focus()}})})}}}]).directive("uibModalAnimationClass",function(){return{compile:function(e,t){t.modalAnimation&&e.addClass(t.uibModalAnimationClass)}}}).directive("uibModalTransclude",["$animate",function(e){return{link:function(t,n,i,r,o){o(t.$parent,function(t){n.empty(),e.enter(t,n)})}}}]).factory("$uibModalStack",["$animate","$animateCss","$document","$compile","$rootScope","$q","$$multiMap","$$stackedMap","$uibPosition",function(e,t,n,i,r,o,a,s,l){function u(){for(var e=-1,t=y.keys(),n=0;n<t.length;n++)y.get(t[n]).value.backdrop&&(e=n);return e>-1&&k>e&&(e=k),e}function c(e,t){var n=y.get(e).value,i=n.appendTo;y.remove(e),(C=y.top())&&(k=parseInt(C.value.modalDomEl.attr("index"),10)),d(n.modalDomEl,n.modalScope,function(){var t=n.openedClass||b;w.remove(t,e);var r=w.hasKey(t);i.toggleClass(t,r),!r&&$&&$.heightOverflow&&$.scrollbarWidth&&($.originalRight?i.css({paddingRight:$.originalRight+"px"}):i.css({paddingRight:""}),$=null),p(!0)},n.closedDeferred),function(){if(g&&-1===u()){d(g,v,function(){null}),g=void 0,v=void 0}}(),t&&t.focus?t.focus():i.focus&&i.focus()}function p(e){var t;y.length()>0&&(t=y.top().value).modalDomEl.toggleClass(t.windowTopClass||"",e)}function d(t,n,i,r){var a,s=null;return n.$broadcast(x.NOW_CLOSING_EVENT,function(){return a||(a=o.defer(),s=a.promise),function(){a.resolve()}}),o.when(s).then(function o(){o.done||(o.done=!0,e.leave(t).then(function(){i&&i(),t.remove(),r&&r.resolve()}),n.$destroy())})}function f(e){if(e.isDefaultPrevented())return e;var t=y.top();if(t)switch(e.which){case 27:t.value.keyboard&&(e.preventDefault(),r.$apply(function(){x.dismiss(t.key,"escape key press")}));break;case 9:var n=x.loadFocusElementList(t),i=!1;e.shiftKey?(x.isFocusInFirstItem(e,n)||x.isModalFocused(e,t))&&(i=x.focusLastFocusableElement(n)):x.isFocusInLastItem(e,n)&&(i=x.focusFirstFocusableElement(n)),i&&(e.preventDefault(),e.stopPropagation())}}function h(e,t,n){return!e.value.modalScope.$broadcast("modal.closing",t,n).defaultPrevented}function m(){Array.prototype.forEach.call(document.querySelectorAll("["+T+"]"),function(e){var t=parseInt(e.getAttribute(T),10)-1;e.setAttribute(T,t),t||(e.removeAttribute(T),e.removeAttribute("aria-hidden"))})}var g,v,$,b="modal-open",y=s.createNew(),w=a.createNew(),x={NOW_CLOSING_EVENT:"modal.stack.now-closing"},k=0,C=null,T="data-bootstrap-modal-aria-hidden-count",D=/[A-Z]/g;return r.$watch(u,function(e){v&&(v.index=e)}),n.on("keydown",f),r.$on("$destroy",function(){n.off("keydown",f)}),x.open=function(t,o){var a=n[0].activeElement,s=o.openedClass||b;p(!1),C=y.top(),y.add(t,{deferred:o.deferred,renderDeferred:o.renderDeferred,closedDeferred:o.closedDeferred,modalScope:o.scope,backdrop:o.backdrop,keyboard:o.keyboard,openedClass:o.openedClass,windowTopClass:o.windowTopClass,animation:o.animation,appendTo:o.appendTo}),w.put(s,t);var c,d=o.appendTo,f=u();f>=0&&!g&&((v=r.$new(!0)).modalOptions=o,v.index=f,(g=angular.element('<div uib-modal-backdrop="modal-backdrop"></div>')).attr({class:"modal-backdrop","ng-style":"{'z-index': 1040 + (index && 1 || 0) + index*10}","uib-modal-animation-class":"fade","modal-in-class":"in"}),o.backdropClass&&g.addClass(o.backdropClass),o.animation&&g.attr("modal-animation","true"),i(g)(v),e.enter(g,d),l.isScrollable(d)&&(($=l.scrollbarPadding(d)).heightOverflow&&$.scrollbarWidth&&d.css({paddingRight:$.right+"px"}))),o.component?(c=document.createElement(function(e){return e.replace(D,function(e,t){return(t?"-":"")+e.toLowerCase()})}(o.component.name)),(c=angular.element(c)).attr({resolve:"$resolve","modal-instance":"$uibModalInstance",close:"$close($value)",dismiss:"$dismiss($value)"})):c=o.content,k=C?parseInt(C.value.modalDomEl.attr("index"),10)+1:0;var h=angular.element('<div uib-modal-window="modal-window"></div>');h.attr({class:"modal","template-url":o.windowTemplateUrl,"window-top-class":o.windowTopClass,role:"dialog","aria-labelledby":o.ariaLabelledBy,"aria-describedby":o.ariaDescribedBy,size:o.size,index:k,animate:"animate","ng-style":"{'z-index': 1050 + $$topModalIndex*10, display: 'block'}",tabindex:-1,"uib-modal-animation-class":"fade","modal-in-class":"in"}).append(c),o.windowClass&&h.addClass(o.windowClass),o.animation&&h.attr("modal-animation","true"),d.addClass(s),o.scope&&(o.scope.$$topModalIndex=k),e.enter(i(h)(o.scope),d),y.top().value.modalDomEl=h,y.top().value.modalOpener=a,function e(t){if(t&&"BODY"!==t[0].tagName)return function(e){var t=e.parent()?e.parent().children():[];return Array.prototype.filter.call(t,function(t){return t!==e[0]})}(t).forEach(function(e){var t="true"===e.getAttribute("aria-hidden"),n=parseInt(e.getAttribute(T),10);n||(n=t?1:0),e.setAttribute(T,n+1),e.setAttribute("aria-hidden","true")}),e(t.parent())}(h)},x.close=function(e,t){var n=y.get(e);return m(),n&&h(n,t,!0)?(n.value.modalScope.$$uibDestructionScheduled=!0,n.value.deferred.resolve(t),c(e,n.value.modalOpener),!0):!n},x.dismiss=function(e,t){var n=y.get(e);return m(),n&&h(n,t,!1)?(n.value.modalScope.$$uibDestructionScheduled=!0,n.value.deferred.reject(t),c(e,n.value.modalOpener),!0):!n},x.dismissAll=function(e){for(var t=this.getTop();t&&this.dismiss(t.key,e);)t=this.getTop()},x.getTop=function(){return y.top()},x.modalRendered=function(e){var t=y.get(e);t&&t.value.renderDeferred.resolve()},x.focusFirstFocusableElement=function(e){return e.length>0&&(e[0].focus(),!0)},x.focusLastFocusableElement=function(e){return e.length>0&&(e[e.length-1].focus(),!0)},x.isModalFocused=function(e,t){if(e&&t){var n=t.value.modalDomEl;if(n&&n.length)return(e.target||e.srcElement)===n[0]}return!1},x.isFocusInFirstItem=function(e,t){return t.length>0&&(e.target||e.srcElement)===t[0]},x.isFocusInLastItem=function(e,t){return t.length>0&&(e.target||e.srcElement)===t[t.length-1]},x.loadFocusElementList=function(e){if(e){var t=e.value.modalDomEl;if(t&&t.length){var n=t[0].querySelectorAll("a[href], area[href], input:not([disabled]):not([tabindex='-1']), button:not([disabled]):not([tabindex='-1']),select:not([disabled]):not([tabindex='-1']), textarea:not([disabled]):not([tabindex='-1']), iframe, object, embed, *[tabindex]:not([tabindex='-1']), *[contenteditable=true]");return n?Array.prototype.filter.call(n,function(e){return function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)}(e)}):n}}},x}]).provider("$uibModal",function(){var e={options:{animation:!0,backdrop:!0,keyboard:!0},$get:["$rootScope","$q","$document","$templateRequest","$controller","$uibResolve","$uibModalStack",function(t,n,i,r,o,a,s){function l(e){return e.template?n.when(e.template):r(angular.isFunction(e.templateUrl)?e.templateUrl():e.templateUrl)}var u={},c=null;return u.getPromiseChain=function(){return c},u.open=function(r){function u(){return p}var p,d,f=n.defer(),h=n.defer(),m=n.defer(),g=n.defer(),v={result:f.promise,opened:h.promise,closed:m.promise,rendered:g.promise,close:function(e){return s.close(v,e)},dismiss:function(e){return s.dismiss(v,e)}};if((r=angular.extend({},e.options,r)).resolve=r.resolve||{},r.appendTo=r.appendTo||i.find("body").eq(0),!r.appendTo.length)throw new Error("appendTo element not found. Make sure that the element passed is in DOM.");if(!r.component&&!r.template&&!r.templateUrl)throw new Error("One of component or template or templateUrl options is required.");return p=r.component?n.when(a.resolve(r.resolve,{},null,null)):n.all([l(r),a.resolve(r.resolve,{},null,null)]),d=c=n.all([c]).then(u,u).then(function(e){function n(t,n,i,r){t.$scope=a,t.$scope.$resolve={},i?t.$scope.$uibModalInstance=v:t.$uibModalInstance=v;var o=n?e[1]:e;angular.forEach(o,function(e,n){r&&(t[n]=e),t.$scope.$resolve[n]=e})}var i=r.scope||t,a=i.$new();a.$close=v.close,a.$dismiss=v.dismiss,a.$on("$destroy",function(){a.$$uibDestructionScheduled||a.$dismiss("$uibUnscheduledDestruction")});var l,u,c={scope:a,deferred:f,renderDeferred:g,closedDeferred:m,animation:r.animation,backdrop:r.backdrop,keyboard:r.keyboard,backdropClass:r.backdropClass,windowTopClass:r.windowTopClass,windowClass:r.windowClass,windowTemplateUrl:r.windowTemplateUrl,ariaLabelledBy:r.ariaLabelledBy,ariaDescribedBy:r.ariaDescribedBy,size:r.size,openedClass:r.openedClass,appendTo:r.appendTo},p={},d={};r.component?(n(p,!1,!0,!1),p.name=r.component,c.component=p):r.controller&&(n(d,!0,!1,!0),u=o(r.controller,d,!0,r.controllerAs),r.controllerAs&&r.bindToController&&((l=u.instance).$close=a.$close,l.$dismiss=a.$dismiss,angular.extend(l,{$resolve:d.$scope.$resolve},i)),l=u(),angular.isFunction(l.$onInit)&&l.$onInit()),r.component||(c.content=e[0]),s.open(v,c),h.resolve(!0)},function(e){h.reject(e),f.reject(e)}).finally(function(){c===d&&(c=null)}),v},u}]};return e}),angular.module("ui.bootstrap.paging",[]).factory("uibPaging",["$parse",function(e){return{create:function(t,n,i){t.setNumPages=i.numPages?e(i.numPages).assign:angular.noop,t.ngModelCtrl={$setViewValue:angular.noop},t._watchers=[],t.init=function(e,r){t.ngModelCtrl=e,t.config=r,e.$render=function(){t.render()},i.itemsPerPage?t._watchers.push(n.$parent.$watch(i.itemsPerPage,function(e){t.itemsPerPage=parseInt(e,10),n.totalPages=t.calculateTotalPages(),t.updatePage()})):t.itemsPerPage=r.itemsPerPage,n.$watch("totalItems",function(e,i){(angular.isDefined(e)||e!==i)&&(n.totalPages=t.calculateTotalPages(),t.updatePage())})},t.calculateTotalPages=function(){var e=t.itemsPerPage<1?1:Math.ceil(n.totalItems/t.itemsPerPage);return Math.max(e||0,1)},t.render=function(){n.page=parseInt(t.ngModelCtrl.$viewValue,10)||1},n.selectPage=function(e,i){i&&i.preventDefault(),(!n.ngDisabled||!i)&&n.page!==e&&e>0&&e<=n.totalPages&&(i&&i.target&&i.target.blur(),t.ngModelCtrl.$setViewValue(e),t.ngModelCtrl.$render())},n.getText=function(e){return n[e+"Text"]||t.config[e+"Text"]},n.noPrevious=function(){return 1===n.page},n.noNext=function(){return n.page===n.totalPages},t.updatePage=function(){t.setNumPages(n.$parent,n.totalPages),n.page>n.totalPages?n.selectPage(n.totalPages):t.ngModelCtrl.$render()},n.$on("$destroy",function(){for(;t._watchers.length;)t._watchers.shift()()})}}}]),angular.module("ui.bootstrap.pager",["ui.bootstrap.paging","ui.bootstrap.tabindex"]).controller("UibPagerController",["$scope","$attrs","uibPaging","uibPagerConfig",function(e,t,n,i){e.align=angular.isDefined(t.align)?e.$parent.$eval(t.align):i.align,n.create(this,e,t)}]).constant("uibPagerConfig",{itemsPerPage:10,previousText:"« Previous",nextText:"Next »",align:!0}).directive("uibPager",["uibPagerConfig",function(e){return{scope:{totalItems:"=",previousText:"@",nextText:"@",ngDisabled:"="},require:["uibPager","?ngModel"],restrict:"A",controller:"UibPagerController",controllerAs:"pager",templateUrl:function(e,t){return t.templateUrl||"uib/template/pager/pager.html"},link:function(t,n,i,r){n.addClass("pager");var o=r[0],a=r[1];a&&o.init(a,e)}}}]),angular.module("ui.bootstrap.pagination",["ui.bootstrap.paging","ui.bootstrap.tabindex"]).controller("UibPaginationController",["$scope","$attrs","$parse","uibPaging","uibPaginationConfig",function(e,t,n,i,r){function o(e,t,n){return{number:e,text:t,active:n}}var a=this,s=angular.isDefined(t.maxSize)?e.$parent.$eval(t.maxSize):r.maxSize,l=angular.isDefined(t.rotate)?e.$parent.$eval(t.rotate):r.rotate,u=angular.isDefined(t.forceEllipses)?e.$parent.$eval(t.forceEllipses):r.forceEllipses,c=angular.isDefined(t.boundaryLinkNumbers)?e.$parent.$eval(t.boundaryLinkNumbers):r.boundaryLinkNumbers,p=angular.isDefined(t.pageLabel)?function(n){return e.$parent.$eval(t.pageLabel,{$page:n})}:angular.identity;e.boundaryLinks=angular.isDefined(t.boundaryLinks)?e.$parent.$eval(t.boundaryLinks):r.boundaryLinks,e.directionLinks=angular.isDefined(t.directionLinks)?e.$parent.$eval(t.directionLinks):r.directionLinks,t.$set("role","menu"),i.create(this,e,t),t.maxSize&&a._watchers.push(e.$parent.$watch(n(t.maxSize),function(e){s=parseInt(e,10),a.render()}));var d=this.render;this.render=function(){d(),e.page>0&&e.page<=e.totalPages&&(e.pages=function(e,t){var n=[],i=1,r=t,a=angular.isDefined(s)&&t>s;a&&(l?(r=(i=Math.max(e-Math.floor(s/2),1))+s-1)>t&&(i=(r=t)-s+1):(i=(Math.ceil(e/s)-1)*s+1,r=Math.min(i+s-1,t)));for(var d=i;r>=d;d++){var f=o(d,p(d),d===e);n.push(f)}if(a&&s>0&&(!l||u||c)){if(i>1){if(!c||i>3){var h=o(i-1,"...",!1);n.unshift(h)}if(c){if(3===i){var m=o(2,"2",!1);n.unshift(m)}var g=o(1,"1",!1);n.unshift(g)}}if(t>r){if(!c||t-2>r){var v=o(r+1,"...",!1);n.push(v)}if(c){if(r===t-2){var $=o(t-1,t-1,!1);n.push($)}var b=o(t,t,!1);n.push(b)}}}return n}(e.page,e.totalPages))}}]).constant("uibPaginationConfig",{itemsPerPage:10,boundaryLinks:!1,boundaryLinkNumbers:!1,directionLinks:!0,firstText:"First",previousText:"Previous",nextText:"Next",lastText:"Last",rotate:!0,forceEllipses:!1}).directive("uibPagination",["$parse","uibPaginationConfig",function(e,t){return{scope:{totalItems:"=",firstText:"@",previousText:"@",nextText:"@",lastText:"@",ngDisabled:"="},require:["uibPagination","?ngModel"],restrict:"A",controller:"UibPaginationController",controllerAs:"pagination",templateUrl:function(e,t){return t.templateUrl||"uib/template/pagination/pagination.html"},link:function(e,n,i,r){n.addClass("pagination");var o=r[0],a=r[1];a&&o.init(a,t)}}}]),angular.module("ui.bootstrap.tooltip",["ui.bootstrap.position","ui.bootstrap.stackedMap"]).provider("$uibTooltip",function(){var e={placement:"top",placementClassPrefix:"",animation:!0,popupDelay:0,popupCloseDelay:0,useContentExp:!1},t={mouseenter:"mouseleave",click:"click",outsideClick:"outsideClick",focus:"blur",none:""},n={};this.options=function(e){angular.extend(n,e)},this.setTriggers=function(e){angular.extend(t,e)},this.$get=["$window","$compile","$timeout","$document","$uibPosition","$interpolate","$rootScope","$parse","$$stackedMap",function(i,r,o,a,s,l,u,c,p){function d(e){if(27===e.which){var t=f.top();t&&(t.value.close(),t=null)}}var f=p.createNew();return a.on("keyup",d),u.$on("$destroy",function(){a.off("keyup",d)}),function(i,u,p,d){function h(e){var n=(e||d.trigger||p).split(" "),i=n.map(function(e){return t[e]||e});return{show:n,hide:i}}d=angular.extend({},e,n,d);var m=function(e){return e.replace(/[A-Z]/g,function(e,t){return(t?"-":"")+e.toLowerCase()})}(i),g=l.startSymbol(),v=l.endSymbol(),$="<div "+m+'-popup uib-title="'+g+"title"+v+'" '+(d.useContentExp?'content-exp="contentExp()" ':'content="'+g+"content"+v+'" ')+'origin-scope="origScope" class="uib-position-measure '+u+'" tooltip-animation-class="fade"uib-tooltip-classes ng-class="{ in: isOpen }" ></div>';return{compile:function(e,t){var n=r($);return function(e,t,r,l){function p(){V.isOpen?g():m()}function m(){j&&!e.$eval(r[u+"Enable"])||(y(),function(){V.title=r[u+"Title"],V.content=U?U(e):r[i],V.popupClass=r[u+"Class"],V.placement=angular.isDefined(r[u+"Placement"])?r[u+"Placement"]:d.placement;var t=s.parsePlacement(V.placement);N=t[1]?t[0]+"-"+t[1]:t[0];var n=parseInt(r[u+"PopupDelay"],10),o=parseInt(r[u+"PopupCloseDelay"],10);V.popupDelay=isNaN(n)?d.popupDelay:n,V.popupCloseDelay=isNaN(o)?d.popupCloseDelay:o}(),V.popupDelay?E||(E=o(v,V.popupDelay,!1)):v())}function g(){$(),V.popupCloseDelay?O||(O=o(b,V.popupCloseDelay,!1)):b()}function v(){return $(),y(),V.content?(T||(D=V.$new(),T=n(D,function(e){I?a.find("body").append(e):t.after(e)}),f.add(V,{close:b}),L.length=0,U?(L.push(e.$watch(U,function(e){V.content=e,!e&&V.isOpen&&b()})),L.push(D.$watch(function(){R||(R=!0,D.$$postDigest(function(){R=!1,V&&V.isOpen&&H()}))}))):L.push(r.$observe(i,function(e){V.content=e,!e&&V.isOpen?b():H()})),L.push(r.$observe(u+"Title",function(e){V.title=e,V.isOpen&&H()})),L.push(r.$observe(u+"Placement",function(e){V.placement=e||d.placement,V.isOpen&&H()}))),void V.$evalAsync(function(){V.isOpen=!0,x(!0),H()})):angular.noop}function $(){E&&(o.cancel(E),E=null),A&&(o.cancel(A),A=null)}function b(){V&&V.$evalAsync(function(){V&&(V.isOpen=!1,x(!1),V.animation?S||(S=o(w,150,!1)):w())})}function y(){O&&(o.cancel(O),O=null),S&&(o.cancel(S),S=null)}function w(){$(),y(),L.length&&(angular.forEach(L,function(e){e()}),L.length=0),T&&(T.remove(),T=null,M&&o.cancel(M)),f.remove(V),D&&(D.$destroy(),D=null)}function x(t){F&&angular.isFunction(F.assign)&&F.assign(e,t)}function k(e){V&&V.isOpen&&T&&(t[0].contains(e.target)||T[0].contains(e.target)||g())}function C(e){27===e.which&&g()}var T,D,S,E,O,A,M,N,I=!!angular.isDefined(d.appendToBody)&&d.appendToBody,P=h(void 0),j=angular.isDefined(r[u+"Enable"]),V=e.$new(!0),R=!1,F=!!angular.isDefined(r[u+"IsOpen"])&&c(r[u+"IsOpen"]),U=!!d.useContentExp&&c(r[i]),L=[],H=function(){T&&T.html()&&(A||(A=o(function(){var e=s.positionElements(t,T,V.placement,I),n=angular.isDefined(T.offsetHeight)?T.offsetHeight:T.prop("offsetHeight"),i=I?s.offset(t):s.position(t);T.css({top:e.top+"px",left:e.left+"px"});var r=e.placement.split("-");T.hasClass(r[0])||(T.removeClass(N.split("-")[0]),T.addClass(r[0])),T.hasClass(d.placementClassPrefix+e.placement)||(T.removeClass(d.placementClassPrefix+N),T.addClass(d.placementClassPrefix+e.placement)),M=o(function(){var e=angular.isDefined(T.offsetHeight)?T.offsetHeight:T.prop("offsetHeight"),t=s.adjustTop(r,i,n,e);t&&T.css(t),M=null},0,!1),T.hasClass("uib-position-measure")?(s.positionArrow(T,e.placement),T.removeClass("uib-position-measure")):N!==e.placement&&s.positionArrow(T,e.placement),N=e.placement,A=null},0,!1)))};V.origScope=e,V.isOpen=!1,V.contentExp=function(){return V.content},r.$observe("disabled",function(e){e&&$(),e&&V.isOpen&&b()}),F&&e.$watch(F,function(e){V&&!e===V.isOpen&&p()});var q=function(){P.show.forEach(function(e){"outsideClick"===e?t.off("click",p):(t.off(e,m),t.off(e,p)),t.off("keypress",C)}),P.hide.forEach(function(e){"outsideClick"===e?a.off("click",k):t.off(e,g)})};!function(){var n=[],i=[],o=e.$eval(r[u+"Trigger"]);q(),angular.isObject(o)?(Object.keys(o).forEach(function(e){n.push(e),i.push(o[e])}),P={show:n,hide:i}):P=h(o),"none"!==P.show&&P.show.forEach(function(e,n){"outsideClick"===e?(t.on("click",p),a.on("click",k)):e===P.hide[n]?t.on(e,p):e&&(t.on(e,m),t.on(P.hide[n],g)),t.on("keypress",C)})}();var B=e.$eval(r[u+"Animation"]);V.animation=angular.isDefined(B)?!!B:d.animation;var _,W=u+"AppendToBody";_=W in r&&void 0===r[W]||e.$eval(r[W]),I=angular.isDefined(_)?_:I,e.$on("$destroy",function(){q(),w(),V=null})}}}}}]}).directive("uibTooltipTemplateTransclude",["$animate","$sce","$compile","$templateRequest",function(e,t,n,i){return{link:function(r,o,a){var s,l,u,c=r.$eval(a.tooltipTemplateTranscludeScope),p=0,d=function(){l&&(l.remove(),l=null),s&&(s.$destroy(),s=null),u&&(e.leave(u).then(function(){l=null}),l=u,u=null)};r.$watch(t.parseAsResourceUrl(a.uibTooltipTemplateTransclude),function(t){var a=++p;t?(i(t,!0).then(function(i){if(a===p){var r=c.$new(),l=n(i)(r,function(t){d(),e.enter(t,o)});u=l,(s=r).$emit("$includeContentLoaded",t)}},function(){a===p&&(d(),r.$emit("$includeContentError",t))}),r.$emit("$includeContentRequested",t)):d()}),r.$on("$destroy",d)}}}]).directive("uibTooltipClasses",["$uibPosition",function(e){return{restrict:"A",link:function(t,n,i){if(t.placement){var r=e.parsePlacement(t.placement);n.addClass(r[0])}t.popupClass&&n.addClass(t.popupClass),t.animation&&n.addClass(i.tooltipAnimationClass)}}}]).directive("uibTooltipPopup",function(){return{restrict:"A",scope:{content:"@"},templateUrl:"uib/template/tooltip/tooltip-popup.html"}}).directive("uibTooltip",["$uibTooltip",function(e){return e("uibTooltip","tooltip","mouseenter")}]).directive("uibTooltipTemplatePopup",function(){return{restrict:"A",scope:{contentExp:"&",originScope:"&"},templateUrl:"uib/template/tooltip/tooltip-template-popup.html"}}).directive("uibTooltipTemplate",["$uibTooltip",function(e){return e("uibTooltipTemplate","tooltip","mouseenter",{useContentExp:!0})}]).directive("uibTooltipHtmlPopup",function(){return{restrict:"A",scope:{contentExp:"&"},templateUrl:"uib/template/tooltip/tooltip-html-popup.html"}}).directive("uibTooltipHtml",["$uibTooltip",function(e){return e("uibTooltipHtml","tooltip","mouseenter",{useContentExp:!0})}]),angular.module("ui.bootstrap.popover",["ui.bootstrap.tooltip"]).directive("uibPopoverTemplatePopup",function(){return{restrict:"A",scope:{uibTitle:"@",contentExp:"&",originScope:"&"},templateUrl:"uib/template/popover/popover-template.html"}}).directive("uibPopoverTemplate",["$uibTooltip",function(e){return e("uibPopoverTemplate","popover","click",{useContentExp:!0})}]).directive("uibPopoverHtmlPopup",function(){return{restrict:"A",scope:{contentExp:"&",uibTitle:"@"},templateUrl:"uib/template/popover/popover-html.html"}}).directive("uibPopoverHtml",["$uibTooltip",function(e){return e("uibPopoverHtml","popover","click",{useContentExp:!0})}]).directive("uibPopoverPopup",function(){return{restrict:"A",scope:{uibTitle:"@",content:"@"},templateUrl:"uib/template/popover/popover.html"}}).directive("uibPopover",["$uibTooltip",function(e){return e("uibPopover","popover","click")}]),angular.module("ui.bootstrap.progressbar",[]).constant("uibProgressConfig",{animate:!0,max:100}).controller("UibProgressController",["$scope","$attrs","uibProgressConfig",function(e,t,n){function i(){return angular.isDefined(e.maxParam)?e.maxParam:n.max}var r=this,o=angular.isDefined(t.animate)?e.$parent.$eval(t.animate):n.animate;this.bars=[],e.max=i(),this.addBar=function(e,t,n){o||t.css({transition:"none"}),this.bars.push(e),e.max=i(),e.title=n&&angular.isDefined(n.title)?n.title:"progressbar",e.$watch("value",function(t){e.recalculatePercentage()}),e.recalculatePercentage=function(){var t=r.bars.reduce(function(e,t){return t.percent=+(100*t.value/t.max).toFixed(2),e+t.percent},0);t>100&&(e.percent-=t-100)},e.$on("$destroy",function(){t=null,r.removeBar(e)})},this.removeBar=function(e){this.bars.splice(this.bars.indexOf(e),1),this.bars.forEach(function(e){e.recalculatePercentage()})},e.$watch("maxParam",function(e){r.bars.forEach(function(e){e.max=i(),e.recalculatePercentage()})})}]).directive("uibProgress",function(){return{replace:!0,transclude:!0,controller:"UibProgressController",require:"uibProgress",scope:{maxParam:"=?max"},templateUrl:"uib/template/progressbar/progress.html"}}).directive("uibBar",function(){return{replace:!0,transclude:!0,require:"^uibProgress",scope:{value:"=",type:"@"},templateUrl:"uib/template/progressbar/bar.html",link:function(e,t,n,i){i.addBar(e,t,n)}}}).directive("uibProgressbar",function(){return{replace:!0,transclude:!0,controller:"UibProgressController",scope:{value:"=",maxParam:"=?max",type:"@"},templateUrl:"uib/template/progressbar/progressbar.html",link:function(e,t,n,i){i.addBar(e,angular.element(t.children()[0]),{title:n.title})}}}),angular.module("ui.bootstrap.rating",[]).constant("uibRatingConfig",{max:5,stateOn:null,stateOff:null,enableReset:!0,titles:["one","two","three","four","five"]}).controller("UibRatingController",["$scope","$attrs","uibRatingConfig",function(e,t,n){var i={$setViewValue:angular.noop},r=this;this.init=function(r){(i=r).$render=this.render,i.$formatters.push(function(e){return angular.isNumber(e)&&e<<0!==e&&(e=Math.round(e)),e}),this.stateOn=angular.isDefined(t.stateOn)?e.$parent.$eval(t.stateOn):n.stateOn,this.stateOff=angular.isDefined(t.stateOff)?e.$parent.$eval(t.stateOff):n.stateOff,this.enableReset=angular.isDefined(t.enableReset)?e.$parent.$eval(t.enableReset):n.enableReset;var o=angular.isDefined(t.titles)?e.$parent.$eval(t.titles):n.titles;this.titles=angular.isArray(o)&&o.length>0?o:n.titles;var a=angular.isDefined(t.ratingStates)?e.$parent.$eval(t.ratingStates):new Array(angular.isDefined(t.max)?e.$parent.$eval(t.max):n.max);e.range=this.buildTemplateObjects(a)},this.buildTemplateObjects=function(e){for(var t=0,n=e.length;n>t;t++)e[t]=angular.extend({index:t},{stateOn:this.stateOn,stateOff:this.stateOff,title:this.getTitle(t)},e[t]);return e},this.getTitle=function(e){return e>=this.titles.length?e+1:this.titles[e]},e.rate=function(t){if(!e.readonly&&t>=0&&t<=e.range.length){var n=r.enableReset&&i.$viewValue===t?0:t;i.$setViewValue(n),i.$render()}},e.enter=function(t){e.readonly||(e.value=t),e.onHover({value:t})},e.reset=function(){e.value=i.$viewValue,e.onLeave()},e.onKeydown=function(t){/(37|38|39|40)/.test(t.which)&&(t.preventDefault(),t.stopPropagation(),e.rate(e.value+(38===t.which||39===t.which?1:-1)))},this.render=function(){e.value=i.$viewValue,e.title=r.getTitle(e.value-1)}}]).directive("uibRating",function(){return{require:["uibRating","ngModel"],restrict:"A",scope:{readonly:"=?readOnly",onHover:"&",onLeave:"&"},controller:"UibRatingController",templateUrl:"uib/template/rating/rating.html",link:function(e,t,n,i){var r=i[0],o=i[1];r.init(o)}}}),angular.module("ui.bootstrap.tabs",[]).controller("UibTabsetController",["$scope",function(e){function t(e){for(var t=0;t<r.tabs.length;t++)if(r.tabs[t].index===e)return t}var n,i,r=this;r.tabs=[],r.select=function(e,o){if(!i){var a=t(n),s=r.tabs[a];if(s){if(s.tab.onDeselect({$event:o,$selectedIndex:e}),o&&o.isDefaultPrevented())return;s.tab.active=!1}var l=r.tabs[e];l?(l.tab.onSelect({$event:o}),l.tab.active=!0,r.active=l.index,n=l.index):!l&&angular.isDefined(n)&&(r.active=null,n=null)}},r.addTab=function(e){if(r.tabs.push({tab:e,index:e.index}),r.tabs.sort(function(e,t){return e.index>t.index?1:e.index<t.index?-1:0}),e.index===r.active||!angular.isDefined(r.active)&&1===r.tabs.length){var n=t(e.index);r.select(n)}},r.removeTab=function(e){for(var t,n=0;n<r.tabs.length;n++)if(r.tabs[n].tab===e){t=n;break}if(r.tabs[t].index===r.active){var i=t===r.tabs.length-1?t-1:t+1%r.tabs.length;r.select(i)}r.tabs.splice(t,1)},e.$watch("tabset.active",function(e){angular.isDefined(e)&&e!==n&&r.select(t(e))}),e.$on("$destroy",function(){i=!0})}]).directive("uibTabset",function(){return{transclude:!0,replace:!0,scope:{},bindToController:{active:"=?",type:"@"},controller:"UibTabsetController",controllerAs:"tabset",templateUrl:function(e,t){return t.templateUrl||"uib/template/tabs/tabset.html"},link:function(e,t,n){e.vertical=!!angular.isDefined(n.vertical)&&e.$parent.$eval(n.vertical),e.justified=!!angular.isDefined(n.justified)&&e.$parent.$eval(n.justified)}}}).directive("uibTab",["$parse",function(e){return{require:"^uibTabset",replace:!0,templateUrl:function(e,t){return t.templateUrl||"uib/template/tabs/tab.html"},transclude:!0,scope:{heading:"@",index:"=?",classes:"@?",onSelect:"&select",onDeselect:"&deselect"},controller:function(){},controllerAs:"tab",link:function(t,n,i,r,o){t.disabled=!1,i.disable&&t.$parent.$watch(e(i.disable),function(e){t.disabled=!!e}),angular.isUndefined(i.index)&&(r.tabs&&r.tabs.length?t.index=Math.max.apply(null,r.tabs.map(function(e){return e.index}))+1:t.index=0),angular.isUndefined(i.classes)&&(t.classes=""),t.select=function(e){if(!t.disabled){for(var n,i=0;i<r.tabs.length;i++)if(r.tabs[i].tab===t){n=i;break}r.select(n,e)}},r.addTab(t),t.$on("$destroy",function(){r.removeTab(t)}),t.$transcludeFn=o}}}]).directive("uibTabHeadingTransclude",function(){return{restrict:"A",require:"^uibTab",link:function(e,t){e.$watch("headingElement",function(e){e&&(t.html(""),t.append(e))})}}}).directive("uibTabContentTransclude",function(){return{restrict:"A",require:"^uibTabset",link:function(e,t,n){var i=e.$eval(n.uibTabContentTransclude).tab;i.$transcludeFn(i.$parent,function(e){angular.forEach(e,function(e){!function(e){return e.tagName&&(e.hasAttribute("uib-tab-heading")||e.hasAttribute("data-uib-tab-heading")||e.hasAttribute("x-uib-tab-heading")||"uib-tab-heading"===e.tagName.toLowerCase()||"data-uib-tab-heading"===e.tagName.toLowerCase()||"x-uib-tab-heading"===e.tagName.toLowerCase()||"uib:tab-heading"===e.tagName.toLowerCase())}(e)?t.append(e):i.headingElement=e})})}}}),angular.module("ui.bootstrap.timepicker",[]).constant("uibTimepickerConfig",{hourStep:1,minuteStep:1,secondStep:1,showMeridian:!0,showSeconds:!1,meridians:null,readonlyInput:!1,mousewheel:!0,arrowkeys:!0,showSpinners:!0,templateUrl:"uib/template/timepicker/timepicker.html"}).controller("UibTimepickerController",["$scope","$element","$attrs","$parse","$log","$locale","uibTimepickerConfig",function(e,t,n,i,r,o,a){function s(){var t=+e.hours;return(e.showMeridian?t>0&&13>t:t>=0&&24>t)&&""!==e.hours?(e.showMeridian&&(12===t&&(t=0),e.meridian===C[1]&&(t+=12)),t):void 0}function l(){var t=+e.minutes;return t>=0&&60>t&&""!==e.minutes?t:void 0}function u(){var t=+e.seconds;return t>=0&&60>t?t:void 0}function c(e,t){return null===e?"":angular.isDefined(e)&&e.toString().length<2&&!t?"0"+e:e.toString()}function p(e){d(),k.$setViewValue(new Date(w)),f(e)}function d(){$&&$.$setValidity("hours",!0),b&&b.$setValidity("minutes",!0),y&&y.$setValidity("seconds",!0),k.$setValidity("time",!0),e.invalidHours=!1,e.invalidMinutes=!1,e.invalidSeconds=!1}function f(t){if(k.$modelValue){var n=w.getHours(),i=w.getMinutes(),r=w.getSeconds();e.showMeridian&&(n=0===n||12===n?12:n%12),e.hours="h"===t?n:c(n,!T),"m"!==t&&(e.minutes=c(i)),e.meridian=w.getHours()<12?C[0]:C[1],"s"!==t&&(e.seconds=c(r)),e.meridian=w.getHours()<12?C[0]:C[1]}else e.hours=null,e.minutes=null,e.seconds=null,e.meridian=C[0]}function h(e){w=g(w,e),p()}function m(e,t){return g(e,60*t)}function g(e,t){var n=new Date(e.getTime()+1e3*t),i=new Date(e);return i.setHours(n.getHours(),n.getMinutes(),n.getSeconds()),i}function v(){return(null===e.hours||""===e.hours)&&(null===e.minutes||""===e.minutes)&&(!e.showSeconds||e.showSeconds&&(null===e.seconds||""===e.seconds))}var $,b,y,w=new Date,x=[],k={$setViewValue:angular.noop},C=angular.isDefined(n.meridians)?e.$parent.$eval(n.meridians):a.meridians||o.DATETIME_FORMATS.AMPMS,T=!angular.isDefined(n.padHours)||e.$parent.$eval(n.padHours);e.tabindex=angular.isDefined(n.tabindex)?n.tabindex:0,t.removeAttr("tabindex"),this.init=function(t,i){(k=t).$render=this.render,k.$formatters.unshift(function(e){return e?new Date(e):null});var r=i.eq(0),o=i.eq(1),s=i.eq(2);$=r.controller("ngModel"),b=o.controller("ngModel"),y=s.controller("ngModel"),(angular.isDefined(n.mousewheel)?e.$parent.$eval(n.mousewheel):a.mousewheel)&&this.setupMousewheelEvents(r,o,s),(angular.isDefined(n.arrowkeys)?e.$parent.$eval(n.arrowkeys):a.arrowkeys)&&this.setupArrowkeyEvents(r,o,s),e.readonlyInput=angular.isDefined(n.readonlyInput)?e.$parent.$eval(n.readonlyInput):a.readonlyInput,this.setupInputEvents(r,o,s)};var D=a.hourStep;n.hourStep&&x.push(e.$parent.$watch(i(n.hourStep),function(e){D=+e}));var S,E,O=a.minuteStep;n.minuteStep&&x.push(e.$parent.$watch(i(n.minuteStep),function(e){O=+e})),x.push(e.$parent.$watch(i(n.min),function(e){var t=new Date(e);S=isNaN(t)?void 0:t})),x.push(e.$parent.$watch(i(n.max),function(e){var t=new Date(e);E=isNaN(t)?void 0:t}));var A=!1;n.ngDisabled&&x.push(e.$parent.$watch(i(n.ngDisabled),function(e){A=e})),e.noIncrementHours=function(){var e=m(w,60*D);return A||e>E||w>e&&S>e},e.noDecrementHours=function(){var e=m(w,60*-D);return A||S>e||e>w&&e>E},e.noIncrementMinutes=function(){var e=m(w,O);return A||e>E||w>e&&S>e},e.noDecrementMinutes=function(){var e=m(w,-O);return A||S>e||e>w&&e>E},e.noIncrementSeconds=function(){var e=g(w,M);return A||e>E||w>e&&S>e},e.noDecrementSeconds=function(){var e=g(w,-M);return A||S>e||e>w&&e>E},e.noToggleMeridian=function(){return w.getHours()<12?A||m(w,720)>E:A||m(w,-720)<S};var M=a.secondStep;n.secondStep&&x.push(e.$parent.$watch(i(n.secondStep),function(e){M=+e})),e.showSeconds=a.showSeconds,n.showSeconds&&x.push(e.$parent.$watch(i(n.showSeconds),function(t){e.showSeconds=!!t})),e.showMeridian=a.showMeridian,n.showMeridian&&x.push(e.$parent.$watch(i(n.showMeridian),function(t){if(e.showMeridian=!!t,k.$error.time){var n=s(),i=l();angular.isDefined(n)&&angular.isDefined(i)&&(w.setHours(n),p())}else f()})),this.setupMousewheelEvents=function(t,n,i){var r=function(e){e.originalEvent&&(e=e.originalEvent);var t=e.wheelDelta?e.wheelDelta:-e.deltaY;return e.detail||t>0};t.on("mousewheel wheel",function(t){A||e.$apply(r(t)?e.incrementHours():e.decrementHours()),t.preventDefault()}),n.on("mousewheel wheel",function(t){A||e.$apply(r(t)?e.incrementMinutes():e.decrementMinutes()),t.preventDefault()}),i.on("mousewheel wheel",function(t){A||e.$apply(r(t)?e.incrementSeconds():e.decrementSeconds()),t.preventDefault()})},this.setupArrowkeyEvents=function(t,n,i){t.on("keydown",function(t){A||(38===t.which?(t.preventDefault(),e.incrementHours(),e.$apply()):40===t.which&&(t.preventDefault(),e.decrementHours(),e.$apply()))}),n.on("keydown",function(t){A||(38===t.which?(t.preventDefault(),e.incrementMinutes(),e.$apply()):40===t.which&&(t.preventDefault(),e.decrementMinutes(),e.$apply()))}),i.on("keydown",function(t){A||(38===t.which?(t.preventDefault(),e.incrementSeconds(),e.$apply()):40===t.which&&(t.preventDefault(),e.decrementSeconds(),e.$apply()))})},this.setupInputEvents=function(t,n,i){if(e.readonlyInput)return e.updateHours=angular.noop,e.updateMinutes=angular.noop,void(e.updateSeconds=angular.noop);var r=function(t,n,i){k.$setViewValue(null),k.$setValidity("time",!1),angular.isDefined(t)&&(e.invalidHours=t,$&&$.$setValidity("hours",!1)),angular.isDefined(n)&&(e.invalidMinutes=n,b&&b.$setValidity("minutes",!1)),angular.isDefined(i)&&(e.invalidSeconds=i,y&&y.$setValidity("seconds",!1))};e.updateHours=function(){var e=s(),t=l();k.$setDirty(),angular.isDefined(e)&&angular.isDefined(t)?(w.setHours(e),w.setMinutes(t),S>w||w>E?r(!0):p("h")):r(!0)},t.on("blur",function(t){k.$setTouched(),v()?d():null===e.hours||""===e.hours?r(!0):!e.invalidHours&&e.hours<10&&e.$apply(function(){e.hours=c(e.hours,!T)})}),e.updateMinutes=function(){var e=l(),t=s();k.$setDirty(),angular.isDefined(e)&&angular.isDefined(t)?(w.setHours(t),w.setMinutes(e),S>w||w>E?r(void 0,!0):p("m")):r(void 0,!0)},n.on("blur",function(t){k.$setTouched(),v()?d():null===e.minutes?r(void 0,!0):!e.invalidMinutes&&e.minutes<10&&e.$apply(function(){e.minutes=c(e.minutes)})}),e.updateSeconds=function(){var e=u();k.$setDirty(),angular.isDefined(e)?(w.setSeconds(e),p("s")):r(void 0,void 0,!0)},i.on("blur",function(t){v()?d():!e.invalidSeconds&&e.seconds<10&&e.$apply(function(){e.seconds=c(e.seconds)})})},this.render=function(){var t=k.$viewValue;isNaN(t)?(k.$setValidity("time",!1),r.error('Timepicker directive: "ng-model" value must be a Date object, a number of milliseconds since 01.01.1970 or a string representing an RFC2822 or ISO 8601 date.')):(t&&(w=t),S>w||w>E?(k.$setValidity("time",!1),e.invalidHours=!0,e.invalidMinutes=!0):d(),f())},e.showSpinners=angular.isDefined(n.showSpinners)?e.$parent.$eval(n.showSpinners):a.showSpinners,e.incrementHours=function(){e.noIncrementHours()||h(60*D*60)},e.decrementHours=function(){e.noDecrementHours()||h(60*-D*60)},e.incrementMinutes=function(){e.noIncrementMinutes()||h(60*O)},e.decrementMinutes=function(){e.noDecrementMinutes()||h(60*-O)},e.incrementSeconds=function(){e.noIncrementSeconds()||h(M)},e.decrementSeconds=function(){e.noDecrementSeconds()||h(-M)},e.toggleMeridian=function(){var t=l(),n=s();e.noToggleMeridian()||(angular.isDefined(t)&&angular.isDefined(n)?h(720*(w.getHours()<12?60:-60)):e.meridian=e.meridian===C[0]?C[1]:C[0])},e.blur=function(){k.$setTouched()},e.$on("$destroy",function(){for(;x.length;)x.shift()()})}]).directive("uibTimepicker",["uibTimepickerConfig",function(e){return{require:["uibTimepicker","?^ngModel"],restrict:"A",controller:"UibTimepickerController",controllerAs:"timepicker",scope:{},templateUrl:function(t,n){return n.templateUrl||e.templateUrl},link:function(e,t,n,i){var r=i[0],o=i[1];o&&r.init(o,t.find("input"))}}}]),angular.module("ui.bootstrap.typeahead",["ui.bootstrap.debounce","ui.bootstrap.position"]).factory("uibTypeaheadParser",["$parse",function(e){var t=/^\s*([\s\S]+?)(?:\s+as\s+([\s\S]+?))?\s+for\s+(?:([\$\w][\$\w\d]*))\s+in\s+([\s\S]+?)$/;return{parse:function(n){var i=n.match(t);if(!i)throw new Error('Expected typeahead specification in form of "_modelValue_ (as _label_)? for _item_ in _collection_" but got "'+n+'".');return{itemName:i[3],source:e(i[4]),viewMapper:e(i[2]||i[1]),modelMapper:e(i[1])}}}}]).controller("UibTypeaheadController",["$scope","$element","$attrs","$compile","$parse","$q","$timeout","$document","$window","$rootScope","$$debounce","$uibPosition","uibTypeaheadParser",function(e,t,n,i,r,o,a,s,l,u,c,p,d){function f(){F.moveInProgress||(F.moveInProgress=!0,F.$digest()),Y()}function h(){F.position=O?p.offset(t):p.position(t),F.position.top+=t.prop("offsetHeight")}var m,g,v=[9,13,27,38,40],$=e.$eval(n.typeaheadMinLength);$||0===$||($=1),e.$watch(n.typeaheadMinLength,function(e){$=e||0===e?e:1});var b=e.$eval(n.typeaheadWaitMs)||0,y=!1!==e.$eval(n.typeaheadEditable);e.$watch(n.typeaheadEditable,function(e){y=!1!==e});var w,x,k=r(n.typeaheadLoading).assign||angular.noop,C=n.typeaheadShouldSelect?r(n.typeaheadShouldSelect):function(e,t){var n=t.$event;return 13===n.which||9===n.which},T=r(n.typeaheadOnSelect),D=!!angular.isDefined(n.typeaheadSelectOnBlur)&&e.$eval(n.typeaheadSelectOnBlur),S=r(n.typeaheadNoResults).assign||angular.noop,E=n.typeaheadInputFormatter?r(n.typeaheadInputFormatter):void 0,O=!!n.typeaheadAppendToBody&&e.$eval(n.typeaheadAppendToBody),A=n.typeaheadAppendTo?e.$eval(n.typeaheadAppendTo):null,M=!1!==e.$eval(n.typeaheadFocusFirst),N=!!n.typeaheadSelectOnExact&&e.$eval(n.typeaheadSelectOnExact),I=r(n.typeaheadIsOpen).assign||angular.noop,P=e.$eval(n.typeaheadShowHint)||!1,j=r(n.ngModel),V=r(n.ngModel+"($$$p)"),R=d.parse(n.uibTypeahead),F=e.$new(),U=e.$on("$destroy",function(){F.$destroy()});F.$on("$destroy",U);var L,H,q="typeahead-"+F.$id+"-"+Math.floor(1e4*Math.random());t.attr({"aria-autocomplete":"list","aria-expanded":!1,"aria-owns":q}),P&&((L=angular.element("<div></div>")).css("position","relative"),t.after(L),(H=t.clone()).attr("placeholder",""),H.attr("tabindex","-1"),H.val(""),H.css({position:"absolute",top:"0px",left:"0px","border-color":"transparent","box-shadow":"none",opacity:1,background:"none 0% 0% / auto repeat scroll padding-box border-box rgb(255, 255, 255)",color:"#999"}),t.css({position:"relative","vertical-align":"top","background-color":"transparent"}),H.attr("id")&&H.removeAttr("id"),L.append(H),H.after(t));var B=angular.element("<div uib-typeahead-popup></div>");B.attr({id:q,matches:"matches",active:"activeIdx",select:"select(activeIdx, evt)","move-in-progress":"moveInProgress",query:"query",position:"position","assign-is-open":"assignIsOpen(isOpen)",debounce:"debounceUpdate"}),angular.isDefined(n.typeaheadTemplateUrl)&&B.attr("template-url",n.typeaheadTemplateUrl),angular.isDefined(n.typeaheadPopupTemplateUrl)&&B.attr("popup-template-url",n.typeaheadPopupTemplateUrl);var _=function(){F.matches=[],F.activeIdx=-1,t.attr("aria-expanded",!1),P&&H.val("")},W=function(e){return q+"-option-"+e};F.$watch("activeIdx",function(e){0>e?t.removeAttr("aria-activedescendant"):t.attr("aria-activedescendant",W(e))});var z=function(n,i){var r={$viewValue:n};k(e,!0),S(e,!1),o.when(R.source(e,r)).then(function(o){var a=n===m.$viewValue;if(a&&w)if(o&&o.length>0){F.activeIdx=M?0:-1,S(e,!1),F.matches.length=0;for(var s=0;s<o.length;s++)r[R.itemName]=o[s],F.matches.push({id:W(s),label:R.viewMapper(F,r),model:o[s]});if(F.query=n,h(),t.attr("aria-expanded",!0),N&&1===F.matches.length&&function(e,t){return!!(F.matches.length>t&&e)&&e.toUpperCase()===F.matches[t].label.toUpperCase()}(n,0)&&(angular.isNumber(F.debounceUpdate)||angular.isObject(F.debounceUpdate)?c(function(){F.select(0,i)},angular.isNumber(F.debounceUpdate)?F.debounceUpdate:F.debounceUpdate.default):F.select(0,i)),P){var l=F.matches[0].label;angular.isString(n)&&n.length>0&&l.slice(0,n.length).toUpperCase()===n.toUpperCase()?H.val(n+l.slice(n.length)):H.val("")}}else _(),S(e,!0);a&&k(e,!1)},function(){_(),k(e,!1),S(e,!0)})};O&&(angular.element(l).on("resize",f),s.find("body").on("scroll",f));var Y=c(function(){F.matches.length&&h(),F.moveInProgress=!1},200);F.moveInProgress=!1,F.query=void 0;var G,K=function(){G&&a.cancel(G)};_(),F.assignIsOpen=function(t){I(e,t)},F.select=function(i,r){var o,s,l={};x=!0,l[R.itemName]=s=F.matches[i].model,o=R.modelMapper(e,l),function(t,n){angular.isFunction(j(e))&&g.getOption("getterSetter")?V(t,{$$$p:n}):j.assign(t,n)}(e,o),m.$setValidity("editable",!0),m.$setValidity("parse",!0),T(e,{$item:s,$model:o,$label:R.viewMapper(e,l),$event:r}),_(),!1!==F.$eval(n.typeaheadFocusOnSelect)&&a(function(){t[0].focus()},0,!1)},t.on("keydown",function(t){if(0!==F.matches.length&&-1!==v.indexOf(t.which)){var n,i=C(e,{$event:t});if(-1===F.activeIdx&&i||9===t.which&&t.shiftKey)return _(),void F.$digest();switch(t.preventDefault(),t.which){case 27:t.stopPropagation(),_(),e.$digest();break;case 38:F.activeIdx=(F.activeIdx>0?F.activeIdx:F.matches.length)-1,F.$digest(),(n=B[0].querySelectorAll(".uib-typeahead-match")[F.activeIdx]).parentNode.scrollTop=n.offsetTop;break;case 40:F.activeIdx=(F.activeIdx+1)%F.matches.length,F.$digest(),(n=B[0].querySelectorAll(".uib-typeahead-match")[F.activeIdx]).parentNode.scrollTop=n.offsetTop;break;default:i&&F.$apply(function(){angular.isNumber(F.debounceUpdate)||angular.isObject(F.debounceUpdate)?c(function(){F.select(F.activeIdx,t)},angular.isNumber(F.debounceUpdate)?F.debounceUpdate:F.debounceUpdate.default):F.select(F.activeIdx,t)})}}}),t.on("focus",function(e){w=!0,0!==$||m.$viewValue||a(function(){z(m.$viewValue,e)},0)}),t.on("blur",function(e){D&&F.matches.length&&-1!==F.activeIdx&&!x&&(x=!0,F.$apply(function(){angular.isObject(F.debounceUpdate)&&angular.isNumber(F.debounceUpdate.blur)?c(function(){F.select(F.activeIdx,e)},F.debounceUpdate.blur):F.select(F.activeIdx,e)})),!y&&m.$error.editable&&(m.$setViewValue(),F.$apply(function(){m.$setValidity("editable",!0),m.$setValidity("parse",!0)}),t.val("")),w=!1,x=!1});var J=function(n){t[0]!==n.target&&3!==n.which&&0!==F.matches.length&&(_(),u.$$phase||e.$digest())};s.on("click",J),e.$on("$destroy",function(){s.off("click",J),(O||A)&&X.remove(),O&&(angular.element(l).off("resize",f),s.find("body").off("scroll",f)),B.remove(),P&&L.remove()});var X=i(B)(F);O?s.find("body").append(X):A?angular.element(A).eq(0).append(X):t.after(X),this.init=function(t){g=function(e){var t;return angular.version.minor<6?(t=e.$options||{}).getOption=function(e){return t[e]}:t=e.$options,t}(m=t),F.debounceUpdate=r(g.getOption("debounce"))(e),m.$parsers.unshift(function(t){return w=!0,0===$||t&&t.length>=$?b>0?(K(),function(e){G=a(function(){z(e)},b)}(t)):z(t):(k(e,!1),K(),_()),y?t:t?void m.$setValidity("editable",!1):(m.$setValidity("editable",!0),null)}),m.$formatters.push(function(t){var n,i={};return y||m.$setValidity("editable",!0),E?(i.$model=t,E(e,i)):(i[R.itemName]=t,n=R.viewMapper(e,i),i[R.itemName]=void 0,n!==R.viewMapper(e,i)?n:t)})}}]).directive("uibTypeahead",function(){return{controller:"UibTypeaheadController",require:["ngModel","uibTypeahead"],link:function(e,t,n,i){i[1].init(i[0])}}}).directive("uibTypeaheadPopup",["$$debounce",function(e){return{scope:{matches:"=",query:"=",active:"=",position:"&",moveInProgress:"=",select:"&",assignIsOpen:"&",debounce:"&"},replace:!0,templateUrl:function(e,t){return t.popupTemplateUrl||"uib/template/typeahead/typeahead-popup.html"},link:function(t,n,i){t.templateUrl=i.templateUrl,t.isOpen=function(){var e=t.matches.length>0;return t.assignIsOpen({isOpen:e}),e},t.isActive=function(e){return t.active===e},t.selectActive=function(e){t.active=e},t.selectMatch=function(n,i){var r=t.debounce();angular.isNumber(r)||angular.isObject(r)?e(function(){t.select({activeIdx:n,evt:i})},angular.isNumber(r)?r:r.default):t.select({activeIdx:n,evt:i})}}}}]).directive("uibTypeaheadMatch",["$templateRequest","$compile","$parse",function(e,t,n){return{scope:{index:"=",match:"=",query:"="},link:function(i,r,o){var a=n(o.templateUrl)(i.$parent)||"uib/template/typeahead/typeahead-match.html";e(a).then(function(e){var n=angular.element(e.trim());r.replaceWith(n),t(n)(i)})}}}]).filter("uibTypeaheadHighlight",["$sce","$injector","$log",function(e,t,n){var i;return i=t.has("$sanitize"),function(t,r){return!i&&function(e){return/<.*>/g.test(e)}(t)&&n.warn("Unsafe use of typeahead please use ngSanitize"),t=r?(""+t).replace(new RegExp(function(e){return e.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")}(r),"gi"),"<strong>$&</strong>"):t,i||(t=e.trustAsHtml(t)),t}}]),angular.module("uib/template/accordion/accordion-group.html",[]).run(["$templateCache",function(e){e.put("uib/template/accordion/accordion-group.html",'<div role="tab" id="{{::headingId}}" aria-selected="{{isOpen}}" class="panel-heading" ng-keypress="toggleOpen($event)">\n  <h4 class="panel-title">\n    <a role="button" data-toggle="collapse" href aria-expanded="{{isOpen}}" aria-controls="{{::panelId}}" tabindex="0" class="accordion-toggle" ng-click="toggleOpen()" uib-accordion-transclude="heading" ng-disabled="isDisabled" uib-tabindex-toggle><span uib-accordion-header ng-class="{\'text-muted\': isDisabled}">{{heading}}</span></a>\n  </h4>\n</div>\n<div id="{{::panelId}}" aria-labelledby="{{::headingId}}" aria-hidden="{{!isOpen}}" role="tabpanel" class="panel-collapse collapse" uib-collapse="!isOpen">\n  <div class="panel-body" ng-transclude></div>\n</div>\n')}]),angular.module("uib/template/accordion/accordion.html",[]).run(["$templateCache",function(e){e.put("uib/template/accordion/accordion.html",'<div role="tablist" class="panel-group" ng-transclude></div>')}]),angular.module("uib/template/alert/alert.html",[]).run(["$templateCache",function(e){e.put("uib/template/alert/alert.html",'<button ng-show="closeable" type="button" class="close" ng-click="close({$event: $event})">\n  <span aria-hidden="true">&times;</span>\n  <span class="sr-only">Close</span>\n</button>\n<div ng-transclude></div>\n')}]),angular.module("uib/template/carousel/carousel.html",[]).run(["$templateCache",function(e){e.put("uib/template/carousel/carousel.html",'<div class="carousel-inner" ng-transclude></div>\n<a role="button" href class="left carousel-control" ng-click="prev()" ng-class="{ disabled: isPrevDisabled() }" ng-show="slides.length > 1">\n  <span aria-hidden="true" class="glyphicon glyphicon-chevron-left"></span>\n  <span class="sr-only">previous</span>\n</a>\n<a role="button" href class="right carousel-control" ng-click="next()" ng-class="{ disabled: isNextDisabled() }" ng-show="slides.length > 1">\n  <span aria-hidden="true" class="glyphicon glyphicon-chevron-right"></span>\n  <span class="sr-only">next</span>\n</a>\n<ol class="carousel-indicators" ng-show="slides.length > 1">\n  <li ng-repeat="slide in slides | orderBy:indexOfSlide track by $index" ng-class="{ active: isActive(slide) }" ng-click="select(slide)">\n    <span class="sr-only">slide {{ $index + 1 }} of {{ slides.length }}<span ng-if="isActive(slide)">, currently active</span></span>\n  </li>\n</ol>\n')}]),angular.module("uib/template/carousel/slide.html",[]).run(["$templateCache",function(e){e.put("uib/template/carousel/slide.html",'<div class="text-center" ng-transclude></div>\n')}]),angular.module("uib/template/datepicker/datepicker.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/datepicker.html",'<div ng-switch="datepickerMode">\n  <div uib-daypicker ng-switch-when="day" tabindex="0" class="uib-daypicker"></div>\n  <div uib-monthpicker ng-switch-when="month" tabindex="0" class="uib-monthpicker"></div>\n  <div uib-yearpicker ng-switch-when="year" tabindex="0" class="uib-yearpicker"></div>\n</div>\n')}]),angular.module("uib/template/datepicker/day.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/day.html",'<table role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-default btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-left"></i><span class="sr-only">previous</span></button></th>\n      <th colspan="{{::5 + showWeeks}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-default btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-default btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-right"></i><span class="sr-only">next</span></button></th>\n    </tr>\n    <tr>\n      <th ng-if="showWeeks" class="text-center"></th>\n      <th ng-repeat="label in ::labels track by $index" class="text-center"><small aria-label="{{::label.full}}">{{::label.abbr}}</small></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-weeks" ng-repeat="row in rows track by $index" role="row">\n      <td ng-if="showWeeks" class="text-center h6"><em>{{ weekNumbers[$index] }}</em></td>\n      <td ng-repeat="dt in row" class="uib-day text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-default btn-sm"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-muted\': dt.secondary, \'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("uib/template/datepicker/month.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/month.html",'<table role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-default btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-left"></i><span class="sr-only">previous</span></button></th>\n      <th colspan="{{::yearHeaderColspan}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-default btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-default btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-right"></i><span class="sr-only">next</span></i></button></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-months" ng-repeat="row in rows track by $index" role="row">\n      <td ng-repeat="dt in row" class="uib-month text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-default"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("uib/template/datepicker/year.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepicker/year.html",'<table role="grid" aria-labelledby="{{::uniqueId}}-title" aria-activedescendant="{{activeDateId}}">\n  <thead>\n    <tr>\n      <th><button type="button" class="btn btn-default btn-sm pull-left uib-left" ng-click="move(-1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-left"></i><span class="sr-only">previous</span></button></th>\n      <th colspan="{{::columns - 2}}"><button id="{{::uniqueId}}-title" role="heading" aria-live="assertive" aria-atomic="true" type="button" class="btn btn-default btn-sm uib-title" ng-click="toggleMode()" ng-disabled="datepickerMode === maxMode" tabindex="-1"><strong>{{title}}</strong></button></th>\n      <th><button type="button" class="btn btn-default btn-sm pull-right uib-right" ng-click="move(1)" tabindex="-1"><i aria-hidden="true" class="glyphicon glyphicon-chevron-right"></i><span class="sr-only">next</span></button></th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr class="uib-years" ng-repeat="row in rows track by $index" role="row">\n      <td ng-repeat="dt in row" class="uib-year text-center" role="gridcell"\n        id="{{::dt.uid}}"\n        ng-class="::dt.customClass">\n        <button type="button" class="btn btn-default"\n          uib-is-class="\n            \'btn-info\' for selectedDt,\n            \'active\' for activeDt\n            on dt"\n          ng-click="select(dt.date)"\n          ng-disabled="::dt.disabled"\n          tabindex="-1"><span ng-class="::{\'text-info\': dt.current}">{{::dt.label}}</span></button>\n      </td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("uib/template/datepickerPopup/popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/datepickerPopup/popup.html",'<ul role="presentation" class="uib-datepicker-popup dropdown-menu uib-position-measure" dropdown-nested ng-if="isOpen" ng-keydown="keydown($event)" ng-click="$event.stopPropagation()">\n  <li ng-transclude></li>\n  <li ng-if="showButtonBar" class="uib-button-bar">\n    <span class="btn-group pull-left">\n      <button type="button" class="btn btn-sm btn-info uib-datepicker-current" ng-click="select(\'today\', $event)" ng-disabled="isDisabled(\'today\')">{{ getText(\'current\') }}</button>\n      <button type="button" class="btn btn-sm btn-danger uib-clear" ng-click="select(null, $event)">{{ getText(\'clear\') }}</button>\n    </span>\n    <button type="button" class="btn btn-sm btn-success pull-right uib-close" ng-click="close($event)">{{ getText(\'close\') }}</button>\n  </li>\n</ul>\n')}]),angular.module("uib/template/modal/window.html",[]).run(["$templateCache",function(e){e.put("uib/template/modal/window.html","<div class=\"modal-dialog {{size ? 'modal-' + size : ''}}\"><div class=\"modal-content\" uib-modal-transclude></div></div>\n")}]),angular.module("uib/template/pager/pager.html",[]).run(["$templateCache",function(e){e.put("uib/template/pager/pager.html",'<li ng-class="{disabled: noPrevious()||ngDisabled, previous: align}"><a href ng-click="selectPage(page - 1, $event)" ng-disabled="noPrevious()||ngDisabled" uib-tabindex-toggle>{{::getText(\'previous\')}}</a></li>\n<li ng-class="{disabled: noNext()||ngDisabled, next: align}"><a href ng-click="selectPage(page + 1, $event)" ng-disabled="noNext()||ngDisabled" uib-tabindex-toggle>{{::getText(\'next\')}}</a></li>\n')}]),angular.module("uib/template/pagination/pagination.html",[]).run(["$templateCache",function(e){e.put("uib/template/pagination/pagination.html",'<li role="menuitem" ng-if="::boundaryLinks" ng-class="{disabled: noPrevious()||ngDisabled}" class="pagination-first"><a href ng-click="selectPage(1, $event)" ng-disabled="noPrevious()||ngDisabled" uib-tabindex-toggle>{{::getText(\'first\')}}</a></li>\n<li role="menuitem" ng-if="::directionLinks" ng-class="{disabled: noPrevious()||ngDisabled}" class="pagination-prev"><a href ng-click="selectPage(page - 1, $event)" ng-disabled="noPrevious()||ngDisabled" uib-tabindex-toggle>{{::getText(\'previous\')}}</a></li>\n<li role="menuitem" ng-repeat="page in pages track by $index" ng-class="{active: page.active,disabled: ngDisabled&&!page.active}" class="pagination-page"><a href ng-click="selectPage(page.number, $event)" ng-disabled="ngDisabled&&!page.active" uib-tabindex-toggle>{{page.text}}</a></li>\n<li role="menuitem" ng-if="::directionLinks" ng-class="{disabled: noNext()||ngDisabled}" class="pagination-next"><a href ng-click="selectPage(page + 1, $event)" ng-disabled="noNext()||ngDisabled" uib-tabindex-toggle>{{::getText(\'next\')}}</a></li>\n<li role="menuitem" ng-if="::boundaryLinks" ng-class="{disabled: noNext()||ngDisabled}" class="pagination-last"><a href ng-click="selectPage(totalPages, $event)" ng-disabled="noNext()||ngDisabled" uib-tabindex-toggle>{{::getText(\'last\')}}</a></li>\n')}]),angular.module("uib/template/tooltip/tooltip-html-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/tooltip/tooltip-html-popup.html",'<div class="tooltip-arrow"></div>\n<div class="tooltip-inner" ng-bind-html="contentExp()"></div>\n')}]),angular.module("uib/template/tooltip/tooltip-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/tooltip/tooltip-popup.html",'<div class="tooltip-arrow"></div>\n<div class="tooltip-inner" ng-bind="content"></div>\n')}]),angular.module("uib/template/tooltip/tooltip-template-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/tooltip/tooltip-template-popup.html",'<div class="tooltip-arrow"></div>\n<div class="tooltip-inner"\n  uib-tooltip-template-transclude="contentExp()"\n  tooltip-template-transclude-scope="originScope()"></div>\n')}]),angular.module("uib/template/popover/popover-html.html",[]).run(["$templateCache",function(e){e.put("uib/template/popover/popover-html.html",'<div class="arrow"></div>\n\n<div class="popover-inner">\n    <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n    <div class="popover-content" ng-bind-html="contentExp()"></div>\n</div>\n')}]),angular.module("uib/template/popover/popover-template.html",[]).run(["$templateCache",function(e){e.put("uib/template/popover/popover-template.html",'<div class="arrow"></div>\n\n<div class="popover-inner">\n    <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n    <div class="popover-content"\n      uib-tooltip-template-transclude="contentExp()"\n      tooltip-template-transclude-scope="originScope()"></div>\n</div>\n')}]),angular.module("uib/template/popover/popover.html",[]).run(["$templateCache",function(e){e.put("uib/template/popover/popover.html",'<div class="arrow"></div>\n\n<div class="popover-inner">\n    <h3 class="popover-title" ng-bind="uibTitle" ng-if="uibTitle"></h3>\n    <div class="popover-content" ng-bind="content"></div>\n</div>\n')}]),angular.module("uib/template/progressbar/bar.html",[]).run(["$templateCache",function(e){e.put("uib/template/progressbar/bar.html",'<div class="progress-bar" ng-class="type && \'progress-bar-\' + type" role="progressbar" aria-valuenow="{{value}}" aria-valuemin="0" aria-valuemax="{{max}}" ng-style="{width: (percent < 100 ? percent : 100) + \'%\'}" aria-valuetext="{{percent | number:0}}%" aria-labelledby="{{::title}}" ng-transclude></div>\n')}]),angular.module("uib/template/progressbar/progress.html",[]).run(["$templateCache",function(e){e.put("uib/template/progressbar/progress.html",'<div class="progress" ng-transclude aria-labelledby="{{::title}}"></div>')}]),angular.module("uib/template/progressbar/progressbar.html",[]).run(["$templateCache",function(e){e.put("uib/template/progressbar/progressbar.html",'<div class="progress">\n  <div class="progress-bar" ng-class="type && \'progress-bar-\' + type" role="progressbar" aria-valuenow="{{value}}" aria-valuemin="0" aria-valuemax="{{max}}" ng-style="{width: (percent < 100 ? percent : 100) + \'%\'}" aria-valuetext="{{percent | number:0}}%" aria-labelledby="{{::title}}" ng-transclude></div>\n</div>\n')}]),angular.module("uib/template/rating/rating.html",[]).run(["$templateCache",function(e){e.put("uib/template/rating/rating.html",'<span ng-mouseleave="reset()" ng-keydown="onKeydown($event)" tabindex="0" role="slider" aria-valuemin="0" aria-valuemax="{{range.length}}" aria-valuenow="{{value}}" aria-valuetext="{{title}}">\n    <span ng-repeat-start="r in range track by $index" class="sr-only">({{ $index < value ? \'*\' : \' \' }})</span>\n    <i ng-repeat-end ng-mouseenter="enter($index + 1)" ng-click="rate($index + 1)" class="glyphicon" ng-class="$index < value && (r.stateOn || \'glyphicon-star\') || (r.stateOff || \'glyphicon-star-empty\')" ng-attr-title="{{r.title}}"></i>\n</span>\n')}]),angular.module("uib/template/tabs/tab.html",[]).run(["$templateCache",function(e){e.put("uib/template/tabs/tab.html",'<li ng-class="[{active: active, disabled: disabled}, classes]" class="uib-tab nav-item">\n  <a href ng-click="select($event)" class="nav-link" uib-tab-heading-transclude>{{heading}}</a>\n</li>\n')}]),angular.module("uib/template/tabs/tabset.html",[]).run(["$templateCache",function(e){e.put("uib/template/tabs/tabset.html",'<div>\n  <ul class="nav nav-{{tabset.type || \'tabs\'}}" ng-class="{\'nav-stacked\': vertical, \'nav-justified\': justified}" ng-transclude></ul>\n  <div class="tab-content">\n    <div class="tab-pane"\n         ng-repeat="tab in tabset.tabs"\n         ng-class="{active: tabset.active === tab.index}"\n         uib-tab-content-transclude="tab">\n    </div>\n  </div>\n</div>\n')}]),angular.module("uib/template/timepicker/timepicker.html",[]).run(["$templateCache",function(e){e.put("uib/template/timepicker/timepicker.html",'<table class="uib-timepicker">\n  <tbody>\n    <tr class="text-center" ng-show="::showSpinners">\n      <td class="uib-increment hours"><a ng-click="incrementHours()" ng-class="{disabled: noIncrementHours()}" class="btn btn-link" ng-disabled="noIncrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></a></td>\n      <td>&nbsp;</td>\n      <td class="uib-increment minutes"><a ng-click="incrementMinutes()" ng-class="{disabled: noIncrementMinutes()}" class="btn btn-link" ng-disabled="noIncrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></a></td>\n      <td ng-show="showSeconds">&nbsp;</td>\n      <td ng-show="showSeconds" class="uib-increment seconds"><a ng-click="incrementSeconds()" ng-class="{disabled: noIncrementSeconds()}" class="btn btn-link" ng-disabled="noIncrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-up"></span></a></td>\n      <td ng-show="showMeridian"></td>\n    </tr>\n    <tr>\n      <td class="form-group uib-time hours" ng-class="{\'has-error\': invalidHours}">\n        <input type="text" placeholder="HH" ng-model="hours" ng-change="updateHours()" class="form-control text-center" ng-readonly="::readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementHours()" ng-blur="blur()">\n      </td>\n      <td class="uib-separator">:</td>\n      <td class="form-group uib-time minutes" ng-class="{\'has-error\': invalidMinutes}">\n        <input type="text" placeholder="MM" ng-model="minutes" ng-change="updateMinutes()" class="form-control text-center" ng-readonly="::readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementMinutes()" ng-blur="blur()">\n      </td>\n      <td ng-show="showSeconds" class="uib-separator">:</td>\n      <td class="form-group uib-time seconds" ng-class="{\'has-error\': invalidSeconds}" ng-show="showSeconds">\n        <input type="text" placeholder="SS" ng-model="seconds" ng-change="updateSeconds()" class="form-control text-center" ng-readonly="readonlyInput" maxlength="2" tabindex="{{::tabindex}}" ng-disabled="noIncrementSeconds()" ng-blur="blur()">\n      </td>\n      <td ng-show="showMeridian" class="uib-time am-pm"><button type="button" ng-class="{disabled: noToggleMeridian()}" class="btn btn-default text-center" ng-click="toggleMeridian()" ng-disabled="noToggleMeridian()" tabindex="{{::tabindex}}">{{meridian}}</button></td>\n    </tr>\n    <tr class="text-center" ng-show="::showSpinners">\n      <td class="uib-decrement hours"><a ng-click="decrementHours()" ng-class="{disabled: noDecrementHours()}" class="btn btn-link" ng-disabled="noDecrementHours()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></a></td>\n      <td>&nbsp;</td>\n      <td class="uib-decrement minutes"><a ng-click="decrementMinutes()" ng-class="{disabled: noDecrementMinutes()}" class="btn btn-link" ng-disabled="noDecrementMinutes()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></a></td>\n      <td ng-show="showSeconds">&nbsp;</td>\n      <td ng-show="showSeconds" class="uib-decrement seconds"><a ng-click="decrementSeconds()" ng-class="{disabled: noDecrementSeconds()}" class="btn btn-link" ng-disabled="noDecrementSeconds()" tabindex="-1"><span class="glyphicon glyphicon-chevron-down"></span></a></td>\n      <td ng-show="showMeridian"></td>\n    </tr>\n  </tbody>\n</table>\n')}]),angular.module("uib/template/typeahead/typeahead-match.html",[]).run(["$templateCache",function(e){e.put("uib/template/typeahead/typeahead-match.html",'<a href\n   tabindex="-1"\n   ng-bind-html="match.label | uibTypeaheadHighlight:query"\n   ng-attr-title="{{match.label}}"></a>\n')}]),angular.module("uib/template/typeahead/typeahead-popup.html",[]).run(["$templateCache",function(e){e.put("uib/template/typeahead/typeahead-popup.html",'<ul class="dropdown-menu" ng-show="isOpen() && !moveInProgress" ng-style="{top: position().top+\'px\', left: position().left+\'px\'}" role="listbox" aria-hidden="{{!isOpen()}}">\n    <li class="uib-typeahead-match" ng-repeat="match in matches track by $index" ng-class="{active: isActive($index) }" ng-mouseenter="selectActive($index)" ng-click="selectMatch($index, $event)" role="option" id="{{::match.id}}">\n        <div uib-typeahead-match index="$index" match="match" query="query" template-url="templateUrl"></div>\n    </li>\n</ul>\n')}]),angular.module("ui.bootstrap.carousel").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibCarouselCss&&angular.element(document).find("head").prepend('<style type="text/css">.ng-animate.item:not(.left):not(.right){-webkit-transition:0s ease-in-out left;transition:0s ease-in-out left}</style>'),angular.$$uibCarouselCss=!0}),angular.module("ui.bootstrap.datepicker").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibDatepickerCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-datepicker .uib-title{width:100%;}.uib-day button,.uib-month button,.uib-year button{min-width:100%;}.uib-left,.uib-right{width:100%}</style>'),angular.$$uibDatepickerCss=!0}),angular.module("ui.bootstrap.position").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibPositionCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-position-measure{display:block !important;visibility:hidden !important;position:absolute !important;top:-9999px !important;left:-9999px !important;}.uib-position-scrollbar-measure{position:absolute !important;top:-9999px !important;width:50px !important;height:50px !important;overflow:scroll !important;}.uib-position-body-scrollbar-measure{overflow:scroll !important;}</style>'),angular.$$uibPositionCss=!0}),angular.module("ui.bootstrap.datepickerPopup").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibDatepickerpopupCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-datepicker-popup.dropdown-menu{display:block;float:none;margin:0;}.uib-button-bar{padding:10px 9px 2px;}</style>'),angular.$$uibDatepickerpopupCss=!0}),angular.module("ui.bootstrap.tooltip").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibTooltipCss&&angular.element(document).find("head").prepend('<style type="text/css">[uib-tooltip-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-popup].tooltip.right-bottom > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-html-popup].tooltip.right-bottom > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.top-left > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.top-right > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.bottom-left > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.bottom-right > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.left-top > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.left-bottom > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.right-top > .tooltip-arrow,[uib-tooltip-template-popup].tooltip.right-bottom > .tooltip-arrow,[uib-popover-popup].popover.top-left > .arrow,[uib-popover-popup].popover.top-right > .arrow,[uib-popover-popup].popover.bottom-left > .arrow,[uib-popover-popup].popover.bottom-right > .arrow,[uib-popover-popup].popover.left-top > .arrow,[uib-popover-popup].popover.left-bottom > .arrow,[uib-popover-popup].popover.right-top > .arrow,[uib-popover-popup].popover.right-bottom > .arrow,[uib-popover-html-popup].popover.top-left > .arrow,[uib-popover-html-popup].popover.top-right > .arrow,[uib-popover-html-popup].popover.bottom-left > .arrow,[uib-popover-html-popup].popover.bottom-right > .arrow,[uib-popover-html-popup].popover.left-top > .arrow,[uib-popover-html-popup].popover.left-bottom > .arrow,[uib-popover-html-popup].popover.right-top > .arrow,[uib-popover-html-popup].popover.right-bottom > .arrow,[uib-popover-template-popup].popover.top-left > .arrow,[uib-popover-template-popup].popover.top-right > .arrow,[uib-popover-template-popup].popover.bottom-left > .arrow,[uib-popover-template-popup].popover.bottom-right > .arrow,[uib-popover-template-popup].popover.left-top > .arrow,[uib-popover-template-popup].popover.left-bottom > .arrow,[uib-popover-template-popup].popover.right-top > .arrow,[uib-popover-template-popup].popover.right-bottom > .arrow{top:auto;bottom:auto;left:auto;right:auto;margin:0;}[uib-popover-popup].popover,[uib-popover-html-popup].popover,[uib-popover-template-popup].popover{display:block !important;}</style>'),angular.$$uibTooltipCss=!0}),angular.module("ui.bootstrap.timepicker").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibTimepickerCss&&angular.element(document).find("head").prepend('<style type="text/css">.uib-time input{width:50px;}</style>'),angular.$$uibTimepickerCss=!0}),angular.module("ui.bootstrap.typeahead").run(function(){!angular.$$csp().noInlineStyle&&!angular.$$uibTypeaheadCss&&angular.element(document).find("head").prepend('<style type="text/css">[uib-typeahead-popup].dropdown-menu{display:block;}</style>'),angular.$$uibTypeaheadCss=!0}),("function"==typeof define&&define.amd?define:function(e,t){"undefined"!=typeof module&&module.exports?module.exports=t(require("jquery")):window.toastr=t(window.jQuery)})(["jquery"],function(e){return function(){function t(t,n){return t||(t=a()),(l=e("#"+t.containerId)).length?l:(n&&(l=function(t){return(l=e("<div/>").attr("id",t.containerId).addClass(t.positionClass)).appendTo(e(t.target)),l}(t)),l)}function n(t){for(var n=l.children(),r=n.length-1;r>=0;r--)i(e(n[r]),t)}function i(t,n,i){var r=!(!i||!i.force)&&i.force;return!(!t||!r&&0!==e(":focus",t).length||(t[n.hideMethod]({duration:n.hideDuration,easing:n.hideEasing,complete:function(){s(t)}}),0))}function r(e){u&&u(e)}function o(n){function i(e){return null==e&&(e=""),e.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function o(t){var n=t&&!1!==h.closeMethod?h.closeMethod:h.hideMethod,i=t&&!1!==h.closeDuration?h.closeDuration:h.hideDuration,o=t&&!1!==h.closeEasing?h.closeEasing:h.hideEasing;if(!e(":focus",v).length||t)return clearTimeout(x.intervalId),v[n]({duration:i,easing:o,complete:function(){s(v),clearTimeout(g),h.onHidden&&"hidden"!==k.state&&h.onHidden(),k.state="hidden",k.endTime=new Date,r(k)}})}function u(){(h.timeOut>0||h.extendedTimeOut>0)&&(g=setTimeout(o,h.extendedTimeOut),x.maxHideTime=parseFloat(h.extendedTimeOut),x.hideEta=(new Date).getTime()+x.maxHideTime)}function d(){clearTimeout(g),x.hideEta=0,v.stop(!0,!0)[h.showMethod]({duration:h.showDuration,easing:h.showEasing})}function f(){var e=(x.hideEta-(new Date).getTime())/x.maxHideTime*100;y.width(e+"%")}var h=a(),m=n.iconClass||h.iconClass;if(void 0!==n.optionsOverride&&(h=e.extend(h,n.optionsOverride),m=n.optionsOverride.iconClass||m),!function(e,t){if(e.preventDuplicates){if(t.message===c)return!0;c=t.message}return!1}(h,n)){p++,l=t(h,!0);var g=null,v=e("<div/>"),$=e("<div/>"),b=e("<div/>"),y=e("<div/>"),w=e(h.closeHtml),x={intervalId:null,hideEta:null,maxHideTime:null},k={toastId:p,state:"visible",startTime:new Date,options:h,map:n};return n.iconClass&&v.addClass(h.toastClass).addClass(m),function(){if(n.title){var e=n.title;h.escapeHtml&&(e=i(n.title)),$.append(e).addClass(h.titleClass),v.append($)}}(),function(){if(n.message){var e=n.message;h.escapeHtml&&(e=i(n.message)),b.append(e).addClass(h.messageClass),v.append(b)}}(),h.closeButton&&(w.addClass(h.closeClass).attr("role","button"),v.prepend(w)),h.progressBar&&(y.addClass(h.progressClass),v.prepend(y)),h.rtl&&v.addClass("rtl"),h.newestOnTop?l.prepend(v):l.append(v),function(){var e="";switch(n.iconClass){case"toast-success":case"toast-info":e="polite";break;default:e="assertive"}v.attr("aria-live",e)}(),v.hide(),v[h.showMethod]({duration:h.showDuration,easing:h.showEasing,complete:h.onShown}),h.timeOut>0&&(g=setTimeout(o,h.timeOut),x.maxHideTime=parseFloat(h.timeOut),x.hideEta=(new Date).getTime()+x.maxHideTime,h.progressBar&&(x.intervalId=setInterval(f,10))),h.closeOnHover&&v.hover(d,u),!h.onclick&&h.tapToDismiss&&v.click(o),h.closeButton&&w&&w.click(function(e){e.stopPropagation?e.stopPropagation():void 0!==e.cancelBubble&&!0!==e.cancelBubble&&(e.cancelBubble=!0),h.onCloseClick&&h.onCloseClick(e),o(!0)}),h.onclick&&v.click(function(e){h.onclick(e),o()}),r(k),h.debug&&console&&console.log(k),v}}function a(){return e.extend({},{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1},f.options)}function s(e){l||(l=t()),e.is(":visible")||(e.remove(),e=null,0===l.children().length&&(l.remove(),c=void 0))}var l,u,c,p=0,d={error:"error",info:"info",success:"success",warning:"warning"},f={clear:function(e,r){var o=a();l||t(o),i(e,o,r)||n(o)},remove:function(n){var i=a();return l||t(i),n&&0===e(":focus",n).length?void s(n):void(l.children().length&&l.remove())},error:function(e,t,n){return o({type:d.error,iconClass:a().iconClasses.error,message:e,optionsOverride:n,title:t})},getContainer:t,info:function(e,t,n){return o({type:d.info,iconClass:a().iconClasses.info,message:e,optionsOverride:n,title:t})},options:{},subscribe:function(e){u=e},success:function(e,t,n){return o({type:d.success,iconClass:a().iconClasses.success,message:e,optionsOverride:n,title:t})},version:"2.1.3",warning:function(e,t,n){return o({type:d.warning,iconClass:a().iconClasses.warning,message:e,optionsOverride:n,title:t})}};return f}()});var app=angular.module("app",["ngSanitize","ui.bootstrap"]);function appAlert(e,t){toastr.error(t,e,{timeOut:1e4,closeButton:!0,progressBar:!0})}function appInfo(e,t,n){n||(n=1e4),toastr.info(t,e,{timeOut:n,closeButton:!0,progressBar:!0})}function addFormError(e,t){var n="<div class='help-block validation-error text-red'>"+t+"</div>",i=null!==document.getElementById("warning-"+e),r=null!==document.getElementById(e);e=e.replace(/\./g,"\\.");i?$("#warning-"+e).html(n).parent().addClass("has-error"):r?$("#"+e).parent().append(n).addClass("has-error"):appAlert(t)}function removeFormErrors(){$(".validation-error").remove(),$(".has-error").removeClass("has-error")}function empty(e){return""===$.trim(e)}function replaceAll(e,t,n){return e.replace(new RegExp(t,"g"),n)}function removeMasksAndLeadingZero(e){for("0,"==e.substr(0,2)&&(e=e.substr(2));"0"==e.substr(0,1);)e=e.substr(1);return onlyNumbers(e)}function fixDatepickerDate(e){try{var t=e.getFullYear(),n=e.getMonth(),i=e.getDate();return new Date(t,n,i)}catch(t){if(e&&e.indexOf("-")>=0){var r=e.split("-");return new Date(r[0],r[1]-1,r[2])}}return""}function isMobile(){return $(window).width()<=703}function loadingCenter(e,t,n){null!=n&&null!=n||(n=50),loading(e,t,n,"center")}function loadingTop(e,t,n,i){null!=n&&null!=n||(n=50),null!=i&&null!=i||(i="30px"),loading(e,t,n,i)}function loading(e,t,n,i){if(null!=n&&null!=n||(n=50),null!=i&&null!=i||(i="30px"),$("#"+e).length){var r=$("#"+e).width(),o=$("#"+e).height();if(t){var a=document.createElement("DIV");a.id="loading-"+e,a.style.width=r+"px",a.style.height=o+"px",a.style.left=$("#"+e).offset().left+"px",a.style.top=$("#"+e).offset().top+"px",a.style.position="absolute",a.style.backgroundColor="rgba(255,255,255,.7)",a.style.textAlign="center",a.style.zIndex="999999","center"==i&&(i=parseInt(o)/2-n/2+"px");var s=document.createElementNS("http://www.w3.org/2000/svg","svg");s.setAttribute("width","50px"),s.setAttribute("height","50px"),s.setAttribute("class","spinner-container"),$(s).css("margin-top",i);var l=parseInt(n/2),u=document.createElementNS("http://www.w3.org/2000/svg","circle");u.setAttribute("cx",l+"px"),u.setAttribute("cy",l+"px"),u.setAttribute("r",l-5+"px"),u.setAttribute("fill","none"),u.setAttribute("class","path"),s.appendChild(u),a.appendChild(s),$(document.body).append(a)}else $("#loading-"+e).remove()}}function maskCurrencyInputBR(e,t){t||""==t||(t="R$ "),e.value=maskCurrencyBR(e.value,t)}function maskCurrencyBR(e,t){if(!e)return"";t||""==t||(t="R$ "),-1!=(e=""+e).indexOf(t)&&(e=e.substr(t.length)),-1!=e.indexOf(".")&&e.indexOf(".")==e.length-2&&(e+="0");var n="",i=removeMasksAndLeadingZero(e);if(!i)return"";var r=i.length;return isNaN(i)?maskCurrencyBR(n=onlyNumbers(i),t):(1==r&&(n=t+"0,0"+i),2==r&&(n=t+"0,"+i),r>2&&r<=5&&(n=t+i.substr(0,r-2)+","+i.substr(r-2,r)),r>=6&&r<=8&&(n=t+i.substr(0,r-5)+"."+i.substr(r-5,3)+","+i.substr(r-2,r)),r>=9&&r<=11&&(n=t+i.substr(0,r-8)+"."+i.substr(r-8,3)+"."+i.substr(r-5,3)+","+i.substr(r-2,r)),r>=12&&r<=14&&(n=t+i.substr(0,r-11)+"."+i.substr(r-11,3)+"."+i.substr(r-8,3)+"."+i.substr(r-5,3)+","+i.substr(r-2,r)),r>=15&&r<=18&&(n=t+i.substr(0,r-14)+"."+i.substr(r-14,3)+"."+i.substr(r-11,3)+"."+i.substr(r-8,3)+"."+i.substr(r-5,3)+","+i.substr(r-2,r)),n)}function maskDateBR(e){var t=onlyNumbers(e);if(!t)return"";var n=t;if(t.length>=5)n=t.substring(0,2)+"/"+t.substring(2,4)+"/"+t.substring(4,8);else if(t.length>=3){n=t.substring(0,2)+"/"+t.substring(2)}return n}function maskDateInputBR(e){e.value=maskDateBR(e.value)}function maskHours(e){var t=onlyNumbers(e);return t?t.length>=3?t.substring(0,2)+":"+t.substring(2,4):t:""}function maskPersonPINBR(e){var t=onlyNumbers(e);return t?t.length>=10?t.substring(0,3)+"."+t.substring(3,6)+"."+t.substring(6,9)+"-"+t.substring(9,11):t.length>=7?t.substring(0,3)+"."+t.substring(3,6)+"."+t.substring(6):t.length>=4?t.substring(0,3)+"."+t.substring(3):t:""}function maskCompanyPINBR(e){var t=onlyNumbers(e);return t?t.length>=13?t.substring(0,2)+"."+t.substring(2,5)+"."+t.substring(5,8)+"/"+t.substring(8,12)+"-"+t.substring(12,14):t.length>=9?t.substring(0,2)+"."+t.substring(2,5)+"."+t.substring(5,8)+"/"+t.substring(8,12):t.length>=6?t.substring(0,2)+"."+t.substring(2,5)+"."+t.substring(5):t.length>=3?t.substring(0,2)+"."+t.substring(2):t:""}function maskPhoneNumberBR(e){var t=onlyNumbers(e);return t?t.length>=7?"("+t.substring(0,2)+")"+t.substring(2,6)+"-"+t.substring(6,11):t.length>=3?"("+t.substring(0,2)+")"+t.substring(2):t:""}function maskZipCodeBR(e){isMobile()||$("#"+e).keyup(function(){var e=onlyNumbers(this.value);if(e.length>=6){var t=e.substring(0,2),n=e.substring(2,5),i=e.substring(5);i.length>3&&(i=i.substring(0,3)),this.value=t+"."+n+"-"+i}else if(e.length>=3){t=e.substring(0,2),n=e.substring(2);this.value=t+"."+n}else this.value=e})}function arrRemove(e,t){var n=e.indexOf(t);-1!=n&&e.splice(n,1)}function onlyNumbers(e){if(e)return e.replace(/[^0-9]/gi,"")}function validatePersonPINBR(e){var t,n=0;if(!(e=onlyNumbers(e))||11!=e.length||"00000000000"==e||"11111111111"==e||"22222222222"==e||"33333333333"==e||"44444444444"==e||"55555555555"==e||"66666666666"==e||"77777777777"==e||"88888888888"==e||"99999999999"==e)return!1;for(var i=1;i<=9;i++)n+=parseInt(e.substring(i-1,i))*(11-i);if(10!=(t=10*n%11)&&11!=t||(t=0),t!=parseInt(e.substring(9,10)))return!1;n=0;for(i=1;i<=10;i++)n+=parseInt(e.substring(i-1,i))*(12-i);return 10!=(t=10*n%11)&&11!=t||(t=0),t==parseInt(e.substring(10,11))}function copyProperties(e,t){for(var n in t)t.hasOwnProperty(n)&&e.hasOwnProperty(n)&&(t[n]=e[n])}app.config(["$compileProvider",function(e){e.debugInfoEnabled(!1)}]),app.config(["$httpProvider",function(e){e.defaults.headers.common["X-Requested-With"]="XMLHttpRequest",e.interceptors.push(["$q",function(e){var t=!1;return{request:function(e){return $(".block-on-load").prop("disabled",!0),e.timeout||(e.timeout=1e4),e},response:function(e){return $(".block-on-load").prop("disabled",!1),removeFormErrors(),e},responseError:function(n){if($(".block-on-load").prop("disabled",!1),removeFormErrors(),"901"==n.status){if(!t){t=!0,appAlert("Usuário não logado ou sessão expirada. Por favor, identifique-se novamente.");window.location.href="/auth/login"}return e.reject(n)}if("403"!=n.status||n.config.ignoreAllWarnings)if("401"!=n.status||n.config.ignoreAllWarnings)if("404"!=n.status||n.config.ignoreAllWarnings)if("500"!=n.status||n.config.ignoreAllWarnings)-1!=n.status||n.config.ignoreAllWarnings||appAlert("Erro de comunicação com o servidor. Aguarde um pouco e tente novamente.");else{appAlert("Erro inesperado. Por favor, tente novamente em alguns minutos.")}else appAlert(n.data);else appAlert("Sessão expirada"),window.location.href="/auth/login";else appAlert("Você não tem permissão para executar esta ação.");if(422==n.status&&!n.config.ignore422&&!n.config.ignoreAllWarnings){var i=n.config.uid,r=0;for(var o in n.data.errors){var a=i?o+"-"+i:o;0==r&&($("#"+a.replace(/\./g,"\\.")).focus(),r++),addFormError(a,n.data.errors[o])}n.data.default&&appAlert(n.data.default)}return e.reject(n)}}}])}]),app.service("UIDService",function(){var e=1;this.generate=function(){return e++}}),app.directive("focusMe",["$timeout",function(e){return{link:function(t,n){e(function(){n[0].focus()},100)}}}]),app.directive("onEnter",function(){return{restrict:"A",link:function(e,t,n){t.bind("keyup",function(t){13===t.which&&(e.$apply(function(){e.$eval(n.onEnter)}),t.preventDefault())})}}}),app.directive("maskCompanyPin",function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,i){t.bind("keyup",function(e){t.val(maskCompanyPINBR(t.val())),i.$setViewValue(t.val()),i.$render()})}}}),app.directive("maskCurrency",function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,i){t.bind("keyup",function(e){var r=null!=n.currencyPrefix?n.currencyPrefix:null;t.val(maskCurrencyBR(t.val(),r)),i.$setViewValue(t.val()),i.$render()})}}}),app.directive("maskDate",function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,i){t.bind("keyup",function(e){t.val(maskDateBR(t.val())),i.$setViewValue(t.val()),i.$render()})}}}),app.directive("maskHours",function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,i){t.bind("keyup",function(e){t.val(maskHours(t.val())),i.$setViewValue(t.val()),i.$render()})}}}),app.directive("maskNumber",function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,i){t.bind("keyup",function(e){t.val(onlyNumbers(t.val())),i.$setViewValue(t.val()),i.$render()})}}}),app.directive("maskPersonPin",function(){return{restrict:"A",link:function(e,t,n,i){t.bind("keyup",function(){t.val(maskPersonPINBR(t.val())),i&&i.$setViewValue&&(i.$setViewValue(t.val()),i.$render())})}}}),app.directive("maskPhoneNumber",function(){return{restrict:"A",require:"ngModel",link:function(e,t,n,i){t.bind("keyup",function(e){t.val(maskPhoneNumberBR(t.val())),i.$setViewValue(t.val()),i.$render()})}}}),app.controller("LandpageController",["$scope","$http","$timeout",function(e,t,n){e.options={},e.favoritas=[],e.pesquisar=function(){if(e.options.norma){var t="/legislacao/"+e.options.norma.apelido;document.location.href=t}else alert("Favor selecionar uma norma")},e.focusNormas=function(){n(function(){$(".titulo-norma").css("max-width",$("#pesquisa-norma").css("width"))},100)},n(function(){if(localStorage){var t=localStorage.getItem("favoritas");if(t){t=JSON.parse(t);var n=Object.keys(t).map(function(e){return[e,t[e]]});for(i in n.sort(function(e,t){return t[1]-e[1]}),n=n.slice(0,5)){var r=n[i][0];e.favoritas.push(r)}}}},100)}]),app.controller("BuscaCovidController",["$scope","$http","$timeout",function(e,t,n){e.options={},e.documentos=null,e.pesquisar=function(){e.options.covidText?(loadingTop("resultados-busca-covid",!0),t.get("/busca-covid/search/"+e.options.covidText).then(function(t){e.documentos=t.data.hits}).finally(function(){loadingTop("resultados-busca-covid",!1)})):alert("Digite o trecho a ser pesquisado.")}}]),app.controller("LegislacaoController",["$scope","$http","$uibModal","$timeout","$interval",function(e,t,n,i,r){e.jurisprudencias=[],e.filtros={nome_corpus:"mix",urn:null},e.lastText="",e.verHistoricoCompleto=function(){loading("historico-artigo",!0),t.get("/historico-artigo/"+e.filtros.urn).then(function(t){e.jurisprudencias.alteracoes=t.data,e.jurisprudencias.has_alteracoes=$(e.jurisprudencias.alteracoes).size()>0,e.jurisprudencias.historico_completo=!0}).finally(function(){loading("historico-artigo",!1)})},e.buscarJurisprudencia=function(n,r){$("p").removeClass("artigo_selecionado"),$(n.currentTarget).closest("strike").length>=1||(e.filtros={nome_corpus:"mix",urn:r},$(n.currentTarget).closest("p").addClass("artigo_selecionado"),loadingTop("pageBody",!0),t.get("/jurisprudencia/"+r,{timeout:6e4}).then(function(t){e.jurisprudencias.controle_constitucionalidade=t.data.jurisprudencias[90],e.jurisprudencias.sumula_vinculante=t.data.jurisprudencias[100],e.jurisprudencias.jur_teses=t.data.jurisprudencias[110],e.jurisprudencias.repercussao_geral=t.data.temas[80],e.jurisprudencias.sumulas=t.data.jurisprudencias[70],e.jurisprudencias.repetitivos=t.data.temas[60],e.jurisprudencias.posicionamentos_agrupados_stj=t.data.posicionamentos_agrupados_stj,e.jurisprudencias.posicionamentos_agrupados_stj_emstj=t.data.posicionamentos_agrupados_stj_emstj,e.jurisprudencias.posicionamentos_isolados_stj=t.data.posicionamentos_isolados_stj,e.jurisprudencias.alteracoes=t.data.alteracoes,$(n.currentTarget).closest("p").append($("#divJurisprudencias")),$("#divJurisprudencias").show(),$("body").addClass("reading-mask"),e.jurisprudencias.has_alteracoes=$(e.jurisprudencias.alteracoes).size()>0,e.jurisprudencias.historico_completo=!1,i(function(){$(window).scrollTop($(n.currentTarget).offset().top-10),$('[data-toggle="tooltip"]').tooltip()},100)}).finally(function(){loadingTop("pageBody",!1)}))},e.buscarPosicionamentosAgrupadosSTJ=function(){loadingTop("divPosicionamentosAgrupadosSTJ",!0),t.post("/jurisprudencia/posicionamentos-agrupados-stj",e.filtros).then(function(t){e.jurisprudencias.posicionamentos_agrupados_stj=t.data}).finally(function(){loadingTop("divPosicionamentosAgrupadosSTJ",!1)})},e.listarJurisprudenciasSemelhantes=function(i){loadingTop("pageBody",!0),t.get("/jurisprudencias/semelhantes/"+i.id).then(function(t){n.open({templateUrl:"modalJurisprudenciasSemelhantes",controllerAs:"$ctrl",controller:["$uibModalInstance","posicionamento_agrupado_stj","semelhantes","parent",function(e,t,n,i){this.posicionamento_agrupado_stj=t,this.semelhantes=n,this.ok=function(){e.close()},this.getSemelhanca=function(e){return parseFloat(100*e).toFixed(2)},this.formatarTexto=i.formatarTexto,loadingTop("pageBody",!1)}],size:"lg",resolve:{posicionamento_agrupado_stj:function(){return i},semelhantes:function(){return t.data},parent:function(){return e}}})}).finally(function(){loadingTop("pageBody",!1)})},e.formatarTexto=function(e){return null==e?"":e=(e=(e=(e=e.replace(/&nbsp;/gi," ").replace(/art\./gi,"artigo ").replace(/arts\./gi,"artigos ")).replace(/(\.\W+)ACÓRDÃO/g,"$1<br><br>ACÓRDÃO<br>")).replace(/(\.\W+)([0-9]{1,2})\W? /g,"$1<br><br>$2. ")).replace(/(\.\W+)(V?I{0,3}V?X?I{0,3}) -\W/g,"$1<br><br>$2 - ")},e.goToAnchor=function(){var t=document.location.toString().split("#")[0];document.location=t+"#art-"+$("#artigo-search").val();var n="#art-"+$("#artigo-search").val();return e.abrirArtigo(n),!1},e.abrirArtigo=function(e){setTimeout(function(){angular.element(e).triggerHandler("click")},100)},e.fecharArtigo=function(){$("#divJurisprudencias").hide(),$("body").removeClass("reading-mask"),$("p").removeClass("artigo_selecionado")},e.getDispositivosSemReferencia=function(){t.get("/jurisprudencia/dispositivos-sem-referencia/"+e.norma_id).then(function(e){angular.forEach(e.data,function(e,t){var n=$('a.numero_artigo[ng-click*="'+e.urn+"'\"]").attr("id");$('a.numero_artigo[ng-click*="'+e.urn+"'\"]").replaceWith("<span id='"+n+"'>"+$("#"+n).text()+"</span>")})}).finally(function(){loadingTop("divPosicionamentosAgrupadosSTJ",!1)})},e.init=function(t,n){e.norma_id=t,e.getDispositivosSemReferencia();var i=window.location.hash;if(i&&e.abrirArtigo(i),$("body").click(function(e){$(e.target).parents("div#divJurisprudencias").length||$(this).removeClass("reading-mask")}),localStorage){var r=localStorage.getItem("favoritas");r||(r="{}"),(r=JSON.parse(r))[n]?r[n]+=1:r[n]=1,localStorage.setItem("favoritas",JSON.stringify(r))}},e.verificarTextoSelecionado=function(n){var i="";if(window.getSelection&&(i=window.getSelection().toString()),null!=n&&i.length>=15&&i.length<=600&&e.lastText!=i){e.lastText=i;var r={jurisprudencia_id:n,texto:i};t.post("/salvar-marcacao-texto",r,{ignoreAllWarnings:!0}).then(function(e){}).finally(function(){})}}}]),app.controller("SimilaridadeController",["$scope","$http","$uibModal","$timeout",function(e,t,n,i){e.options={},e.sugestoes=null,e.sugerirReferenciaLegislativa=function(){loadingTop("divSugestoes",!0),t.post("/similaridade/sugerir-referencia-legislativa",e.options).then(function(t){e.sugestoes=t.data}).finally(function(){loadingTop("divSugestoes",!1)})}}]);
