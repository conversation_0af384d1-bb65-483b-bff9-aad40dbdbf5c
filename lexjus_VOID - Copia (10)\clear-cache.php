<?php
/**
 * Utilitário para Limpar Cache do LexJus
 *
 * Este arquivo pode ser executado para forçar a limpeza de cache
 * durante desenvolvimento ou após atualizações importantes
 */

// Headers para evitar cache desta própria página
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

// Verificar se é uma requisição autorizada (adicione autenticação se necessário)
$authorized = true; // Mude para false em produção e implemente autenticação

if (!$authorized) {
    http_response_code(403);
    die('Acesso negado');
}

// Tentar incluir sistema de cache busting
$cacheSystemAvailable = false;
if (file_exists('includes/cache_buster.php')) {
    try {
        require_once 'includes/cache_buster.php';
        $cacheSystemAvailable = true;
    } catch (Exception $e) {
        $cacheError = $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpar Cache - LexJus</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .action-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            font-size: 14px;
            transition: background 0.2s;
        }
        .action-button:hover {
            background: #005a87;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .success {
            background: #28a745;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Gerenciador de Cache - LexJus</h1>

        <div class="info-box">
            <strong>ℹ️ Informações:</strong><br>
            Esta ferramenta ajuda a resolver problemas de cache do navegador.<br>
            Use quando os usuários relatarem que não veem as atualizações mais recentes.
        </div>

        <?php
        // Processar ações
        if ($_POST) {
            echo '<div class="result success" style="display: block;">';

            if (isset($_POST['clear_cache'])) {
                // Limpar cache interno do PHP
                if ($cacheSystemAvailable) {
                    CacheBuster::clearCache();
                    echo '✅ Cache interno do PHP limpo<br>';
                } else {
                    echo '❌ Sistema de cache não disponível<br>';
                }
            }

            if (isset($_POST['update_versions'])) {
                // Forçar atualização de versões
                touch('style.css');
                touch('script.js');
                touch('css/sistema-revisao.css');
                touch('js/sistema-revisao.js');
                echo '✅ Timestamps dos arquivos atualizados<br>';
            }

            if (isset($_POST['clear_logs'])) {
                // Limpar logs
                $logFile = 'logs/version_checks.log';
                if (file_exists($logFile)) {
                    unlink($logFile);
                    echo '✅ Logs de verificação de versão limpos<br>';
                }
            }

            echo '</div>';
        }
        ?>

        <h2>📊 Status Atual</h2>
        <div class="file-list">
            <?php if ($cacheSystemAvailable): ?>
                <strong>Versão Global:</strong> <?php echo cache_version(); ?><br>
                <strong>Data/Hora:</strong> <?php echo date('d/m/Y H:i:s', cache_version()); ?><br><br>
            <?php else: ?>
                <strong>⚠️ Sistema de Cache:</strong> Não disponível<br>
                <?php if (isset($cacheError)): ?>
                    <strong>Erro:</strong> <?php echo htmlspecialchars($cacheError); ?><br>
                <?php endif; ?>
                <br>
            <?php endif; ?>

            <strong>Versões dos Arquivos:</strong><br>
            <?php
            $files = ['style.css', 'script.js', 'css/sistema-revisao.css', 'js/sistema-revisao.js'];
            foreach ($files as $file) {
                if (file_exists($file)) {
                    $version = filemtime($file);
                    $size = filesize($file);
                    echo "• {$file}: v{$version} (" . number_format($size/1024, 1) . " KB)<br>";
                } else {
                    echo "• {$file}: ❌ Não encontrado<br>";
                }
            }
            ?>
        </div>

        <h2>🔧 Ações Disponíveis</h2>

        <form method="post" style="display: inline;">
            <button type="submit" name="clear_cache" class="action-button">
                🗑️ Limpar Cache Interno
            </button>
        </form>

        <form method="post" style="display: inline;">
            <button type="submit" name="update_versions" class="action-button">
                🔄 Forçar Atualização de Versões
            </button>
        </form>

        <form method="post" style="display: inline;">
            <button type="submit" name="clear_logs" class="action-button">
                📝 Limpar Logs
            </button>
        </form>

        <div class="warning-box">
            <strong>⚠️ Instruções para Usuários:</strong><br>
            Se os usuários ainda não veem as atualizações após usar as ações acima:
            <ol>
                <li>Pressionar <strong>Ctrl + F5</strong> (Windows) ou <strong>Cmd + Shift + R</strong> (Mac)</li>
                <li>Ou abrir o DevTools (F12) → aba Network → marcar "Disable cache"</li>
                <li>Ou limpar cache do navegador manualmente</li>
            </ol>
        </div>

        <h2>🔍 Verificar Atualizações</h2>
        <button onclick="checkUpdates()" class="action-button">
            📡 Verificar API de Versão
        </button>

        <div id="api-result" class="result"></div>

        <h2>📋 URLs Úteis</h2>
        <div class="file-list">
            • <a href="api/version.php" target="_blank">API de Versão</a><br>
            • <a href="api/version.php?debug=1" target="_blank">API de Versão (Debug)</a><br>
            • <a href="index.php" target="_blank">Sistema Principal</a><br>
        </div>
    </div>

    <script>
        async function checkUpdates() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = '🔄 Verificando...';

            try {
                const response = await fetch('api/version.php?debug=1');
                const data = await response.json();

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ API funcionando!</strong><br>
                    Versão: ${data.version}<br>
                    Última atualização: ${data.last_update}<br>
                    Hash: ${data.version_hash}<br>
                    <details>
                        <summary>Ver detalhes</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                resultDiv.className = 'result';
                resultDiv.style.background = '#f8d7da';
                resultDiv.style.border = '1px solid #f5c6cb';
                resultDiv.style.color = '#721c24';
                resultDiv.innerHTML = `❌ Erro: ${error.message}`;
            }
        }
    </script>
</body>
</html>
