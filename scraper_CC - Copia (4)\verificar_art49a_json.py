#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def verificar_art49a_json():
    """Verifica como o Art. 49-A foi extraído no JSON"""
    
    print("=== VERIFICAÇÃO DO ART. 49-A NO JSON ===")
    
    # Carregar o JSON
    with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Buscar todas as versões do Art. 49-A
    artigos_49a = []
    for artigo in data['artigos']:
        numero = str(artigo['numero'])
        if '49-A' in numero:
            artigos_49a.append(artigo)
    
    print(f"Encontradas {len(artigos_49a)} versões do Art. 49-A:")
    
    for i, artigo in enumerate(artigos_49a, 1):
        print(f"\nVERSÃO {i}:")
        print(f"  Número: {artigo['numero']}")
        print(f"  Sufixo letra: '{artigo.get('sufixo_letra', 'N/A')}'")
        print(f"  Versão numérica: '{artigo.get('versao_numerica', 'N/A')}'")
        print(f"  Versão atual: {artigo.get('versao_atual', 'N/A')}")
        print(f"  Caput: {artigo['caput'][:100]}...")
        
        # Verificar se tem parágrafo único
        if 'paragrafo_unico' in artigo:
            print(f"  ✅ Parágrafo único: {artigo['paragrafo_unico'][:100]}...")
        else:
            print(f"  ❌ Parágrafo único: NÃO ENCONTRADO")
        
        # Verificar outros campos
        print(f"  Incisos: {len(artigo.get('incisos', {}))}")
        print(f"  Parágrafos: {len(artigo.get('paragrafos', {}))}")
        
        # Mostrar estrutura completa
        print(f"  Campos disponíveis: {list(artigo.keys())}")
    
    # Verificar se há algum parágrafo único em outros artigos próximos
    print(f"\n=== VERIFICANDO PARÁGRAFOS ÚNICOS EM ARTIGOS PRÓXIMOS ===")
    artigos_com_paragrafo_unico = []
    
    for artigo in data['artigos']:
        if 'paragrafo_unico' in artigo and artigo['paragrafo_unico']:
            numero_ordenacao = artigo.get('numero_ordenacao', 0)
            if 45 <= numero_ordenacao <= 55:  # Artigos próximos ao 49
                artigos_com_paragrafo_unico.append(artigo)
    
    print(f"Artigos próximos (45-55) com parágrafo único: {len(artigos_com_paragrafo_unico)}")
    for artigo in artigos_com_paragrafo_unico:
        print(f"  Art. {artigo['numero']}: {artigo['paragrafo_unico'][:80]}...")

if __name__ == "__main__":
    verificar_art49a_json()
