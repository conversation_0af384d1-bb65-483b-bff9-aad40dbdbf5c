# 🚨 SOLUÇÃO DEFINITIVA - ERRO SERVICE WORKER

## ❌ **ERRO PERSISTENTE:**
```
sw.js:89 Uncaught (in promise) TypeError: Failed to execute 'put' on 'Cache': 
Request scheme 'chrome-extension' is unsupported
```

## 🎯 **3 SOLUÇÕES DISPONÍVEIS:**

### **SOLUÇÃO 1: 🔄 FORÇAR ATUALIZAÇÃO DO SERVICE WORKER**

1. **Substituir arquivo em produção:**
   ```bash
   Upload: lexjus/sw.js → https://planejaaqui.com.br/lexjus/sw.js
   ```

2. **Forçar atualização no navegador:**
   ```bash
   1. Abrir DevTools (F12)
   2. Ir em Application → Service Workers
   3. Clicar em "Unregister" no Service Worker atual
   4. Recarregar página (Ctrl+Shift+R)
   ```

### **SOLUÇÃO 2: 🛑 DESATIVAR SERVICE WORKER TEMPORARIAMENTE**

**Opção A: Usar versão desabilitada**
```bash
1. Renomear: sw.js → sw_backup.js
2. Renomear: sw_disabled.js → sw.js
3. Upload para produção
```

**Opção B: Desativar no cache-manager.js**
Editar o arquivo `js/cache-manager.js` e comentar:
```javascript
// Comentar estas linhas:
// if ('serviceWorker' in navigator) {
//     navigator.serviceWorker.register('/lexjus/sw.js')
// }
```

### **SOLUÇÃO 3: 🧹 LIMPAR CACHE COMPLETO**

**No navegador:**
```bash
1. F12 → Application → Storage
2. Clicar "Clear storage"
3. Ctrl+Shift+R (hard refresh)
```

**Via código (adicionar ao console):**
```javascript
// Executar no console do navegador:
navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
        registration.unregister();
    }
});
caches.keys().then(function(names) {
    for(let name of names) {
        caches.delete(name);
    }
});
location.reload(true);
```

## 🔧 **DIAGNÓSTICO:**

O erro acontece porque:
1. **Service Worker antigo** ainda está ativo
2. **Cache do navegador** mantém versão antiga
3. **Extensões do Chrome** interferem no cache

## ✅ **VERIFICAÇÃO DA CORREÇÃO:**

Após aplicar qualquer solução, verificar:

**✅ DEVE APARECER:**
```
✅ Service Worker registrado
✅ Sistema funcionando sem erros
```

**❌ NÃO DEVE APARECER:**
```
❌ TypeError: Failed to execute 'put' on 'Cache'
❌ Request scheme 'chrome-extension' is unsupported
```

## 🚀 **RECOMENDAÇÃO:**

**Para correção imediata:** Use **SOLUÇÃO 2** (desativar temporariamente)
**Para correção definitiva:** Use **SOLUÇÃO 1** (forçar atualização)

---

**📁 Arquivos disponíveis:**
- `sw.js` - Versão corrigida
- `sw_disabled.js` - Versão sem cache (para emergência)

**🎯 Ambos eliminam o erro!**
