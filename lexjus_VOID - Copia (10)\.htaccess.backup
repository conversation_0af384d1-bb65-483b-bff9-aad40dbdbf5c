# Cache Busting e Otimizações para LexJus
# Este arquivo resolve problemas de cache do navegador

# Habilitar compressão GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configurações de Cache
<IfModule mod_expires.c>
    ExpiresActive On

    # Cache para arquivos estáticos (imagens, fontes)
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # Cache mais curto para CSS e JS (já que usamos cache busting)
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType application/x-javascript "access plus 1 week"
    ExpiresByType text/javascript "access plus 1 week"

    # Cache muito curto para HTML e PHP
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/x-httpd-php "access plus 1 hour"

    # Cache para JSON (dados dos artigos)
    ExpiresByType application/json "access plus 1 day"
</IfModule>

# Headers de Cache Control
<IfModule mod_headers.c>
    # Para arquivos com parâmetro ?v= (nosso cache busting)
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
        Header append Vary "Accept-Encoding"
    </FilesMatch>

    # Para arquivos PHP e HTML
    <FilesMatch "\.(php|html)$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </FilesMatch>

    # Para arquivos JSON
    <FilesMatch "\.json$">
        Header set Cache-Control "public, max-age=86400"
    </FilesMatch>

    # Remover ETags (conflitam com nosso cache busting)
    Header unset ETag
    FileETag None
</IfModule>

# Prevenir acesso direto a arquivos sensíveis
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Configurações de charset
AddDefaultCharset UTF-8
AddCharset UTF-8 .css .js .json .html .php

# Configurações de MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType application/json .json
    AddType image/svg+xml .svg
    AddType font/woff .woff
    AddType font/woff2 .woff2
</IfModule>
