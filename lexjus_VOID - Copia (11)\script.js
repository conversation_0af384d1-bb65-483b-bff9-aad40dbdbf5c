document.addEventListener('DOMContentLoaded', function () {
    const cards = document.querySelectorAll('.card');
    const modal = document.getElementById('artigoModal');
    const modalCloseBtn = modal.querySelector('.modal-close-btn');
    const searchInput = document.getElementById('searchInput');
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');

    // Variáveis de progresso
    let artigosLidos = new Set();
    let artigoAtual = null;

    // Variáveis de notas
    let notasArtigos = {};
    let editandoNota = null;

    // Variáveis de favoritos
    let favoritos = new Set();
    let listas = {};
    let editandoLista = null;
    let modalAnterior = null; // Para rastrear qual modal estava aberto antes

    // Inicializar sistema
    initializeTheme();

    // Carregar todos os dados em paralelo
    initializeData();

    // Função para inicializar todos os dados de forma otimizada
    async function initializeData() {
        // Mostrar loading global
        showGlobalLoading();

        try {
            // Atualizar status de carregamento
            updateLoadingStatus('Carregando progresso de leitura...');
            updateLoadingProgress(10);

            // Carregar todos os dados em paralelo
            const [progressData, notasData, favoritosData, listasData] = await Promise.allSettled([
                loadProgressFromServer(),
                loadNotasFromServer(),
                loadFavoritosFromServer(),
                loadListasFromServer()
            ]);

            // Atualizar progresso
            updateLoadingStatus('Processando dados...');
            updateLoadingProgress(70);

            // Processar resultados
            processLoadResults(progressData, notasData, favoritosData, listasData);

            // Atualizar progresso
            updateLoadingStatus('Atualizando interface...');
            updateLoadingProgress(90);

            // Atualizar interface uma única vez após tudo carregar
            updateAllVisualStates();

            // Finalizar carregamento
            updateLoadingStatus('Concluído!');
            updateLoadingProgress(100);

            // Pequeno delay para mostrar conclusão
            await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
            console.error('Erro ao inicializar dados:', error);
            updateLoadingStatus('Carregando dados locais...');
            updateLoadingProgress(50);

            // Fallback para localStorage
            loadFromLocalStorage();
            updateAllVisualStates();

            updateLoadingStatus('Concluído!');
            updateLoadingProgress(100);
            await new Promise(resolve => setTimeout(resolve, 300));
        } finally {
            // Esconder loading global
            hideGlobalLoading();
        }
    }

    // Funções do loading global
    function showGlobalLoading() {
        const loadingElement = document.getElementById('globalLoading');
        if (loadingElement) {
            loadingElement.classList.remove('hidden');
            // Adicionar efeito sutil aos cards
            cards.forEach(card => card.classList.add('loading'));
        }
    }

    function hideGlobalLoading() {
        const loadingElement = document.getElementById('globalLoading');
        if (loadingElement) {
            loadingElement.classList.add('hidden');
            // Animar entrada dos cards
            animateCardsEntrance();
        }
    }

    function animateCardsEntrance() {
        cards.forEach((card, index) => {
            // Remover loading e adicionar animação de entrada
            setTimeout(() => {
                card.classList.remove('loading');
                card.classList.add('card-loading');
            }, index * 50); // Delay escalonado para efeito cascata
        });
    }

    function updateLoadingStatus(message) {
        const statusElement = document.getElementById('loadingStatus');
        if (statusElement) {
            statusElement.textContent = message;
        }
    }

    function updateLoadingProgress(percentage) {
        const progressBar = document.getElementById('loadingProgressBar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
    }

    function processLoadResults(progressData, notasData, favoritosData, listasData) {
        // Processar progresso
        if (progressData.status === 'fulfilled' && progressData.value) {
            const progresso = progressData.value.progresso || {};
            const artigosLidosArray = Object.keys(progresso).filter(artigo => progresso[artigo].lido);
            artigosLidos = new Set(artigosLidosArray);
        } else {
            loadProgressFromLocalStorage();
        }

        // Processar notas
        if (notasData.status === 'fulfilled' && notasData.value) {
            const anotacoes = notasData.value.anotacoes || [];
            notasArtigos = {};
            anotacoes.forEach(anotacao => {
                if (!notasArtigos[anotacao.artigo_numero]) {
                    notasArtigos[anotacao.artigo_numero] = [];
                }
                notasArtigos[anotacao.artigo_numero].push({
                    id: anotacao.id.toString(),
                    texto: anotacao.texto,
                    dataCriacao: anotacao.data_criacao,
                    dataModificacao: anotacao.data_atualizacao
                });
            });
        } else {
            loadNotasFromLocalStorage();
        }

        // Processar favoritos
        if (favoritosData.status === 'fulfilled' && favoritosData.value) {
            const favoritosArray = favoritosData.value.favoritos.map(f => f.artigo_numero);
            favoritos = new Set(favoritosArray);
        } else {
            loadFavoritosFromLocalStorage();
        }

        // Processar listas
        if (listasData.status === 'fulfilled' && listasData.value) {
            const listasArray = listasData.value.listas || [];
            console.log('Processando listas do servidor:', listasArray);
            listas = {};
            listasArray.forEach(lista => {
                console.log('Lista:', lista.nome, 'Cor:', lista.cor);
                listas[lista.id] = {
                    nome: lista.nome,
                    cor: lista.cor || '#e74c3c', // ✅ Usar cor do servidor
                    artigos: lista.artigos || [],
                    dataCriacao: lista.data_criacao,
                    dataModificacao: lista.data_atualizacao
                };
            });
            console.log('Listas processadas:', listas);
        } else {
            console.log('Carregando listas do localStorage (fallback)');
            loadListasFromLocalStorage();
        }

        // Salvar tudo no localStorage para cache
        saveAllToLocalStorage();
    }

    function updateAllVisualStates() {
        updateCardsVisualState();
        updateFavoritosCount();
        updateListasCount();
        calculateStats();
        updateProgress();
    }

    function saveAllToLocalStorage() {
        saveProgress();
        saveNotas();
        saveFavoritos();
        saveListas();
    }

    function loadFromLocalStorage() {
        loadProgressFromLocalStorage();
        loadNotasFromLocalStorage();
        loadFavoritosFromLocalStorage();
        loadListasFromLocalStorage();
    }

    // Funções para carregar do servidor
    async function loadProgressFromServer() {
        const response = await fetch('./api/progresso.php');
        if (!response.ok) throw new Error('Erro ao carregar progresso');
        return await response.json();
    }

    async function loadNotasFromServer() {
        const response = await fetch('./api/anotacoes.php');
        if (!response.ok) throw new Error('Erro ao carregar notas');
        return await response.json();
    }

    async function loadFavoritosFromServer() {
        const response = await fetch('./api/favoritos.php');
        if (!response.ok) throw new Error('Erro ao carregar favoritos');
        return await response.json();
    }

    async function loadListasFromServer() {
        const response = await fetch('./api/listas.php');
        if (!response.ok) throw new Error('Erro ao carregar listas');
        const data = await response.json();
        console.log('Dados das listas carregados do servidor:', data);
        return data;
    }

    // Funções para carregar do localStorage
    function loadProgressFromLocalStorage() {
        const savedProgress = localStorage.getItem('artigosLidos');
        if (savedProgress) {
            try {
                const progressArray = JSON.parse(savedProgress);
                artigosLidos = new Set(progressArray);
            } catch (e) {
                console.error('Erro ao carregar progresso do localStorage:', e);
                artigosLidos = new Set();
            }
        }
    }

    function loadNotasFromLocalStorage() {
        const savedNotas = localStorage.getItem('notasArtigos');
        if (savedNotas) {
            try {
                notasArtigos = JSON.parse(savedNotas);
            } catch (e) {
                console.error('Erro ao carregar notas do localStorage:', e);
                notasArtigos = {};
            }
        }
    }

    function loadFavoritosFromLocalStorage() {
        const savedFavoritos = localStorage.getItem('favoritos');
        if (savedFavoritos) {
            try {
                const favoritosArray = JSON.parse(savedFavoritos);
                favoritos = new Set(favoritosArray);
            } catch (e) {
                console.error('Erro ao carregar favoritos do localStorage:', e);
                favoritos = new Set();
            }
        }
    }

    function loadListasFromLocalStorage() {
        const savedListas = localStorage.getItem('listas');
        if (savedListas) {
            try {
                listas = JSON.parse(savedListas);
            } catch (e) {
                console.error('Erro ao carregar listas do localStorage:', e);
                listas = {};
            }
        }
    }

    // Funcionalidade de busca
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterCards(searchTerm);
        });
    }

    // Toggle de tema
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Elementos do Modal para popular
    const modalArtigoNumero = document.getElementById('modalArtigoNumero');
    const modalArtigoCaput = document.getElementById('modalArtigoCaput');

    const modalIncisosContainer = document.getElementById('modalArtigoIncisosContainer');
    const modalIncisosLista = document.getElementById('modalArtigoIncisosLista');

    const modalParagrafoUnicoContainer = document.getElementById('modalArtigoParagrafoUnicoContainer');
    const modalParagrafoUnico = document.getElementById('modalArtigoParagrafoUnico');

    const modalParagrafosNumeradosContainer = document.getElementById('modalArtigoParagrafosNumeradosContainer');
    const modalParagrafosNumeradosLista = document.getElementById('modalArtigoParagrafosNumeradosLista');

    const modalAlineasArtigoContainer = document.getElementById('modalArtigoAlineasArtigoContainer');
    const modalAlineasArtigoLista = document.getElementById('modalArtigoAlineasArtigoLista');

    // Elementos de navegação do modal
    const btnAnterior = document.getElementById('btnAnterior');
    const btnProximo = document.getElementById('btnProximo');

    // Variáveis para navegação
    let cardsArray = Array.from(cards);
    let favoritosCardsArray = []; // Array específico para favoritos
    let currentCardIndex = 0;
    let navegacaoTipo = 'todos'; // 'todos' ou 'favoritos'

    // Função para abrir modal com navegação
    function abrirModalComNavegacao(cardIndex) {
        const card = cardsArray[cardIndex];
        if (!card) return;

        currentCardIndex = cardIndex;

        const numero = card.dataset.artigo;
        const caput = card.dataset.caput;
        const incisos = JSON.parse(card.dataset.incisos || '[]');
        const paragrafoUnico = card.dataset.paragrafoUnico;
        const paragrafosNumerados = JSON.parse(card.dataset.paragrafosNumerados || '[]');
        const alineasDoArtigo = JSON.parse(card.dataset.alineasArtigo || '[]');

        // Definir artigo atual
        artigoAtual = numero;

        // Atualizar estado dos botões de navegação
        atualizarBotoesNavegacao();

        modalArtigoNumero.textContent = numero;
        modalArtigoCaput.innerHTML = caput; // Usar innerHTML para interpretar tags <br> se houver

        // Limpa listas anteriores
        modalIncisosLista.innerHTML = '';
        modalParagrafoUnico.innerHTML = '';
        modalParagrafosNumeradosLista.innerHTML = '';
        modalAlineasArtigoLista.innerHTML = '';

        // Popula Incisos com suas alíneas aninhadas
        if (incisos && incisos.length > 0) {
            // Processar alíneas do artigo para associá-las aos incisos corretos
            const alineasPorInciso = {};
            if (alineasDoArtigo && alineasDoArtigo.length > 0) {
                alineasDoArtigo.forEach(alinea => {
                    // Extrair o inciso correspondente da descrição
                    const match = alinea.match(/\(pertencente a: inciso - ([^)]+)\)/);
                    if (match) {
                        const incisoKey = match[1].trim();
                        if (!alineasPorInciso[incisoKey]) {
                            alineasPorInciso[incisoKey] = [];
                        }
                        // Extrair apenas a alínea sem a descrição de pertencimento
                        const alineaLimpa = alinea.replace(/\s*\(pertencente a:.*?\)/, '');
                        alineasPorInciso[incisoKey].push(alineaLimpa);
                    }
                });
            }

            incisos.forEach(inciso => {
                const li = document.createElement('li');
                li.innerHTML = inciso;

                // Verificar se este inciso tem alíneas
                let alineasEncontradas = [];

                // Buscar alíneas que pertencem a este inciso
                Object.keys(alineasPorInciso).forEach(key => {
                    // Extrair o número do inciso atual (ex: "XXVIII" de "XXVIII - são assegurados...")
                    const incisoNumero = inciso.split(' - ')[0].trim(); // Ex: "XXVIII"

                    // Verificar se a chave começa com o número do inciso seguido de espaço e hífen
                    if (key.startsWith(incisoNumero + ' - ')) {
                        alineasEncontradas = alineasPorInciso[key];
                    }
                });

                // Se encontrou alíneas, criar uma sublista
                if (alineasEncontradas.length > 0) {
                    const subUl = document.createElement('ul');
                    subUl.style.cssText = `
                        margin-top: 0.75rem;
                        padding-left: 1.5rem;
                        list-style: none;
                    `;

                    alineasEncontradas.forEach(alinea => {
                        const subLi = document.createElement('li');
                        subLi.innerHTML = alinea;
                        subLi.style.cssText = `
                            margin-bottom: 0.5rem;
                            padding: 0.75rem;
                            background: var(--hover);
                            border-radius: 6px;
                            border-left: 3px solid var(--primary);
                            font-size: 0.9rem;
                            transition: all 0.2s ease;
                        `;

                        // Adicionar hover effect
                        subLi.addEventListener('mouseenter', function() {
                            this.style.background = 'rgba(0, 0, 139, 0.1)';
                            this.style.transform = 'translateX(5px)';
                        });

                        subLi.addEventListener('mouseleave', function() {
                            this.style.background = 'var(--hover)';
                            this.style.transform = 'translateX(0)';
                        });

                        subUl.appendChild(subLi);
                    });

                    li.appendChild(subUl);
                }

                modalIncisosLista.appendChild(li);
            });
            modalIncisosContainer.style.display = 'block';
        } else {
            modalIncisosContainer.style.display = 'none';
        }

        // Popula Parágrafo Único
        if (paragrafoUnico) {
            modalParagrafoUnico.innerHTML = paragrafoUnico; // Usar innerHTML
            modalParagrafoUnicoContainer.style.display = 'block';
        } else {
            modalParagrafoUnicoContainer.style.display = 'none';
        }

        // Popula Parágrafos Numerados e suas Alíneas
        if (paragrafosNumerados && paragrafosNumerados.length > 0) {
            paragrafosNumerados.forEach(pn => {
                const pnBloco = document.createElement('div');
                pnBloco.className = 'paragrafo-numerado-bloco';

                const pnNumero = document.createElement('span');
                pnNumero.className = 'paragrafo-numero';
                pnNumero.innerHTML = pn.numero;
                pnBloco.appendChild(pnNumero);

                const pnTexto = document.createElement('span');
                pnTexto.className = 'paragrafo-texto';
                pnTexto.innerHTML = pn.texto;
                pnBloco.appendChild(pnTexto);

                if (pn.alineas && pn.alineas.length > 0) {
                    const alineasUl = document.createElement('ul');
                    alineasUl.className = 'alineas-lista';
                    pn.alineas.forEach(alinea => {
                        const li = document.createElement('li');
                        li.innerHTML = alinea;
                        alineasUl.appendChild(li);
                    });
                    pnBloco.appendChild(alineasUl);
                }
                modalParagrafosNumeradosLista.appendChild(pnBloco);
            });
            modalParagrafosNumeradosContainer.style.display = 'block';
        } else {
            modalParagrafosNumeradosContainer.style.display = 'none';
        }

        // Esconder container de alíneas do artigo (não usado)
        modalAlineasArtigoContainer.style.display = 'none';

        // Atualizar botão de marcar como lido
        updateReadButton();

        // Atualizar botão de favorito
        updateFavoritoButton();

        // Atualizar seção de notas
        updateNotasSection();

        modal.style.display = 'block'; // Exibe o modal

        // Resetar scroll para o topo quando abrir o modal
        setTimeout(() => {
            resetarScrollModal();
        }, 50);
    }

    // Função para abrir modal com navegação específica para favoritos
    function abrirModalComNavegacaoFavoritos(favoritoIndex) {
        const card = favoritosCardsArray[favoritoIndex];
        if (!card) return;

        currentCardIndex = favoritoIndex;

        const numero = card.dataset.artigo;
        const caput = card.dataset.caput;
        const incisos = JSON.parse(card.dataset.incisos || '[]');
        const paragrafoUnico = card.dataset.paragrafoUnico;
        const paragrafosNumerados = JSON.parse(card.dataset.paragrafosNumerados || '[]');
        const alineasDoArtigo = JSON.parse(card.dataset.alineasArtigo || '[]');

        // Definir artigo atual
        artigoAtual = numero;

        // Atualizar estado dos botões de navegação
        atualizarBotoesNavegacao();

        modalArtigoNumero.textContent = numero;
        modalArtigoCaput.innerHTML = caput; // Usar innerHTML para interpretar tags <br> se houver

        // Limpa listas anteriores
        modalIncisosLista.innerHTML = '';
        modalParagrafoUnico.innerHTML = '';
        modalParagrafosNumeradosLista.innerHTML = '';
        modalAlineasArtigoLista.innerHTML = '';

        // Popula Incisos com suas alíneas aninhadas
        if (incisos && incisos.length > 0) {
            // Processar alíneas do artigo para associá-las aos incisos corretos
            const alineasPorInciso = {};
            if (alineasDoArtigo && alineasDoArtigo.length > 0) {
                alineasDoArtigo.forEach(alinea => {
                    // Extrair o inciso correspondente da descrição
                    const match = alinea.match(/\(pertencente a: inciso - ([^)]+)\)/);
                    if (match) {
                        const incisoKey = match[1].trim();
                        if (!alineasPorInciso[incisoKey]) {
                            alineasPorInciso[incisoKey] = [];
                        }
                        // Extrair apenas a alínea sem a descrição de pertencimento
                        const alineaLimpa = alinea.replace(/\s*\(pertencente a:.*?\)/, '');
                        alineasPorInciso[incisoKey].push(alineaLimpa);
                    }
                });
            }

            incisos.forEach(inciso => {
                const li = document.createElement('li');
                li.innerHTML = inciso;

                // Verificar se este inciso tem alíneas
                let alineasEncontradas = [];

                // Buscar alíneas que pertencem a este inciso
                Object.keys(alineasPorInciso).forEach(key => {
                    // Extrair o número do inciso atual (ex: "XXVIII" de "XXVIII - são assegurados...")
                    const incisoNumero = inciso.split(' - ')[0].trim(); // Ex: "XXVIII"

                    // Verificar se a chave começa com o número do inciso seguido de espaço e hífen
                    if (key.startsWith(incisoNumero + ' - ')) {
                        alineasEncontradas = alineasPorInciso[key];
                    }
                });

                // Se encontrou alíneas, criar uma sublista
                if (alineasEncontradas.length > 0) {
                    const subUl = document.createElement('ul');
                    subUl.style.cssText = `
                        margin-top: 0.75rem;
                        padding-left: 1.5rem;
                        list-style: none;
                    `;

                    alineasEncontradas.forEach(alinea => {
                        const subLi = document.createElement('li');
                        subLi.innerHTML = alinea;
                        subLi.style.cssText = `
                            margin-bottom: 0.5rem;
                            padding: 0.75rem;
                            background: var(--hover);
                            border-radius: 6px;
                            border-left: 3px solid var(--primary);
                            font-size: 0.9rem;
                            transition: all 0.2s ease;
                        `;

                        // Adicionar hover effect
                        subLi.addEventListener('mouseenter', function() {
                            this.style.background = 'rgba(0, 0, 139, 0.1)';
                            this.style.transform = 'translateX(5px)';
                        });

                        subLi.addEventListener('mouseleave', function() {
                            this.style.background = 'var(--hover)';
                            this.style.transform = 'translateX(0)';
                        });

                        subUl.appendChild(subLi);
                    });

                    li.appendChild(subUl);
                }

                modalIncisosLista.appendChild(li);
            });
            modalIncisosContainer.style.display = 'block';
        } else {
            modalIncisosContainer.style.display = 'none';
        }

        // Popula Parágrafo Único
        if (paragrafoUnico) {
            modalParagrafoUnico.innerHTML = paragrafoUnico; // Usar innerHTML
            modalParagrafoUnicoContainer.style.display = 'block';
        } else {
            modalParagrafoUnicoContainer.style.display = 'none';
        }

        // Popula Parágrafos Numerados e suas Alíneas
        if (paragrafosNumerados && paragrafosNumerados.length > 0) {
            paragrafosNumerados.forEach(pn => {
                const pnBloco = document.createElement('div');
                pnBloco.className = 'paragrafo-numerado-bloco';

                const pnNumero = document.createElement('span');
                pnNumero.className = 'paragrafo-numero';
                pnNumero.innerHTML = pn.numero;
                pnBloco.appendChild(pnNumero);

                const pnTexto = document.createElement('span');
                pnTexto.className = 'paragrafo-texto';
                pnTexto.innerHTML = pn.texto;
                pnBloco.appendChild(pnTexto);

                if (pn.alineas && pn.alineas.length > 0) {
                    const alineasUl = document.createElement('ul');
                    alineasUl.className = 'alineas-lista';
                    pn.alineas.forEach(alinea => {
                        const li = document.createElement('li');
                        li.innerHTML = alinea;
                        alineasUl.appendChild(li);
                    });
                    pnBloco.appendChild(alineasUl);
                }
                modalParagrafosNumeradosLista.appendChild(pnBloco);
            });
            modalParagrafosNumeradosContainer.style.display = 'block';
        } else {
            modalParagrafosNumeradosContainer.style.display = 'none';
        }

        // Esconder container de alíneas do artigo (não usado)
        modalAlineasArtigoContainer.style.display = 'none';

        // Atualizar botão de marcar como lido
        updateReadButton();

        // Atualizar botão de favorito
        updateFavoritoButton();

        // Atualizar seção de notas
        updateNotasSection();

        modal.style.display = 'block'; // Exibe o modal

        // Resetar scroll para o topo quando abrir o modal
        setTimeout(() => {
            resetarScrollModal();
        }, 50);
    }

    // Funções auxiliares para navegação
    function atualizarBotoesNavegacao() {
        const arrayAtual = navegacaoTipo === 'favoritos' ? favoritosCardsArray : cardsArray;

        if (btnAnterior) {
            btnAnterior.disabled = currentCardIndex === 0;
        }
        if (btnProximo) {
            btnProximo.disabled = currentCardIndex === arrayAtual.length - 1;
        }
    }

    function navegarParaArtigo(direction) {
        const arrayAtual = navegacaoTipo === 'favoritos' ? favoritosCardsArray : cardsArray;
        const newIndex = currentCardIndex + direction;

        if (newIndex >= 0 && newIndex < arrayAtual.length) {
            // Adicionar animação de transição
            const modalContent = document.querySelector('.modal-content');
            modalContent.classList.add('transitioning');

            setTimeout(() => {
                if (navegacaoTipo === 'favoritos') {
                    abrirModalComNavegacaoFavoritos(newIndex);
                } else {
                    abrirModalComNavegacao(newIndex);
                }
                modalContent.classList.remove('transitioning');

                // Resetar scroll para o topo do modal
                resetarScrollModal();
            }, 150);
        }
    }

    // Função para scroll suave dentro do modal
    function scrollModalContent(pixels) {
        const modalContent = document.querySelector('.modal-content');
        if (modalContent) {
            modalContent.scrollBy({
                top: pixels,
                behavior: 'smooth'
            });
        }
    }

    // Função para resetar scroll do modal para o topo
    function resetarScrollModal() {
        const modalContent = document.querySelector('.modal-content');
        if (modalContent) {
            modalContent.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    }

    // Event listeners para navegação
    if (btnAnterior) {
        btnAnterior.addEventListener('click', () => navegarParaArtigo(-1));
    }

    if (btnProximo) {
        btnProximo.addEventListener('click', () => navegarParaArtigo(1));
    }

    // Navegação por teclado
    document.addEventListener('keydown', function(e) {
        // Só funcionar se o modal estiver aberto
        if (modal.style.display === 'block') {
            // Verificar se não está editando uma nota (para não interferir na digitação)
            const isEditingNote = document.querySelector('#editorNovaNota:focus') ||
                                 document.querySelector('input:focus') ||
                                 document.querySelector('textarea:focus');

            if (isEditingNote) {
                // Se estiver editando, só permitir ESC para fechar
                if (e.key === 'Escape') {
                    e.preventDefault();
                    modal.style.display = 'none';
                    modal.classList.remove('modal-priority');
                }
                return;
            }

            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    navegarParaArtigo(-1);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    navegarParaArtigo(1);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    scrollModalContent(-100); // Scroll para cima
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    scrollModalContent(100); // Scroll para baixo
                    break;
                case ' ': // Espaço
                case 'Enter':
                    e.preventDefault();
                    if (artigoAtual) {
                        toggleReadStatus(artigoAtual);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    modal.style.display = 'none';
                    modal.classList.remove('modal-priority');
                    break;
            }
        }
    });

    // Atualizar event listeners dos cards para usar a nova função
    cards.forEach((card, index) => {
        card.addEventListener('click', function () {
            // Resetar para navegação normal (todos os artigos)
            navegacaoTipo = 'todos';
            abrirModalComNavegacao(index);
        });
    });

    // Fecha o modal ao clicar no botão 'x'
    modalCloseBtn.addEventListener('click', function () {
        modal.style.display = 'none';
        modal.classList.remove('modal-priority');

        // Reabrir modal anterior se necessário
        if (modalAnterior === 'favoritosModal') {
            document.getElementById('favoritosModal').style.display = 'block';
            modalAnterior = null;
        } else if (modalAnterior === 'listasModal') {
            document.getElementById('listasModal').style.display = 'block';
            modalAnterior = null;
        }
    });

    // Fecha o modal ao clicar fora da área de conteúdo do modal
    window.addEventListener('click', function (event) {
        if (event.target === modal) {
            modal.style.display = 'none';
            modal.classList.remove('modal-priority');

            // Reabrir modal anterior se necessário
            if (modalAnterior === 'favoritosModal') {
                document.getElementById('favoritosModal').style.display = 'block';
                modalAnterior = null;
            } else if (modalAnterior === 'listasModal') {
                document.getElementById('listasModal').style.display = 'block';
                modalAnterior = null;
            }
        }
    });

    // Event listener para o botão "Marcar como Lido"
    const btnMarcarLido = document.getElementById('btnMarcarLido');
    if (btnMarcarLido) {
        btnMarcarLido.addEventListener('click', function() {
            if (artigoAtual) {
                toggleReadStatus(artigoAtual);
            }
        });
    }

    // Event listeners para o sistema de notas
    const btnAdicionarNota = document.getElementById('btnAdicionarNota');
    const btnSalvarNota = document.getElementById('btnSalvarNota');
    const btnCancelarNota = document.getElementById('btnCancelarNota');
    const formNovaNota = document.getElementById('formNovaNota');
    const editorNovaNota = document.getElementById('editorNovaNota');

    if (btnAdicionarNota) {
        btnAdicionarNota.addEventListener('click', function() {
            mostrarFormNovaNota();
        });
    }

    if (btnSalvarNota) {
        btnSalvarNota.addEventListener('click', function() {
            salvarNota();
        });
    }

    if (btnCancelarNota) {
        btnCancelarNota.addEventListener('click', function() {
            cancelarNota();
        });
    }

    // Atalho Enter + Ctrl para salvar nota
    if (editorNovaNota) {
        editorNovaNota.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                salvarNota();
            }

            // Atalhos de formatação
            if (e.ctrlKey) {
                switch(e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault();
                        document.execCommand('bold');
                        updateToolbarState();
                        break;
                    case 'i':
                        e.preventDefault();
                        document.execCommand('italic');
                        updateToolbarState();
                        break;
                    case 'u':
                        e.preventDefault();
                        document.execCommand('underline');
                        updateToolbarState();
                        break;
                }
            }
        });

        // Atualizar estado da toolbar quando a seleção muda
        editorNovaNota.addEventListener('mouseup', updateToolbarState);
        editorNovaNota.addEventListener('keyup', updateToolbarState);
    }

    // Event listeners para a toolbar de formatação
    initializeToolbar();

    // Event listeners para favoritos
    initializeFavoritos();

    // Função para calcular estatísticas
    function calculateStats() {
        // Atualizar elementos de estatísticas
        const totalArtigosEl = document.getElementById('totalArtigos');
        const artigosLidosEl = document.getElementById('artigosLidos');

        if (totalArtigosEl) {
            animateNumber(totalArtigosEl, cards.length);
        }
        if (artigosLidosEl) {
            animateNumber(artigosLidosEl, artigosLidos.size);
        }
    }

    // Função para animar números
    function animateNumber(element, target) {
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 30);
    }

    // Função para filtrar cards
    function filterCards(searchTerm) {
        // Se o termo de busca estiver vazio, mostrar todos os cards
        if (!searchTerm || searchTerm.trim() === '') {
            cards.forEach(card => {
                card.style.display = 'flex';
                card.style.opacity = '1';
                card.classList.remove('exact-match', 'partial-match');
            });
            hideNoResultsMessage();
            // Remove contador de resultados
            const existingCounter = document.getElementById('resultsCounter');
            if (existingCounter) {
                existingCounter.remove();
            }
            return;
        }

        let hasResults = false;
        let exactMatches = [];
        let partialMatches = [];

        cards.forEach(card => {
            const artigo = (card.dataset.artigo || '').toLowerCase();
            const caput = (card.dataset.caput || '').toLowerCase();

            // Buscar também nos incisos e parágrafos
            let incisos = '';
            let paragrafos = '';

            try {
                const incisosArray = JSON.parse(card.dataset.incisos || '[]');
                incisos = incisosArray.join(' ').toLowerCase();

                const paragrafosArray = JSON.parse(card.dataset.paragrafosNumerados || '[]');
                paragrafos = paragrafosArray.map(p => p.texto || '').join(' ').toLowerCase();
            } catch (e) {
                // Se houver erro no parse, continuar sem os dados
            }

            const paragrafoUnico = (card.dataset.paragrafoUnico || '').toLowerCase();

            // Primeiro: verificar correspondência exata do artigo
            let isExactMatch = false;
            let isPartialMatch = false;

            // Busca exata por número de artigo
            if (/^\d+$/.test(searchTerm)) {
                // Se for apenas um número, buscar correspondência exata
                isExactMatch = artigo === `art. ${searchTerm}º` ||
                              artigo === `art. ${searchTerm}.` ||
                              artigo === `art.${searchTerm}` ||
                              artigo === `artigo ${searchTerm}`;
            } else if (searchTerm.startsWith('art')) {
                // Se começar com "art", buscar correspondência exata
                isExactMatch = artigo === searchTerm || artigo.startsWith(searchTerm);
            }

            // Se não for correspondência exata, verificar correspondência parcial
            if (!isExactMatch) {
                isPartialMatch = caput.includes(searchTerm) ||
                               incisos.includes(searchTerm) ||
                               paragrafos.includes(searchTerm) ||
                               paragrafoUnico.includes(searchTerm);

                // Busca por palavras parciais (mínimo 3 caracteres)
                if (!isPartialMatch && searchTerm.length >= 3) {
                    const words = searchTerm.split(' ');
                    isPartialMatch = words.some(word =>
                        word.length >= 3 && (
                            caput.includes(word) ||
                            incisos.includes(word) ||
                            paragrafos.includes(word) ||
                            paragrafoUnico.includes(word)
                        )
                    );
                }
            }

            // Remover classes anteriores
            card.classList.remove('exact-match', 'partial-match');

            if (isExactMatch) {
                card.style.display = 'flex';
                card.style.opacity = '1';
                card.classList.add('exact-match');
                card.style.animation = 'fadeInUp 0.3s ease-out';
                exactMatches.push(card);
                hasResults = true;
            } else if (isPartialMatch) {
                card.style.display = 'flex';
                card.style.opacity = '1';
                card.classList.add('partial-match');
                card.style.animation = 'fadeInUp 0.3s ease-out';
                partialMatches.push(card);
                hasResults = true;
            } else {
                card.style.display = 'none';
                card.style.opacity = '0';
            }
        });

        // Reorganizar: correspondências exatas primeiro
        const gridContainer = document.querySelector('.grid-container');
        exactMatches.forEach(card => {
            gridContainer.appendChild(card);
        });
        partialMatches.forEach(card => {
            gridContainer.appendChild(card);
        });

        // Contar resultados visíveis
        const totalResults = exactMatches.length + partialMatches.length;

        // Se não houver resultados, mostrar uma mensagem
        if (!hasResults) {
            showNoResultsMessage(searchTerm);
        } else {
            hideNoResultsMessage();
            showResultsCount(totalResults, searchTerm, exactMatches.length, partialMatches.length);
        }
    }

    // Função para mostrar mensagem de "nenhum resultado"
    function showNoResultsMessage(searchTerm) {
        hideNoResultsMessage(); // Remove mensagem anterior se existir

        const gridContainer = document.querySelector('.grid-container');
        const noResultsDiv = document.createElement('div');
        noResultsDiv.id = 'noResultsMessage';
        noResultsDiv.style.cssText = `
            grid-column: 1 / -1;
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
            font-size: 1.2rem;
        `;
        noResultsDiv.innerHTML = `
            <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <p>Nenhum artigo encontrado para "<strong>${searchTerm}</strong>"</p>
            <p style="font-size: 1rem; margin-top: 0.5rem;">Tente buscar por número do artigo, palavras-chave ou termos específicos.</p>
        `;

        gridContainer.appendChild(noResultsDiv);
    }

    // Função para esconder mensagem de "nenhum resultado"
    function hideNoResultsMessage() {
        const existingMessage = document.getElementById('noResultsMessage');
        if (existingMessage) {
            existingMessage.remove();
        }
    }

    // Função para mostrar contagem de resultados
    function showResultsCount(totalCount, searchTerm, exactCount = 0, partialCount = 0) {
        // Remove contador anterior se existir
        const existingCounter = document.getElementById('resultsCounter');
        if (existingCounter) {
            existingCounter.remove();
        }

        // Se não há termo de busca, não mostrar contador
        if (!searchTerm || searchTerm.trim() === '') {
            return;
        }

        const searchContainer = document.querySelector('.search-container');
        const counterDiv = document.createElement('div');
        counterDiv.id = 'resultsCounter';
        counterDiv.style.cssText = `
            text-align: center;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        `;

        let message = '';
        if (exactCount > 0 && partialCount > 0) {
            message = `
                <i class="fas fa-bullseye" style="color: var(--primary); margin-right: 0.5rem;"></i>
                <strong style="color: var(--primary);">${exactCount}</strong> correspondência${exactCount !== 1 ? 's' : ''} exata${exactCount !== 1 ? 's' : ''} +
                <strong>${partialCount}</strong> relacionada${partialCount !== 1 ? 's' : ''} para "<strong>${searchTerm}</strong>"
            `;
        } else if (exactCount > 0) {
            message = `
                <i class="fas fa-bullseye" style="color: var(--primary); margin-right: 0.5rem;"></i>
                <strong style="color: var(--primary);">${exactCount}</strong> correspondência${exactCount !== 1 ? 's' : ''} exata${exactCount !== 1 ? 's' : ''} para "<strong>${searchTerm}</strong>"
            `;
        } else {
            message = `
                <i class="fas fa-search" style="color: var(--primary); margin-right: 0.5rem;"></i>
                ${totalCount} resultado${totalCount !== 1 ? 's' : ''} relacionado${totalCount !== 1 ? 's' : ''} para "<strong>${searchTerm}</strong>"
            `;
        }

        counterDiv.innerHTML = message;
        searchContainer.appendChild(counterDiv);
    }

    // Funções de tema
    function initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.body.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);
    }

    function toggleTheme() {
        const currentTheme = document.body.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeIcon(newTheme);
    }

    function updateThemeIcon(theme) {
        if (themeIcon) {
            if (theme === 'dark') {
                themeIcon.className = 'fas fa-sun';
            } else {
                themeIcon.className = 'fas fa-moon';
            }
        }
    }

    // Funções de progresso de leitura
    async function loadProgress() {
        try {
            // Tentar carregar do servidor primeiro
            const response = await fetch('./api/progresso.php');
            if (response.ok) {
                const data = await response.json();
                const progresso = data.progresso || {};

                // Converter para Set com artigos lidos
                const artigosLidosArray = Object.keys(progresso).filter(artigo => progresso[artigo].lido);
                artigosLidos = new Set(artigosLidosArray);

                // Salvar no localStorage para cache
                saveProgress();
                return;
            }
        } catch (error) {
            console.error('Erro ao carregar progresso do servidor:', error);
        }

        // Fallback para localStorage se o servidor falhar
        const savedProgress = localStorage.getItem('artigosLidos');
        if (savedProgress) {
            try {
                const progressArray = JSON.parse(savedProgress);
                artigosLidos = new Set(progressArray);
            } catch (e) {
                console.error('Erro ao carregar progresso do localStorage:', e);
                artigosLidos = new Set();
            }
        }
    }

    function saveProgress() {
        const progressArray = Array.from(artigosLidos);
        localStorage.setItem('artigosLidos', JSON.stringify(progressArray));
    }

    async function toggleReadStatus(artigo) {
        const wasRead = artigosLidos.has(artigo);
        const willBeRead = !wasRead;

        try {
            // Atualizar no servidor
            const response = await fetch('./api/progresso.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    artigo_numero: artigo,
                    lido: willBeRead
                })
            });

            if (!response.ok) {
                throw new Error('Erro ao atualizar progresso no servidor');
            }

            // Atualizar localStorage apenas se o servidor confirmou
            if (willBeRead) {
                artigosLidos.add(artigo);
            } else {
                artigosLidos.delete(artigo);
            }

            saveProgress();
            updateProgress();
            updateReadButton();
            updateCardsVisualState();
            calculateStats();

        } catch (error) {
            console.error('Erro ao atualizar progresso:', error);
            alert('Erro ao atualizar progresso. Tente novamente.');
        }
    }

    function updateProgress() {
        const totalArtigos = cards.length;
        const artigosLidosCount = artigosLidos.size;
        const percentage = totalArtigos > 0 ? Math.round((artigosLidosCount / totalArtigos) * 100) : 0;

        // Atualizar texto do progresso
        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = `${artigosLidosCount} de ${totalArtigos} artigos lidos (${percentage}%)`;
        }

        // Atualizar barra de progresso
        const progressFill = document.getElementById('progressFill');
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
    }

    function updateReadButton() {
        const btnMarcarLido = document.getElementById('btnMarcarLido');

        if (btnMarcarLido && artigoAtual) {
            const btnTexto = btnMarcarLido.querySelector('span');
            const isLido = artigosLidos.has(artigoAtual);

            if (isLido) {
                btnMarcarLido.classList.add('lido');
                if (btnTexto) btnTexto.textContent = 'Marcar como Não Lido';
                btnMarcarLido.querySelector('i').className = 'fas fa-check-circle';
            } else {
                btnMarcarLido.classList.remove('lido');
                if (btnTexto) btnTexto.textContent = 'Marcar como Lido';
                btnMarcarLido.querySelector('i').className = 'far fa-check-circle';
            }
        }
    }

    function updateCardsVisualState() {
        // Usar requestAnimationFrame para otimizar performance
        requestAnimationFrame(() => {
            cards.forEach(card => {
                const artigo = card.dataset.artigo;

                // Atualizar classe de artigo lido
                card.classList.toggle('artigo-lido', artigosLidos.has(artigo));

                // Remover indicadores existentes de forma mais eficiente
                const existingIndicators = card.querySelectorAll('.card-indicator');
                existingIndicators.forEach(indicator => indicator.remove());

                // Verificar estados
                const temNotas = notasArtigos[artigo] && notasArtigos[artigo].length > 0;
                const temFavorito = favoritos.has(artigo);
                const temListas = Object.values(listas).some(lista => lista.artigos.includes(artigo));

                // Atualizar classes para compatibilidade
                card.classList.toggle('tem-notas', temNotas);
                card.classList.toggle('favorito', temFavorito);
                card.classList.toggle('tem-listas', temListas);

                // Criar indicadores de forma mais eficiente
                const indicatorsToCreate = [];

                if (temFavorito) {
                    indicatorsToCreate.push({
                        className: 'card-indicator indicator-favorito',
                        icon: 'fas fa-heart',
                        title: 'Favorito'
                    });
                }

                if (temListas) {
                    indicatorsToCreate.push({
                        className: 'card-indicator indicator-listas',
                        icon: 'fas fa-bookmark',
                        title: 'Em lista personalizada'
                    });
                }

                if (temNotas) {
                    indicatorsToCreate.push({
                        className: 'card-indicator indicator-notas',
                        icon: 'fas fa-sticky-note',
                        title: 'Tem anotações'
                    });
                }

                // Criar todos os indicadores de uma vez
                indicatorsToCreate.forEach(indicator => {
                    const indicatorElement = document.createElement('div');
                    indicatorElement.className = indicator.className;
                    indicatorElement.innerHTML = `<i class="${indicator.icon}" title="${indicator.title}"></i>`;
                    card.appendChild(indicatorElement);
                });
            });
        });
    }

    // Funções do sistema de notas
    async function loadNotas() {
        try {
            // Tentar carregar do servidor primeiro
            const response = await fetch('./api/anotacoes.php');
            if (response.ok) {
                const data = await response.json();
                const anotacoes = data.anotacoes || [];

                // Converter para estrutura compatível
                notasArtigos = {};
                anotacoes.forEach(anotacao => {
                    if (!notasArtigos[anotacao.artigo_numero]) {
                        notasArtigos[anotacao.artigo_numero] = [];
                    }
                    notasArtigos[anotacao.artigo_numero].push({
                        id: anotacao.id.toString(),
                        texto: anotacao.texto,
                        dataCriacao: anotacao.data_criacao,
                        dataModificacao: anotacao.data_atualizacao
                    });
                });

                // Salvar no localStorage para cache
                saveNotas();
                return;
            }
        } catch (error) {
            console.error('Erro ao carregar notas do servidor:', error);
        }

        // Fallback para localStorage se o servidor falhar
        const savedNotas = localStorage.getItem('notasArtigos');
        if (savedNotas) {
            try {
                notasArtigos = JSON.parse(savedNotas);
            } catch (e) {
                console.error('Erro ao carregar notas do localStorage:', e);
                notasArtigos = {};
            }
        }
    }

    function saveNotas() {
        localStorage.setItem('notasArtigos', JSON.stringify(notasArtigos));
    }

    function mostrarFormNovaNota() {
        const formNovaNota = document.getElementById('formNovaNota');
        const editorNovaNota = document.getElementById('editorNovaNota');

        if (formNovaNota && editorNovaNota) {
            formNovaNota.style.display = 'block';
            editorNovaNota.innerHTML = '';
            editorNovaNota.focus();

            // Se estiver editando, preencher com o texto da nota
            if (editandoNota) {
                editorNovaNota.innerHTML = editandoNota.texto;
            }

            // Atualizar estado da toolbar
            setTimeout(updateToolbarState, 100);
        }
    }

    function cancelarNota() {
        const formNovaNota = document.getElementById('formNovaNota');
        const editorNovaNota = document.getElementById('editorNovaNota');

        if (formNovaNota && editorNovaNota) {
            formNovaNota.style.display = 'none';
            editorNovaNota.innerHTML = '';
            editandoNota = null;
        }
    }

    async function salvarNota() {
        const editorNovaNota = document.getElementById('editorNovaNota');

        if (!editorNovaNota || !artigoAtual) return;

        const textoNota = editorNovaNota.innerHTML.trim();
        const textoLimpo = editorNovaNota.textContent.trim();

        if (!textoLimpo) {
            alert('Por favor, digite o texto da nota.');
            return;
        }

        try {
            // Inicializar array de notas para o artigo se não existir
            if (!notasArtigos[artigoAtual]) {
                notasArtigos[artigoAtual] = [];
            }

            if (editandoNota) {
                // Editando nota existente
                const response = await fetch('./api/anotacoes.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: parseInt(editandoNota.id),
                        texto: textoNota
                    })
                });

                if (!response.ok) {
                    throw new Error('Erro ao atualizar nota no servidor');
                }

                const index = notasArtigos[artigoAtual].findIndex(nota => nota.id === editandoNota.id);
                if (index !== -1) {
                    notasArtigos[artigoAtual][index].texto = textoNota;
                    notasArtigos[artigoAtual][index].dataModificacao = new Date().toISOString();
                }
                editandoNota = null;
            } else {
                // Nova nota
                const response = await fetch('./api/anotacoes.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        artigo_numero: artigoAtual,
                        texto: textoNota
                    })
                });

                if (!response.ok) {
                    throw new Error('Erro ao criar nota no servidor');
                }

                const data = await response.json();
                const novaNota = {
                    id: data.id.toString(),
                    texto: textoNota,
                    dataCriacao: new Date().toISOString(),
                    dataModificacao: new Date().toISOString()
                };
                notasArtigos[artigoAtual].push(novaNota);
            }

            saveNotas();
            updateNotasSection();
            updateCardsVisualState();
            cancelarNota();

        } catch (error) {
            console.error('Erro ao salvar nota:', error);
            alert('Erro ao salvar nota. Tente novamente.');
        }
    }

    async function excluirNota(notaId) {
        if (!artigoAtual || !notasArtigos[artigoAtual]) return;

        if (confirm('Tem certeza que deseja excluir esta nota?')) {
            try {
                // Excluir no servidor
                const response = await fetch('./api/anotacoes.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id: parseInt(notaId) })
                });

                if (!response.ok) {
                    throw new Error('Erro ao excluir nota no servidor');
                }

                // Atualizar localStorage apenas se o servidor confirmou
                notasArtigos[artigoAtual] = notasArtigos[artigoAtual].filter(nota => nota.id !== notaId);

                // Se não há mais notas, remover o array
                if (notasArtigos[artigoAtual].length === 0) {
                    delete notasArtigos[artigoAtual];
                }

                saveNotas();
                updateNotasSection();
                updateCardsVisualState();

            } catch (error) {
                console.error('Erro ao excluir nota:', error);
                alert('Erro ao excluir nota. Tente novamente.');
            }
        }
    }

    function editarNota(notaId) {
        if (!artigoAtual || !notasArtigos[artigoAtual]) return;

        const nota = notasArtigos[artigoAtual].find(n => n.id === notaId);
        if (nota) {
            editandoNota = nota;
            mostrarFormNovaNota();
        }
    }

    function updateNotasSection() {
        const notasContainer = document.getElementById('notasContainer');
        const nenhumaNota = document.getElementById('nenhumaNota');

        if (!notasContainer || !artigoAtual) return;

        const notasDoArtigo = notasArtigos[artigoAtual] || [];

        if (notasDoArtigo.length === 0) {
            // Mostrar mensagem de nenhuma nota
            nenhumaNota.style.display = 'block';
            // Remover notas existentes
            const notasExistentes = notasContainer.querySelectorAll('.nota-item');
            notasExistentes.forEach(nota => nota.remove());
        } else {
            // Esconder mensagem de nenhuma nota
            nenhumaNota.style.display = 'none';

            // Limpar container
            const notasExistentes = notasContainer.querySelectorAll('.nota-item');
            notasExistentes.forEach(nota => nota.remove());

            // Adicionar notas
            notasDoArtigo.forEach(nota => {
                const notaElement = criarElementoNota(nota);
                notasContainer.appendChild(notaElement);
            });
        }
    }

    function criarElementoNota(nota) {
        const notaDiv = document.createElement('div');
        notaDiv.className = 'nota-item';

        const dataFormatada = new Date(nota.dataCriacao).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        const foiEditada = nota.dataModificacao !== nota.dataCriacao;
        const dataModificacao = foiEditada ? new Date(nota.dataModificacao).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : null;

        notaDiv.innerHTML = `
            <div class="nota-content">${nota.texto}</div>
            <div class="nota-meta">
                <div class="nota-data">
                    <i class="fas fa-clock"></i>
                    ${dataFormatada}${foiEditada ? ` (editada em ${dataModificacao})` : ''}
                </div>
                <div class="nota-actions">
                    <button class="btn-editar-nota" onclick="editarNota('${nota.id}')" title="Editar nota">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-excluir-nota" onclick="excluirNota('${nota.id}')" title="Excluir nota">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        return notaDiv;
    }

    // Funções da toolbar de formatação
    function initializeToolbar() {
        // Event listeners para botões de formatação
        document.querySelectorAll('.toolbar-btn[data-command]').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const command = this.getAttribute('data-command');

                if (command === 'removeFormat') {
                    document.execCommand('removeFormat');
                    document.execCommand('unlink');
                } else {
                    document.execCommand(command);
                }

                updateToolbarState();

                // Manter foco no editor
                const editor = document.getElementById('editorNovaNota');
                if (editor) editor.focus();
            });
        });

        // Event listeners para botões de cor
        document.querySelectorAll('.color-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const color = this.getAttribute('data-color');
                document.execCommand('foreColor', false, color);

                // Atualizar estado visual dos botões de cor
                document.querySelectorAll('.color-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Manter foco no editor
                const editor = document.getElementById('editorNovaNota');
                if (editor) editor.focus();
            });
        });

        // Event listener para seletor de tamanho de fonte
        const fontSizeSelect = document.querySelector('.toolbar-select[data-command="fontSize"]');
        if (fontSizeSelect) {
            fontSizeSelect.addEventListener('change', function() {
                const size = this.value;
                document.execCommand('fontSize', false, '3'); // Tamanho temporário

                // Aplicar tamanho personalizado
                const fontElements = document.querySelectorAll('#editorNovaNota font[size="3"]');
                fontElements.forEach(el => {
                    el.removeAttribute('size');
                    el.style.fontSize = size;
                });

                // Manter foco no editor
                const editor = document.getElementById('editorNovaNota');
                if (editor) editor.focus();
            });
        }
    }

    function updateToolbarState() {
        // Atualizar estado dos botões de formatação
        const commands = ['bold', 'italic', 'underline'];
        commands.forEach(command => {
            const btn = document.querySelector(`.toolbar-btn[data-command="${command}"]`);
            if (btn) {
                if (document.queryCommandState(command)) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            }
        });

        // Atualizar cor ativa (simplificado)
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
                ? range.commonAncestorContainer.parentElement
                : range.commonAncestorContainer;

            if (parentElement && parentElement.style && parentElement.style.color) {
                const color = rgbToHex(parentElement.style.color) || parentElement.style.color;
                const colorBtn = document.querySelector(`.color-btn[data-color="${color}"]`);

                // Remover active de todos os botões de cor
                document.querySelectorAll('.color-btn').forEach(btn => btn.classList.remove('active'));

                // Adicionar active ao botão correspondente
                if (colorBtn) {
                    colorBtn.classList.add('active');
                }
            }
        }
    }

    function rgbToHex(rgb) {
        if (!rgb || rgb.indexOf('rgb') === -1) return rgb;

        const result = rgb.match(/\d+/g);
        if (!result || result.length < 3) return rgb;

        const r = parseInt(result[0]);
        const g = parseInt(result[1]);
        const b = parseInt(result[2]);

        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // Funções do sistema de favoritos
    async function loadFavoritos() {
        try {
            // Tentar carregar do servidor primeiro
            const response = await fetch('./api/favoritos.php');
            if (response.ok) {
                const data = await response.json();
                const favoritosArray = data.favoritos.map(f => f.artigo_numero);
                favoritos = new Set(favoritosArray);

                // Salvar no localStorage para cache
                saveFavoritos();
                updateFavoritosCount();
                return;
            }
        } catch (error) {
            console.error('Erro ao carregar favoritos do servidor:', error);
        }

        // Fallback para localStorage se o servidor falhar
        const savedFavoritos = localStorage.getItem('favoritos');
        if (savedFavoritos) {
            try {
                const favoritosArray = JSON.parse(savedFavoritos);
                favoritos = new Set(favoritosArray);
                updateFavoritosCount();
            } catch (e) {
                console.error('Erro ao carregar favoritos do localStorage:', e);
                favoritos = new Set();
            }
        }
    }

    function saveFavoritos() {
        const favoritosArray = Array.from(favoritos);
        localStorage.setItem('favoritos', JSON.stringify(favoritosArray));
    }

    async function loadListas() {
        try {
            // Tentar carregar do servidor primeiro
            const response = await fetch('./api/listas.php');
            if (response.ok) {
                const data = await response.json();
                const listasArray = data.listas || [];

                // Converter array para objeto com estrutura compatível
                listas = {};
                listasArray.forEach(lista => {
                    listas[lista.id] = {
                        nome: lista.nome,
                        cor: lista.cor || '#e74c3c', // Usar cor do servidor ou padrão
                        artigos: lista.artigos || [],
                        dataCriacao: lista.data_criacao,
                        dataModificacao: lista.data_atualizacao
                    };
                });

                // Salvar no localStorage para cache
                saveListas();
                updateListasCount();
                return;
            }
        } catch (error) {
            console.error('Erro ao carregar listas do servidor:', error);
        }

        // Fallback para localStorage se o servidor falhar
        const savedListas = localStorage.getItem('listas');
        if (savedListas) {
            try {
                listas = JSON.parse(savedListas);
            } catch (e) {
                console.error('Erro ao carregar listas do localStorage:', e);
                listas = {};
            }
        }
    }

    function saveListas() {
        localStorage.setItem('listas', JSON.stringify(listas));
    }

    function updateFavoritosCount() {
        const countElement = document.getElementById('favoritosCount');
        if (countElement) {
            countElement.textContent = favoritos.size;
        }
    }

    function updateListasCount() {
        const countElement = document.getElementById('listasCount');
        if (countElement) {
            countElement.textContent = Object.keys(listas).length;
        }
    }

    function initializeFavoritos() {
        // Botões no header
        const btnFavoritos = document.getElementById('btnFavoritos');
        const btnListas = document.getElementById('btnListas');

        if (btnFavoritos) {
            btnFavoritos.addEventListener('click', abrirModalFavoritos);
        }

        if (btnListas) {
            btnListas.addEventListener('click', abrirModalListas);
        }

        // Botões no modal do artigo
        const btnFavoritar = document.getElementById('btnFavoritar');
        const btnAdicionarLista = document.getElementById('btnAdicionarLista');
        const btnGerenciarListas = document.getElementById('btnGerenciarListas');

        if (btnFavoritar) {
            btnFavoritar.addEventListener('click', toggleFavorito);
        }

        if (btnAdicionarLista) {
            btnAdicionarLista.addEventListener('click', abrirModalAdicionarLista);
        }

        if (btnGerenciarListas) {
            btnGerenciarListas.addEventListener('click', abrirModalGerenciarListas);
        }

        // Event listeners para modais
        initializeModalFavoritos();
        initializeModalListas();
        initializeModalNovaLista();
        initializeModalAdicionarLista();
        initializeModalGerenciarListas();
    }

    async function toggleFavorito() {
        if (!artigoAtual) return;

        const wasFavorito = favoritos.has(artigoAtual);
        const willBeFavorito = !wasFavorito;

        try {
            if (willBeFavorito) {
                // Adicionar aos favoritos
                const response = await fetch('./api/favoritos.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ artigo_numero: artigoAtual })
                });

                if (!response.ok) {
                    throw new Error('Erro ao adicionar favorito no servidor');
                }

                favoritos.add(artigoAtual);
            } else {
                // Remover dos favoritos
                const response = await fetch('./api/favoritos.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ artigo_numero: artigoAtual })
                });

                if (!response.ok) {
                    throw new Error('Erro ao remover favorito no servidor');
                }

                favoritos.delete(artigoAtual);
            }

            saveFavoritos();
            updateCardsVisualState();
            updateFavoritosCount();
            updateFavoritoButton();

        } catch (error) {
            console.error('Erro ao atualizar favorito:', error);
            alert('Erro ao atualizar favorito. Tente novamente.');
        }
    }

    function updateFavoritoButton() {
        const btnFavoritar = document.getElementById('btnFavoritar');
        if (!btnFavoritar || !artigoAtual) return;

        const icon = btnFavoritar.querySelector('i');
        const span = btnFavoritar.querySelector('span');

        if (favoritos.has(artigoAtual)) {
            btnFavoritar.classList.add('favoritado');
            icon.className = 'fas fa-heart';
            span.textContent = 'Favoritado';
        } else {
            btnFavoritar.classList.remove('favoritado');
            icon.className = 'far fa-heart';
            span.textContent = 'Favoritar';
        }

        // Atualizar visibilidade do botão de gerenciar listas
        updateGerenciarListasButton();
    }

    function updateGerenciarListasButton() {
        const btnGerenciarListas = document.getElementById('btnGerenciarListas');
        if (!btnGerenciarListas || !artigoAtual) return;

        // Verificar se o artigo está em alguma lista
        const temListas = Object.values(listas).some(lista => lista.artigos.includes(artigoAtual));

        if (temListas) {
            btnGerenciarListas.style.display = 'inline-flex';
        } else {
            btnGerenciarListas.style.display = 'none';
        }
    }

    function abrirModalFavoritos() {
        const modal = document.getElementById('favoritosModal');
        if (modal) {
            modal.style.display = 'block';
            updateTabFavoritos();
        }
    }

    function abrirModalListas() {
        const modal = document.getElementById('listasModal');
        if (modal) {
            modal.style.display = 'block';
            updateTabListas();
        }
    }

    function updateTabFavoritos() {
        const container = document.getElementById('favoritosContainer');
        const nenhumFavorito = document.getElementById('nenhumFavorito');

        if (!container) return;

        // Limpar container
        container.innerHTML = '';

        if (favoritos.size === 0) {
            container.appendChild(nenhumFavorito.cloneNode(true));
            return;
        }

        // Criar lista de favoritos
        favoritos.forEach(artigo => {
            const card = Array.from(cards).find(c => c.dataset.artigo === artigo);
            if (card) {
                const favoritoItem = createFavoritoItem(artigo, card.dataset.caput);
                container.appendChild(favoritoItem);
            }
        });
    }

    function createFavoritoItem(artigo, caput) {
        const item = document.createElement('div');
        item.className = 'favorito-item';
        item.innerHTML = `
            <h3>${artigo}</h3>
            <p>${caput.substring(0, 150)}${caput.length > 150 ? '...' : ''}</p>
        `;

        item.addEventListener('click', () => {
            // Verificar se há modais abertos
            const modalsAbertos = document.querySelectorAll('.modal[style*="block"]');
            const temModalAberto = modalsAbertos.length > 0;

            // Rastrear que o modal de favoritos estava aberto
            if (temModalAberto) {
                modalAnterior = 'favoritosModal';
            }

            // Configurar navegação para favoritos
            navegacaoTipo = 'favoritos';

            // Criar array específico de favoritos
            favoritosCardsArray = [];
            favoritos.forEach(favoritoArtigo => {
                const card = Array.from(cards).find(c => c.dataset.artigo === favoritoArtigo);
                if (card) {
                    favoritosCardsArray.push(card);
                }
            });

            // Encontrar índice do artigo clicado no array de favoritos
            const favoritoIndex = favoritosCardsArray.findIndex(card => card.dataset.artigo === artigo);

            if (favoritoIndex !== -1) {
                // Abrir modal com navegação específica para favoritos
                abrirModalComNavegacaoFavoritos(favoritoIndex);

                // Se havia modal aberto, adicionar prioridade
                if (temModalAberto) {
                    const artigoModal = document.getElementById('artigoModal');
                    artigoModal.classList.add('modal-priority');
                }
            }
        });

        return item;
    }

    function updateTabListas() {
        const container = document.getElementById('listasContainer');
        const nenhumaLista = document.getElementById('nenhumaLista');

        if (!container) return;

        // Limpar container
        container.innerHTML = '';

        const listasArray = Object.keys(listas);
        if (listasArray.length === 0) {
            container.appendChild(nenhumaLista.cloneNode(true));
            return;
        }

        // Criar lista de listas
        listasArray.forEach(listaId => {
            const lista = listas[listaId];
            const listaItem = createListaItem(listaId, lista);
            container.appendChild(listaItem);
        });
    }

    function createListaItem(listaId, lista) {
        const item = document.createElement('div');
        item.className = 'lista-item';
        item.innerHTML = `
            <div class="lista-header">
                <h3 class="lista-nome">
                    <span class="lista-cor" style="background: ${lista.cor}"></span>
                    ${lista.nome}
                    <span class="lista-count">${lista.artigos.length}</span>
                </h3>
                <div class="lista-actions">
                    <button class="btn-editar-lista" title="Editar lista">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-excluir-lista" title="Excluir lista">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="lista-artigos">
                ${lista.artigos.map(artigo => `
                    <span class="artigo-tag" data-artigo="${artigo}">${artigo}</span>
                `).join('')}
            </div>
        `;

        // Event listeners
        const btnEditar = item.querySelector('.btn-editar-lista');
        const btnExcluir = item.querySelector('.btn-excluir-lista');

        btnEditar.addEventListener('click', (e) => {
            e.stopPropagation();
            editarLista(listaId);
        });

        btnExcluir.addEventListener('click', (e) => {
            e.stopPropagation();
            excluirLista(listaId);
        });

        // Event listeners para tags de artigos
        const artigoTags = item.querySelectorAll('.artigo-tag');
        artigoTags.forEach(tag => {
            tag.addEventListener('click', (e) => {
                e.stopPropagation();
                const artigo = tag.dataset.artigo;

                // Verificar se há modais abertos
                const modalsAbertos = document.querySelectorAll('.modal[style*="block"]');
                const temModalAberto = modalsAbertos.length > 0;

                // Rastrear que o modal de listas estava aberto
                if (temModalAberto) {
                    modalAnterior = 'listasModal';
                }

                // Abrir modal do artigo (sem fechar o modal de listas)
                const card = Array.from(cards).find(c => c.dataset.artigo === artigo);
                if (card) {
                    card.click();

                    // Se havia modal aberto, adicionar prioridade
                    if (temModalAberto) {
                        const artigoModal = document.getElementById('artigoModal');
                        artigoModal.classList.add('modal-priority');
                    }
                }
            });
        });

        return item;
    }

    function initializeModalFavoritos() {
        const modal = document.getElementById('favoritosModal');
        const closeBtn = modal?.querySelector('.modal-close-btn');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        // Fechar modal ao clicar fora
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    }

    function initializeModalListas() {
        const modal = document.getElementById('listasModal');
        const closeBtn = modal?.querySelector('.modal-close-btn');
        const btnNovaLista = document.getElementById('btnNovaLista');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        if (btnNovaLista) {
            btnNovaLista.addEventListener('click', abrirModalNovaLista);
        }

        // Fechar modal ao clicar fora
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    }

    // Função removida - não é mais necessária com modais separados

    function abrirModalNovaLista() {
        const modal = document.getElementById('novaListaModal');
        const titulo = document.getElementById('tituloModalLista');
        const input = document.getElementById('nomeListaInput');

        if (modal && titulo && input) {
            titulo.textContent = 'Nova Lista';
            input.value = '';

            // Resetar seleção de cor
            const primeiraCorRadio = document.getElementById('cor1');
            if (primeiraCorRadio) {
                primeiraCorRadio.checked = true;
            }

            editandoLista = null;
            modal.style.display = 'block';
            input.focus();
        }
    }

    function initializeModalNovaLista() {
        const modal = document.getElementById('novaListaModal');
        const form = document.getElementById('formNovaLista');
        const closeBtn = modal?.querySelector('.modal-close-btn');
        const btnCancelar = document.getElementById('btnCancelarLista');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        if (btnCancelar) {
            btnCancelar.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                salvarLista();
            });
        }

        // Fechar modal ao clicar fora
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    }

    async function salvarLista() {
        const nomeInput = document.getElementById('nomeListaInput');
        const corSelecionada = document.querySelector('input[name="corLista"]:checked');

        if (!nomeInput || !corSelecionada) return;

        const nome = nomeInput.value.trim();
        const cor = corSelecionada.value;

        if (!nome) {
            alert('Por favor, digite o nome da lista.');
            nomeInput.focus();
            return;
        }

        try {
            if (!editandoLista) {
                // Nova lista
                const response = await fetch('./api/listas.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        nome: nome,
                        descricao: '', // Pode ser expandido no futuro
                        cor: cor
                    })
                });

                if (!response.ok) {
                    throw new Error('Erro ao criar lista no servidor');
                }

                const data = await response.json();
                const listaId = data.lista_id;

                listas[listaId] = {
                    nome: nome,
                    cor: cor,
                    artigos: [],
                    dataCriacao: new Date().toISOString()
                };
            } else {
                // Editando lista existente
                console.log('Editando lista:', editandoLista, 'Nome:', nome);

                const response = await fetch('./api/listas.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: parseInt(editandoLista), // Garantir que seja número
                        nome: nome,
                        descricao: '',
                        cor: cor
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Erro na resposta:', response.status, errorText);
                    throw new Error('Erro ao atualizar lista no servidor: ' + response.status);
                }

                listas[editandoLista].nome = nome;
                listas[editandoLista].cor = cor;
                listas[editandoLista].dataModificacao = new Date().toISOString();
            }

            saveListas();
            updateTabListas();
            updateCardsVisualState();
            updateListasCount();

            // Fechar modal
            document.getElementById('novaListaModal').style.display = 'none';
            editandoLista = null;

        } catch (error) {
            console.error('Erro ao salvar lista:', error);
            alert('Erro ao salvar lista. Tente novamente.');
        }
    }

    function generateListaId(nome) {
        return nome.toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '') + '-' + Date.now();
    }

    function editarLista(listaId) {
        const lista = listas[listaId];
        if (!lista) return;

        const modal = document.getElementById('novaListaModal');
        const titulo = document.getElementById('tituloModalLista');
        const input = document.getElementById('nomeListaInput');

        if (modal && titulo && input) {
            titulo.textContent = 'Editar Lista';
            input.value = lista.nome;

            // Selecionar cor atual
            const corRadio = document.querySelector(`input[name="corLista"][value="${lista.cor}"]`);
            if (corRadio) {
                corRadio.checked = true;
            }

            editandoLista = listaId;
            modal.style.display = 'block';
            input.focus();
        }
    }

    async function excluirLista(listaId) {
        const lista = listas[listaId];
        if (!lista) return;

        if (confirm(`Tem certeza que deseja excluir a lista "${lista.nome}"?`)) {
            try {
                // Excluir no servidor
                const response = await fetch('./api/listas.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id: parseInt(listaId) })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Erro ao excluir lista:', response.status, errorText);
                    throw new Error('Erro ao excluir lista no servidor: ' + response.status);
                }

                // Atualizar localStorage apenas se o servidor confirmou
                delete listas[listaId];
                saveListas();
                updateTabListas();
                updateCardsVisualState();
                updateListasCount();

            } catch (error) {
                console.error('Erro ao excluir lista:', error);
                alert('Erro ao excluir lista. Tente novamente.');
            }
        }
    }

    function abrirModalAdicionarLista() {
        if (!artigoAtual) return;

        const modal = document.getElementById('adicionarListaModal');
        const artigoSpan = document.getElementById('artigoParaLista');

        if (modal && artigoSpan) {
            artigoSpan.textContent = artigoAtual;
            modal.style.display = 'block';
            updateListasDisponiveis();
        }
    }

    function initializeModalAdicionarLista() {
        const modal = document.getElementById('adicionarListaModal');
        const closeBtn = modal?.querySelector('.modal-close-btn');
        const btnCriarPrimeira = document.getElementById('btnCriarPrimeiraLista');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        if (btnCriarPrimeira) {
            btnCriarPrimeira.addEventListener('click', () => {
                modal.style.display = 'none';
                abrirModalNovaLista();
            });
        }

        // Fechar modal ao clicar fora
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    }

    function updateListasDisponiveis() {
        const container = document.getElementById('listasDisponiveis');
        if (!container) return;

        // Limpar container
        container.innerHTML = '';

        const listasArray = Object.keys(listas);

        if (listasArray.length === 0) {
            container.innerHTML = `
                <div class="nenhuma-lista-disponivel">
                    <i class="fas fa-bookmark"></i>
                    <p>Nenhuma lista criada ainda.</p>
                    <button id="btnCriarPrimeiraLista" class="btn-criar-primeira-lista">
                        <i class="fas fa-plus"></i>
                        Criar Primeira Lista
                    </button>
                </div>
            `;

            // Re-adicionar event listener
            const btnCriarPrimeira = container.querySelector('#btnCriarPrimeiraLista');
            if (btnCriarPrimeira) {
                btnCriarPrimeira.addEventListener('click', () => {
                    document.getElementById('adicionarListaModal').style.display = 'none';
                    abrirModalNovaLista();
                });
            }
            return;
        }

        // Criar opções de listas
        listasArray.forEach(listaId => {
            const lista = listas[listaId];
            const opcao = createListaOpcao(listaId, lista);
            container.appendChild(opcao);
        });
    }

    function createListaOpcao(listaId, lista) {
        const opcao = document.createElement('div');
        opcao.className = 'lista-opcao';
        opcao.innerHTML = `
            <div class="lista-opcao-info">
                <span class="lista-cor" style="background: ${lista.cor}"></span>
                <span class="lista-opcao-nome">${lista.nome}</span>
            </div>
            <span class="lista-opcao-count">${lista.artigos.length}</span>
        `;

        opcao.addEventListener('click', () => {
            adicionarArtigoALista(listaId);
        });

        return opcao;
    }

    async function adicionarArtigoALista(listaId) {
        if (!artigoAtual || !listas[listaId]) return;

        const lista = listas[listaId];

        if (lista.artigos.includes(artigoAtual)) {
            alert(`O artigo ${artigoAtual} já está na lista "${lista.nome}".`);
            return;
        }

        try {
            // Adicionar no servidor
            const response = await fetch('./api/lista_artigos.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    lista_id: parseInt(listaId),
                    artigo_numero: artigoAtual
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Erro ao adicionar artigo:', response.status, errorText);
                throw new Error('Erro ao adicionar artigo à lista no servidor: ' + response.status);
            }

            // Atualizar localStorage apenas se o servidor confirmou
            lista.artigos.push(artigoAtual);
            lista.dataModificacao = new Date().toISOString();

            saveListas();
            updateCardsVisualState();
            updateGerenciarListasButton();

            // Fechar modal
            document.getElementById('adicionarListaModal').style.display = 'none';

            // Mostrar confirmação
            alert(`Artigo ${artigoAtual} adicionado à lista "${lista.nome}".`);

        } catch (error) {
            console.error('Erro ao adicionar artigo à lista:', error);
            alert('Erro ao adicionar artigo à lista. Tente novamente.');
        }
    }

    function abrirModalGerenciarListas() {
        if (!artigoAtual) return;

        const modal = document.getElementById('gerenciarListasModal');
        const artigoSpan = document.getElementById('artigoParaGerenciar');

        if (modal && artigoSpan) {
            artigoSpan.textContent = artigoAtual;
            modal.style.display = 'block';
            updateListasDoArtigo();
        }
    }

    function initializeModalGerenciarListas() {
        const modal = document.getElementById('gerenciarListasModal');
        const closeBtn = modal?.querySelector('.modal-close-btn');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.style.display = 'none';
            });
        }

        // Fechar modal ao clicar fora
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    }

    function updateListasDoArtigo() {
        const container = document.getElementById('listasDoArtigo');
        if (!container || !artigoAtual) return;

        // Limpar container
        container.innerHTML = '';

        // Encontrar listas que contêm este artigo
        const listasDoArtigo = Object.keys(listas).filter(listaId =>
            listas[listaId].artigos.includes(artigoAtual)
        );

        if (listasDoArtigo.length === 0) {
            container.innerHTML = `
                <div class="nenhuma-lista-artigo">
                    <i class="fas fa-bookmark"></i>
                    <p>Este artigo não está em nenhuma lista.</p>
                </div>
            `;
            return;
        }

        // Criar itens para cada lista
        listasDoArtigo.forEach(listaId => {
            const lista = listas[listaId];
            const item = createListaArtigoItem(listaId, lista);
            container.appendChild(item);
        });
    }

    function createListaArtigoItem(listaId, lista) {
        const item = document.createElement('div');
        item.className = 'lista-artigo-item';
        item.innerHTML = `
            <div class="lista-artigo-info">
                <span class="lista-cor" style="background: ${lista.cor}"></span>
                <span class="lista-artigo-nome">${lista.nome}</span>
            </div>
            <button class="btn-remover-da-lista" data-lista-id="${listaId}">
                <i class="fas fa-times"></i>
                Remover
            </button>
        `;

        // Event listener para remover da lista
        const btnRemover = item.querySelector('.btn-remover-da-lista');
        btnRemover.addEventListener('click', () => {
            removerArtigoDaLista(listaId);
        });

        return item;
    }

    async function removerArtigoDaLista(listaId) {
        if (!artigoAtual || !listas[listaId]) return;

        const lista = listas[listaId];
        const index = lista.artigos.indexOf(artigoAtual);

        if (index > -1) {
            try {
                // Remover no servidor
                const response = await fetch('./api/lista_artigos.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        lista_id: parseInt(listaId),
                        artigo_numero: artigoAtual
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Erro ao remover artigo:', response.status, errorText);
                    throw new Error('Erro ao remover artigo da lista no servidor: ' + response.status);
                }

                // Atualizar localStorage apenas se o servidor confirmou
                lista.artigos.splice(index, 1);
                lista.dataModificacao = new Date().toISOString();

                saveListas();
                updateCardsVisualState();
                updateListasDoArtigo();
                updateGerenciarListasButton();

                // Atualizar modal de listas se estiver aberto
                const listasModal = document.getElementById('listasModal');
                if (listasModal && listasModal.style.display === 'block') {
                    updateTabListas();
                }

            } catch (error) {
                console.error('Erro ao remover artigo da lista:', error);
                alert('Erro ao remover artigo da lista. Tente novamente.');
            }
        }
    }

    // Atualizar botão de favoritar quando modal abrir
    cards.forEach(card => {
        card.addEventListener('click', function() {
            // Aguardar um pouco para o modal abrir
            setTimeout(() => {
                updateFavoritoButton();
            }, 100);
        });
    });

    // Tornar funções globais para os event handlers inline
    window.editarNota = editarNota;
    window.excluirNota = excluirNota;
});