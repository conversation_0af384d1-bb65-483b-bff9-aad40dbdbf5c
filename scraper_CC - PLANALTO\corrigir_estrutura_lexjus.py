#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT PARA CORRIGIR A ESTRUTURA PARA SER COMPATÍVEL COM O SISTEMA LEXJUS
Converte parágrafos únicos com incisos para a estrutura correta do LexJus
"""

import json
import re

def corrigir_estrutura_lexjus():
    """Corrige a estrutura para ser compatível com o sistema LexJus"""

    # Carregar o arquivo JSON
    with open('cc_estrutura_corrigida.json', 'r', encoding='utf-8') as f:
        artigos = json.load(f)

    artigos_corrigidos = 0

    for artigo in artigos:
        artigo_id = artigo["artigo"]

        # Verificar se tem paragrafo_unico_incisos (estrutura que criei)
        if artigo.get('paragrafo_unico_incisos'):
            # Converter para estrutura de parágrafo numerado com incisos
            paragrafo_unico_texto = artigo.get('paragrafo_unico', '')
            incisos_paragrafo = artigo['paragrafo_unico_incisos']

            # Criar parágrafo numerado com os incisos - ESTRUTURA CORRETA PARA LEXJUS
            paragrafo_numerado = {
                "numero": "Parágrafo único",
                "texto": paragrafo_unico_texto,
                "incisos": [],  # Sistema LexJus não usa incisos aqui
                "alineas": incisos_paragrafo  # Sistema LexJus usa ALINEAS para os incisos do parágrafo
            }

            # Adicionar aos parágrafos numerados
            artigo["paragrafos_numerados"].append(paragrafo_numerado)

            # Limpar parágrafo único e incisos especiais
            artigo["paragrafo_unico"] = None
            del artigo['paragrafo_unico_incisos']

            artigos_corrigidos += 1
            print(f"Corrigido: {artigo_id} - parágrafo único com incisos convertido")

        # Casos especiais baseados na análise do HTML
        if artigo_id == "Art. 5º":
            # Art. 5º: Parágrafo único com incisos - ESTRUTURA CORRETA PARA LEXJUS
            artigo["caput"] = "A menoridade cessa aos dezoito anos completos, quando a pessoa fica habilitada à prática de todos os atos da vida civil."
            artigo["incisos"] = []  # Incisos não pertencem ao caput
            artigo["paragrafos_numerados"] = [{
                "numero": "Parágrafo único",
                "texto": "Cessará, para os menores, a incapacidade:",
                "incisos": [],  # Sistema LexJus não usa incisos aqui
                "alineas": [  # Sistema LexJus usa ALINEAS para os incisos do parágrafo
                    "I - pela concessão dos pais, ou de um deles na falta do outro, mediante instrumento público, independentemente de homologação judicial, ou por sentença do juiz, ouvido o tutor, se o menor tiver dezesseis anos completos;",
                    "II - pelo casamento;",
                    "III - pelo exercício de emprego público efetivo;",
                    "IV - pela colação de grau em curso de ensino superior;",
                    "V - pelo estabelecimento civil ou comercial, ou pela existência de relação de emprego, desde que, em função deles, o menor com dezesseis anos completos tenha economia própria."
                ]
            }]
            artigo["paragrafo_unico"] = None  # Não usa paragrafo_unico quando tem incisos

        elif artigo_id == "Art. 7º":
            # Art. 7º: Incisos no caput, parágrafo único sem incisos
            artigo["caput"] = "Pode ser declarada a morte presumida, sem decretação de ausência:"
            artigo["incisos"] = [
                "I - se for extremamente provável a morte de quem estava em perigo de vida;",
                "II - se alguém, desaparecido em campanha ou feito prisioneiro, não for encontrado até dois anos após o término da guerra."
            ]
            artigo["paragrafos_numerados"] = []
            artigo["paragrafo_unico"] = "A declaração da morte presumida, nesses casos, somente poderá ser requerida depois de esgotadas as buscas e averiguações, devendo a sentença fixar a data provável do falecimento."

        elif artigo_id == "Art. 9º":
            # Art. 9º: Incisos no caput
            artigo["caput"] = "Serão registrados em registro público:"
            artigo["incisos"] = [
                "I - os nascimentos, casamentos e óbitos;",
                "II - a emancipação por outorga dos pais ou por sentença do juiz;",
                "III - a interdição por incapacidade absoluta ou relativa;",
                "IV - a sentença declaratória de ausência e de morte presumida."
            ]
            artigo["paragrafos_numerados"] = []
            artigo["paragrafo_unico"] = None

        elif artigo_id == "Art. 10º":
            # Art. 10º: Incisos no caput
            artigo["caput"] = "Far-se-á averbação em registro público:"
            artigo["incisos"] = [
                "I - das sentenças que decretarem a nulidade ou anulação do casamento, o divórcio, a separação judicial e o restabelecimento da sociedade conjugal;",
                "II - dos atos judiciais ou extrajudiciais que declararem ou reconhecerem a filiação;"
            ]
            artigo["paragrafos_numerados"] = []
            artigo["paragrafo_unico"] = None

        elif artigo_id == "Art. 27º":
            # Art. 27º: Incisos no caput
            artigo["caput"] = "Para o efeito previsto no artigo anterior, somente se consideram interessados:"
            artigo["incisos"] = [
                "I - o cônjuge não separado judicialmente;",
                "II - os herdeiros presumidos, legítimos ou testamentários;",
                "III - os que tiverem sobre os bens do ausente direito dependente de sua morte;",
                "IV - os credores de obrigações vencidas e não pagas."
            ]
            artigo["paragrafos_numerados"] = []
            artigo["paragrafo_unico"] = None

        elif artigo_id == "Art. 41º":
            # Art. 41º: Incisos no caput + parágrafo único
            artigo["caput"] = "São pessoas jurídicas de direito público interno:"
            artigo["incisos"] = [
                "I - a União;",
                "II - os Estados, o Distrito Federal e os Territórios;",
                "III - os Municípios;",
                "IV - as autarquias, inclusive as associações públicas;",
                "V - as demais entidades de caráter público criadas por lei."
            ]
            artigo["paragrafos_numerados"] = []
            artigo["paragrafo_unico"] = "Salvo disposição em contrário, as pessoas jurídicas de direito público, a que se tenha dado estrutura de direito privado, regem-se, no que couber, quanto ao seu funcionamento, pelas normas deste Código."

        elif artigo_id == "Art. 44º":
            # Art. 44º: Incisos no caput
            artigo["caput"] = "São pessoas jurídicas de direito privado:"
            artigo["incisos"] = [
                "I - as associações;",
                "II - as sociedades;",
                "III - as fundações;",
                "IV - as organizações religiosas;",
                "V - os partidos políticos;",
                "VI - as empresas individuais de responsabilidade limitada."
            ]
            # Manter parágrafos numerados existentes
            artigo["paragrafo_unico"] = None

    # Salvar arquivo corrigido
    with open('cc_lexjus_final.json', 'w', encoding='utf-8') as f:
        json.dump(artigos, f, ensure_ascii=False, indent=2)

    print(f"\n✅ Correção para LexJus concluída!")
    print(f"📊 {artigos_corrigidos} artigos com estrutura corrigida")
    print(f"📁 Arquivo salvo: cc_lexjus_final.json")

    # Estatísticas finais
    artigos_com_caput = sum(1 for art in artigos if art['caput'])
    total_incisos = sum(len(art['incisos']) for art in artigos)
    total_paragrafos = sum(len(art['paragrafos_numerados']) for art in artigos)
    total_paragrafos_unicos = sum(1 for art in artigos if art['paragrafo_unico'])

    print(f"\n📈 ESTATÍSTICAS FINAIS:")
    print(f"   Total de artigos: {len(artigos)}")
    print(f"   Artigos com caput: {artigos_com_caput} ({artigos_com_caput/len(artigos)*100:.1f}%)")
    print(f"   Total de incisos: {total_incisos}")
    print(f"   Total de parágrafos numerados: {total_paragrafos}")
    print(f"   Total de parágrafos únicos: {total_paragrafos_unicos}")

    # Mostrar exemplos corrigidos
    print(f"\n📋 EXEMPLOS DE ARTIGOS CORRIGIDOS PARA LEXJUS:")
    for artigo_id in ["Art. 5º", "Art. 7º", "Art. 9º"]:
        for art in artigos:
            if art['artigo'] == artigo_id:
                print(f"\n{art['artigo']}:")
                print(f"   Caput: {art['caput'][:60]}...")
                print(f"   Incisos do caput: {len(art['incisos'])}")
                print(f"   Parágrafos numerados: {len(art['paragrafos_numerados'])}")
                if art['paragrafos_numerados']:
                    for p in art['paragrafos_numerados']:
                        print(f"     - {p['numero']}: {len(p['incisos'])} incisos")
                if art['paragrafo_unico']:
                    print(f"   Parágrafo único: {art['paragrafo_unico'][:60]}...")
                break

    return artigos

if __name__ == '__main__':
    print("INICIANDO CORREÇÃO DA ESTRUTURA PARA LEXJUS")
    print("Objetivo: CONVERTER PARA ESTRUTURA COMPATÍVEL COM LEXJUS")
    print("-" * 80)

    artigos_corrigidos = corrigir_estrutura_lexjus()

    print(f"\n🎉 CORREÇÃO PARA LEXJUS CONCLUÍDA COM SUCESSO!")
    print(f"✅ Estrutura agora é 100% compatível com o sistema LexJus!")
    print(f"✅ Parágrafos únicos com incisos convertidos para parágrafos numerados!")
    print(f"✅ Arquivo pronto para uso: cc_lexjus_final.json")
