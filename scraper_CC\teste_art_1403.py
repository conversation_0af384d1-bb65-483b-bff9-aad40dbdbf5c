import json

with open('codigo_civil_formato_lexjus_final.json', 'r', encoding='utf-8') as f:
    artigos = json.load(f)

for artigo in artigos:
    if '1403' in artigo.get('artigo', ''):
        print(f"Artigo: {artigo['artigo']}")
        print(f"Caput: {artigo['caput']}")
        print(f"Incisos: {len(artigo.get('incisos', []))}")
        for i, inciso in enumerate(artigo.get('incisos', [])):
            print(f"  {i+1}. {inciso}")
        
        if artigo['caput'].startswith('403'):
            print("ERRO: Caput ainda começa com número!")
        else:
            print("OK: Caput correto!")
        break
