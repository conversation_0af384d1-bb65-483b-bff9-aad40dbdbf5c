/**
 * Sistema de Revisão Inteligente para LexJus VOID
 * Baseado em repetição espaçada e algoritmos de memorização
 */

class SistemaRevisao {
    constructor() {
        this.revisoesPendentes = [];
        this.revisaoAtual = null;
        this.estatisticas = {};
        this.configuracao = {};
        this.tempoInicioRevisao = null;

        this.init();
    }

    /**
     * Inicializa o sistema de revisão
     */
    async init() {
        try {
            await this.carregarDados();
            this.criarInterface();
            this.configurarEventListeners();
            this.atualizarInterface();
        } catch (error) {
            console.error('Erro ao inicializar sistema de revisão:', error);
        }
    }

    /**
     * Carrega dados do servidor
     */
    async carregarDados() {
        try {
            // Carregar revisões pendentes
            const responsePendentes = await fetch('./api/revisao.php?acao=pendentes');
            if (responsePendentes.ok) {
                const dataPendentes = await responsePendentes.json();
                this.revisoesPendentes = dataPendentes.revisoes_pendentes || [];
            }

            // Carregar estatísticas
            const responseStats = await fetch('./api/revisao.php?acao=estatisticas');
            if (responseStats.ok) {
                const dataStats = await responseStats.json();
                this.estatisticas = dataStats.estatisticas || {};
            }

            // Carregar configuração
            const responseConfig = await fetch('./api/revisao.php?acao=configuracao');
            if (responseConfig.ok) {
                const dataConfig = await responseConfig.json();
                this.configuracao = dataConfig.configuracao || {};
            }

        } catch (error) {
            console.error('Erro ao carregar dados de revisão:', error);
        }
    }

    /**
     * Cria a interface do sistema de revisão
     */
    criarInterface() {
        // Adicionar botão de revisão na barra de navegação
        const navControls = document.querySelector('.nav-controls');
        if (navControls) {
            const btnRevisao = document.createElement('button');
            btnRevisao.id = 'btnRevisao';
            btnRevisao.className = 'nav-btn';
            btnRevisao.title = 'Sistema de Revisão';
            btnRevisao.innerHTML = `
                <i class="fas fa-brain"></i>
                <span class="btn-text">Revisão</span>
                <span id="revisaoCount" class="count-badge">0</span>
            `;
            navControls.insertBefore(btnRevisao, navControls.lastElementChild);
        }

        // Criar modal de revisão
        this.criarModalRevisao();

        // Criar modal de estatísticas
        this.criarModalEstatisticas();
    }

    /**
     * Cria o modal principal de revisão
     */
    criarModalRevisao() {
        const modalHTML = `
            <div id="revisaoModal" class="modal">
                <div class="modal-content modal-revisao">
                    <span class="modal-close-btn">&times;</span>

                    <!-- Header do Modal -->
                    <div class="modal-header">
                        <h2><i class="fas fa-brain"></i> Sistema de Revisão</h2>
                        <div class="revisao-stats-mini">
                            <span class="stat-mini">
                                <i class="fas fa-clock"></i>
                                <span id="pendentesCount">0</span> pendentes
                            </span>
                            <span class="stat-mini">
                                <i class="fas fa-trophy"></i>
                                <span id="dominadosCount">0</span> dominados
                            </span>
                        </div>
                    </div>

                    <!-- Área de Revisão -->
                    <div id="areaRevisao" class="area-revisao">
                        <!-- Estado: Aguardando -->
                        <div id="estadoAguardando" class="estado-revisao">
                            <div class="revisao-dashboard">
                                <h3>📚 Painel de Revisão</h3>

                                <div class="revisao-cards">
                                    <div class="revisao-card pendentes">
                                        <div class="card-icon"><i class="fas fa-clock"></i></div>
                                        <div class="card-content">
                                            <div class="card-number" id="dashPendentes">0</div>
                                            <div class="card-label">Pendentes</div>
                                        </div>
                                    </div>

                                    <div class="revisao-card aprendendo">
                                        <div class="card-icon"><i class="fas fa-graduation-cap"></i></div>
                                        <div class="card-content">
                                            <div class="card-number" id="dashAprendendo">0</div>
                                            <div class="card-label">Aprendendo</div>
                                        </div>
                                    </div>

                                    <div class="revisao-card dominados">
                                        <div class="card-icon"><i class="fas fa-trophy"></i></div>
                                        <div class="card-content">
                                            <div class="card-number" id="dashDominados">0</div>
                                            <div class="card-label">Dominados</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="revisao-actions">
                                    <button id="btnIniciarRevisao" class="btn-iniciar-revisao">
                                        <i class="fas fa-play"></i>
                                        Iniciar Revisão
                                    </button>
                                    <button id="btnEstatisticas" class="btn-estatisticas">
                                        <i class="fas fa-chart-bar"></i>
                                        Ver Estatísticas
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Estado: Revisando -->
                        <div id="estadoRevisando" class="estado-revisao" style="display: none;">
                            <div class="revisao-sessao">
                                <div class="sessao-header">
                                    <div class="artigo-info">
                                        <h3 id="artigoRevisaoNumero">Art. 1º</h3>
                                        <div class="artigo-meta">
                                            <span class="meta-item">
                                                <i class="fas fa-repeat"></i>
                                                <span id="artigoRepeticoes">0</span> repetições
                                            </span>
                                            <span class="meta-item">
                                                <i class="fas fa-star"></i>
                                                Facilidade: <span id="artigoFacilidade">2.5</span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="sessao-progresso">
                                        <span id="sessaoAtual">1</span> / <span id="sessaoTotal">10</span>
                                    </div>
                                </div>

                                <div class="artigo-conteudo" id="artigoRevisaoConteudo">
                                    <!-- Conteúdo do artigo será carregado aqui -->
                                </div>

                                <div class="revisao-controles">
                                    <h4>Como você avalia seu conhecimento sobre este artigo?</h4>
                                    <div class="qualidade-buttons">
                                        <button class="btn-qualidade" data-qualidade="0">
                                            <i class="fas fa-times-circle"></i>
                                            <span>Não lembro</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="1">
                                            <i class="fas fa-frown"></i>
                                            <span>Muito difícil</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="2">
                                            <i class="fas fa-meh"></i>
                                            <span>Difícil</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="3">
                                            <i class="fas fa-smile"></i>
                                            <span>Normal</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="4">
                                            <i class="fas fa-grin"></i>
                                            <span>Fácil</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="5">
                                            <i class="fas fa-star"></i>
                                            <span>Muito fácil</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Estado: Resultado -->
                        <div id="estadoResultado" class="estado-revisao" style="display: none;">
                            <div class="revisao-resultado">
                                <div class="resultado-header">
                                    <i class="fas fa-check-circle resultado-icon"></i>
                                    <h3>Sessão Concluída!</h3>
                                </div>

                                <div class="resultado-stats">
                                    <div class="stat-resultado">
                                        <span class="stat-label">Artigos revisados:</span>
                                        <span class="stat-value" id="resultadoRevisados">0</span>
                                    </div>
                                    <div class="stat-resultado">
                                        <span class="stat-label">Tempo total:</span>
                                        <span class="stat-value" id="resultadoTempo">0min</span>
                                    </div>
                                    <div class="stat-resultado">
                                        <span class="stat-label">Próxima sessão:</span>
                                        <span class="stat-value" id="resultadoProxima">Amanhã</span>
                                    </div>
                                </div>

                                <div class="resultado-actions">
                                    <button id="btnNovaRevisao" class="btn-nova-revisao">
                                        <i class="fas fa-redo"></i>
                                        Nova Sessão
                                    </button>
                                    <button id="btnFecharRevisao" class="btn-fechar-revisao">
                                        <i class="fas fa-times"></i>
                                        Fechar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Cria o modal de estatísticas
     */
    criarModalEstatisticas() {
        const modalHTML = `
            <div id="estatisticasModal" class="modal">
                <div class="modal-content modal-estatisticas">
                    <span class="modal-close-btn">&times;</span>

                    <div class="modal-header">
                        <h2><i class="fas fa-chart-bar"></i> Estatísticas de Revisão</h2>
                    </div>

                    <div class="estatisticas-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-book"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statTotalArtigos">0</div>
                                    <div class="stat-label">Total de Artigos</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statPendentes">0</div>
                                    <div class="stat-label">Pendentes</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-trophy"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statDominados">0</div>
                                    <div class="stat-label">Dominados</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-star"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statFacilidadeMedia">0</div>
                                    <div class="stat-label">Facilidade Média</div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-section">
                            <h3>Distribuição por Status</h3>
                            <div class="progress-bars">
                                <div class="progress-item">
                                    <span class="progress-label">Novos</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill novos" id="progressNovos"></div>
                                    </div>
                                    <span class="progress-value" id="valueNovos">0</span>
                                </div>

                                <div class="progress-item">
                                    <span class="progress-label">Aprendendo</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill aprendendo" id="progressAprendendo"></div>
                                    </div>
                                    <span class="progress-value" id="valueAprendendo">0</span>
                                </div>

                                <div class="progress-item">
                                    <span class="progress-label">Revisando</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill revisando" id="progressRevisando"></div>
                                    </div>
                                    <span class="progress-value" id="valueRevisando">0</span>
                                </div>

                                <div class="progress-item">
                                    <span class="progress-label">Dominados</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill dominados" id="progressDominados"></div>
                                    </div>
                                    <span class="progress-value" id="valueDominados">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Configura event listeners
     */
    configurarEventListeners() {
        // Botão principal de revisão
        document.getElementById('btnRevisao')?.addEventListener('click', () => {
            this.abrirModalRevisao();
        });

        // Botão iniciar revisão
        document.getElementById('btnIniciarRevisao')?.addEventListener('click', () => {
            this.iniciarSessaoRevisao();
        });

        // Botão estatísticas
        document.getElementById('btnEstatisticas')?.addEventListener('click', () => {
            this.abrirModalEstatisticas();
        });

        // Botões de qualidade
        document.querySelectorAll('.btn-qualidade').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const qualidade = parseInt(e.currentTarget.dataset.qualidade);
                this.processarResposta(qualidade);
            });
        });

        // Botões de resultado
        document.getElementById('btnNovaRevisao')?.addEventListener('click', () => {
            this.iniciarSessaoRevisao();
        });

        document.getElementById('btnFecharRevisao')?.addEventListener('click', () => {
            this.fecharModalRevisao();
        });

        // Fechar modais
        document.querySelectorAll('.modal-close-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Integração com sistema existente - adicionar artigos à revisão
        this.integrarComSistemaExistente();

        // Adicionar botão para limpar revisão (temporário para resolver o problema)
        this.adicionarBotaoLimparRevisao();
    }

    /**
     * Monitora mudanças no status de leitura dos artigos para sincronizar com revisão
     */
    integrarComSistemaExistente() {
        console.log('Integrando sistema de revisão automático baseado em status de leitura');

        // Interceptar chamadas para a API de progresso
        this.interceptarAPIProgresso();

        // Monitorar mudanças no localStorage
        this.monitorarLocalStorage();

        // Verificar artigos já lidos ao inicializar
        this.sincronizarArtigosLidos();
    }

    /**
     * Intercepta chamadas para a API de progresso para detectar mudanças
     */
    interceptarAPIProgresso() {
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const response = await originalFetch(...args);

            // Se é uma chamada para progresso.php
            if (args[0] && args[0].includes('progresso.php')) {
                try {
                    const requestBody = args[1]?.body;

                    if (requestBody) {
                        const data = JSON.parse(requestBody);
                        if (data.artigo_numero !== undefined && data.lido !== undefined) {
                            console.log('Detectada mudança de status:', data);
                            // Aguardar um pouco para garantir que a API processou
                            setTimeout(() => {
                                this.processarMudancaStatusLeitura(data.artigo_numero, data.lido);
                            }, 500);
                        }
                    }
                } catch (error) {
                    console.log('Erro ao interceptar progresso:', error);
                }
            }

            return response;
        };
    }

    /**
     * Monitora mudanças no localStorage para detectar alterações
     */
    monitorarLocalStorage() {
        // Verificar mudanças no localStorage a cada 2 segundos
        setInterval(() => {
            this.verificarMudancasLocalStorage();
        }, 2000);
    }

    /**
     * Verifica se houve mudanças no localStorage
     */
    verificarMudancasLocalStorage() {
        try {
            const artigosLidos = JSON.parse(localStorage.getItem('lexjus_artigos_lidos') || '[]');
            const artigosLidosAnteriores = this.artigosLidosAnteriores || [];

            // Verificar artigos que foram adicionados
            const novosLidos = artigosLidos.filter(artigo => !artigosLidosAnteriores.includes(artigo));
            novosLidos.forEach(artigo => {
                console.log('Artigo marcado como lido:', artigo);
                this.processarMudancaStatusLeitura(artigo, true);
            });

            // Verificar artigos que foram removidos
            const removidosLidos = artigosLidosAnteriores.filter(artigo => !artigosLidos.includes(artigo));
            removidosLidos.forEach(artigo => {
                console.log('Artigo desmarcado como lido:', artigo);
                this.processarMudancaStatusLeitura(artigo, false);
            });

            // Atualizar referência
            this.artigosLidosAnteriores = [...artigosLidos];
        } catch (error) {
            console.error('Erro ao verificar localStorage:', error);
        }
    }

    /**
     * Sincroniza artigos já lidos com o sistema de revisão
     */
    async sincronizarArtigosLidos() {
        try {
            const artigosLidos = JSON.parse(localStorage.getItem('lexjus_artigos_lidos') || '[]');
            console.log('Inicializando monitoramento para', artigosLidos.length, 'artigos lidos');

            // Apenas inicializar referência, SEM sincronizar automaticamente
            // Isso evita adicionar artigos antigos que o usuário não quer na revisão
            this.artigosLidosAnteriores = [...artigosLidos];

            console.log('Monitoramento inicializado - apenas mudanças futuras serão processadas');
        } catch (error) {
            console.error('Erro ao inicializar monitoramento:', error);
        }
    }

    /**
     * Processa mudança no status de leitura de um artigo
     */
    async processarMudancaStatusLeitura(artigoNumero, lido) {
        try {
            console.log(`Processando mudança: Artigo ${artigoNumero} - Lido: ${lido}`);

            if (lido) {
                // Artigo foi marcado como lido - verificar se já está na revisão
                const jaEstaRevisao = await this.verificarArtigoNaRevisao(artigoNumero);

                if (!jaEstaRevisao) {
                    await this.garantirArtigoNaRevisao(artigoNumero);
                    this.mostrarNotificacao(`📚 Artigo ${artigoNumero} adicionado à revisão automática!`, 'sucesso');
                } else {
                    console.log(`Artigo ${artigoNumero} já está na revisão, ignorando`);
                }
            } else {
                // Artigo foi desmarcado como lido - remover da revisão
                await this.removerArtigoDaRevisao(artigoNumero);
                this.mostrarNotificacao(`📖 Artigo ${artigoNumero} removido da revisão`, 'info');
            }

            // Recarregar dados para atualizar interface
            await this.carregarDados();
            this.atualizarInterface();

        } catch (error) {
            console.error('Erro ao processar mudança de status:', error);
        }
    }

    /**
     * Verifica se um artigo já está no sistema de revisão
     */
    async verificarArtigoNaRevisao(artigoNumero) {
        try {
            // Limpar formatação do número do artigo
            const numeroLimpo = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

            // Verificar se está na lista de revisões carregadas
            if (this.revisoesPendentes && this.revisoesPendentes.length > 0) {
                const encontrado = this.revisoesPendentes.some(revisao =>
                    revisao.artigo_numero === numeroLimpo ||
                    revisao.artigo_numero === artigoNumero
                );
                if (encontrado) {
                    return true;
                }
            }

            // Verificar no servidor se não encontrou localmente
            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'verificar',
                    artigo_numero: numeroLimpo
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.existe || false;
            }

            return false;
        } catch (error) {
            console.error('Erro ao verificar artigo na revisão:', error);
            return false;
        }
    }

    /**
     * Garante que um artigo está no sistema de revisão
     */
    async garantirArtigoNaRevisao(artigoNumero) {
        try {
            // Limpar formatação do número do artigo
            artigoNumero = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'iniciar',
                    artigo_numero: artigoNumero
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    console.log(`Artigo ${artigoNumero} garantido na revisão:`, data.mensagem);
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Erro ao garantir artigo na revisão:', error);
            return false;
        }
    }

    /**
     * Remove um artigo do sistema de revisão
     */
    async removerArtigoDaRevisao(artigoNumero) {
        try {
            // Limpar formatação do número do artigo
            artigoNumero = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'remover',
                    artigo_numero: artigoNumero
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    console.log(`Artigo ${artigoNumero} removido da revisão`);
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Erro ao remover artigo da revisão:', error);
            return false;
        }
    }

    /**
     * Adiciona botão temporário para limpar revisão
     */
    adicionarBotaoLimparRevisao() {
        // Verificar se já existe
        if (document.getElementById('btnLimparRevisao')) {
            return;
        }

        // Encontrar onde adicionar o botão
        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
            const btnLimpar = document.createElement('button');
            btnLimpar.id = 'btnLimparRevisao';
            btnLimpar.className = 'btn-limpar-revisao';
            btnLimpar.innerHTML = '<i class="fas fa-trash"></i> Limpar Revisão';
            btnLimpar.title = 'Remove todos os artigos da revisão (temporário)';
            btnLimpar.style.cssText = `
                background: #e74c3c;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                margin-left: 10px;
            `;

            btnLimpar.addEventListener('click', async () => {
                if (confirm('Tem certeza que deseja remover TODOS os artigos da revisão?')) {
                    await this.limparTodasRevisoes();
                }
            });

            headerActions.appendChild(btnLimpar);
            console.log('Botão de limpar revisão adicionado');
        }
    }

    /**
     * Remove todos os artigos da revisão
     */
    async limparTodasRevisoes() {
        try {
            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'limpar_todas'
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    this.mostrarNotificacao('🗑️ Todas as revisões foram removidas!', 'sucesso');
                    await this.carregarDados();
                    this.atualizarInterface();
                } else {
                    throw new Error(data.erro || 'Erro ao limpar revisões');
                }
            } else {
                throw new Error('Erro na requisição');
            }
        } catch (error) {
            console.error('Erro ao limpar revisões:', error);
            this.mostrarNotificacao('❌ Erro ao limpar revisões: ' + error.message, 'erro');
        }
    }

    /**
     * Abre o modal principal de revisão
     */
    abrirModalRevisao() {
        const modal = document.getElementById('revisaoModal');
        if (modal) {
            modal.style.display = 'block';
            this.mostrarEstado('aguardando');
            this.atualizarDashboard();
        }
    }

    /**
     * Fecha o modal de revisão
     */
    fecharModalRevisao() {
        const modal = document.getElementById('revisaoModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * Abre o modal de estatísticas
     */
    abrirModalEstatisticas() {
        const modal = document.getElementById('estatisticasModal');
        if (modal) {
            modal.style.display = 'block';
            this.atualizarEstatisticas();
        }
    }

    /**
     * Mostra um estado específico do modal
     */
    mostrarEstado(estado) {
        const estados = ['aguardando', 'revisando', 'resultado'];
        estados.forEach(e => {
            const elemento = document.getElementById(`estado${e.charAt(0).toUpperCase() + e.slice(1)}`);
            if (elemento) {
                elemento.style.display = e === estado ? 'block' : 'none';
            }
        });
    }

    /**
     * Atualiza a interface principal
     */
    atualizarInterface() {
        // Atualizar contador na barra de navegação
        const contador = document.getElementById('revisaoCount');
        if (contador) {
            contador.textContent = this.revisoesPendentes.length;
        }

        // Atualizar contadores mini no header
        const pendentesCount = document.getElementById('pendentesCount');
        if (pendentesCount) {
            pendentesCount.textContent = this.revisoesPendentes.length;
        }

        const dominadosCount = document.getElementById('dominadosCount');
        if (dominadosCount && this.estatisticas.dominados !== undefined) {
            dominadosCount.textContent = this.estatisticas.dominados;
        }
    }

    /**
     * Atualiza o dashboard de revisão
     */
    atualizarDashboard() {
        if (!this.estatisticas) return;

        const elementos = {
            'dashPendentes': this.estatisticas.pendentes || 0,
            'dashAprendendo': this.estatisticas.aprendendo || 0,
            'dashDominados': this.estatisticas.dominados || 0
        };

        Object.entries(elementos).forEach(([id, valor]) => {
            const elemento = document.getElementById(id);
            if (elemento) {
                elemento.textContent = valor;
            }
        });

        // Habilitar/desabilitar botão de iniciar revisão
        const btnIniciar = document.getElementById('btnIniciarRevisao');
        if (btnIniciar) {
            const temPendentes = this.revisoesPendentes.length > 0;
            btnIniciar.disabled = !temPendentes;
            btnIniciar.innerHTML = temPendentes
                ? '<i class="fas fa-play"></i> Iniciar Revisão'
                : '<i class="fas fa-check"></i> Nenhuma revisão pendente';
        }
    }

    /**
     * Inicia uma sessão de revisão
     */
    async iniciarSessaoRevisao() {
        if (this.revisoesPendentes.length === 0) {
            this.mostrarNotificacao('Nenhuma revisão pendente no momento!', 'info');
            return;
        }

        this.sessaoAtual = {
            artigos: [...this.revisoesPendentes].slice(0, this.configuracao.max_revisoes_dia || 20),
            indiceAtual: 0,
            respostas: [],
            tempoInicio: Date.now()
        };

        this.mostrarEstado('revisando');
        this.carregarProximoArtigo();
    }

    /**
     * Carrega o próximo artigo da sessão
     */
    async carregarProximoArtigo() {
        if (!this.sessaoAtual || this.sessaoAtual.indiceAtual >= this.sessaoAtual.artigos.length) {
            this.finalizarSessao();
            return;
        }

        const artigo = this.sessaoAtual.artigos[this.sessaoAtual.indiceAtual];
        this.revisaoAtual = artigo;
        this.tempoInicioRevisao = Date.now();

        console.log('Carregando próximo artigo da sessão:', artigo);

        // Atualizar interface
        this.atualizarInterfaceRevisao(artigo);

        // Carregar conteúdo do artigo (sempre funciona, com fallback se necessário)
        try {
            await this.carregarConteudoArtigo(artigo.artigo_numero);
        } catch (error) {
            console.error('Erro ao carregar conteúdo, usando fallback:', error);
            this.mostrarConteudoFallback(artigo.artigo_numero);
        }
    }

    /**
     * Atualiza a interface durante a revisão
     */
    atualizarInterfaceRevisao(artigo) {
        // Atualizar número do artigo com prefixo "Art."
        const numeroElement = document.getElementById('artigoRevisaoNumero');
        if (numeroElement) {
            numeroElement.textContent = `Art. ${artigo.artigo_numero}`;
        }

        // Atualizar metadados
        const repeticoesElement = document.getElementById('artigoRepeticoes');
        if (repeticoesElement) {
            repeticoesElement.textContent = artigo.repeticoes;
        }

        const facilidadeElement = document.getElementById('artigoFacilidade');
        if (facilidadeElement) {
            facilidadeElement.textContent = artigo.facilidade.toFixed(1);
        }

        // Atualizar progresso da sessão
        const sessaoAtualElement = document.getElementById('sessaoAtual');
        if (sessaoAtualElement) {
            sessaoAtualElement.textContent = this.sessaoAtual.indiceAtual + 1;
        }

        const sessaoTotalElement = document.getElementById('sessaoTotal');
        if (sessaoTotalElement) {
            sessaoTotalElement.textContent = this.sessaoAtual.artigos.length;
        }

        // Resetar botões de qualidade
        document.querySelectorAll('.btn-qualidade').forEach(btn => {
            btn.classList.remove('selected');
        });
    }

    /**
     * Carrega o conteúdo do artigo
     */
     async carregarConteudoArtigo(artigoNumero) {
        try {
            console.log('Carregando conteúdo do artigo:', artigoNumero);

            // Primeiro tentar buscar no DOM (se o artigo estiver carregado)
            const card = this.buscarCardArtigo(artigoNumero);

            if (card) {
                console.log('Artigo encontrado no DOM');
                this.carregarConteudoDoCard(card);
                return;
            }

            // Se não encontrou no DOM, mostrar conteúdo de estudo direto
            console.log('Artigo não encontrado no DOM, mostrando interface de estudo');
            this.mostrarConteudoEstudo(artigoNumero);

        } catch (error) {
            console.error('Erro ao carregar conteúdo do artigo:', error);
            this.mostrarConteudoEstudo(artigoNumero);
        }
    }

    /**
     * Busca card do artigo no DOM com diferentes formatos
     */
    buscarCardArtigo(artigoNumero) {
        // Limpar formatação
        const numeroLimpo = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

        // Tentar diferentes seletores
        const seletores = [
            `[data-artigo="${artigoNumero}"]`,
            `[data-artigo="${numeroLimpo}"]`,
            `[data-artigo="Art. ${numeroLimpo}"]`,
            `[data-artigo="Art. ${numeroLimpo}."]`,
            `[data-artigo="${numeroLimpo}º"]`,
            `[data-artigo="Art. ${numeroLimpo}º"]`
        ];

        for (const seletor of seletores) {
            const card = document.querySelector(seletor);
            if (card) {
                console.log(`Card encontrado com seletor: ${seletor}`);
                return card;
            }
        }

        console.log('Card não encontrado com nenhum seletor para:', artigoNumero);
        return null;
    }

    /**
     * Mostra conteúdo de estudo baseado apenas no banco de dados
     */
    mostrarConteudoEstudo(artigoNumero) {
        console.log('Mostrando conteúdo de estudo para artigo:', artigoNumero);

        const conteudoElement = document.getElementById('artigoRevisaoConteudo');
        if (conteudoElement) {
            conteudoElement.innerHTML = `
                <div class="artigo-estudo">
                    <div class="estudo-header">
                        <i class="fas fa-book-open"></i>
                        <h3>Art. ${artigoNumero}</h3>
                        <span class="badge-constituicao">Constituição Federal de 1988</span>
                    </div>
                    <div class="estudo-content">
                        <div class="estudo-instrucoes">
                            <h4>📚 Como estudar este artigo:</h4>
                            <ol>
                                <li><strong>Leia</strong> o Art. ${artigoNumero} na Constituição Federal</li>
                                <li><strong>Compreenda</strong> o contexto e aplicação prática</li>
                                <li><strong>Memorize</strong> os pontos principais</li>
                                <li><strong>Avalie</strong> seu conhecimento abaixo</li>
                            </ol>
                        </div>

                        <div class="estudo-dicas">
                            <h4>💡 Dicas de estudo:</h4>
                            <ul>
                                <li>🔍 <strong>Contextualize:</strong> Entenda onde este artigo se encaixa na Constituição</li>
                                <li>📖 <strong>Relacione:</strong> Conecte com outros artigos relacionados</li>
                                <li>⚖️ <strong>Aplique:</strong> Pense em casos práticos onde se aplica</li>
                                <li>🧠 <strong>Memorize:</strong> Use técnicas de memorização para fixar</li>
                            </ul>
                        </div>

                        <div class="estudo-revisao">
                            <div class="revisao-info">
                                <i class="fas fa-brain"></i>
                                <div>
                                    <h4>Sistema de Revisão Inteligente</h4>
                                    <p>Este artigo está sendo revisado usando o algoritmo de repetição espaçada.
                                    Sua avaliação determinará quando você verá este artigo novamente.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Carrega conteúdo de um card do DOM
     */
    carregarConteudoDoCard(card) {
        try {
            // Extrair dados do card
            const caput = card.dataset.caput || '';
            const incisos = JSON.parse(card.dataset.incisos || '[]');
            const paragrafoUnico = card.dataset.paragrafoUnico || '';
            const paragrafosNumerados = JSON.parse(card.dataset.paragrafosNumerados || '[]');

            this.montarConteudoHTML(caput, incisos, paragrafoUnico, paragrafosNumerados);
        } catch (error) {
            console.error('Erro ao extrair dados do card:', error);
            throw error;
        }
    }

    /**
     * Monta o HTML do conteúdo do artigo
     */
    montarConteudoHTML(caput, incisos, paragrafoUnico, paragrafosNumerados) {
        let conteudoHTML = '';

        if (caput) {
            conteudoHTML += `<div class="artigo-caput"><strong>Caput:</strong><br>${caput}</div>`;
        }

        if (incisos.length > 0) {
            conteudoHTML += '<div class="artigo-incisos"><strong>Incisos:</strong><ul>';
            incisos.forEach(inciso => {
                conteudoHTML += `<li>${inciso}</li>`;
            });
            conteudoHTML += '</ul></div>';
        }

        if (paragrafoUnico) {
            conteudoHTML += `<div class="artigo-paragrafo-unico"><strong>Parágrafo Único:</strong><br>${paragrafoUnico}</div>`;
        }

        if (paragrafosNumerados.length > 0) {
            conteudoHTML += '<div class="artigo-paragrafos"><strong>Parágrafos:</strong>';
            paragrafosNumerados.forEach(pn => {
                conteudoHTML += `<div class="paragrafo-numerado">`;
                conteudoHTML += `<span class="paragrafo-numero">${pn.numero}</span>`;
                conteudoHTML += `<span class="paragrafo-texto">${pn.texto}</span>`;
                if (pn.alineas && pn.alineas.length > 0) {
                    conteudoHTML += '<ul class="alineas-lista">';
                    pn.alineas.forEach(alinea => {
                        conteudoHTML += `<li>${alinea}</li>`;
                    });
                    conteudoHTML += '</ul>';
                }
                conteudoHTML += '</div>';
            });
            conteudoHTML += '</div>';
        }

        // Inserir conteúdo no modal
        const conteudoElement = document.getElementById('artigoRevisaoConteudo');
        if (conteudoElement) {
            conteudoElement.innerHTML = conteudoHTML;
        }
    }

    /**
     * [REMOVIDO] Função de fallback não é mais necessária
     * O sistema agora usa apenas o banco de dados e interface de estudo
     */
    // mostrarConteudoFallback(artigoNumero) {
    //     // Esta função foi removida pois o sistema não depende mais de APIs externas
    //     // O conteúdo é mostrado através da função mostrarConteudoEstudo()
    // }

    /**
     * Processa a resposta do usuário
     */
    async processarResposta(qualidade) {
        if (!this.revisaoAtual) return;

        const tempoResposta = Math.round((Date.now() - this.tempoInicioRevisao) / 1000);

        // Marcar botão selecionado
        document.querySelectorAll('.btn-qualidade').forEach(btn => {
            btn.classList.remove('selected');
            if (parseInt(btn.dataset.qualidade) === qualidade) {
                btn.classList.add('selected');
            }
        });

        try {
            // Enviar resposta para o servidor
            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'responder',
                    artigo_numero: this.revisaoAtual.artigo_numero,
                    qualidade: qualidade,
                    tempo_resposta: tempoResposta
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    // Registrar resposta na sessão
                    this.sessaoAtual.respostas.push({
                        artigo: this.revisaoAtual.artigo_numero,
                        qualidade: qualidade,
                        tempo: tempoResposta,
                        resultado: data
                    });

                    // Mostrar feedback visual
                    this.mostrarFeedbackResposta(qualidade, data);

                    // Avançar para próximo artigo após delay
                    setTimeout(() => {
                        this.sessaoAtual.indiceAtual++;
                        this.carregarProximoArtigo();
                    }, 1500);
                }
            }
        } catch (error) {
            console.error('Erro ao processar resposta:', error);
            this.mostrarNotificacao('Erro ao processar resposta', 'erro');
        }
    }

    /**
     * Mostra feedback visual da resposta
     */
    mostrarFeedbackResposta(qualidade, resultado) {
        const feedbackMessages = {
            0: 'Não se preocupe, continue praticando!',
            1: 'Difícil, mas você vai melhorar!',
            2: 'Razoável, continue estudando!',
            3: 'Bom trabalho!',
            4: 'Excelente!',
            5: 'Perfeito! Você domina este artigo!'
        };

        const message = feedbackMessages[qualidade] || 'Resposta registrada!';

        // Criar elemento de feedback temporário
        const feedback = document.createElement('div');
        feedback.className = `feedback-resposta qualidade-${qualidade}`;
        feedback.innerHTML = `
            <div class="feedback-content">
                <i class="fas ${qualidade >= 3 ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                <span>${message}</span>
                <small>Próxima revisão: ${this.formatarProximaRevisao(resultado.data_proxima_revisao)}</small>
            </div>
        `;

        const container = document.querySelector('.revisao-controles');
        if (container) {
            container.appendChild(feedback);

            // Remover após animação
            setTimeout(() => {
                feedback.remove();
            }, 1400);
        }
    }

    /**
     * Formata a data da próxima revisão
     */
    formatarProximaRevisao(dataString) {
        const data = new Date(dataString);
        const agora = new Date();
        const diffDias = Math.ceil((data - agora) / (1000 * 60 * 60 * 24));

        if (diffDias === 0) return 'Hoje';
        if (diffDias === 1) return 'Amanhã';
        if (diffDias < 7) return `Em ${diffDias} dias`;
        if (diffDias < 30) return `Em ${Math.ceil(diffDias / 7)} semanas`;
        return `Em ${Math.ceil(diffDias / 30)} meses`;
    }

    /**
     * Finaliza a sessão de revisão
     */
    finalizarSessao() {
        const tempoTotal = Math.round((Date.now() - this.sessaoAtual.tempoInicio) / 1000 / 60);

        // Atualizar interface de resultado
        const resultadoRevisados = document.getElementById('resultadoRevisados');
        if (resultadoRevisados) {
            resultadoRevisados.textContent = this.sessaoAtual.respostas.length;
        }

        const resultadoTempo = document.getElementById('resultadoTempo');
        if (resultadoTempo) {
            resultadoTempo.textContent = `${tempoTotal}min`;
        }

        // Calcular próxima sessão
        const proximaSessao = this.calcularProximaSessao();
        const resultadoProxima = document.getElementById('resultadoProxima');
        if (resultadoProxima) {
            resultadoProxima.textContent = proximaSessao;
        }

        // Mostrar estado de resultado
        this.mostrarEstado('resultado');

        // Recarregar dados
        this.carregarDados().then(() => {
            this.atualizarInterface();
        });
    }

    /**
     * Calcula quando será a próxima sessão
     */
    calcularProximaSessao() {
        // Lógica simples - pode ser melhorada
        const agora = new Date();
        const amanha = new Date(agora);
        amanha.setDate(amanha.getDate() + 1);

        return this.formatarProximaRevisao(amanha.toISOString());
    }

    /**
     * Atualiza as estatísticas no modal
     */
    atualizarEstatisticas() {
        if (!this.estatisticas) return;

        const elementos = {
            'statTotalArtigos': this.estatisticas.total_artigos || 0,
            'statPendentes': this.estatisticas.pendentes || 0,
            'statDominados': this.estatisticas.dominados || 0,
            'statFacilidadeMedia': (this.estatisticas.facilidade_media || 0).toFixed(1)
        };

        Object.entries(elementos).forEach(([id, valor]) => {
            const elemento = document.getElementById(id);
            if (elemento) {
                elemento.textContent = valor;
            }
        });

        // Atualizar barras de progresso
        const total = this.estatisticas.total_artigos || 1;
        const categorias = ['novos', 'aprendendo', 'revisando', 'dominados'];

        categorias.forEach(categoria => {
            const valor = this.estatisticas[categoria] || 0;
            const percentual = (valor / total) * 100;

            const progressElement = document.getElementById(`progress${categoria.charAt(0).toUpperCase() + categoria.slice(1)}`);
            if (progressElement) {
                progressElement.style.width = `${percentual}%`;
            }

            const valueElement = document.getElementById(`value${categoria.charAt(0).toUpperCase() + categoria.slice(1)}`);
            if (valueElement) {
                valueElement.textContent = valor;
            }
        });
    }

    /**
     * Mostra notificação para o usuário
     */
    mostrarNotificacao(mensagem, tipo = 'info') {
        console.log(`[${tipo.toUpperCase()}] ${mensagem}`);

        // Criar notificação toast
        this.criarToast(mensagem, tipo);
    }

    /**
     * Cria uma notificação toast
     */
    criarToast(mensagem, tipo) {
        // Remover toasts existentes
        const existingToasts = document.querySelectorAll('.toast-notification');
        existingToasts.forEach(toast => toast.remove());

        // Criar elemento toast
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${tipo}`;

        const icon = tipo === 'sucesso' ? 'fa-check-circle' :
                    tipo === 'erro' ? 'fa-times-circle' :
                    tipo === 'info' ? 'fa-info-circle' :
                    'fa-info-circle';

        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${icon}"></i>
                <span>${mensagem}</span>
            </div>
        `;

        // Adicionar estilos
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${tipo === 'sucesso' ? '#28a745' :
                        tipo === 'erro' ? '#dc3545' :
                        tipo === 'info' ? '#17a2b8' : '#007bff'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: inherit;
            font-size: 14px;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
        `;

        // Adicionar ao DOM
        document.body.appendChild(toast);

        // Remover após 4 segundos
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 6000);

        // Adicionar animações CSS se não existirem
        this.adicionarAnimacoesToast();
    }

    /**
     * Adiciona animações CSS para os toasts
     */
    adicionarAnimacoesToast() {
        if (document.getElementById('toast-animations')) return;

        const style = document.createElement('style');
        style.id = 'toast-animations';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .toast-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .toast-content i {
                font-size: 16px;
            }
        `;

        document.head.appendChild(style);
    }
}

// Inicializar sistema quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.sistemaRevisao = new SistemaRevisao();
});
