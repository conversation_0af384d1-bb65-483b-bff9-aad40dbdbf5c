/**
 * Service Worker Desabilitado para LexJus
 * 
 * Esta versão não faz cache para evitar erros com extensões do Chrome
 */

console.log('🔧 Service Worker: Versão desabilitada carregada');

// Instalação do Service Worker (sem cache)
self.addEventListener('install', event => {
    console.log('🔧 Service Worker: Instalado (sem cache)');
    self.skipWaiting();
});

// Ativação do Service Worker
self.addEventListener('activate', event => {
    console.log('✅ Service Worker: Ativado (sem cache)');
    
    // Limpar qualquer cache existente
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    console.log('🗑️ Service Worker: Removendo cache:', cacheName);
                    return caches.delete(cacheName);
                })
            );
        })
    );
    
    self.clients.claim();
});

// Interceptação de requisições (sem cache)
self.addEventListener('fetch', event => {
    // Simplesmente passar todas as requisições para a rede
    // Sem tentar fazer cache
    console.log('🌐 Service Worker: Passando requisição para rede:', event.request.url);
});

// Mensagens do cliente
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }

    if (event.data && event.data.type === 'CLEAR_CACHE') {
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
        }).then(() => {
            console.log('🧹 Service Worker: Cache limpo');
            event.ports[0].postMessage({ success: true });
        });
    }
});
