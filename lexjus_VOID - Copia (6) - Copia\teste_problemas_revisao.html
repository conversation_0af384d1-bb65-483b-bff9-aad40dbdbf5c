<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Problemas - Sistema de Revisão</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Teste de Problemas - Sistema de Revisão</h1>
        <p>Esta página testa os problemas mais comuns do sistema de revisão.</p>

        <!-- Status Geral -->
        <div class="test-section">
            <h2>📊 Status Geral do Sistema</h2>
            <div id="statusGeral">
                <p>🔄 Verificando...</p>
            </div>
            <button class="test-button" onclick="verificarStatusGeral()">🔄 Verificar Status</button>
        </div>

        <!-- Teste de APIs -->
        <div class="test-section">
            <h2>🔗 Teste das APIs</h2>
            <div id="statusAPIs">
                <p>⏳ Aguardando teste...</p>
            </div>
            <button class="test-button" onclick="testarAPIs()">🧪 Testar APIs</button>
        </div>

        <!-- Teste de Sincronização -->
        <div class="test-section">
            <h2>🔄 Teste de Sincronização</h2>
            <div id="statusSincronizacao">
                <p>⏳ Aguardando teste...</p>
            </div>
            <button class="test-button" onclick="testarSincronizacao()">🔄 Testar Sincronização</button>
        </div>

        <!-- Teste de Adição de Artigos -->
        <div class="test-section">
            <h2>➕ Teste de Adição de Artigos</h2>
            <div>
                <input type="text" id="artigoTeste" placeholder="Ex: 1, 2º, Art. 3" value="999">
                <button class="test-button" onclick="testarAdicaoArtigo()">➕ Adicionar Artigo</button>
                <button class="test-button" onclick="testarRemocaoArtigo()">➖ Remover Artigo</button>
            </div>
            <div id="statusAdicao">
                <p>⏳ Aguardando teste...</p>
            </div>
        </div>

        <!-- Teste de Limpeza -->
        <div class="test-section">
            <h2>🗑️ Teste de Limpeza</h2>
            <div id="statusLimpeza">
                <p>⏳ Aguardando teste...</p>
            </div>
            <button class="test-button" onclick="testarLimpeza()" style="background: #dc3545;">🗑️ Limpar Todas as Revisões</button>
        </div>

        <!-- Logs -->
        <div class="test-section">
            <h2>📝 Logs de Debug</h2>
            <div id="logsArea" class="log-area">
                <p>Logs aparecerão aqui...</p>
            </div>
            <button class="test-button" onclick="limparLogs()">🧹 Limpar Logs</button>
            <button class="test-button" onclick="exportarLogs()">💾 Exportar Logs</button>
        </div>
    </div>

    <script>
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsArea = document.getElementById('logsArea');
            const p = document.createElement('p');
            p.className = type;
            p.textContent = logEntry;
            logsArea.appendChild(p);
            logsArea.scrollTop = logsArea.scrollHeight;
        }

        function limparLogs() {
            logs = [];
            document.getElementById('logsArea').innerHTML = '<p>Logs limpos...</p>';
        }

        function exportarLogs() {
            const blob = new Blob([logs.join('\n')], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `logs_revisao_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        async function verificarStatusGeral() {
            log('🔄 Iniciando verificação do status geral...', 'info');
            const statusDiv = document.getElementById('statusGeral');
            
            try {
                // Testar API de estatísticas
                const response = await fetch('./api/revisao.php?acao=estatisticas');
                const data = await response.json();
                
                if (data.estatisticas) {
                    const stats = data.estatisticas;
                    statusDiv.innerHTML = `
                        <div>
                            <span class="status-indicator status-ok"></span>
                            <strong>Sistema Funcionando</strong>
                        </div>
                        <ul>
                            <li>Total de artigos: ${stats.total_artigos || 0}</li>
                            <li>Pendentes: ${stats.pendentes || 0}</li>
                            <li>Dominados: ${stats.dominados || 0}</li>
                            <li>Facilidade média: ${stats.facilidade_media || 0}</li>
                        </ul>
                    `;
                    log('✅ Sistema funcionando corretamente', 'success');
                } else {
                    throw new Error('Resposta inválida da API');
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div>
                        <span class="status-indicator status-error"></span>
                        <strong>Erro no Sistema</strong>
                    </div>
                    <p class="error">Erro: ${error.message}</p>
                `;
                log(`❌ Erro no sistema: ${error.message}`, 'error');
            }
        }

        async function testarAPIs() {
            log('🔗 Testando APIs...', 'info');
            const statusDiv = document.getElementById('statusAPIs');
            const apis = [
                { nome: 'Estatísticas', url: './api/revisao.php?acao=estatisticas' },
                { nome: 'Pendentes', url: './api/revisao.php?acao=pendentes' },
                { nome: 'Configuração', url: './api/revisao.php?acao=configuracao' }
            ];

            let resultados = [];

            for (const api of apis) {
                try {
                    log(`🧪 Testando API: ${api.nome}`, 'info');
                    const response = await fetch(api.url);
                    const data = await response.json();
                    
                    resultados.push(`
                        <div>
                            <span class="status-indicator status-ok"></span>
                            <strong>${api.nome}:</strong> OK
                        </div>
                    `);
                    log(`✅ API ${api.nome}: OK`, 'success');
                } catch (error) {
                    resultados.push(`
                        <div>
                            <span class="status-indicator status-error"></span>
                            <strong>${api.nome}:</strong> ERRO - ${error.message}
                        </div>
                    `);
                    log(`❌ API ${api.nome}: ${error.message}`, 'error');
                }
            }

            statusDiv.innerHTML = resultados.join('');
        }

        async function testarSincronizacao() {
            log('🔄 Testando sincronização...', 'info');
            const statusDiv = document.getElementById('statusSincronizacao');
            
            try {
                // Simular mudança no localStorage
                const artigosLidos = JSON.parse(localStorage.getItem('lexjus_artigos_lidos') || '[]');
                log(`📚 Artigos lidos no localStorage: ${artigosLidos.length}`, 'info');
                
                // Verificar se o sistema de revisão está ativo
                if (window.sistemaRevisao) {
                    statusDiv.innerHTML = `
                        <div>
                            <span class="status-indicator status-ok"></span>
                            <strong>Sistema de Revisão Ativo</strong>
                        </div>
                        <p>Artigos lidos: ${artigosLidos.length}</p>
                    `;
                    log('✅ Sistema de revisão está ativo', 'success');
                } else {
                    statusDiv.innerHTML = `
                        <div>
                            <span class="status-indicator status-warning"></span>
                            <strong>Sistema de Revisão Não Carregado</strong>
                        </div>
                        <p>O JavaScript do sistema pode não estar carregado.</p>
                    `;
                    log('⚠️ Sistema de revisão não está carregado', 'warning');
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div>
                        <span class="status-indicator status-error"></span>
                        <strong>Erro na Sincronização</strong>
                    </div>
                    <p class="error">Erro: ${error.message}</p>
                `;
                log(`❌ Erro na sincronização: ${error.message}`, 'error');
            }
        }

        async function testarAdicaoArtigo() {
            const artigo = document.getElementById('artigoTeste').value;
            log(`➕ Testando adição do artigo: ${artigo}`, 'info');
            
            try {
                const response = await fetch('./api/revisao.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        acao: 'iniciar',
                        artigo_numero: artigo
                    })
                });

                const data = await response.json();
                
                if (data.sucesso) {
                    log(`✅ Artigo ${artigo} adicionado: ${data.mensagem}`, 'success');
                    document.getElementById('statusAdicao').innerHTML = `
                        <div class="success">✅ Artigo ${artigo} adicionado com sucesso!</div>
                    `;
                } else {
                    throw new Error(data.erro || 'Erro desconhecido');
                }
            } catch (error) {
                log(`❌ Erro ao adicionar artigo ${artigo}: ${error.message}`, 'error');
                document.getElementById('statusAdicao').innerHTML = `
                    <div class="error">❌ Erro: ${error.message}</div>
                `;
            }
        }

        async function testarRemocaoArtigo() {
            const artigo = document.getElementById('artigoTeste').value;
            log(`➖ Testando remoção do artigo: ${artigo}`, 'info');
            
            try {
                const response = await fetch('./api/revisao.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        acao: 'remover',
                        artigo_numero: artigo
                    })
                });

                const data = await response.json();
                
                if (data.sucesso) {
                    log(`✅ Artigo ${artigo} removido: ${data.mensagem}`, 'success');
                    document.getElementById('statusAdicao').innerHTML = `
                        <div class="success">✅ Artigo ${artigo} removido com sucesso!</div>
                    `;
                } else {
                    throw new Error(data.erro || 'Erro desconhecido');
                }
            } catch (error) {
                log(`❌ Erro ao remover artigo ${artigo}: ${error.message}`, 'error');
                document.getElementById('statusAdicao').innerHTML = `
                    <div class="error">❌ Erro: ${error.message}</div>
                `;
            }
        }

        async function testarLimpeza() {
            if (!confirm('⚠️ Tem certeza que deseja limpar TODAS as revisões? Esta ação não pode ser desfeita!')) {
                return;
            }

            log('🗑️ Iniciando limpeza de todas as revisões...', 'warning');
            
            try {
                const response = await fetch('./api/revisao.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        acao: 'limpar_todas'
                    })
                });

                const data = await response.json();
                
                if (data.sucesso) {
                    log(`✅ Limpeza concluída: ${data.mensagem}`, 'success');
                    document.getElementById('statusLimpeza').innerHTML = `
                        <div class="success">✅ Todas as revisões foram removidas!</div>
                        <p>Linhas afetadas: ${data.linhas_afetadas || 'N/A'}</p>
                    `;
                } else {
                    throw new Error(data.erro || 'Erro desconhecido');
                }
            } catch (error) {
                log(`❌ Erro na limpeza: ${error.message}`, 'error');
                document.getElementById('statusLimpeza').innerHTML = `
                    <div class="error">❌ Erro: ${error.message}</div>
                `;
            }
        }

        // Inicializar página
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 Página de teste carregada', 'info');
            verificarStatusGeral();
        });
    </script>
</body>
</html>
