import json
import re

# Carregar o arquivo
with open('codigo_civil_formato_lexjus_final.json', 'r', encoding='utf-8') as f:
    artigos = json.load(f)

# Encontrar e corrigir o Art. 1403º
for artigo in artigos:
    if '1403' in artigo.get('artigo', ''):
        caput = artigo.get('caput', '')
        print(f"Artigo encontrado: {artigo['artigo']}")
        print(f"Caput antes: {caput}")
        
        # Remover números do início do caput
        if re.match(r'^[0-9]+\s+', caput):
            caput_corrigido = re.sub(r'^[0-9]+\s+', '', caput)
            artigo['caput'] = caput_corrigido
            print(f"Caput depois: {caput_corrigido}")
            print("Correção aplicada!")
        else:
            print("Caput já está correto")
        break

# Salvar o arquivo corrigido
with open('codigo_civil_formato_lexjus_final.json', 'w', encoding='utf-8') as f:
    json.dump(artigos, f, ensure_ascii=False, indent=2)

print("Arquivo salvo!")
