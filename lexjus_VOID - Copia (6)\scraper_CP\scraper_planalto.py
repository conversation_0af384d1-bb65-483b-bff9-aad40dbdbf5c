import requests
from bs4 import BeautifulSoup
import json
import re

# URL do Código Penal no site do Planalto (versão original especificada)
URL_CODIGO_PENAL = 'https://www.planalto.gov.br/ccivil_03/decreto-lei/del2848.htm'
ARQUIVO_SAIDA_JS = 'codigo_penal.js'

def buscar_conteudo_pagina(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        return response.text
    except requests.exceptions.Timeout:
        print(f"Erro ao buscar a página {url}: Timeout após 30 segundos.")
        return None
    except requests.exceptions.RequestException as e:
        print(f"Erro ao buscar a página {url}: {e}")
        return None

def limpar_texto(texto):
    if texto:
        texto = texto.strip()
        texto = re.sub(r'\s+', ' ', texto) # Remove espaços múltiplos, quebras de linha etc.
        texto = texto.replace("\xa0", " ") # Substitui non-breaking spaces
        return texto.strip()
    return ""

def remover_referencias(texto):
    if not texto:
        return texto

    # Remove todas as referências legislativas entre parênteses
    # (Redação dada pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Redação\s+dada\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # (Incluído pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Incluíd[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # (Alterado pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Alterad[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # (Revogado pela Lei nº X, de data)
    texto = re.sub(r'\s*\(Revogad[oa]\s+pela\s+Lei\s+n[ºo°]\s*[\d\.-]+,?\s*de\s+[\d\.-]+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Versões mais genéricas
    # (Redação dada pela Emenda/Decreto/etc.)
    texto = re.sub(r'\s*\(Redação\s+dada\s+pel[oa]\s+(?:Emenda|Decreto|Lei|Medida\s+Provisória).*?\)\s*', ' ', texto, flags=re.IGNORECASE)

    # (Incluído pela Emenda/Decreto/etc.)
    texto = re.sub(r'\s*\(Incluíd[oa]\s+pel[oa]\s+(?:Emenda|Decreto|Lei|Medida\s+Provisória).*?\)\s*', ' ', texto, flags=re.IGNORECASE)

    # (Vide Lei nº X)
    texto = re.sub(r'\s*\(Vide\s+Lei\s+n[ºo°]\s*[\d\.-]+.*?\)\s*', ' ', texto, flags=re.IGNORECASE)

    # (Vide ADPF X) - Arguição de Descumprimento de Preceito Fundamental
    texto = re.sub(r'\s*\(Vide\s+ADPF\s+\d+\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Outras referências específicas
    texto = re.sub(r'\s*\(Revogad[oa]\s+pela\s+Lei.*?\)\s*', ' ', texto, flags=re.IGNORECASE)
    texto = re.sub(r'\s*\(VETADO\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Outras referências comuns
    texto = re.sub(r'\s*\(Regulamento\)\s*', ' ', texto, flags=re.IGNORECASE)
    texto = re.sub(r'\s*\(Vigência\)\s*', ' ', texto, flags=re.IGNORECASE)

    # Remove links HTML que possam ter sobrado
    texto = re.sub(r'\s*\(\s*<a\s+href=.*?>.*?<\/a>\s*\)\s*', ' ', texto)

    # Remove espaços múltiplos e limpa
    texto = re.sub(r'\s+', ' ', texto)

    return limpar_texto(texto)


def analisar_e_extrair_artigos_codigo_penal(html_content):
    """
    Função específica para extrair artigos do Código Penal
    que usa estrutura HTML diferente com <li> e <strike>
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    contexto_atual = "caput"

    # Buscar a div com id="art" que contém os artigos
    div_artigos = soup.find('div', {'id': 'art'})
    if not div_artigos:
        print("AVISO: Não foi encontrada a div com id='art'. Tentando buscar em todo o documento.")
        # Fallback: buscar em todo o documento
        elementos_li = soup.find_all('li')
    else:
        elementos_li = div_artigos.find_all('li')

    print(f"DEBUG: Encontrados {len(elementos_li)} elementos <li> para processar")

    for li_tag in elementos_li:
        # Extrair texto do <li>, removendo tags <strike> mas mantendo o conteúdo
        texto_raw = li_tag.get_text(separator=' ', strip=False)
        texto_limpo = limpar_texto(texto_raw)

        if not texto_limpo:
            continue

        # DEBUG: Imprimir cada item processado
        print(f"DEBUG LI PROCESSANDO: \"{texto_limpo[:100]}\"...")

        # Regex para identificar artigos no formato "Art. X°" ou "Art. X"
        match_artigo = re.match(r'^(Art\.\s*\d+[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para parágrafos únicos
        match_paragrafo_unico = re.match(r'^Parágrafo único[\.:]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para parágrafos numerados
        match_paragrafo_numerado = re.match(r'^(§\s*\d+[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para incisos romanos
        match_inciso = re.match(r'^([IVX]+)\s*[-–]\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para alíneas
        match_alinea = re.match(r'^([a-z])\)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        if match_artigo:
            # Salvar artigo anterior se existir
            if artigo_atual:
                artigos_extraidos.append(artigo_atual)

            numero_art = remover_referencias(limpar_texto(match_artigo.group(1)))
            caput_texto = remover_referencias(limpar_texto(match_artigo.group(2)))

            print(f"DEBUG: Encontrado Artigo: '{numero_art}' - Caput: '{caput_texto[:50]}...'")

            artigo_atual = {
                "artigo": numero_art,
                "caput": caput_texto,
                "incisos": [],
                "paragrafos_numerados": [],
                "paragrafo_unico": None,
            }
            contexto_atual = "caput"

        elif artigo_atual:
            if match_inciso:
                numero_inciso = match_inciso.group(1)
                texto_inciso = remover_referencias(limpar_texto(match_inciso.group(2)))
                inciso_completo = f"{numero_inciso} - {texto_inciso}"

                if contexto_atual == "caput":
                    artigo_atual["incisos"].append(inciso_completo)
                elif contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    # Inciso de um parágrafo numerado
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "incisos" not in paragrafo_atual:
                        paragrafo_atual["incisos"] = []
                    paragrafo_atual["incisos"].append(inciso_completo)

            elif match_paragrafo_unico:
                texto_pu = remover_referencias(limpar_texto(match_paragrafo_unico.group(1)))
                artigo_atual["paragrafo_unico"] = texto_pu
                contexto_atual = "paragrafo_unico"

            elif match_paragrafo_numerado:
                num_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(1)))
                texto_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(2)))
                paragrafo_obj = {"numero": num_parag, "texto": texto_parag, "incisos": [], "alineas": []}
                artigo_atual["paragrafos_numerados"].append(paragrafo_obj)
                contexto_atual = "paragrafo_numerado"

            elif match_alinea:
                letra_alinea = match_alinea.group(1)
                texto_alinea = remover_referencias(limpar_texto(match_alinea.group(2)))
                alinea_completa = f"{letra_alinea}) {texto_alinea}"

                if contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "alineas" not in paragrafo_atual:
                        paragrafo_atual["alineas"] = []
                    paragrafo_atual["alineas"].append(alinea_completa)

    # Adicionar último artigo se existir
    if artigo_atual:
        artigos_extraidos.append(artigo_atual)

    if not artigos_extraidos:
        print("AVISO: Nenhum artigo foi extraído. Verifique a estrutura HTML.")
        return [
            {"artigo": "Art. X", "caput": "Falha na extração. Verifique o script e o HTML de origem.", "incisos": [], "paragrafo_unico": None}
        ]

    print(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def salvar_em_json(dados, nome_arquivo):
    try:
        with open(nome_arquivo, 'w', encoding='utf-8') as f:
            json.dump(dados, f, ensure_ascii=False, indent=2)
        print(f"Dados salvos com sucesso em {nome_arquivo}")
    except IOError as e:
        print(f"Erro ao salvar o arquivo {nome_arquivo}: {e}")

def salvar_em_js(dados, nome_arquivo):
    """
    Salva os dados em formato JavaScript como uma variável
    """
    try:
        with open(nome_arquivo, 'w', encoding='utf-8') as f:
            # Escrever cabeçalho do arquivo JS
            from datetime import datetime
            f.write("// Código Penal Brasileiro - Dados extraídos automaticamente\n")
            f.write(f"// Gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"// Total de artigos: {len(dados)}\n\n")

            # Escrever a variável JavaScript
            f.write("const codigoPenalArtigos = ")
            json.dump(dados, f, ensure_ascii=False, indent=2)
            f.write(";\n\n")

            # Adicionar função de busca
            f.write("""
// Função para buscar artigo por número
function buscarArtigo(numero) {
    return codigoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}

// Função para buscar artigos por texto
function buscarArtigosPorTexto(texto) {
    const textoLower = texto.toLowerCase();
    return codigoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        codigoPenalArtigos,
        buscarArtigo,
        buscarArtigosPorTexto
    };
}
""")
        print(f"Dados salvos com sucesso em {nome_arquivo}")
    except IOError as e:
        print(f"Erro ao salvar o arquivo {nome_arquivo}: {e}")

def processar_arquivo_local(caminho_arquivo):
    """
    Processa um arquivo HTML local
    """
    try:
        with open(caminho_arquivo, 'r', encoding='utf-8') as f:
            html_content = f.read()
        return html_content
    except FileNotFoundError:
        print(f"Arquivo não encontrado: {caminho_arquivo}")
        return None
    except Exception as e:
        print(f"Erro ao ler arquivo {caminho_arquivo}: {e}")
        return None

def analisar_e_extrair_artigos_versao_compilada(html_content):
    """
    Função para extrair artigos da versão compilada do Código Penal
    que usa estrutura HTML similar à Constituição com <p> tags
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    contexto_atual = "caput"

    # Itera sobre todos os parágrafos
    for p_tag in soup.find_all('p'):
        texto_paragrafo_raw = p_tag.get_text(separator=' ', strip=False)
        texto_limpo = limpar_texto(texto_paragrafo_raw)

        if not texto_limpo:
            continue

        # DEBUG: Imprimir cada parágrafo processado (limitado para não poluir)
        if len(artigos_extraidos) < 5:  # Só mostrar debug para os primeiros artigos
            print(f"DEBUG PARAGRAFO: \"{texto_limpo[:100]}\"...")

        # Regex para identificar artigos
        match_artigo = re.match(r'^(Art\.\s*\d+[-\w]*[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_inciso = re.match(r'^([IVX]+)\s*[-–]\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_paragrafo_unico = re.match(r'^Parágrafo único[\.:]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_paragrafo_numerado = re.match(r'^(§\s*\d+[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_alinea = re.match(r'^([a-z])\)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        if match_artigo:
            # Salvar artigo anterior se existir
            if artigo_atual:
                artigos_extraidos.append(artigo_atual)

            numero_art = remover_referencias(limpar_texto(match_artigo.group(1)))
            caput_texto = remover_referencias(limpar_texto(match_artigo.group(2)))

            # Print de progresso a cada 50 artigos
            if len(artigos_extraidos) % 50 == 0:
                print(f"Processando... {len(artigos_extraidos)} artigos extraídos. Atual: {numero_art}")

            artigo_atual = {
                "artigo": numero_art,
                "caput": caput_texto,
                "incisos": [],
                "paragrafos_numerados": [],
                "paragrafo_unico": None,
            }
            contexto_atual = "caput"

        elif artigo_atual:
            if match_inciso:
                numero_inciso = match_inciso.group(1)
                texto_inciso = remover_referencias(limpar_texto(match_inciso.group(2)))
                inciso_completo = f"{numero_inciso} - {texto_inciso}"

                if contexto_atual == "caput":
                    artigo_atual["incisos"].append(inciso_completo)
                elif contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "incisos" not in paragrafo_atual:
                        paragrafo_atual["incisos"] = []
                    paragrafo_atual["incisos"].append(inciso_completo)

            elif match_paragrafo_unico:
                texto_pu = remover_referencias(limpar_texto(match_paragrafo_unico.group(1)))
                artigo_atual["paragrafo_unico"] = texto_pu
                contexto_atual = "paragrafo_unico"

            elif match_paragrafo_numerado:
                num_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(1)))
                texto_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(2)))
                paragrafo_obj = {"numero": num_parag, "texto": texto_parag, "incisos": [], "alineas": []}
                artigo_atual["paragrafos_numerados"].append(paragrafo_obj)
                contexto_atual = "paragrafo_numerado"

            elif match_alinea:
                letra_alinea = match_alinea.group(1)
                texto_alinea = remover_referencias(limpar_texto(match_alinea.group(2)))
                alinea_completa = f"{letra_alinea}) {texto_alinea}"

                if contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "alineas" not in paragrafo_atual:
                        paragrafo_atual["alineas"] = []
                    paragrafo_atual["alineas"].append(alinea_completa)

            # Detectar fim de seções/títulos para resetar contexto
            elif texto_limpo.isupper() and any(palavra in texto_limpo for palavra in
                ["TÍTULO", "CAPÍTULO", "SEÇÃO", "LIVRO", "DISPOSIÇÕES", "PARTE"]):
                if artigo_atual:
                    artigos_extraidos.append(artigo_atual)
                    artigo_atual = None
                    contexto_atual = "caput"

    # Adicionar último artigo se existir
    if artigo_atual:
        artigos_extraidos.append(artigo_atual)

    print(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def analisar_e_extrair_artigos_hibrido(html_content):
    """
    Função híbrida que tenta extrair artigos de diferentes estruturas HTML
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    contexto_atual = "caput"

    print("Analisando estrutura HTML...")

    # Primeiro tentar encontrar elementos <li> (como no arquivo local)
    elementos_li = soup.find_all('li')
    elementos_p = soup.find_all('p')

    print(f"Encontrados {len(elementos_li)} elementos <li> e {len(elementos_p)} elementos <p>")

    # Decidir qual estrutura usar baseado na quantidade de elementos
    if len(elementos_li) > 50:  # Se há muitos <li>, usar estrutura de lista
        print("Usando estrutura de lista (<li>)...")
        elementos_para_processar = elementos_li
        usar_estrutura_li = True
    else:  # Caso contrário, usar estrutura de parágrafos
        print("Usando estrutura de parágrafos (<p>)...")
        elementos_para_processar = elementos_p
        usar_estrutura_li = False

    for elemento in elementos_para_processar:
        texto_raw = elemento.get_text(separator=' ', strip=False)
        texto_limpo = limpar_texto(texto_raw)

        if not texto_limpo:
            continue

        # DEBUG limitado
        if len(artigos_extraidos) < 5:
            print(f"DEBUG: \"{texto_limpo[:100]}\"...")

        # Regex para identificar artigos - mais flexível
        match_artigo = re.match(r'^(Art\.?\s*\d+[-\w]*[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_inciso = re.match(r'^([IVX]+)\s*[-–]\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_paragrafo_unico = re.match(r'^Parágrafo único[\.:]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_paragrafo_numerado = re.match(r'^(§\s*\d+[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)
        match_alinea = re.match(r'^([a-z])\)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        if match_artigo:
            # Salvar artigo anterior se existir
            if artigo_atual:
                artigos_extraidos.append(artigo_atual)

            numero_art = remover_referencias(limpar_texto(match_artigo.group(1)))
            caput_texto = remover_referencias(limpar_texto(match_artigo.group(2)))

            # Print de progresso
            if len(artigos_extraidos) % 50 == 0:
                print(f"Processando... {len(artigos_extraidos)} artigos extraídos. Atual: {numero_art}")

            artigo_atual = {
                "artigo": numero_art,
                "caput": caput_texto,
                "incisos": [],
                "paragrafos_numerados": [],
                "paragrafo_unico": None,
            }
            contexto_atual = "caput"

        elif artigo_atual:
            if match_inciso:
                numero_inciso = match_inciso.group(1)
                texto_inciso = remover_referencias(limpar_texto(match_inciso.group(2)))
                inciso_completo = f"{numero_inciso} - {texto_inciso}"

                if contexto_atual == "caput":
                    artigo_atual["incisos"].append(inciso_completo)
                elif contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "incisos" not in paragrafo_atual:
                        paragrafo_atual["incisos"] = []
                    paragrafo_atual["incisos"].append(inciso_completo)

            elif match_paragrafo_unico:
                texto_pu = remover_referencias(limpar_texto(match_paragrafo_unico.group(1)))
                artigo_atual["paragrafo_unico"] = texto_pu
                contexto_atual = "paragrafo_unico"

            elif match_paragrafo_numerado:
                num_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(1)))
                texto_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(2)))
                paragrafo_obj = {"numero": num_parag, "texto": texto_parag, "incisos": [], "alineas": []}
                artigo_atual["paragrafos_numerados"].append(paragrafo_obj)
                contexto_atual = "paragrafo_numerado"

            elif match_alinea:
                letra_alinea = match_alinea.group(1)
                texto_alinea = remover_referencias(limpar_texto(match_alinea.group(2)))
                alinea_completa = f"{letra_alinea}) {texto_alinea}"

                if contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "alineas" not in paragrafo_atual:
                        paragrafo_atual["alineas"] = []
                    paragrafo_atual["alineas"].append(alinea_completa)

            # Detectar fim de seções/títulos
            elif texto_limpo.isupper() and any(palavra in texto_limpo for palavra in
                ["TÍTULO", "CAPÍTULO", "SEÇÃO", "LIVRO", "DISPOSIÇÕES", "PARTE"]):
                if artigo_atual:
                    artigos_extraidos.append(artigo_atual)
                    artigo_atual = None
                    contexto_atual = "caput"

    # Adicionar último artigo se existir
    if artigo_atual:
        artigos_extraidos.append(artigo_atual)

    print(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def analisar_estrutura_especifica_html(html_content):
    """
    Função específica para analisar a estrutura exata do arquivo HTML fornecido
    com <div id="art"> -> <ul> -> <li> -> <strike>
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    contexto_atual = "caput"

    print("Analisando estrutura específica do HTML fornecido...")

    # Buscar especificamente a div com id="art"
    div_art = soup.find('div', {'id': 'art'})
    if not div_art:
        print("ERRO: Não foi encontrada a div com id='art'")
        return []

    # Buscar todos os <li> dentro da div
    elementos_li = div_art.find_all('li')
    print(f"Encontrados {len(elementos_li)} elementos <li> na div#art")

    for i, li_tag in enumerate(elementos_li):
        # Extrair texto removendo tags HTML mas mantendo o conteúdo
        texto_raw = li_tag.get_text(separator=' ', strip=True)
        texto_limpo = limpar_texto(texto_raw)

        if not texto_limpo:
            continue

        # DEBUG para os primeiros elementos
        if i < 10:
            print(f"DEBUG [{i}]: \"{texto_limpo[:80]}\"...")

        # Pular títulos e seções (texto em maiúsculo sem "Art.")
        if (texto_limpo.isupper() and "Art." not in texto_limpo and
            any(palavra in texto_limpo for palavra in ["TÍTULO", "CAPÍTULO", "SEÇÃO", "PARTE"])):
            continue

        # Pular subtítulos descritivos
        if (not texto_limpo.startswith(("Art.", "I ", "II ", "III ", "IV ", "V ", "VI ", "VII ", "VIII ", "IX ", "X ",
                                       "a)", "b)", "c)", "d)", "e)", "f)", "g)", "h)", "i)", "j)",
                                       "§", "Parágrafo único")) and
            len(texto_limpo.split()) < 10):
            continue

        # Regex para identificar artigos
        match_artigo = re.match(r'^(Art\.?\s*\d+[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para incisos romanos
        match_inciso = re.match(r'^([IVX]+)\s*[-–]\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para parágrafos únicos
        match_paragrafo_unico = re.match(r'^Parágrafo único[\.:]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para parágrafos numerados
        match_paragrafo_numerado = re.match(r'^(§\s*\d+[°º]?\.?)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para alíneas
        match_alinea = re.match(r'^([a-z])\)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        if match_artigo:
            # Salvar artigo anterior se existir
            if artigo_atual:
                artigos_extraidos.append(artigo_atual)

            numero_art = remover_referencias(limpar_texto(match_artigo.group(1)))
            caput_texto = remover_referencias(limpar_texto(match_artigo.group(2)))

            # Print de progresso
            if len(artigos_extraidos) % 10 == 0:
                print(f"Processando... {len(artigos_extraidos)} artigos extraídos. Atual: {numero_art}")

            artigo_atual = {
                "artigo": numero_art,
                "caput": caput_texto,
                "incisos": [],
                "paragrafos_numerados": [],
                "paragrafo_unico": None,
            }
            contexto_atual = "caput"

        elif artigo_atual:
            if match_inciso:
                numero_inciso = match_inciso.group(1)
                texto_inciso = remover_referencias(limpar_texto(match_inciso.group(2)))
                inciso_completo = f"{numero_inciso} - {texto_inciso}"

                if contexto_atual == "caput":
                    artigo_atual["incisos"].append(inciso_completo)
                elif contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "incisos" not in paragrafo_atual:
                        paragrafo_atual["incisos"] = []
                    paragrafo_atual["incisos"].append(inciso_completo)

            elif match_paragrafo_unico:
                texto_pu = remover_referencias(limpar_texto(match_paragrafo_unico.group(1)))
                artigo_atual["paragrafo_unico"] = texto_pu
                contexto_atual = "paragrafo_unico"

            elif match_paragrafo_numerado:
                num_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(1)))
                texto_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(2)))
                paragrafo_obj = {"numero": num_parag, "texto": texto_parag, "incisos": [], "alineas": []}
                artigo_atual["paragrafos_numerados"].append(paragrafo_obj)
                contexto_atual = "paragrafo_numerado"

            elif match_alinea:
                letra_alinea = match_alinea.group(1)
                texto_alinea = remover_referencias(limpar_texto(match_alinea.group(2)))
                alinea_completa = f"{letra_alinea}) {texto_alinea}"

                if contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "alineas" not in paragrafo_atual:
                        paragrafo_atual["alineas"] = []
                    paragrafo_atual["alineas"].append(alinea_completa)
                elif contexto_atual == "caput":
                    # Alínea direta do caput (caso especial)
                    if "alineas_caput" not in artigo_atual:
                        artigo_atual["alineas_caput"] = []
                    artigo_atual["alineas_caput"].append(alinea_completa)

    # Adicionar último artigo se existir
    if artigo_atual:
        artigos_extraidos.append(artigo_atual)

    print(f"Extração concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def analisar_estrutura_compilada_online(html_content):
    """
    Função para analisar a estrutura da versão compilada online
    que usa <p> tags em vez de <li>
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    artigos_extraidos = []
    artigo_atual = None
    contexto_atual = "caput"

    print("Analisando estrutura da versão compilada online...")

    # Buscar todos os elementos <p>
    elementos_p = soup.find_all('p')
    print(f"Total de elementos <p> encontrados: {len(elementos_p)}")

    for i, p_tag in enumerate(elementos_p):
        # Extrair texto removendo tags HTML
        texto_raw = p_tag.get_text(separator=' ', strip=True)
        texto_limpo = limpar_texto(texto_raw)

        if not texto_limpo:
            continue

        # DEBUG limitado
        if len(artigos_extraidos) < 5:
            print(f"DEBUG [{i}]: \"{texto_limpo[:80]}\"...")

        # Pular títulos e seções
        if (texto_limpo.isupper() and "Art." not in texto_limpo and
            any(palavra in texto_limpo for palavra in ["TÍTULO", "CAPÍTULO", "SEÇÃO", "PARTE"])):
            continue

        # Regex para identificar artigos na versão compilada
        match_artigo = re.match(r'^Art\.?\s*(\d+[°º]?)\s*[-–]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para incisos romanos
        match_inciso = re.match(r'^([IVX]+)\s*[-–]\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para parágrafos únicos
        match_paragrafo_unico = re.match(r'^Parágrafo único[\.:]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para parágrafos numerados
        match_paragrafo_numerado = re.match(r'^§\s*(\d+[°º]?)\s*[-–]?\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        # Regex para alíneas
        match_alinea = re.match(r'^([a-z])\)\s*(.*)', texto_limpo, re.IGNORECASE | re.DOTALL)

        if match_artigo:
            # Salvar artigo anterior se existir
            if artigo_atual:
                artigos_extraidos.append(artigo_atual)

            numero_art = f"Art. {match_artigo.group(1)}"
            caput_texto = remover_referencias(limpar_texto(match_artigo.group(2)))

            # Print de progresso a cada 50 artigos
            if len(artigos_extraidos) % 50 == 0:
                print(f"Processando... {len(artigos_extraidos)} artigos extraídos. Atual: {numero_art}")

            artigo_atual = {
                "artigo": numero_art,
                "caput": caput_texto,
                "incisos": [],
                "paragrafos_numerados": [],
                "paragrafo_unico": None,
            }
            contexto_atual = "caput"

        elif artigo_atual:
            if match_inciso:
                numero_inciso = match_inciso.group(1)
                texto_inciso = remover_referencias(limpar_texto(match_inciso.group(2)))
                inciso_completo = f"{numero_inciso} - {texto_inciso}"

                if contexto_atual == "caput":
                    artigo_atual["incisos"].append(inciso_completo)
                elif contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "incisos" not in paragrafo_atual:
                        paragrafo_atual["incisos"] = []
                    paragrafo_atual["incisos"].append(inciso_completo)

            elif match_paragrafo_unico:
                texto_pu = remover_referencias(limpar_texto(match_paragrafo_unico.group(1)))
                artigo_atual["paragrafo_unico"] = texto_pu
                contexto_atual = "paragrafo_unico"

            elif match_paragrafo_numerado:
                num_parag = f"§ {match_paragrafo_numerado.group(1)}"
                texto_parag = remover_referencias(limpar_texto(match_paragrafo_numerado.group(2)))
                paragrafo_obj = {"numero": num_parag, "texto": texto_parag, "incisos": [], "alineas": []}
                artigo_atual["paragrafos_numerados"].append(paragrafo_obj)
                contexto_atual = "paragrafo_numerado"

            elif match_alinea:
                letra_alinea = match_alinea.group(1)
                texto_alinea = remover_referencias(limpar_texto(match_alinea.group(2)))
                alinea_completa = f"{letra_alinea}) {texto_alinea}"

                if contexto_atual == "paragrafo_numerado" and artigo_atual["paragrafos_numerados"]:
                    paragrafo_atual = artigo_atual["paragrafos_numerados"][-1]
                    if "alineas" not in paragrafo_atual:
                        paragrafo_atual["alineas"] = []
                    paragrafo_atual["alineas"].append(alinea_completa)

    # Adicionar último artigo se existir
    if artigo_atual:
        artigos_extraidos.append(artigo_atual)

    print(f"Extração da versão compilada concluída. Total de {len(artigos_extraidos)} artigos processados.")
    return artigos_extraidos

def main():
    print("=== SCRAPER DO CÓDIGO PENAL ===")
    print("Comparando estruturas: arquivo local vs versão compilada online")

    # Primeiro processar arquivo local
    arquivo_local = 'codigo penal.html'
    print(f"\n1. Processando arquivo local: {arquivo_local}")
    html_content_local = processar_arquivo_local(arquivo_local)

    artigos_local = []
    if html_content_local:
        print("✅ Arquivo local carregado com sucesso!")
        artigos_local = analisar_estrutura_especifica_html(html_content_local)
        print(f"📊 Arquivo local: {len(artigos_local)} artigos extraídos")
    else:
        print("❌ Não foi possível carregar o arquivo local")

    # Tentar buscar versão compilada online
    print(f"\n2. Tentando buscar versão compilada online: {URL_CODIGO_PENAL}")
    html_content_online = buscar_conteudo_pagina(URL_CODIGO_PENAL)

    artigos_online = []
    if html_content_online:
        print("✅ Versão online carregada com sucesso!")
        artigos_online = analisar_estrutura_compilada_online(html_content_online)
        print(f"📊 Versão online: {len(artigos_online)} artigos extraídos")
    else:
        print("❌ Não foi possível carregar a versão online")

    # Decidir qual usar
    if len(artigos_online) > len(artigos_local):
        print(f"\n🎯 Usando versão online ({len(artigos_online)} artigos)")
        artigos_finais = artigos_online
        fonte = "online"
    elif len(artigos_local) > 0:
        print(f"\n🎯 Usando arquivo local ({len(artigos_local)} artigos)")
        artigos_finais = artigos_local
        fonte = "local"
    else:
        print("\n❌ Nenhuma fonte produziu artigos válidos")
        return

    # Salvar resultados
    if artigos_finais:
        # Salvar em formato JavaScript
        salvar_em_js(artigos_finais, ARQUIVO_SAIDA_JS)
        # Também salvar em JSON para backup
        salvar_em_json(artigos_finais, 'codigo_penal.json')

        print(f"\n🎉 Processamento concluído! Arquivos gerados:")
        print(f"📄 {ARQUIVO_SAIDA_JS} ({len(artigos_finais)} artigos)")
        print(f"📄 codigo_penal.json ({len(artigos_finais)} artigos)")
        print(f"📍 Fonte: {fonte}")

        # Mostrar alguns exemplos
        if len(artigos_finais) > 0:
            print(f"\n📋 Primeiro artigo: {artigos_finais[0]['artigo']}")
            print(f"📋 Último artigo: {artigos_finais[-1]['artigo']}")

        # Verificar completude
        if len(artigos_finais) < 100:
            print("⚠️  AVISO: Número relativamente baixo de artigos extraídos.")
        elif len(artigos_finais) >= 300:
            print("✅ Extração parece muito completa!")
        else:
            print("✅ Extração realizada com sucesso!")
    else:
        print("❌ Nenhum artigo foi extraído.")

if __name__ == '__main__':
    main()
