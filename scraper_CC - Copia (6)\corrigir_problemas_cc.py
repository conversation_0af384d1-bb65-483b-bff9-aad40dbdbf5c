#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def corrigir_problemas_cc():
    """Corrige os problemas identificados no arquivo codigo_civil_formato_lexjus_final.json"""
    
    print("=== CORREÇÃO DE PROBLEMAS DO CÓDIGO CIVIL ===")
    
    # Carregar o JSON
    with open('codigo_civil_formato_lexjus_final.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Total de artigos antes da correção: {len(data)}")
    
    # 1. Remover artigos com caput vazio e sufixos suspeitos
    print("\n1. Removendo artigos com caput vazio e sufixos suspeitos...")
    artigos_limpos = []
    removidos = []
    
    for i, art in enumerate(data):
        artigo = art['artigo']
        caput = art['caput'].strip()
        
        # Verificar se é um artigo suspeito (sufixo -I, -V com caput vazio)
        if (('-Iº' in artigo or '-Vº' in artigo) and 
            (caput == '' or len(caput) < 10)):
            removidos.append(f"Posição {i}: {artigo} - Caput vazio")
            continue
        
        # Verificar se é um parágrafo único sendo tratado como artigo
        if '-Pº' in artigo and caput.startswith('Parágrafo único'):
            removidos.append(f"Posição {i}: {artigo} - Parágrafo único duplicado")
            continue
        
        artigos_limpos.append(art)
    
    print(f"Artigos removidos: {len(removidos)}")
    for rem in removidos[:10]:  # Mostrar apenas os primeiros 10
        print(f"  {rem}")
    if len(removidos) > 10:
        print(f"  ... e mais {len(removidos) - 10} artigos")
    
    # 2. Remover duplicatas
    print("\n2. Removendo duplicatas...")
    artigos_unicos = []
    artigos_vistos = set()
    duplicatas_removidas = []
    
    for art in artigos_limpos:
        artigo = art['artigo']
        
        # Criar uma chave única baseada no artigo
        chave = artigo.replace('º', '')
        
        if chave in artigos_vistos:
            duplicatas_removidas.append(artigo)
            continue
        
        artigos_vistos.add(chave)
        artigos_unicos.append(art)
    
    print(f"Duplicatas removidas: {len(duplicatas_removidas)}")
    for dup in duplicatas_removidas:
        print(f"  {dup}")
    
    # 3. Verificar e corrigir estrutura dos artigos com sufixos
    print("\n3. Verificando estrutura dos artigos com sufixos...")
    artigos_corrigidos = []
    
    for art in artigos_unicos:
        artigo = art['artigo']
        caput = art['caput']
        
        # Verificar se o caput contém o número do artigo duplicado
        if '-A' in artigo:
            # Extrair o número base do artigo
            match = re.match(r'Art\. (\d+)-([A-Z])º?', artigo)
            if match:
                numero_base = match.group(1)
                letra = match.group(2)
                
                # Verificar se o caput contém o número do artigo duplicado
                padrao_duplicado = rf'Art\. {numero_base}-{letra}\.'
                if padrao_duplicado in caput:
                    # Remover a duplicação do número do artigo no caput
                    caput_limpo = re.sub(padrao_duplicado + r'\s*', '', caput)
                    art['caput'] = caput_limpo.strip()
                    print(f"  Corrigido caput duplicado em {artigo}")
        
        artigos_corrigidos.append(art)
    
    # 4. Ordenar artigos corretamente
    print("\n4. Ordenando artigos...")
    
    def extrair_numero_ordenacao(artigo):
        """Extrai número para ordenação considerando sufixos de letras"""
        match = re.match(r'Art\. (\d+)(?:-([A-Z]))?º?', artigo)
        if match:
            numero = int(match.group(1))
            letra = match.group(2)
            if letra:
                # Adicionar fração baseada na letra (A=0.1, B=0.2, etc.)
                numero += (ord(letra) - ord('A') + 1) * 0.1
            return numero
        return 0
    
    artigos_ordenados = sorted(artigos_corrigidos, key=lambda x: extrair_numero_ordenacao(x['artigo']))
    
    # 5. Salvar arquivo corrigido
    print(f"\n5. Salvando arquivo corrigido...")
    print(f"Total de artigos após correção: {len(artigos_ordenados)}")
    
    with open('codigo_civil_formato_lexjus_corrigido.json', 'w', encoding='utf-8') as f:
        json.dump(artigos_ordenados, f, ensure_ascii=False, indent=2)
    
    print("Arquivo salvo: codigo_civil_formato_lexjus_corrigido.json")
    
    # 6. Verificar artigos 48 no arquivo corrigido
    print("\n6. Verificando artigos 48 no arquivo corrigido:")
    artigos_48_corrigidos = []
    for art in artigos_ordenados:
        if '48' in art['artigo'] and not any(x in art['artigo'] for x in ['148', '248', '348', '448', '548', '648', '748', '848', '948']):
            artigos_48_corrigidos.append(art)
    
    for art in artigos_48_corrigidos:
        print(f"  {art['artigo']}: {art['caput'][:80]}...")
    
    return len(data), len(artigos_ordenados), len(removidos), len(duplicatas_removidas)

if __name__ == "__main__":
    original, final, removidos, duplicatas = corrigir_problemas_cc()
    print(f"\n=== RESUMO ===")
    print(f"Artigos originais: {original}")
    print(f"Artigos removidos: {removidos}")
    print(f"Duplicatas removidas: {duplicatas}")
    print(f"Artigos finais: {final}")
    print(f"Redução: {original - final} artigos ({((original - final) / original * 100):.1f}%)")
