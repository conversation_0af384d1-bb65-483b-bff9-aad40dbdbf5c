<?php
// Usar a versão simplificada que funciona
ob_start();
header('Content-Type: application/json');

session_start();
require_once __DIR__ . '/../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

switch ($metodo) {
    case 'GET':
        if (isset($_GET['acao'])) {
            switch ($_GET['acao']) {
                case 'pendentes':
                    listarRevisoesPendentes($conexao, $usuario_id);
                    break;
                case 'estatisticas':
                    obterEstatisticas($conexao, $usuario_id);
                    break;
                case 'configuracao':
                    obterConfiguracao($conexao, $usuario_id);
                    break;
                case 'historico':
                    obterHistorico($conexao, $usuario_id);
                    break;
                default:
                    listarTodasRevisoes($conexao, $usuario_id);
            }
        } else {
            listarTodasRevisoes($conexao, $usuario_id);
        }
        break;

    case 'POST':
        if (isset($dados['acao'])) {
            switch ($dados['acao']) {
                case 'iniciar':
                    iniciarRevisao($conexao, $usuario_id, $dados);
                    break;
                case 'responder':
                    processarResposta($conexao, $usuario_id, $dados);
                    break;
                case 'verificar':
                    verificarArtigoExiste($conexao, $usuario_id, $dados);
                    break;
                case 'remover':
                    removerArtigoRevisao($conexao, $usuario_id, $dados);
                    break;
                case 'limpar_todas':
                    limparTodasRevisoes($conexao, $usuario_id);
                    break;
                case 'configurar':
                    atualizarConfiguracao($conexao, $usuario_id, $dados);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['erro' => 'Ação não reconhecida']);
            }
        } else {
            http_response_code(400);
            echo json_encode(['erro' => 'Ação não especificada']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}

/**
 * Lista artigos pendentes para revisão
 */
function listarRevisoesPendentes($conexao, $usuario_id) {
    $query = "
        SELECT r.*,
               EXTRACT(EPOCH FROM (r.data_proxima_revisao - CURRENT_TIMESTAMP))/3600 as horas_restantes
        FROM appestudo.lexjus_revisoes r
        WHERE r.usuario_id = $1
          AND r.data_proxima_revisao <= CURRENT_TIMESTAMP
          AND r.status != 'dominado'
        ORDER BY r.data_proxima_revisao ASC, r.facilidade ASC
        LIMIT 50";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao consultar revisões pendentes']);
        return;
    }

    $revisoes = [];
    while ($row = pg_fetch_assoc($result)) {
        $revisoes[] = [
            'id' => (int)$row['id'],
            'artigo_numero' => $row['artigo_numero'],
            'facilidade' => (float)$row['facilidade'],
            'intervalo_dias' => (int)$row['intervalo_dias'],
            'repeticoes' => (int)$row['repeticoes'],
            'status' => $row['status'],
            'ultima_qualidade' => (int)$row['ultima_qualidade'],
            'data_proxima_revisao' => $row['data_proxima_revisao'],
            'horas_restantes' => round((float)$row['horas_restantes'], 1),
            'acertos_consecutivos' => (int)$row['acertos_consecutivos'],
            'total_revisoes' => (int)$row['total_revisoes']
        ];
    }

    echo json_encode(['revisoes_pendentes' => $revisoes]);
}

/**
 * Inicia uma nova revisão para um artigo
 */
function iniciarRevisao($conexao, $usuario_id, $dados) {
    if (!isset($dados['artigo_numero'])) {
        http_response_code(400);
        echo json_encode(['erro' => 'Número do artigo não informado']);
        return;
    }

    $artigo_numero = $dados['artigo_numero'];

    // Verificar se já existe revisão para este artigo
    $query_check = "
        SELECT id FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1 AND artigo_numero = $2";

    $result_check = pg_query_params($conexao, $query_check, [$usuario_id, $artigo_numero]);

    if (pg_num_rows($result_check) > 0) {
        // Já existe - apenas retornar os dados
        $row = pg_fetch_assoc($result_check);
        echo json_encode([
            'sucesso' => true,
            'revisao_id' => (int)$row['id'],
            'mensagem' => 'Revisão já existente'
        ]);
        return;
    }

    // Criar nova revisão
    pg_query($conexao, "BEGIN");

    try {
        $query_insert = "
            INSERT INTO appestudo.lexjus_revisoes
            (usuario_id, artigo_numero, status, data_proxima_revisao)
            VALUES ($1, $2, 'novo', CURRENT_TIMESTAMP)
            RETURNING id";

        $result_insert = pg_query_params($conexao, $query_insert, [$usuario_id, $artigo_numero]);

        if (!$result_insert) {
            throw new Exception(pg_last_error($conexao));
        }

        $row = pg_fetch_assoc($result_insert);
        $revisao_id = $row['id'];

        pg_query($conexao, "COMMIT");

        echo json_encode([
            'sucesso' => true,
            'revisao_id' => (int)$revisao_id,
            'mensagem' => 'Revisão iniciada com sucesso'
        ]);

    } catch (Exception $e) {
        pg_query($conexao, "ROLLBACK");
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao iniciar revisão: ' . $e->getMessage()]);
    }
}

/**
 * Processa a resposta de uma revisão (versão simplificada que funciona)
 */
function processarResposta($conexao, $usuario_id, $dados) {
    ob_clean();

    if (!isset($dados['artigo_numero']) || !isset($dados['qualidade'])) {
        http_response_code(400);
        echo json_encode(['erro' => 'Dados incompletos']);
        return;
    }

    $artigo_numero = $dados['artigo_numero'];
    $qualidade = (int)$dados['qualidade'];

    if ($qualidade < 0 || $qualidade > 5) {
        http_response_code(400);
        echo json_encode(['erro' => 'Qualidade deve estar entre 0 e 5']);
        return;
    }

    try {
        // Buscar revisão atual
        $query_current = "
            SELECT * FROM appestudo.lexjus_revisoes
            WHERE usuario_id = $1 AND artigo_numero = $2";

        $result_current = pg_query_params($conexao, $query_current, [$usuario_id, $artigo_numero]);

        if (!$result_current) {
            throw new Exception('Erro na consulta: ' . pg_last_error($conexao));
        }

        if (pg_num_rows($result_current) === 0) {
            throw new Exception('Revisão não encontrada para o artigo ' . $artigo_numero);
        }

        $revisao_atual = pg_fetch_assoc($result_current);

        // Algoritmo SM-2 simplificado
        $facilidade_atual = (float)$revisao_atual['facilidade'];
        $intervalo_atual = (int)$revisao_atual['intervalo_dias'];
        $repeticoes_atual = (int)$revisao_atual['repeticoes'];

        // Calcular nova facilidade
        $nova_facilidade = $facilidade_atual + (0.1 - (5 - $qualidade) * (0.08 + (5 - $qualidade) * 0.02));
        $nova_facilidade = max(1.30, min(3.00, $nova_facilidade));

        // Calcular novo intervalo e repetições
        if ($qualidade < 3) {
            $novas_repeticoes = 0;
            $novo_intervalo = 1;
            $novo_status = 'dificil';
        } else {
            $novas_repeticoes = $repeticoes_atual + 1;

            if ($novas_repeticoes == 1) {
                $novo_intervalo = 1;
            } elseif ($novas_repeticoes == 2) {
                $novo_intervalo = 6;
            } else {
                $novo_intervalo = round($intervalo_atual * $nova_facilidade);
            }

            if ($novas_repeticoes >= 5) {
                $novo_status = 'dominado';
            } elseif ($novas_repeticoes >= 2) {
                $novo_status = 'revisando';
            } else {
                $novo_status = 'aprendendo';
            }
        }

        $nova_data_revisao = date('Y-m-d H:i:s', strtotime("+{$novo_intervalo} days"));

        // Atualizar revisão
        $query_update = "
            UPDATE appestudo.lexjus_revisoes
            SET facilidade = $1,
                intervalo_dias = $2,
                repeticoes = $3,
                data_ultima_revisao = CURRENT_TIMESTAMP,
                data_proxima_revisao = $4,
                status = $5,
                ultima_qualidade = $6,
                total_revisoes = total_revisoes + 1,
                acertos_consecutivos = CASE
                    WHEN $6 >= 3 THEN acertos_consecutivos + 1
                    ELSE 0
                END
            WHERE usuario_id = $7 AND artigo_numero = $8";

        $result_update = pg_query_params($conexao, $query_update, [
            round($nova_facilidade, 2),
            $novo_intervalo,
            $novas_repeticoes,
            $nova_data_revisao,
            $novo_status,
            $qualidade,
            $usuario_id,
            $artigo_numero
        ]);

        if (!$result_update) {
            throw new Exception('Erro ao atualizar revisão: ' . pg_last_error($conexao));
        }

        echo json_encode([
            'sucesso' => true,
            'nova_facilidade' => round($nova_facilidade, 2),
            'novo_intervalo' => $novo_intervalo,
            'novas_repeticoes' => $novas_repeticoes,
            'data_proxima_revisao' => $nova_data_revisao,
            'novo_status' => $novo_status,
            'mensagem' => 'Resposta processada com sucesso'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao processar resposta: ' . $e->getMessage()]);
    }
}

/**
 * Implementação do algoritmo SM-2 em PHP
 */
function calcularProximaRevisaoPhp($facilidade, $intervalo, $qualidade, $repeticoes) {
    // Calcular nova facilidade baseada na qualidade da resposta
    $nova_facilidade = $facilidade + (0.1 - (5 - $qualidade) * (0.08 + (5 - $qualidade) * 0.02));

    // Garantir que facilidade está dentro dos limites
    if ($nova_facilidade < 1.30) {
        $nova_facilidade = 1.30;
    }
    if ($nova_facilidade > 3.00) {
        $nova_facilidade = 3.00;
    }

    // Calcular novo intervalo e repetições
    if ($qualidade < 3) {
        // Resposta ruim - reiniciar
        $novas_repeticoes = 0;
        $novo_intervalo = 1;
    } else {
        // Resposta boa - avançar
        $novas_repeticoes = $repeticoes + 1;

        if ($novas_repeticoes == 1) {
            $novo_intervalo = 1;
        } elseif ($novas_repeticoes == 2) {
            $novo_intervalo = 6;
        } else {
            $novo_intervalo = round($intervalo * $nova_facilidade);
        }
    }

    // Calcular nova data de revisão
    $nova_data_revisao = date('Y-m-d H:i:s', strtotime("+{$novo_intervalo} days"));

    return [
        'nova_facilidade' => round($nova_facilidade, 2),
        'novo_intervalo' => $novo_intervalo,
        'novas_repeticoes' => $novas_repeticoes,
        'nova_data_revisao' => $nova_data_revisao
    ];
}

/**
 * Determina o novo status baseado na performance
 */
function determinarNovoStatus($qualidade, $repeticoes, $acertos_consecutivos) {
    if ($qualidade < 3) {
        return 'dificil';
    } elseif ($repeticoes >= 5 && $acertos_consecutivos >= 3) {
        return 'dominado';
    } elseif ($repeticoes >= 2) {
        return 'revisando';
    } else {
        return 'aprendendo';
    }
}

/**
 * Obtém estatísticas do usuário
 */
function obterEstatisticas($conexao, $usuario_id) {
    $query = "
        SELECT
            COUNT(*) as total_artigos,
            COUNT(CASE WHEN status = 'novo' THEN 1 END) as novos,
            COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
            COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
            COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
            COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis,
            COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
            ROUND(AVG(facilidade), 2) as facilidade_media,
            ROUND(AVG(tempo_medio_resposta), 0) as tempo_medio_global
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao obter estatísticas']);
        return;
    }

    $stats = pg_fetch_assoc($result);

    // Converter valores para tipos apropriados
    foreach ($stats as $key => $value) {
        if (in_array($key, ['total_artigos', 'novos', 'aprendendo', 'revisando', 'dominados', 'dificeis', 'pendentes', 'tempo_medio_global'])) {
            $stats[$key] = (int)$value;
        } elseif (in_array($key, ['facilidade_media'])) {
            $stats[$key] = (float)$value;
        }
    }

    echo json_encode(['estatisticas' => $stats]);
}

/**
 * Lista todas as revisões do usuário
 */
function listarTodasRevisoes($conexao, $usuario_id) {
    $query = "
        SELECT * FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1
        ORDER BY data_atualizacao DESC";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao consultar revisões']);
        return;
    }

    $revisoes = [];
    while ($row = pg_fetch_assoc($result)) {
        $revisoes[] = [
            'id' => (int)$row['id'],
            'artigo_numero' => $row['artigo_numero'],
            'facilidade' => (float)$row['facilidade'],
            'intervalo_dias' => (int)$row['intervalo_dias'],
            'repeticoes' => (int)$row['repeticoes'],
            'status' => $row['status'],
            'data_proxima_revisao' => $row['data_proxima_revisao'],
            'total_revisoes' => (int)$row['total_revisoes'],
            'acertos_consecutivos' => (int)$row['acertos_consecutivos']
        ];
    }

    echo json_encode(['revisoes' => $revisoes]);
}

/**
 * Obtém configuração do usuário
 */
function obterConfiguracao($conexao, $usuario_id) {
    $query = "
        SELECT * FROM appestudo.lexjus_config_revisao
        WHERE usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (pg_num_rows($result) === 0) {
        // Criar configuração padrão
        $query_insert = "
            INSERT INTO appestudo.lexjus_config_revisao (usuario_id)
            VALUES ($1) RETURNING *";

        $result = pg_query_params($conexao, $query_insert, [$usuario_id]);
    }

    $config = pg_fetch_assoc($result);
    echo json_encode(['configuracao' => $config]);
}

/**
 * Atualiza configuração do usuário
 */
function atualizarConfiguracao($conexao, $usuario_id, $dados) {
    $campos_permitidos = [
        'max_revisoes_dia', 'max_novos_dia', 'horario_preferido_inicio',
        'horario_preferido_fim', 'notificar_revisoes', 'auto_promover_faceis'
    ];

    $updates = [];
    $valores = [$usuario_id];
    $contador = 2;

    foreach ($campos_permitidos as $campo) {
        if (isset($dados[$campo])) {
            $updates[] = "$campo = $" . $contador;
            $valores[] = $dados[$campo];
            $contador++;
        }
    }

    if (empty($updates)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Nenhum campo válido para atualizar']);
        return;
    }

    $query = "
        UPDATE appestudo.lexjus_config_revisao
        SET " . implode(', ', $updates) . "
        WHERE usuario_id = $1";

    $result = pg_query_params($conexao, $query, $valores);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao atualizar configuração']);
        return;
    }

    echo json_encode(['sucesso' => true, 'mensagem' => 'Configuração atualizada']);
}

/**
 * Obtém histórico de revisões
 */
function obterHistorico($conexao, $usuario_id) {
    $limite = isset($_GET['limite']) ? (int)$_GET['limite'] : 50;

    $query = "
        SELECT h.*, r.artigo_numero
        FROM appestudo.lexjus_historico_revisoes h
        JOIN appestudo.lexjus_revisoes r ON h.revisao_id = r.id
        WHERE h.usuario_id = $1
        ORDER BY h.data_revisao DESC
        LIMIT $2";

    $result = pg_query_params($conexao, $query, [$usuario_id, $limite]);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao obter histórico']);
        return;
    }

    $historico = [];
    while ($row = pg_fetch_assoc($result)) {
        $historico[] = [
            'id' => (int)$row['id'],
            'artigo_numero' => $row['artigo_numero'],
            'qualidade_resposta' => (int)$row['qualidade_resposta'],
            'tempo_resposta' => (int)$row['tempo_resposta'],
            'tipo_revisao' => $row['tipo_revisao'],
            'data_revisao' => $row['data_revisao']
        ];
    }

    echo json_encode(['historico' => $historico]);
}

/**
 * Verifica se um artigo já existe no sistema de revisão
 */
function verificarArtigoExiste($conexao, $usuario_id, $dados) {
    if (!isset($dados['artigo_numero'])) {
        http_response_code(400);
        echo json_encode(['erro' => 'Número do artigo não informado']);
        return;
    }

    $artigo_numero = $dados['artigo_numero'];

    // Limpar formatação do número do artigo para comparação
    $artigo_numero_limpo = preg_replace('/^Art\.\s*/', '', $artigo_numero);
    $artigo_numero_limpo = preg_replace('/\.$/', '', $artigo_numero_limpo);
    $artigo_numero_limpo = trim($artigo_numero_limpo);

    // Query mais robusta para encontrar o artigo em diferentes formatos
    $query = "
        SELECT id, artigo_numero, status, facilidade, repeticoes, total_revisoes, data_proxima_revisao
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1
        AND (
            artigo_numero = $2
            OR artigo_numero = $3
            OR TRIM(REGEXP_REPLACE(REGEXP_REPLACE(artigo_numero, '^Art\\.\\s*', ''), '\\.$', '')) = $3
            OR TRIM(REGEXP_REPLACE(REGEXP_REPLACE(artigo_numero, '^Artigo\\s*', ''), '\\.$', '')) = $3
        )";

    $result = pg_query_params($conexao, $query, [$usuario_id, $artigo_numero, $artigo_numero_limpo]);

    if (!$result) {
        http_response_code(500);
        echo json_encode([
            'erro' => 'Erro ao verificar artigo: ' . pg_last_error($conexao),
            'existe' => false,
            'artigo_numero' => $artigo_numero
        ]);
        return;
    }

    $existe = pg_num_rows($result) > 0;
    $detalhes = null;

    if ($existe) {
        $detalhes = pg_fetch_assoc($result);
        $detalhes['id'] = (int)$detalhes['id'];
        $detalhes['facilidade'] = (float)$detalhes['facilidade'];
        $detalhes['repeticoes'] = (int)$detalhes['repeticoes'];
        $detalhes['total_revisoes'] = (int)$detalhes['total_revisoes'];
    }

    // Log para debug
    error_log("Verificando artigo: '$artigo_numero' (limpo: '$artigo_numero_limpo') para usuário $usuario_id - Existe: " . ($existe ? 'SIM' : 'NÃO'));

    echo json_encode([
        'existe' => $existe,
        'artigo_numero' => $artigo_numero,
        'artigo_numero_limpo' => $artigo_numero_limpo,
        'detalhes' => $detalhes,
        'debug' => [
            'query_executada' => true,
            'linhas_encontradas' => pg_num_rows($result)
        ]
    ]);
}

/**
 * Remove um artigo do sistema de revisão
 */
function removerArtigoRevisao($conexao, $usuario_id, $dados) {
    if (!isset($dados['artigo_numero'])) {
        http_response_code(400);
        echo json_encode(['erro' => 'Número do artigo não informado']);
        return;
    }

    $artigo_numero = $dados['artigo_numero'];

    // Limpar formatação do número do artigo
    $artigo_numero_limpo = preg_replace('/^Art\.\s*/', '', $artigo_numero);
    $artigo_numero_limpo = preg_replace('/\.$/', '', $artigo_numero_limpo);
    $artigo_numero_limpo = trim($artigo_numero_limpo);

    // Verificar se o artigo existe antes de remover
    $query_verificar = "
        SELECT id
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1
        AND (
            artigo_numero = $2
            OR artigo_numero = $3
            OR TRIM(REGEXP_REPLACE(REGEXP_REPLACE(artigo_numero, '^Art\\.\\s*', ''), '\\.$', '')) = $3
            OR TRIM(REGEXP_REPLACE(REGEXP_REPLACE(artigo_numero, '^Artigo\\s*', ''), '\\.$', '')) = $3
        )";

    $result_verificar = pg_query_params($conexao, $query_verificar, [$usuario_id, $artigo_numero, $artigo_numero_limpo]);

    if (!$result_verificar) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao verificar artigo: ' . pg_last_error($conexao)]);
        return;
    }

    if (pg_num_rows($result_verificar) === 0) {
        echo json_encode([
            'sucesso' => true,
            'mensagem' => 'Artigo não estava no sistema de revisão',
            'artigo_numero' => $artigo_numero
        ]);
        return;
    }

    // Remover o artigo
    $query_remover = "
        DELETE FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1
        AND (
            artigo_numero = $2
            OR artigo_numero = $3
            OR TRIM(REGEXP_REPLACE(REGEXP_REPLACE(artigo_numero, '^Art\\.\\s*', ''), '\\.$', '')) = $3
            OR TRIM(REGEXP_REPLACE(REGEXP_REPLACE(artigo_numero, '^Artigo\\s*', ''), '\\.$', '')) = $3
        )";

    $result_remover = pg_query_params($conexao, $query_remover, [$usuario_id, $artigo_numero, $artigo_numero_limpo]);

    if (!$result_remover) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao remover artigo: ' . pg_last_error($conexao)]);
        return;
    }

    $linhas_afetadas = pg_affected_rows($result_remover);

    // Log para debug
    error_log("Removendo artigo: '$artigo_numero' (limpo: '$artigo_numero_limpo') para usuário $usuario_id - Linhas afetadas: $linhas_afetadas");

    echo json_encode([
        'sucesso' => true,
        'mensagem' => 'Artigo removido do sistema de revisão',
        'artigo_numero' => $artigo_numero,
        'linhas_afetadas' => $linhas_afetadas
    ]);
}

/**
 * Remove todos os artigos da revisão de um usuário
 */
function limparTodasRevisoes($conexao, $usuario_id) {
    $query = "DELETE FROM appestudo.lexjus_revisoes WHERE usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao limpar revisões: ' . pg_last_error($conexao)]);
        return;
    }

    $linhas_afetadas = pg_affected_rows($result);

    // Log para debug
    error_log("Limpando todas as revisões do usuário $usuario_id - Linhas afetadas: $linhas_afetadas");

    echo json_encode([
        'sucesso' => true,
        'mensagem' => 'Todas as revisões foram removidas',
        'linhas_afetadas' => $linhas_afetadas
    ]);
}
?>
