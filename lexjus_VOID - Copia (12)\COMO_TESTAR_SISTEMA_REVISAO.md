# 🧪 Como Testar o Sistema de Revisão - LexJus VOID

## 🚨 **IMPORTANTE: Resolver Erro de Autenticação**

O erro que você está vendo indica que o sistema de revisão não consegue acessar a API porque o usuário não está autenticado. Aqui está como resolver:

## 🔧 **Passo a Passo para Testar**

### **1. 🗄️ Instalar o Sistema de Revisão**

Primeiro, execute a instalação automática:

```
http://localhost/seu_caminho/lexjus_VOID/instalar_sistema_revisao.php
```

Este arquivo vai:
- ✅ Criar todas as tabelas necessárias
- ✅ Criar índices para performance
- ✅ Criar a função do algoritmo SM-2
- ✅ Configurar triggers automáticos
- ✅ Inserir configuração padrão

### **2. 👤 Fazer Login no Sistema Principal**

**ANTES** de testar a API, você precisa estar logado:

1. Acesse: `http://localhost/seu_caminho/lexjus_VOID/index.php`
2. Faça login com suas credenciais
3. Certifique-se de que está autenticado

### **3. 🧪 Testar a Conexão**

Depois de logado, teste a estrutura:

```
http://localhost/seu_caminho/lexjus_VOID/teste_conexao_revisao.php
```

Este arquivo vai verificar:
- ✅ Se as tabelas existem
- ✅ Se a função foi criada
- ✅ Se consegue inserir dados de teste

### **4. 🔧 Testar as APIs**

Com o usuário logado, teste as funcionalidades:

```
http://localhost/seu_caminho/lexjus_VOID/teste_sistema_revisao.html
```

## 🐛 **Solucionando Problemas Comuns**

### **Erro: "Usuário não autenticado"**

**Causa:** Você não está logado no sistema principal.

**Solução:**
1. Abra uma nova aba
2. Acesse `http://localhost/seu_caminho/lexjus_VOID/index.php`
3. Faça login
4. Volte para a aba de teste

### **Erro: "Unexpected token '<'"**

**Causa:** A API está retornando HTML (página de erro) em vez de JSON.

**Possíveis soluções:**
1. Verificar se o arquivo `conexao_POST.php` existe
2. Verificar se as credenciais do banco estão corretas
3. Verificar se o PostgreSQL está rodando

### **Erro: "Tabela não existe"**

**Causa:** O sistema de revisão não foi instalado.

**Solução:**
1. Execute: `lexjus_VOID/instalar_sistema_revisao.php`
2. Ou execute manualmente: `estrutura/adicionar_sistema_revisao.sql`

## 📋 **Checklist de Verificação**

Antes de testar, certifique-se de que:

- [ ] ✅ PostgreSQL está rodando
- [ ] ✅ Banco de dados LexJus existe
- [ ] ✅ Arquivo `conexao_POST.php` existe e funciona
- [ ] ✅ Você está logado no sistema principal
- [ ] ✅ Tabelas do sistema de revisão foram criadas
- [ ] ✅ Função `calcular_proxima_revisao` existe

## 🎯 **Fluxo de Teste Recomendado**

### **1. Instalação (uma vez apenas):**
```
1. lexjus_VOID/instalar_sistema_revisao.php
2. Verificar se tudo foi criado corretamente
```

### **2. Para cada sessão de teste:**
```
1. Fazer login no sistema principal (index.php)
2. Testar conexão (teste_conexao_revisao.php)
3. Testar funcionalidades (teste_sistema_revisao.html)
```

### **3. Uso normal:**
```
1. Fazer login no sistema principal
2. Clicar no botão "Revisão" na barra de navegação
3. Usar o sistema normalmente
```

## 🔧 **Comandos SQL Manuais (se necessário)**

Se a instalação automática não funcionar, execute manualmente:

```sql
-- 1. Conectar ao banco
psql -d seu_banco_lexjus

-- 2. Executar o arquivo SQL
\i estrutura/adicionar_sistema_revisao.sql

-- 3. Verificar se foi criado
\dt appestudo.lexjus_*
```

## 📊 **Testando Funcionalidades Específicas**

### **Adicionar Artigo à Revisão:**
```javascript
// No console do navegador (com usuário logado):
fetch('./api/revisao.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        acao: 'iniciar',
        artigo_numero: '1'
    })
}).then(r => r.json()).then(console.log);
```

### **Ver Estatísticas:**
```javascript
// No console do navegador (com usuário logado):
fetch('./api/revisao.php?acao=estatisticas')
    .then(r => r.json())
    .then(console.log);
```

### **Responder Revisão:**
```javascript
// No console do navegador (com usuário logado):
fetch('./api/revisao.php', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        acao: 'responder',
        artigo_numero: '1',
        qualidade: 4,
        tempo_resposta: 30
    })
}).then(r => r.json()).then(console.log);
```

## 🎓 **Usando no Sistema Principal**

Depois que tudo estiver funcionando:

1. **Acesse o LexJus VOID normalmente**
2. **Faça login**
3. **Clique no botão "Revisão"** na barra de navegação
4. **Use o dashboard** para ver estatísticas
5. **Clique em "Iniciar Revisão"** para começar uma sessão
6. **No modal de qualquer artigo**, use o botão "Adicionar à Revisão"

## 🆘 **Se Ainda Não Funcionar**

1. **Verifique os logs do servidor web** (Apache/Nginx)
2. **Verifique os logs do PostgreSQL**
3. **Use o console do navegador** para ver erros JavaScript
4. **Teste a conexão básica** com o banco de dados

## 📞 **Suporte**

Se continuar com problemas:

1. Execute `teste_conexao_revisao.php` e me envie o resultado
2. Verifique se consegue acessar outras APIs do sistema (favoritos, listas, etc.)
3. Confirme se está logado verificando se `$_SESSION['idusuario']` existe

---

**🎯 O sistema está pronto para funcionar! Só precisa seguir os passos de instalação e autenticação corretamente.**
