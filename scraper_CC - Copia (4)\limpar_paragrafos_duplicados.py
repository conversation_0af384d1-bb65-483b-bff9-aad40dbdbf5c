#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SCRIPT DE LIMPEZA DE PARÁGRAFOS DUPLICADOS - CÓDIGO CIVIL
=========================================================

Este script remove duplicações do número do parágrafo no campo 'texto' dos parágrafos.

PROBLEMA:
- Campo 'numero': "§ 1º"
- Campo 'texto': "§ 1º Aquele que tiver direito à posse provisória..."

SOLUÇÃO:
- Campo 'numero': "§ 1º" (mantém)
- Campo 'texto': "Aquele que tiver direito à posse provisória..." (remove duplicação)

USO:
    python limpar_paragrafos_duplicados.py

ARQUIVOS:
    Input:  codigo_civil_formato_lexjus_limpo.json
    Output: codigo_civil_formato_lexjus_final.json
"""

import json
import re
import os

def extrair_numero_paragrafo(numero_str):
    """Extrai o número do parágrafo (ex: '§ 1º' -> '1')"""
    # Remover símbolos e extrair apenas o número
    numero = re.sub(r'[§°º\s]', '', numero_str)
    return numero

def criar_padroes_remocao_paragrafo(numero_str):
    """Cria padrões regex para remover duplicações do texto do parágrafo"""
    padroes = []
    
    # Padrões básicos para parágrafos
    padroes.extend([
        rf'^{re.escape(numero_str)}\s+',           # § 1º 
        rf'^{re.escape(numero_str)}\.\s*',         # § 1º.
        rf'^{re.escape(numero_str)}\s*-\s*',       # § 1º -
    ])
    
    # Padrões alternativos
    numero_limpo = extrair_numero_paragrafo(numero_str)
    if numero_limpo:
        padroes.extend([
            rf'^§\s*{re.escape(numero_limpo)}[ºo°]?\s+',     # § 1º
            rf'^§\s*{re.escape(numero_limpo)}[ºo°]?\.\s*',   # § 1º.
            rf'^§\s*{re.escape(numero_limpo)}[ºo°]?\s*-\s*', # § 1º -
        ])
    
    return padroes

def limpar_texto_paragrafo(texto, numero_paragrafo):
    """Remove duplicação do número do parágrafo no texto"""
    texto_original = texto
    padroes = criar_padroes_remocao_paragrafo(numero_paragrafo)
    
    # Tentar cada padrão
    for padrao in padroes:
        match = re.match(padrao, texto, re.IGNORECASE)
        if match:
            texto = texto[match.end():].strip()
            break
    
    # Limpeza adicional: remover pontos órfãos no início
    texto = re.sub(r'^\.+\s*', '', texto)
    
    return texto if texto.strip() else texto_original

def processar_paragrafos_artigo(artigo):
    """Processa os parágrafos de um artigo"""
    modificacoes = 0
    
    # Processar parágrafos numerados
    if 'paragrafos_numerados' in artigo and artigo['paragrafos_numerados']:
        for paragrafo in artigo['paragrafos_numerados']:
            if 'numero' in paragrafo and 'texto' in paragrafo:
                texto_original = paragrafo['texto']
                numero = paragrafo['numero']
                
                texto_limpo = limpar_texto_paragrafo(texto_original, numero)
                
                if texto_limpo != texto_original:
                    paragrafo['texto'] = texto_limpo
                    modificacoes += 1
    
    # Processar parágrafo único (se for um objeto com estrutura similar)
    if 'paragrafo_unico' in artigo and isinstance(artigo['paragrafo_unico'], dict):
        if 'numero' in artigo['paragrafo_unico'] and 'texto' in artigo['paragrafo_unico']:
            texto_original = artigo['paragrafo_unico']['texto']
            numero = artigo['paragrafo_unico']['numero']
            
            texto_limpo = limpar_texto_paragrafo(texto_original, numero)
            
            if texto_limpo != texto_original:
                artigo['paragrafo_unico']['texto'] = texto_limpo
                modificacoes += 1
    
    return modificacoes

def verificar_duplicacoes_paragrafos(data):
    """Verifica se ainda há duplicações nos parágrafos após a limpeza"""
    problemas = []
    
    for artigo in data:
        # Verificar parágrafos numerados
        if 'paragrafos_numerados' in artigo and artigo['paragrafos_numerados']:
            for paragrafo in artigo['paragrafos_numerados']:
                if 'numero' in paragrafo and 'texto' in paragrafo:
                    numero = paragrafo['numero']
                    texto = paragrafo['texto']
                    
                    # Verificar se ainda há duplicação
                    numero_limpo = extrair_numero_paragrafo(numero)
                    padroes_verificar = [
                        rf'^{re.escape(numero)}\s',
                        rf'^§\s*{re.escape(numero_limpo)}[ºo°]?\s',
                    ]
                    
                    for padrao in padroes_verificar:
                        if re.match(padrao, texto, re.IGNORECASE):
                            problemas.append({
                                'artigo': artigo.get('artigo', 'N/A'),
                                'paragrafo': numero,
                                'texto': texto[:80] + '...'
                            })
                            break
    
    return problemas

def main():
    print("=== LIMPEZA DE PARÁGRAFOS DUPLICADOS - CÓDIGO CIVIL ===")
    
    # Verificar se arquivo existe
    arquivo_entrada = 'codigo_civil_formato_lexjus_limpo.json'
    if not os.path.exists(arquivo_entrada):
        print(f"ERRO: Arquivo {arquivo_entrada} não encontrado!")
        print("Execute primeiro os scripts de extração e limpeza do caput.")
        return
    
    try:
        # Carregar arquivo
        print(f"Carregando {arquivo_entrada}...")
        with open(arquivo_entrada, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Total de artigos carregados: {len(data)}")
        
        # Processar cada artigo
        total_modificacoes = 0
        exemplos_mostrados = 0
        
        for i, artigo in enumerate(data):
            if i % 500 == 0:
                print(f"Processando artigo {i+1}/{len(data)}...")
            
            modificacoes = processar_paragrafos_artigo(artigo)
            total_modificacoes += modificacoes
            
            # Mostrar alguns exemplos
            if modificacoes > 0 and exemplos_mostrados < 5:
                print(f"\nExemplo {exemplos_mostrados + 1}:")
                print(f"  {artigo.get('artigo', 'N/A')}")
                print(f"  Parágrafos limpos: {modificacoes}")
                exemplos_mostrados += 1
        
        print(f"\n=== PROCESSAMENTO CONCLUÍDO ===")
        print(f"Total de parágrafos modificados: {total_modificacoes}")
        print(f"Artigos não modificados: {len(data) - len([a for a in data if processar_paragrafos_artigo(a) > 0])}")
        
        # Verificação final
        print(f"\nVerificação final...")
        problemas = verificar_duplicacoes_paragrafos(data)
        
        if problemas:
            print(f"  ⚠️  {len(problemas)} parágrafos ainda com possíveis problemas:")
            for i, problema in enumerate(problemas[:3]):
                print(f"    {i+1}. {problema['artigo']} - {problema['paragrafo']}: {problema['texto']}")
            if len(problemas) > 3:
                print(f"    ... e mais {len(problemas) - 3} parágrafos")
        else:
            print("  ✅ Nenhuma duplicação restante nos parágrafos!")
        
        # Salvar arquivo limpo
        arquivo_saida = 'codigo_civil_formato_lexjus_final.json'
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"\nArquivo limpo salvo: {arquivo_saida}")
        
        # Mostrar exemplos finais
        print(f"\nExemplos do resultado final:")
        for i, artigo in enumerate(data[:5]):
            if 'paragrafos_numerados' in artigo and artigo['paragrafos_numerados']:
                print(f"\n{artigo.get('artigo', 'N/A')}:")
                for j, paragrafo in enumerate(artigo['paragrafos_numerados'][:2]):
                    print(f"  {paragrafo['numero']}: {paragrafo['texto'][:80]}...")
                break
        
        print(f"\n✅ Limpeza de parágrafos concluída com sucesso!")
        
    except Exception as e:
        print(f"ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
