#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para converter o arquivo codigo_civil_lexjus.json
para o formato do LexJus (artigos.json)

Autor: Sistema LexJus
Data: 2025-06-21
"""

import json
import os
import sys
from typing import Dict, List, Any

def converter_numero_romano(numero: str) -> str:
    """Converte números romanos minúsculos para maiúsculos"""
    conversoes = {
        'i': 'I', 'ii': 'II', 'iii': 'III', 'iv': 'IV', 'v': 'V',
        'vi': 'VI', 'vii': 'VII', 'viii': 'VIII', 'ix': 'IX', 'x': 'X',
        'xi': 'XI', 'xii': 'XII', 'xiii': 'XIII', 'xiv': 'XIV', 'xv': 'XV',
        'xvi': 'XVI', 'xvii': 'XVII', 'xviii': 'XVIII', 'xix': 'XIX', 'xx': 'XX'
    }
    return conversoes.get(numero.lower(), numero.upper())

def processar_incisos(incisos_obj: Dict) -> List[str]:
    """Converte objeto de incisos para array de strings"""
    incisos_lista = []

    for chave, inciso in incisos_obj.items():
        if isinstance(inciso, dict) and 'texto' in inciso:
            # Verificar se é inciso de parágrafo único (ex: pi, pii, piii)
            if chave.startswith('p') and len(chave) > 1:
                # Inciso de parágrafo único: pi -> I, pii -> II, etc.
                numero_romano_parte = chave[1:]  # remove o 'p'
                numero_romano = converter_numero_romano(numero_romano_parte)
                texto = inciso['texto'].strip()
                incisos_lista.append(f"{numero_romano} - {texto} (do parágrafo único)")
            else:
                # Inciso normal
                numero_romano = converter_numero_romano(chave)
                texto = inciso['texto'].strip()
                incisos_lista.append(f"{numero_romano} - {texto}")

    return incisos_lista

def processar_paragrafos(paragrafos_obj: Dict) -> tuple:
    """
    Processa parágrafos e retorna tupla (paragrafos_numerados, paragrafo_unico)
    """
    paragrafos_numerados = []
    paragrafo_unico = None

    for chave, paragrafo in paragrafos_obj.items():
        if isinstance(paragrafo, dict) and 'texto' in paragrafo:
            texto = paragrafo['texto'].strip()

            if chave.lower() == 'unico':
                # Remove "Parágrafo único." do início se existir
                if texto.startswith('Parágrafo único.'):
                    paragrafo_unico = texto[17:].strip()
                else:
                    paragrafo_unico = texto
            else:
                # Parágrafo numerado
                numero_formatado = f"§ {chave}º"
                paragrafos_numerados.append({
                    "numero": numero_formatado,
                    "texto": texto,
                    "alineas": []
                })

    return paragrafos_numerados, paragrafo_unico

def processar_alineas(incisos_obj: Dict) -> List[str]:
    """Processa alíneas dos incisos e retorna lista formatada"""
    alineas_lista = []

    for chave_inciso, inciso in incisos_obj.items():
        if isinstance(inciso, dict) and 'alineas' in inciso and inciso['alineas']:
            numero_romano = converter_numero_romano(chave_inciso)
            texto_inciso = inciso.get('texto', '').strip()

            for chave_alinea, alinea in inciso['alineas'].items():
                if isinstance(alinea, dict) and 'texto' in alinea:
                    texto_alinea = alinea['texto'].strip()
                    alineas_lista.append(
                        f"{chave_alinea}) {texto_alinea} (pertencente a: inciso - {numero_romano} - {texto_inciso[:30]}...)"
                    )

    return alineas_lista

def converter_artigo(artigo_cc: Dict) -> Dict:
    """Converte um artigo do formato CC para o formato LexJus"""
    numero = artigo_cc.get('numero', 0)
    caput = artigo_cc.get('caput', '').strip()

    # Remove ponto inicial do caput se existir
    if caput.startswith('.'):
        caput = caput[1:].strip()

    # Processa incisos
    incisos_obj = artigo_cc.get('incisos', {})
    incisos = processar_incisos(incisos_obj)

    # Processa parágrafos
    paragrafos_obj = artigo_cc.get('paragrafos', {})
    paragrafos_numerados, paragrafo_unico = processar_paragrafos(paragrafos_obj)

    # Processa alíneas
    alineas_do_artigo = processar_alineas(incisos_obj)

    # Monta o artigo no formato LexJus
    artigo_lexjus = {
        "artigo": f"Art. {numero}º",
        "caput": caput,
        "incisos": incisos,
        "paragrafos_numerados": paragrafos_numerados,
        "paragrafo_unico": paragrafo_unico
    }

    # Adiciona alíneas apenas se existirem
    if alineas_do_artigo:
        artigo_lexjus["alineas_do_artigo"] = alineas_do_artigo

    return artigo_lexjus

def converter_cc_para_lexjus(arquivo_entrada: str, arquivo_saida: str):
    """Função principal de conversão"""
    try:
        print(f"Carregando arquivo: {arquivo_entrada}")

        # Carrega o arquivo de entrada
        with open(arquivo_entrada, 'r', encoding='utf-8') as f:
            dados_cc = json.load(f)

        print(f"Total de artigos encontrados: {len(dados_cc.get('artigos', []))}")

        # Converte cada artigo
        artigos_lexjus = []
        artigos_processados = 0

        for artigo_cc in dados_cc.get('artigos', []):
            try:
                artigo_convertido = converter_artigo(artigo_cc)
                artigos_lexjus.append(artigo_convertido)
                artigos_processados += 1

                if artigos_processados % 100 == 0:
                    print(f"Processados {artigos_processados} artigos...")

            except Exception as e:
                print(f"Erro ao processar artigo {artigo_cc.get('numero', 'desconhecido')}: {e}")
                continue

        print(f"Conversão concluída. {artigos_processados} artigos convertidos.")

        # Salva o arquivo de saída
        print(f"Salvando arquivo: {arquivo_saida}")
        with open(arquivo_saida, 'w', encoding='utf-8') as f:
            json.dump(artigos_lexjus, f, ensure_ascii=False, indent=2)

        print("Conversão finalizada com sucesso!")
        print(f"Arquivo salvo em: {arquivo_saida}")

    except FileNotFoundError:
        print(f"Erro: Arquivo {arquivo_entrada} não encontrado!")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Erro ao decodificar JSON: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Erro inesperado: {e}")
        sys.exit(1)

def main():
    """Função principal"""
    # Define os caminhos dos arquivos
    arquivo_entrada = "codigo_civil_lexjus_corrigido.json"
    arquivo_saida = "codigo_civil_formato_lexjus.json"

    # Verifica se o arquivo de entrada existe
    if not os.path.exists(arquivo_entrada):
        print(f"Erro: Arquivo {arquivo_entrada} não encontrado!")
        print("Certifique-se de que o arquivo está no mesmo diretório do script.")
        sys.exit(1)

    print("=== Conversor CC para LexJus ===")
    print(f"Entrada: {arquivo_entrada}")
    print(f"Saída: {arquivo_saida}")
    print()

    # Executa a conversão
    converter_cc_para_lexjus(arquivo_entrada, arquivo_saida)

if __name__ == "__main__":
    main()
