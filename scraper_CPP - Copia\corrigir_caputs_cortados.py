#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script para corrigir caputs cortados no arquivo cpp_final_perfeito.json
Busca o texto completo dos caputs diretamente do site do Planalto
"""

import requests
from bs4 import BeautifulSoup
import json
import re
from datetime import datetime

# URL do Código de Processo Penal no site do Planalto
URL_CPP = 'https://www.planalto.gov.br/ccivil_03/decreto-lei/del3689.htm'

def buscar_conteudo_pagina(url):
    """
    Busca o conteúdo HTML de uma página web
    """
    try:
        print(f"Buscando conteúdo da URL: {url}")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Tentar detectar encoding automaticamente
        content = response.text
        
        if content:
            print(f"✅ Página carregada com sucesso! Tamanho: {len(content)} caracteres")
            return content
        else:
            print(f"❌ Falha ao decodificar o conteúdo da página")
            return None

    except requests.RequestException as e:
        print(f"❌ Erro ao buscar a página: {e}")
        return None

def limpar_texto(texto):
    """
    Limpa e normaliza o texto extraído
    """
    if not texto:
        return ""
    
    # Remove quebras de linha e espaços extras
    texto = re.sub(r'\s+', ' ', texto)
    texto = texto.strip()
    
    # Remove apenas caracteres de controle, preservando acentos portugueses
    texto = re.sub(r'[\u00a0\u2000-\u200f\u2028-\u202f\ufeff]', ' ', texto)
    
    # Remove espaços múltiplos novamente após limpeza
    texto = re.sub(r'\s+', ' ', texto)
    
    return texto

def extrair_caput_completo_do_site(html_content, numero_artigo, sufixo_artigo=""):
    """
    Extrai o caput completo de um artigo específico do HTML do site
    """
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Criar padrão de busca para o artigo
    if sufixo_artigo:
        padrao_artigo = f"Art\\.?\\s*{numero_artigo}[ºo°]?\\s*-\\s*{sufixo_artigo}"
    else:
        padrao_artigo = f"Art\\.?\\s*{numero_artigo}[ºo°]?"
    
    # Buscar todos os elementos <p>
    elementos_p = soup.find_all('p')
    
    for i, p_tag in enumerate(elementos_p):
        texto_paragrafo = p_tag.get_text(separator=' ', strip=False)
        texto_limpo = limpar_texto(texto_paragrafo)
        
        # Verificar se este parágrafo contém o artigo procurado
        if re.search(padrao_artigo, texto_limpo, re.IGNORECASE):
            # Extrair o caput completo
            match = re.search(f'{padrao_artigo}\\.?\\s*(.+)', texto_limpo, re.IGNORECASE)
            if match:
                caput_inicial = match.group(1).strip()
                
                # Verificar se o caput continua nos próximos parágrafos
                caput_completo = caput_inicial
                
                # Olhar os próximos parágrafos para ver se há continuação
                for j in range(i + 1, min(i + 5, len(elementos_p))):  # Verificar até 5 parágrafos à frente
                    proximo_p = elementos_p[j]
                    texto_proximo = limpar_texto(proximo_p.get_text(separator=' ', strip=False))
                    
                    # Parar se encontrar outro artigo
                    if re.search(r'Art\\.?\\s*\\d+[ºo°]?', texto_proximo, re.IGNORECASE):
                        break
                    
                    # Parar se encontrar inciso, parágrafo ou título
                    if re.search(r'^([IVX]+\\s*[-–]|§\\s*\\d+|Parágrafo\\s+único|TÍTULO|CAPÍTULO|SEÇÃO)', texto_proximo, re.IGNORECASE):
                        break
                    
                    # Pular elementos muito pequenos ou vazios
                    if len(texto_proximo.strip()) < 3:
                        continue
                    
                    # Se chegou até aqui, é provável que seja continuação do caput
                    caput_completo += " " + texto_proximo
                
                # Limpar o caput final
                caput_completo = limpar_texto(caput_completo)
                
                # Remover caracteres estranhos no início
                caput_completo = re.sub(r'^[ºo°\\.\\s]*', '', caput_completo)
                
                return caput_completo
    
    return None

def identificar_artigos_com_caputs_cortados(artigos):
    """
    Identifica artigos que provavelmente têm caputs cortados
    """
    artigos_cortados = []
    
    for artigo in artigos:
        caput = artigo.get('caput', '')
        
        # Critérios para identificar caputs cortados:
        # 1. Caput muito curto (menos de 10 caracteres)
        # 2. Caput termina com preposição ou artigo
        # 3. Caput termina abruptamente sem pontuação
        
        if (len(caput) < 10 or 
            re.search(r'\\b(de|da|do|das|dos|em|na|no|nas|nos|com|para|por|sem|o|a|os|as)$', caput, re.IGNORECASE) or
            re.search(r'[a-z]$', caput) and not re.search(r'[.;:]$', caput)):
            
            artigos_cortados.append(artigo)
    
    return artigos_cortados

def corrigir_caputs_cortados():
    """
    Função principal para corrigir caputs cortados
    """
    print("=" * 60)
    print("🔧 CORREÇÃO DE CAPUTS CORTADOS - CPP")
    print("=" * 60)
    
    # Carregar arquivo JSON atual
    try:
        with open('cpp_final_perfeito.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Arquivo carregado: {len(artigos)} artigos")
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo: {e}")
        return
    
    # Buscar conteúdo do site
    html_content = buscar_conteudo_pagina(URL_CPP)
    if not html_content:
        print("❌ Não foi possível obter o conteúdo do site")
        return
    
    # Identificar artigos com caputs cortados
    artigos_cortados = identificar_artigos_com_caputs_cortados(artigos)
    print(f"🔍 Identificados {len(artigos_cortados)} artigos com caputs possivelmente cortados")
    
    # Corrigir cada artigo
    artigos_corrigidos = 0
    
    for artigo in artigos_cortados:
        artigo_id = artigo['artigo']
        caput_atual = artigo['caput']
        
        # Extrair número e sufixo do artigo
        match = re.search(r'Art\\.\\s*(\\d+)[ºo°]?(?:-([A-Z]))?', artigo_id)
        if not match:
            continue
        
        numero = int(match.group(1))
        sufixo = match.group(2) if match.group(2) else ""
        
        print(f"🔧 Corrigindo {artigo_id}: '{caput_atual[:50]}...'")
        
        # Buscar caput completo no site
        caput_completo = extrair_caput_completo_do_site(html_content, numero, sufixo)
        
        if caput_completo and len(caput_completo) > len(caput_atual):
            artigo['caput'] = caput_completo
            artigos_corrigidos += 1
            print(f"   ✅ Corrigido para: '{caput_completo[:50]}...'")
        else:
            print(f"   ⚠️  Não foi possível melhorar o caput")
    
    print(f"\n📊 RESULTADOS:")
    print(f"   • Artigos corrigidos: {artigos_corrigidos}")
    print(f"   • Total de artigos: {len(artigos)}")
    
    # Salvar arquivo corrigido
    if artigos_corrigidos > 0:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        arquivo_saida = f'cpp_final_corrigido_{timestamp}.json'
        
        try:
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                json.dump(artigos, f, ensure_ascii=False, indent=2)
            print(f"💾 Arquivo corrigido salvo: {arquivo_saida}")
        except Exception as e:
            print(f"❌ Erro ao salvar arquivo: {e}")
    else:
        print("ℹ️  Nenhuma correção foi necessária")

if __name__ == '__main__':
    corrigir_caputs_cortados()
