<?php
/**
 * Teste Simples de Cache - LexJus
 */

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Cache - LexJus</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            font-size: 18px;
            margin: 20px 0;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste de Cache - LexJus</h1>
        
        <div class="success">
            ✅ Página carregada com sucesso!
        </div>
        
        <div class="info">
            <strong>Informações do Sistema:</strong><br>
            Data/Hora: <?php echo date('d/m/Y H:i:s'); ?><br>
            PHP Version: <?php echo PHP_VERSION; ?><br>
            Servidor: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Desconhecido'; ?><br>
        </div>

        <?php
        // Verificar se o sistema de cache existe
        if (file_exists('includes/cache_buster.php')) {
            require_once 'includes/cache_buster.php';
            echo '<div class="info">';
            echo '<strong>✅ Sistema de Cache Busting:</strong><br>';
            echo 'Versão Global: ' . cache_version() . '<br>';
            echo 'Data: ' . date('d/m/Y H:i:s', cache_version()) . '<br>';
            echo '</div>';
        } else {
            echo '<div class="info" style="background: #fff3cd;">';
            echo '<strong>⚠️ Sistema de Cache Busting:</strong><br>';
            echo 'Arquivo includes/cache_buster.php não encontrado<br>';
            echo '</div>';
        }
        ?>

        <h2>🔧 Ações de Teste</h2>
        
        <?php
        if ($_POST) {
            echo '<div class="info" style="background: #d4edda;">';
            
            if (isset($_POST['test_action'])) {
                echo '✅ Ação executada com sucesso!<br>';
                echo 'Timestamp: ' . time() . '<br>';
            }
            
            echo '</div>';
        }
        ?>
        
        <form method="post">
            <button type="submit" name="test_action" class="button">
                🧪 Executar Teste
            </button>
        </form>
        
        <button onclick="location.reload()" class="button">
            🔄 Recarregar Página
        </button>
        
        <h2>📋 Links de Teste</h2>
        <p>
            <a href="index.php" target="_blank">🏠 Sistema Principal</a><br>
            <a href="api/version.php" target="_blank">📡 API de Versão</a><br>
            <a href="clear-cache.php" target="_blank">🧹 Página de Cache Completa</a><br>
        </p>
        
        <div class="info">
            <strong>🔍 Debug:</strong><br>
            URL Atual: <?php echo $_SERVER['REQUEST_URI']; ?><br>
            Método: <?php echo $_SERVER['REQUEST_METHOD']; ?><br>
            User Agent: <?php echo substr($_SERVER['HTTP_USER_AGENT'], 0, 50) . '...'; ?><br>
        </div>
    </div>
</body>
</html>
