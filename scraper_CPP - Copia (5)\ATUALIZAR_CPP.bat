@echo off
chcp 65001 > nul
echo ============================================================
echo 🚀 ATUALIZACAO DO CPP - LEXJUS
echo ============================================================
echo.
echo Escolha o metodo de atualizacao:
echo.
echo [1] 🌟 UNIFICADO (RECOMENDADO)
echo     - Busca dados atualizados do site oficial
echo     - Aplica todas as correcoes automaticamente
echo     - Processo completo em uma execucao
echo     - Sempre atualizado
echo.
echo [2] ⚡ RAPIDO
echo     - Usa arquivo base ja corrigido
echo     - Apenas gera arquivos finais
echo     - Mais rapido (poucos segundos)
echo     - Requer base atualizada
echo.
echo [3] ❌ CANCELAR
echo.
set /p opcao="Digite sua opcao (1, 2 ou 3): "

if "%opcao%"=="1" goto unificado
if "%opcao%"=="2" goto rapido
if "%opcao%"=="3" goto cancelar
echo.
echo ❌ Opcao invalida! Tente novamente.
echo.
pause
goto inicio

:unificado
echo.
echo ============================================================
echo 🌟 EXECUTANDO ATUALIZACAO UNIFICADA
echo ============================================================
echo.
echo 📋 Este processo ira:
echo    1. Extrair artigos do site oficial
echo    2. Aplicar correcoes de caputs
echo    3. Gerar arquivos finais
echo.
echo ⏱️  Tempo estimado: 10-15 segundos
echo.
python atualizar_cpp_unificado.py
goto fim

:rapido
echo.
echo ============================================================
echo ⚡ EXECUTANDO ATUALIZACAO RAPIDA
echo ============================================================
echo.
echo � Gerando arquivos usando base ja corrigida...
echo.
python atualizar_cpp_rapido.py
goto fim

:cancelar
echo.
echo ❌ Operacao cancelada pelo usuario.
echo.
pause
exit

:fim
echo.
echo ============================================================
echo ✅ ATUALIZACAO CONCLUIDA COM SUCESSO!
echo ============================================================
echo.
echo 📁 Arquivos gerados:
dir cpp_lexjus_*.js /b 2>nul | findstr /r "cpp_lexjus_.*\.js$" | sort /r | head -n 1
echo.
echo 💡 PROXIMOS PASSOS:
echo    1. Copie o arquivo .js mais recente para o sistema LexJus
echo    2. Atualize as referencias no codigo
echo    3. Teste o funcionamento
echo.
echo 📖 Para mais informacoes, consulte o README.md
echo.
pause
