<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Guia do Sistema de Revisão Inteligente</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header h1 {
            font-size: 3em;
            margin: 0;
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header p {
            font-size: 1.2em;
            color: #666;
            margin: 10px 0 0 0;
        }
        .section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #333;
            border-bottom: 3px solid #9b59b6;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .section h3 {
            color: #555;
            margin-top: 25px;
            font-size: 1.3em;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #9b59b6;
        }
        .card h4 {
            margin-top: 0;
            color: #333;
        }
        .status-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
        .status-novo { background: #fff3cd; border-left: 4px solid #ffc107; }
        .status-aprendendo { background: #cce5ff; border-left: 4px solid #007bff; }
        .status-revisando { background: #f8d7da; border-left: 4px solid #dc3545; }
        .status-dominado { background: #d4edda; border-left: 4px solid #28a745; }
        .status-dificil { background: #e2d9f3; border-left: 4px solid #6f42c1; }
        .formula {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .example {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .example h4 {
            margin-top: 0;
            color: #0066cc;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #9b59b6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #9b59b6;
        }
        .btn {
            display: inline-block;
            background: #9b59b6;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            margin: 10px 10px 10px 0;
        }
        .btn:hover {
            background: #8e44ad;
            transform: translateY(-2px);
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .highlight {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
            margin: 20px 0;
        }
        .warning {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #e17055;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 15px;
            border-radius: 25px;
            text-decoration: none;
            color: #333;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            z-index: 1000;
        }
        .back-link:hover {
            background: white;
            transform: translateY(-2px);
        }
        @media (max-width: 768px) {
            .container { padding: 10px; }
            .header { padding: 20px; }
            .header h1 { font-size: 2em; }
            .section { padding: 20px; }
            .grid { grid-template-columns: 1fr; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <a href="index.php" class="back-link">
        <i class="fas fa-arrow-left"></i> Voltar ao LexJus
    </a>

    <div class="container">
        <div class="header">
            <h1>🧠 Sistema de Revisão Inteligente</h1>
            <p>Guia Completo para Dominar a Constituição Federal</p>
        </div>

        <div class="section">
            <h2>🎯 O que é o Sistema de Revisão?</h2>
            <p>O Sistema de Revisão Inteligente usa o <strong>Algoritmo SM-2</strong> (SuperMemo 2) para otimizar sua memorização da Constituição Federal. Baseado em <strong>repetição espaçada</strong>, ele calcula automaticamente quando você deve revisar cada artigo para maximizar a retenção com o mínimo de esforço.</p>
            
            <div class="highlight">
                <h4>🚀 Benefícios Principais:</h4>
                <ul>
                    <li><strong>Memorização eficiente:</strong> Foca no que você tem dificuldade</li>
                    <li><strong>Economia de tempo:</strong> Evita revisões desnecessárias</li>
                    <li><strong>Retenção duradoura:</strong> Baseado em ciência comprovada</li>
                    <li><strong>Progresso visível:</strong> Acompanhe sua evolução em tempo real</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎮 Como Usar</h2>
            
            <div class="grid">
                <div class="card">
                    <h4>📝 1. Adicionar Artigos</h4>
                    <p>Abra qualquer artigo e clique no botão roxo <strong>"Adicionar à Revisão"</strong>. O artigo será incluído automaticamente no sistema.</p>
                </div>
                
                <div class="card">
                    <h4>📖 2. Fazer Revisões</h4>
                    <p>Acesse o Dashboard de Revisão, leia o artigo e avalie seu conhecimento de 0 a 5. O sistema calcula automaticamente a próxima revisão.</p>
                </div>
                
                <div class="card">
                    <h4>📊 3. Acompanhar Progresso</h4>
                    <p>Monitore suas estatísticas, veja quantos artigos estão pendentes e acompanhe sua evolução ao longo do tempo.</p>
                </div>
            </div>

            <div class="example">
                <h4>🎯 Escala de Avaliação:</h4>
                <ul>
                    <li><strong>0 - Não lembro:</strong> Não conseguiu lembrar nada</li>
                    <li><strong>1 - Muito difícil:</strong> Lembrou muito pouco</li>
                    <li><strong>2 - Difícil:</strong> Lembrou parcialmente com dificuldade</li>
                    <li><strong>3 - Normal:</strong> Lembrou bem, mas com algum esforço</li>
                    <li><strong>4 - Fácil:</strong> Lembrou facilmente</li>
                    <li><strong>5 - Muito fácil:</strong> Lembrou perfeitamente</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎭 Estados dos Artigos</h2>
            
            <div class="status-demo status-novo">
                <strong>🟡 Novo:</strong> Primeira vez no sistema - Revisão imediata
            </div>
            
            <div class="status-demo status-aprendendo">
                <strong>🔵 Aprendendo:</strong> 1-2 repetições - Intervalo de 1-6 dias
            </div>
            
            <div class="status-demo status-revisando">
                <strong>🔴 Revisando:</strong> 3+ repetições - Intervalo de semanas
            </div>
            
            <div class="status-demo status-dominado">
                <strong>🟢 Dominado:</strong> 5+ repetições - Intervalo de meses
            </div>
            
            <div class="status-demo status-dificil">
                <strong>🟣 Difícil:</strong> Respostas ruins - Volta para 1 dia
            </div>
        </div>

        <div class="section">
            <h2>🧮 Como o Algoritmo Funciona</h2>
            
            <h3>📊 Cálculo da Facilidade</h3>
            <div class="formula">
Nova Facilidade = Facilidade Atual + (0.1 - (5 - Qualidade) × (0.08 + (5 - Qualidade) × 0.02))
            </div>
            
            <h3>⏰ Cálculo do Intervalo</h3>
            <div class="grid">
                <div class="card">
                    <h4>Respostas Ruins (0-2)</h4>
                    <ul>
                        <li>Repetições = 0</li>
                        <li>Intervalo = 1 dia</li>
                        <li>Status = Difícil</li>
                    </ul>
                </div>
                
                <div class="card">
                    <h4>Respostas Boas (3-5)</h4>
                    <ul>
                        <li>1ª repetição: 1 dia</li>
                        <li>2ª repetição: 6 dias</li>
                        <li>3ª+ repetição: Intervalo × Facilidade</li>
                    </ul>
                </div>
            </div>

            <div class="example">
                <h4>📈 Exemplo: Evolução do Artigo 5º</h4>
                <div class="timeline">
                    <div class="timeline-item">
                        <strong>🆕 Primeira vez:</strong> Qualidade 4 → Facilidade 2.6 → Próxima em 1 dia
                    </div>
                    <div class="timeline-item">
                        <strong>📚 Segunda revisão:</strong> Qualidade 5 → Facilidade 2.7 → Próxima em 6 dias
                    </div>
                    <div class="timeline-item">
                        <strong>🔄 Terceira revisão:</strong> Qualidade 4 → Facilidade 2.8 → Próxima em 17 dias
                    </div>
                    <div class="timeline-item">
                        <strong>🎯 Quinta revisão:</strong> Qualidade 5 → Facilidade 2.9 → Próxima em 45 dias (Dominado!)
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interpretando as Estatísticas</h2>
            
            <h3>🎯 Distribuição Ideal</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">< 20%</div>
                    <div class="stat-label">Novos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">30-40%</div>
                    <div class="stat-label">Aprendendo</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">30-40%</div>
                    <div class="stat-label">Revisando</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">10-20%</div>
                    <div class="stat-label">Dominados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">< 10%</div>
                    <div class="stat-label">Difíceis</div>
                </div>
            </div>

            <div class="warning">
                <h4>🚨 Sinais de Alerta:</h4>
                <ul>
                    <li><strong>Muitos difíceis (>15%):</strong> Seja menos rigoroso nas avaliações</li>
                    <li><strong>Poucos dominados (<5% após 1 mês):</strong> Foque na consistência</li>
                    <li><strong>Facilidade média baixa (<2.0):</strong> Revise o material antes das avaliações</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>💡 Dicas de Sucesso</h2>
            
            <div class="grid">
                <div class="card">
                    <h4>🎯 Consistência</h4>
                    <p>Melhor 10 minutos diários que 2 horas no fim de semana. Configure um horário fixo para revisões.</p>
                </div>
                
                <div class="card">
                    <h4>🧠 Honestidade</h4>
                    <p>Seja honesto nas avaliações. Qualidade 3 (Normal) é perfeitamente aceitável.</p>
                </div>
                
                <div class="card">
                    <h4>📈 Monitoramento</h4>
                    <p>Verifique as estatísticas semanalmente e ajuste sua estratégia baseado nos dados.</p>
                </div>
                
                <div class="card">
                    <h4>🎮 Gamificação</h4>
                    <p>Defina metas diárias, comemore marcos e compete consigo mesmo para melhorar.</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Começando Agora</h2>
            
            <div class="highlight">
                <h4>📋 Roteiro para Iniciantes:</h4>
                <ol>
                    <li><strong>Semana 1:</strong> Adicione 10-15 artigos fundamentais</li>
                    <li><strong>Semana 2:</strong> Mantenha consistência, adicione mais 10-15</li>
                    <li><strong>Semana 3:</strong> Expanda para 40-50 artigos total</li>
                    <li><strong>Semana 4:</strong> Otimize baseado nas estatísticas</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="index.php" class="btn">
                    <i class="fas fa-rocket"></i> Começar Agora
                </a>
                <a href="demo_sistema_revisao.html" class="btn secondary">
                    <i class="fas fa-play"></i> Ver Demo
                </a>
            </div>
        </div>

        <div class="section">
            <h2>🔧 Ferramentas Disponíveis</h2>
            
            <div class="grid">
                <div class="card">
                    <h4>🧪 Testes e Debug</h4>
                    <p>Use as ferramentas de teste para verificar se tudo está funcionando corretamente.</p>
                    <a href="teste_revisao_simples.html" class="btn">Teste Simples</a>
                </div>
                
                <div class="card">
                    <h4>🧹 Limpeza</h4>
                    <p>Remova artigos de teste ou reset completo do sistema quando necessário.</p>
                    <a href="limpar_revisao_simples.php" class="btn">Limpar Sistema</a>
                </div>
                
                <div class="card">
                    <h4>🎨 Demo Visual</h4>
                    <p>Veja todos os estados visuais e animações do sistema em ação.</p>
                    <a href="demo_visual_botao.html" class="btn">Demo Visual</a>
                </div>
                
                <div class="card">
                    <h4>📖 Documentação</h4>
                    <p>Acesse a documentação técnica completa do sistema.</p>
                    <a href="COMO_FUNCIONA_SISTEMA_REVISAO.md" class="btn">Documentação</a>
                </div>
            </div>
        </div>

        <div class="section" style="text-align: center; background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
            <h2 style="border-bottom: 3px solid white; color: white;">🎓 Conclusão</h2>
            <p style="font-size: 1.2em; margin-bottom: 30px;">
                Com dedicação e uso correto, você dominará toda a Constituição Federal de forma eficiente e duradoura!
            </p>
            <a href="index.php" class="btn" style="background: white; color: #667eea; font-size: 1.1em; padding: 15px 30px;">
                <i class="fas fa-brain"></i> Começar Minha Jornada
            </a>
        </div>
    </div>

    <script>
        // Adicionar animações suaves ao scroll
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });

            console.log('📚 Guia do Sistema de Revisão carregado!');
        });
    </script>
</body>
</html>
