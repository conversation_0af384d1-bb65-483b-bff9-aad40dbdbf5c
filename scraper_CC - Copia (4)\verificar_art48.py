#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

# Carregar o JSON
with open('codigo_civil_lexjus_corrigido.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("=== ARTIGOS 48 ENCONTRADOS ===")
for artigo in data['artigos']:
    numero = str(artigo['numero'])
    if '48' in numero:
        print(f"Art. {numero}:")
        print(f"  Caput: {artigo['caput'][:150]}...")
        print(f"  Sufixo letra: '{artigo['sufixo_letra']}'")
        print(f"  Versão numérica: '{artigo['versao_numerica']}'")
        print(f"  Número ordenação: {artigo['numero_ordenacao']}")
        print(f"  Versão atual: {artigo['versao_atual']}")
        print()
