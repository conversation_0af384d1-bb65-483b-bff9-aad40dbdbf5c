# Scraper do Código Penal - Planalto

Este diretório contém todos os arquivos necessários para extrair o Código Penal Brasileiro do site do Planalto e gerar arquivos JavaScript e JSON limpos.

## 📁 Arquivos Incluídos

### Scripts Principais:
- **`scraper_planalto.py`** - Script principal que extrai os artigos do Código Penal
- **`limpar_referencias_finais.py`** - Script para limpeza final das referências legislativas

### Arquivos de Exemplo (Gerados):
- **`codigo_penal_limpo.js`** - Arquivo JavaScript pronto para uso
- **`codigo_penal_limpo.json`** - Arquivo JSON para backup

## 🚀 Como Usar

### 1. Executar o Scraper Principal
```bash
python scraper_planalto.py
```

**O que faz:**
- Busca o Código Penal da URL: `https://www.planalto.gov.br/ccivil_03/decreto-lei/del2848.htm`
- Compara estrutura local vs online
- Extrai todos os artigos (483 artigos completos)
- Gera arquivos `codigo_penal.js` e `codigo_penal.json`

### 2. Limpeza Final (Opcional)
```bash
python limpar_referencias_finais.py
```

**O que faz:**
- Remove referências legislativas como "(Redação dada pela Lei nº X)"
- Remove "(Vide ADPF X)", "(VETADO)", etc.
- Gera arquivos limpos: `codigo_penal_limpo.js` e `codigo_penal_limpo.json`

## 📊 Resultados Esperados

### Estatísticas:
- **483 artigos** extraídos (Art. 1º ao Art. 361)
- **Código Penal completo** (Parte Geral + Parte Especial)
- **Texto limpo** sem referências legislativas

### Estrutura dos Artigos:
```json
{
  "artigo": "Art. 121",
  "caput": "Matar alguém:",
  "incisos": [],
  "paragrafos_numerados": [
    {
      "numero": "§ 1º",
      "texto": "Se o agente comete o crime impelido por motivo de relevante valor social ou moral...",
      "incisos": [],
      "alineas": []
    }
  ],
  "paragrafo_unico": null
}
```

## 🔧 Dependências

### Python Packages:
```bash
pip install requests beautifulsoup4
```

### Imports Necessários:
- `requests` - Para buscar páginas web
- `beautifulsoup4` - Para parsing HTML
- `json` - Para manipulação JSON
- `re` - Para expressões regulares
- `datetime` - Para timestamps

## 📝 Funcionalidades do JavaScript Gerado

### Buscar Artigo por Número:
```javascript
const art121 = buscarArtigo(121); // Retorna artigo sobre homicídio
```

### Buscar Artigos por Texto:
```javascript
const artigos = buscarArtigosPorTexto("homicídio"); // Busca em todo o conteúdo
```

### Usar no HTML:
```html
<script src="codigo_penal_limpo.js"></script>
<script>
  // Agora você pode usar as funções
  console.log(buscarArtigo(1));
</script>
```

## 🎯 URL Fonte

**URL Principal:** `https://www.planalto.gov.br/ccivil_03/decreto-lei/del2848.htm`

O scraper foi configurado para extrair da versão compilada oficial do Código Penal Brasileiro, garantindo que todos os artigos e atualizações estejam incluídos.

## ⚠️ Observações

1. **Conexão Internet:** O scraper precisa de conexão para buscar a página online
2. **Estrutura HTML:** Se o Planalto alterar a estrutura da página, pode ser necessário ajustar as regex
3. **Encoding:** Todos os arquivos são salvos em UTF-8 para suportar caracteres especiais
4. **Limpeza:** As referências legislativas são removidas para melhor legibilidade no sistema de estudos

## 📞 Suporte

Este scraper foi desenvolvido especificamente para extrair o Código Penal do Planalto e gerar arquivos compatíveis com o sistema LexJus.

**Última atualização:** 19/06/2025
**Versão:** 1.0
**Artigos extraídos:** 483
