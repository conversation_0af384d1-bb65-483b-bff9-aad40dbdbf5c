<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug <PERSON> <PERSON><PERSON><PERSON>ão</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .step {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .modal-actions {
            display: flex;
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 10px 0;
        }
        .btn-adicionar-revisao {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: none;
            letter-spacing: 0.5px;
        }
        .btn-adicionar-revisao:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        }
        .btn-adicionar-revisao.pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3); }
            50% { box-shadow: 0 4px 25px rgba(155, 89, 182, 0.6); }
            100% { box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3); }
        }
        .btn-adicionar-revisao.loading {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        .btn-adicionar-revisao.loading i {
            animation: spin 1s linear infinite;
        }
        .btn-adicionar-revisao.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        .btn-adicionar-revisao.error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <h1>🔍 Debug - Botão de Revisão</h1>

    <div class="container">
        <h2>1. 🔍 Verificar Estrutura do DOM</h2>
        <div class="step">
            <p>Vamos verificar se os elementos necessários existem no DOM:</p>
            <button onclick="verificarEstrutura()">Verificar Estrutura</button>
            <div id="estrutura-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>2. 🧪 Simular Modal de Artigo</h2>
        <div class="step">
            <p>Vamos criar uma simulação do modal de artigo para testar:</p>
            <button onclick="criarModalSimulado()">Criar Modal Simulado</button>
            <button onclick="abrirModalSimulado()">Abrir Modal Simulado</button>
            <div id="modal-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>3. 🔧 Testar Sistema de Revisão</h2>
        <div class="step">
            <p>Vamos carregar e testar o sistema de revisão:</p>
            <button onclick="carregarSistemaRevisao()">Carregar Sistema de Revisão</button>
            <button onclick="forcarAdicaoBotao()">Forçar Adição do Botão</button>
            <div id="sistema-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>4. 📊 Status em Tempo Real</h2>
        <div class="step">
            <p>Monitoramento contínuo dos elementos:</p>
            <button onclick="iniciarMonitoramento()">Iniciar Monitoramento</button>
            <button onclick="pararMonitoramento()">Parar Monitoramento</button>
            <div id="monitor-result" class="result"></div>
        </div>
    </div>

    <!-- Modal Simulado -->
    <div id="artigoModal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 600px; border-radius: 8px;">
            <span class="modal-close-btn" style="float: right; font-size: 28px; cursor: pointer;">&times;</span>

            <div class="modal-header">
                <h2 id="modalArtigoNumero">5º</h2>
            </div>

            <div id="modalArtigoConteudo">
                <p>Este é um modal simulado para testar o botão de revisão.</p>
            </div>

            <div class="modal-controls">
                <div class="modal-actions">
                    <button class="btn-favoritar">
                        <i class="far fa-heart"></i>
                        <span>Favoritar</span>
                    </button>
                    <button class="btn-adicionar-lista">
                        <i class="fas fa-bookmark"></i>
                        <span>Adicionar à Lista</span>
                    </button>
                    <button class="btn-marcar-lido">
                        <i class="far fa-check-circle"></i>
                        <span>Marcar como Lido</span>
                    </button>
                    <!-- O botão de revisão será adicionado aqui -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let monitorInterval = null;
        let sistemaRevisao = null;

        function verificarEstrutura() {
            const elementos = {
                'Modal de Artigo': document.getElementById('artigoModal'),
                'Modal Controls': document.querySelector('.modal-controls'),
                'Modal Actions': document.querySelector('.modal-actions'),
                'Número do Artigo': document.getElementById('modalArtigoNumero'),
                'Botão de Revisão': document.getElementById('btnAdicionarRevisao')
            };

            let resultado = 'Verificação da Estrutura do DOM:\n\n';

            for (const [nome, elemento] of Object.entries(elementos)) {
                const existe = elemento !== null;
                const visivel = elemento && elemento.offsetParent !== null;
                resultado += `${existe ? '✅' : '❌'} ${nome}: ${existe ? 'Existe' : 'Não existe'}`;
                if (existe) {
                    resultado += ` (${visivel ? 'Visível' : 'Oculto'})`;
                }
                resultado += '\n';
            }

            resultado += '\n📋 Detalhes:\n';
            resultado += `- Total de elementos .modal-actions: ${document.querySelectorAll('.modal-actions').length}\n`;
            resultado += `- Sistema de revisão carregado: ${window.sistemaRevisao ? 'Sim' : 'Não'}\n`;
            resultado += `- Scripts carregados: ${document.scripts.length}\n`;

            document.getElementById('estrutura-result').textContent = resultado;
            document.getElementById('estrutura-result').className = 'result info';
        }

        function criarModalSimulado() {
            const modal = document.getElementById('artigoModal');
            if (modal) {
                document.getElementById('modal-result').textContent = '✅ Modal simulado já existe no DOM';
                document.getElementById('modal-result').className = 'result success';
            } else {
                document.getElementById('modal-result').textContent = '❌ Erro: Modal não encontrado';
                document.getElementById('modal-result').className = 'result error';
            }
        }

        function abrirModalSimulado() {
            const modal = document.getElementById('artigoModal');
            if (modal) {
                modal.style.display = 'block';

                // Simular variável global do artigo
                window.artigoAtual = '5';

                document.getElementById('modal-result').textContent = '✅ Modal simulado aberto! Artigo atual definido como "5"';
                document.getElementById('modal-result').className = 'result success';

                // Fechar modal ao clicar no X
                const closeBtn = modal.querySelector('.modal-close-btn');
                closeBtn.onclick = () => {
                    modal.style.display = 'none';
                };
            } else {
                document.getElementById('modal-result').textContent = '❌ Erro: Modal não encontrado';
                document.getElementById('modal-result').className = 'result error';
            }
        }

        function carregarSistemaRevisao() {
            try {
                // Verificar se o sistema já foi carregado
                if (window.sistemaRevisao) {
                    document.getElementById('sistema-result').textContent = '✅ Sistema de revisão já carregado!';
                    document.getElementById('sistema-result').className = 'result success';
                    return;
                }

                // Tentar carregar o sistema
                const script = document.createElement('script');
                script.src = 'js/sistema-revisao.js';
                script.onload = () => {
                    document.getElementById('sistema-result').textContent = '✅ Script do sistema de revisão carregado com sucesso!';
                    document.getElementById('sistema-result').className = 'result success';
                };
                script.onerror = () => {
                    document.getElementById('sistema-result').textContent = '❌ Erro ao carregar script do sistema de revisão';
                    document.getElementById('sistema-result').className = 'result error';
                };
                document.head.appendChild(script);

            } catch (error) {
                document.getElementById('sistema-result').textContent = `❌ Erro: ${error.message}`;
                document.getElementById('sistema-result').className = 'result error';
            }
        }

        function forcarAdicaoBotao() {
            const modalActions = document.querySelector('.modal-actions');

            if (!modalActions) {
                document.getElementById('sistema-result').textContent = '❌ Erro: .modal-actions não encontrado';
                document.getElementById('sistema-result').className = 'result error';
                return;
            }

            // Verificar se o botão já existe
            if (document.getElementById('btnAdicionarRevisao')) {
                document.getElementById('sistema-result').textContent = '✅ Botão de revisão já existe!';
                document.getElementById('sistema-result').className = 'result success';
                return;
            }

            // Criar botão manualmente
            const btnRevisao = document.createElement('button');
            btnRevisao.id = 'btnAdicionarRevisao';
            btnRevisao.className = 'btn-adicionar-revisao pulse';
            btnRevisao.title = 'Adicionar este artigo ao sistema de revisão inteligente';
            btnRevisao.innerHTML = `
                <i class="fas fa-brain"></i>
                <span>Adicionar à Revisão</span>
            `;

            btnRevisao.addEventListener('click', () => {
                // Simular estados visuais
                btnRevisao.className = 'btn-adicionar-revisao loading';
                btnRevisao.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Adicionando...</span>';
                btnRevisao.disabled = true;

                setTimeout(() => {
                    btnRevisao.className = 'btn-adicionar-revisao success';
                    btnRevisao.innerHTML = '<i class="fas fa-check"></i> <span>Adicionado!</span>';

                    setTimeout(() => {
                        btnRevisao.className = 'btn-adicionar-revisao';
                        btnRevisao.innerHTML = '<i class="fas fa-check-circle"></i> <span>Na Revisão</span>';
                        btnRevisao.disabled = false;
                        btnRevisao.style.opacity = '0.8';
                    }, 2000);
                }, 1500);

                alert('🧠 Botão de revisão funcionando!\n\nArtigo: ' + (window.artigoAtual || 'Não definido'));
            });

            modalActions.appendChild(btnRevisao);

            document.getElementById('sistema-result').textContent = '✅ Botão de revisão adicionado manualmente com sucesso!';
            document.getElementById('sistema-result').className = 'result success';
        }

        function iniciarMonitoramento() {
            if (monitorInterval) {
                clearInterval(monitorInterval);
            }

            monitorInterval = setInterval(() => {
                const status = {
                    timestamp: new Date().toLocaleTimeString(),
                    modal_existe: !!document.getElementById('artigoModal'),
                    modal_visivel: document.getElementById('artigoModal')?.style.display === 'block',
                    modal_actions_existe: !!document.querySelector('.modal-actions'),
                    botao_revisao_existe: !!document.getElementById('btnAdicionarRevisao'),
                    artigo_atual: window.artigoAtual || 'Não definido',
                    sistema_revisao: !!window.sistemaRevisao
                };

                let resultado = `🔄 Monitoramento (${status.timestamp}):\n\n`;
                for (const [key, value] of Object.entries(status)) {
                    if (key === 'timestamp') continue;
                    const emoji = value === true ? '✅' : value === false ? '❌' : '📝';
                    resultado += `${emoji} ${key.replace(/_/g, ' ')}: ${value}\n`;
                }

                document.getElementById('monitor-result').textContent = resultado;
                document.getElementById('monitor-result').className = 'result info';
            }, 1000);

            document.getElementById('monitor-result').textContent = '🔄 Monitoramento iniciado...';
            document.getElementById('monitor-result').className = 'result info';
        }

        function pararMonitoramento() {
            if (monitorInterval) {
                clearInterval(monitorInterval);
                monitorInterval = null;
                document.getElementById('monitor-result').textContent = '⏹️ Monitoramento parado.';
                document.getElementById('monitor-result').className = 'result info';
            }
        }

        // Verificar estrutura ao carregar
        window.onload = function() {
            console.log('🔍 Debug do botão de revisão carregado!');
            verificarEstrutura();
        };
    </script>
</body>
</html>
