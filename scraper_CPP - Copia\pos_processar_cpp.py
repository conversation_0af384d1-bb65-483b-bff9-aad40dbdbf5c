#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de pós-processamento para o Código de Processo Penal
Remove duplicações e corrige problemas estruturais
"""

import json
import re
from datetime import datetime

def limpar_caput(caput):
    """
    Limpa o caput removendo caracteres estranhos
    """
    if not caput:
        return ""

    # Remover caracteres estranhos no início
    caput = re.sub(r'^[ºo°\.\s]*', '', caput)

    # Remover espaços múltiplos
    caput = re.sub(r'\s+', ' ', caput)

    return caput.strip()

def consolidar_artigos_duplicados(artigos):
    """
    Consolida artigos duplicados (como Art. 3º-A, Art. 3º-B, etc.)
    """
    artigos_consolidados = {}

    for artigo in artigos:
        artigo_num = artigo["artigo"]

        # Se é um artigo com sufixo, consolidar com o artigo base
        if re.search(r'Art\.\s*\d+º-[A-Z]', artigo_num):
            # Extrair número base (ex: "Art. 3º" de "Art. 3º-A")
            base_match = re.search(r'(Art\.\s*\d+º)', artigo_num)
            if base_match:
                base_num = base_match.group(1)

                # Se o artigo base não existe, criar
                if base_num not in artigos_consolidados:
                    artigos_consolidados[base_num] = {
                        "artigo": base_num,
                        "caput": "",
                        "incisos": [],
                        "paragrafos_numerados": [],
                        "paragrafo_unico": None,
                        "artigos_relacionados": []
                    }

                # Adicionar como artigo relacionado
                if "artigos_relacionados" not in artigos_consolidados[base_num]:
                    artigos_consolidados[base_num]["artigos_relacionados"] = []

                artigos_consolidados[base_num]["artigos_relacionados"].append({
                    "sufixo": artigo_num.replace(base_num, "").strip("-"),
                    "caput": limpar_caput(artigo["caput"]),
                    "incisos": artigo["incisos"],
                    "paragrafos_numerados": artigo["paragrafos_numerados"],
                    "paragrafo_unico": artigo["paragrafo_unico"]
                })
        else:
            # Artigo normal
            if artigo_num not in artigos_consolidados:
                artigos_consolidados[artigo_num] = {
                    "artigo": artigo_num,
                    "caput": limpar_caput(artigo["caput"]),
                    "incisos": artigo["incisos"],
                    "paragrafos_numerados": artigo["paragrafos_numerados"],
                    "paragrafo_unico": artigo["paragrafo_unico"]
                }
            else:
                # Mesclar conteúdo se já existe
                existente = artigos_consolidados[artigo_num]

                # Mesclar caput se o existente estiver vazio
                if not existente["caput"] and artigo["caput"]:
                    existente["caput"] = limpar_caput(artigo["caput"])

                # Mesclar incisos (evitar duplicações)
                for inciso in artigo["incisos"]:
                    if inciso not in existente["incisos"]:
                        existente["incisos"].append(inciso)

                # Mesclar parágrafos
                for paragrafo in artigo["paragrafos_numerados"]:
                    # Verificar se já existe este parágrafo
                    existe = any(p["numero"] == paragrafo["numero"] for p in existente["paragrafos_numerados"])
                    if not existe:
                        existente["paragrafos_numerados"].append(paragrafo)

                # Mesclar parágrafo único
                if not existente["paragrafo_unico"] and artigo["paragrafo_unico"]:
                    existente["paragrafo_unico"] = artigo["paragrafo_unico"]

    return list(artigos_consolidados.values())

def remover_incisos_duplicados(artigos):
    """
    Remove incisos duplicados dentro de cada artigo
    """
    for artigo in artigos:
        # Remover duplicações em incisos
        incisos_unicos = []
        for inciso in artigo["incisos"]:
            if inciso not in incisos_unicos:
                incisos_unicos.append(inciso)
        artigo["incisos"] = incisos_unicos

        # Remover duplicações em parágrafos
        paragrafos_unicos = []
        numeros_vistos = set()
        for paragrafo in artigo["paragrafos_numerados"]:
            if paragrafo["numero"] not in numeros_vistos:
                # Remover duplicações em incisos do parágrafo
                incisos_unicos = []
                for inciso in paragrafo["incisos"]:
                    if inciso not in incisos_unicos:
                        incisos_unicos.append(inciso)
                paragrafo["incisos"] = incisos_unicos

                # Remover duplicações em alíneas do parágrafo
                alineas_unicas = []
                for alinea in paragrafo["alineas"]:
                    if alinea not in alineas_unicas:
                        alineas_unicas.append(alinea)
                paragrafo["alineas"] = alineas_unicas

                paragrafos_unicos.append(paragrafo)
                numeros_vistos.add(paragrafo["numero"])

        artigo["paragrafos_numerados"] = paragrafos_unicos

    return artigos

def ordenar_artigos(artigos):
    """
    Ordena os artigos por número
    """
    def extrair_numero(artigo_id):
        match = re.search(r'Art\.\s*(\d+)', artigo_id)
        return int(match.group(1)) if match else 0

    return sorted(artigos, key=lambda x: extrair_numero(x["artigo"]))

def pos_processar_cpp():
    """
    Função principal de pós-processamento
    """
    print("=" * 60)
    print("🔧 PÓS-PROCESSAMENTO DO CÓDIGO DE PROCESSO PENAL")
    print("=" * 60)

    # Carregar arquivo JSON
    try:
        with open('codigo_processo_penal_limpo.json', 'r', encoding='utf-8') as f:
            artigos = json.load(f)
        print(f"✅ Carregados {len(artigos)} artigos do arquivo JSON")
    except FileNotFoundError:
        print("❌ Arquivo codigo_processo_penal_limpo.json não encontrado!")
        return

    print(f"📊 Processando {len(artigos)} artigos...")

    # 1. Consolidar artigos duplicados
    print("🔄 Consolidando artigos duplicados...")
    artigos = consolidar_artigos_duplicados(artigos)
    print(f"   Artigos após consolidação: {len(artigos)}")

    # 2. Remover incisos duplicados
    print("🔄 Removendo incisos duplicados...")
    artigos = remover_incisos_duplicados(artigos)

    # 3. Ordenar artigos
    print("🔄 Ordenando artigos...")
    artigos = ordenar_artigos(artigos)

    # 4. Salvar arquivo processado
    nome_arquivo_json = 'codigo_processo_penal_final.json'
    nome_arquivo_js = 'codigo_processo_penal_final.js'

    try:
        # Salvar JSON
        with open(nome_arquivo_json, 'w', encoding='utf-8') as f:
            json.dump(artigos, f, ensure_ascii=False, indent=2)
        print(f"✅ Arquivo JSON final salvo: {nome_arquivo_json}")

        # Gerar JavaScript
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        js_content = f"""// Código de Processo Penal Brasileiro - Versão Final Processada
// Gerado em: {timestamp}
// Total de artigos: {len(artigos)}
// Pós-processado para remover duplicações e corrigir estrutura

const codigoProcessoPenalArtigos = {json.dumps(artigos, ensure_ascii=False, indent=2)};

// Função para buscar artigo por número
function buscarArtigoCPP(numero) {{
    return codigoProcessoPenalArtigos.find(artigo =>
        artigo.artigo.toLowerCase().includes(numero.toString().toLowerCase())
    );
}}

// Função para buscar artigos por texto
function buscarArtigosCPPPorTexto(texto) {{
    const textoLower = texto.toLowerCase();
    return codigoProcessoPenalArtigos.filter(artigo =>
        artigo.caput.toLowerCase().includes(textoLower) ||
        artigo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
        (artigo.paragrafo_unico && artigo.paragrafo_unico.toLowerCase().includes(textoLower)) ||
        artigo.paragrafos_numerados.some(paragrafo =>
            paragrafo.texto.toLowerCase().includes(textoLower) ||
            paragrafo.incisos.some(inciso => inciso.toLowerCase().includes(textoLower)) ||
            paragrafo.alineas.some(alinea => alinea.toLowerCase().includes(textoLower))
        )
    );
}}

// Exportar para uso em Node.js (se aplicável)
if (typeof module !== 'undefined' && module.exports) {{
    module.exports = {{
        codigoProcessoPenalArtigos,
        buscarArtigoCPP,
        buscarArtigosCPPPorTexto
    }};
}}
"""

        with open(nome_arquivo_js, 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"✅ Arquivo JavaScript final salvo: {nome_arquivo_js}")

        print(f"\n📊 RESULTADO FINAL:")
        print(f"   • Total de artigos processados: {len(artigos)}")
        if artigos:
            print(f"   • Primeiro artigo: {artigos[0]['artigo']}")
            print(f"   • Último artigo: {artigos[-1]['artigo']}")

        print(f"\n✅ PÓS-PROCESSAMENTO CONCLUÍDO COM SUCESSO!")
        print(f"   📄 Arquivo JSON final: {nome_arquivo_json}")
        print(f"   📄 Arquivo JS final: {nome_arquivo_js}")

    except Exception as e:
        print(f"❌ Erro ao salvar arquivos: {e}")

if __name__ == '__main__':
    pos_processar_cpp()
